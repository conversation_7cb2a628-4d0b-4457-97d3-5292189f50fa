#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

from step_viewer import <PERSON><PERSON><PERSON>werTDK
from PyQt5.QtWidgets import QApplication

# Create QApplication
app = QApplication(sys.argv)

print("=== MANUAL ROTATION SAVE TEST ===")

try:
    # Create viewer
    viewer = StepViewerTDK()
    
    # Load original file
    print("1. Loading SOIC16P127_1270X940X610L89X51.STEP...")
    success = viewer.load_step_file_direct('SOIC16P127_1270X940X610L89X51.STEP')
    if not success:
        print("FAILED to load SOIC16P127_1270X940X610L89X51.STEP")
        sys.exit(1)
    
    print("SUCCESS: File loaded")
    
    # Apply 90 degree X rotation
    print("2. Applying 90 degree X rotation...")
    viewer._apply_model_rotation('top', 'x', 90.0)
    
    # Get applied rotation
    applied_rot = viewer.current_rot_left.copy()
    print(f"Applied rotation: {applied_rot}")
    
    # Update text to get direction vectors
    viewer.update_text_overlays()
    
    # Get unified results for top viewer
    unified_results = viewer._calculate_unified_display_numbers("top")
    model_data = unified_results['model']
    print(f"Applied direction vectors: {model_data}")
    
    # Save file
    test_filename = 'test_manual.step'
    print(f"3. Saving to {test_filename}...")
    
    loader = viewer.step_loader_left
    save_success = viewer._save_step_with_current_rotation(test_filename, loader, applied_rot)
    
    if not save_success:
        print("FAILED to save")
        sys.exit(1)
    
    if not os.path.exists(test_filename):
        print("FAILED - file not created")
        sys.exit(1)
    
    file_size = os.path.getsize(test_filename)
    print(f"SUCCESS: File created - {file_size} bytes")
    
    # Load saved file into bottom viewer
    print("4. Loading saved file into bottom viewer...")
    viewer.active_viewer = "bottom"
    viewer.update_viewer_highlights()
    load_success = viewer.load_step_file_direct(test_filename)
    
    if not load_success:
        print("FAILED to load saved file")
        sys.exit(1)
    
    print("SUCCESS: Saved file loaded")
    
    # Get saved rotation
    saved_rot = viewer.current_rot_right.copy()
    print(f"Saved rotation: {saved_rot}")
    
    # Update text for bottom viewer
    viewer.update_text_overlays()
    
    # Get unified results for bottom viewer
    unified_results_bottom = viewer._calculate_unified_display_numbers("bottom")
    model_data_bottom = unified_results_bottom['model']
    print(f"Saved direction vectors: {model_data_bottom}")
    
    # Compare rotations
    tolerance = 0.1
    x_match = abs(applied_rot['x'] - saved_rot['x']) < tolerance
    y_match = abs(applied_rot['y'] - saved_rot['y']) < tolerance  
    z_match = abs(applied_rot['z'] - saved_rot['z']) < tolerance
    
    print("\n=== COMPARISON ===")
    print(f"Applied: X={applied_rot['x']:.1f} Y={applied_rot['y']:.1f} Z={applied_rot['z']:.1f}")
    print(f"Saved:   X={saved_rot['x']:.1f} Y={saved_rot['y']:.1f} Z={saved_rot['z']:.1f}")
    
    if x_match and y_match and z_match:
        print("SUCCESS: Rotation values preserved!")
    else:
        print("FAILURE: Rotation values not preserved")
    
    # Check direction vectors match
    if model_data == model_data_bottom:
        print("SUCCESS: Direction vectors preserved!")
    else:
        print("FAILURE: Direction vectors not preserved")
        print(f"Applied: {model_data}")
        print(f"Saved:   {model_data_bottom}")
    
    # Clean up
    try:
        os.remove(test_filename)
        print("Cleaned up test file")
    except:
        pass
        
except Exception as e:
    print(f"Exception: {e}")
    import traceback
    traceback.print_exc()

print("=== TEST FINISHED ===")
