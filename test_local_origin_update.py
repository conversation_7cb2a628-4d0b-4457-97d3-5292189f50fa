#!/usr/bin/env python3
"""
Test script to verify local origin update functionality
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== TESTING LOCAL ORIGIN UPDATE ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Testing with: {test_file}")
    
    # Check initial position tracking
    print(f"BEFORE loading - current_pos_left: {viewer.current_pos_left}")
    print(f"BEFORE loading - orig_pos_left: {viewer.orig_pos_left}")
    
    # Load STEP file through normal process (should call _update_local_origin_from_step_file)
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ STEP file loaded successfully")
        
        # Check if local origin was updated
        print(f"AFTER loading - current_pos_left: {viewer.current_pos_left}")
        print(f"AFTER loading - orig_pos_left: {viewer.orig_pos_left}")
        
        # Check if axis_data contains the expected local origin
        if hasattr(viewer.step_loader_left, 'axis_data') and viewer.step_loader_left.axis_data:
            axis_data = viewer.step_loader_left.axis_data
            if 'point' in axis_data:
                expected_origin = axis_data['point']
                print(f"Expected local origin from STEP file: {expected_origin}")
                
                # Compare with actual position tracking
                actual_pos = viewer.current_pos_left
                expected_pos = {'x': expected_origin[0], 'y': expected_origin[1], 'z': expected_origin[2]}
                
                if (abs(actual_pos['x'] - expected_pos['x']) < 0.001 and 
                    abs(actual_pos['y'] - expected_pos['y']) < 0.001 and 
                    abs(actual_pos['z'] - expected_pos['z']) < 0.001):
                    print("✅ LOCAL ORIGIN UPDATE SUCCESSFUL!")
                    print(f"   Position tracking matches STEP file local origin")
                else:
                    print("❌ LOCAL ORIGIN UPDATE FAILED!")
                    print(f"   Expected: {expected_pos}")
                    print(f"   Actual: {actual_pos}")
            else:
                print("❌ No point data in axis_data")
        else:
            print("❌ No axis_data found")
    else:
        print("❌ STEP file loading failed")
else:
    print("❌ No STEP files found for testing")

app.quit()
print("=== TEST COMPLETED ===")
