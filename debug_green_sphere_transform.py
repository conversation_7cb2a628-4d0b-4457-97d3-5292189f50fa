#!/usr/bin/env python3

"""
DEBUG: Green Sphere Transform Investigation
This will examine the green sphere actor in detail to see what transforms are available
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import Step<PERSON>iewerTDK as <PERSON><PERSON>iewer
from PyQt5.QtWidgets import QApplication
import time

def debug_green_sphere_transform():
    """Debug the green sphere actor transform in detail"""
    
    print("=" * 80)
    print("DEBUG: GREEN SPHERE TRANSFORM INVESTIGATION")
    print("=" * 80)
    
    app = QApplication(sys.argv)
    
    try:
        # Create the main viewer
        viewer = StepViewer()
        viewer.show()
        
        # Wait for GUI to initialize
        app.processEvents()
        time.sleep(2)
        
        print("\n1. Loading SOIC-16 footprint...")
        
        # Load the SOIC-16 file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        # Set to TOP viewer and load file
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        print("✅ STEP file loaded successfully")
        
        # Wait for loading to complete
        app.processEvents()
        time.sleep(2)
        
        print("\n2. EXAMINING GREEN SPHERE ACTOR IMMEDIATELY AFTER CREATION...")

        # Check the transform immediately after the part origin overlay was created
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            green_sphere_actor = renderer.part_origin_sphere
            print(f"✅ Found green sphere actor: {type(green_sphere_actor)}")

            print("\n   IMMEDIATE EXAMINATION AFTER CREATION:")

            # Check UserTransform
            user_transform = green_sphere_actor.GetUserTransform()
            print(f"   UserTransform: {user_transform}")
            if user_transform:
                matrix = user_transform.GetMatrix()
                print(f"   UserTransform Matrix: {matrix}")
                print(f"   UserTransform Position: ({matrix.GetElement(0,3):.3f}, {matrix.GetElement(1,3):.3f}, {matrix.GetElement(2,3):.3f})")

            # Check UserMatrix
            user_matrix = green_sphere_actor.GetUserMatrix()
            print(f"   UserMatrix: {user_matrix}")
            if user_matrix:
                print(f"   UserMatrix Position: ({user_matrix.GetElement(0,3):.3f}, {user_matrix.GetElement(1,3):.3f}, {user_matrix.GetElement(2,3):.3f})")

            # Check Position and Orientation
            position = green_sphere_actor.GetPosition()
            orientation = green_sphere_actor.GetOrientation()
            print(f"   Actor Position: ({position[0]:.3f}, {position[1]:.3f}, {position[2]:.3f})")
            print(f"   Actor Orientation: ({orientation[0]:.3f}, {orientation[1]:.3f}, {orientation[2]:.3f})")

            # Check stored direction vectors
            if hasattr(green_sphere_actor, 'z_direction'):
                print(f"   Stored Z Direction: {green_sphere_actor.z_direction}")
            else:
                print("   ❌ No stored Z Direction")

            if hasattr(green_sphere_actor, 'x_direction'):
                print(f"   Stored X Direction: {green_sphere_actor.x_direction}")
            else:
                print("   ❌ No stored X Direction")
        else:
            print("❌ Green sphere actor not found")
            return False
        
        print("\n4. TESTING TRANSFORM RETRIEVAL METHODS...")
        
        # Test different ways to get transform information
        print("   Testing GetMatrix()...")
        try:
            matrix = green_sphere_actor.GetMatrix()
            if matrix:
                print(f"   ✅ GetMatrix() works: Position ({matrix.GetElement(0,3):.3f}, {matrix.GetElement(1,3):.3f}, {matrix.GetElement(2,3):.3f})")
            else:
                print("   ❌ GetMatrix() returned None")
        except Exception as e:
            print(f"   ❌ GetMatrix() failed: {e}")
        
        print("   Testing GetTransform()...")
        try:
            transform = green_sphere_actor.GetTransform()
            if transform:
                matrix = transform.GetMatrix()
                print(f"   ✅ GetTransform() works: Position ({matrix.GetElement(0,3):.3f}, {matrix.GetElement(1,3):.3f}, {matrix.GetElement(2,3):.3f})")
            else:
                print("   ❌ GetTransform() returned None")
        except Exception as e:
            print(f"   ❌ GetTransform() failed: {e}")
        
        print("\n5. TESTING MANUAL TRANSFORM CREATION...")
        
        # Create a test transform and see if we can read it back
        import vtk
        test_transform = vtk.vtkTransform()
        test_transform.Translate(-4.19, -3.6673, 0.4914)
        test_transform.RotateX(65.557)
        test_transform.RotateY(24.444)
        test_transform.RotateZ(90.000)
        
        green_sphere_actor.SetUserTransform(test_transform)
        print("   ✅ Set test UserTransform")
        
        # Try to read it back
        retrieved_transform = green_sphere_actor.GetUserTransform()
        if retrieved_transform:
            matrix = retrieved_transform.GetMatrix()
            print(f"   ✅ Retrieved UserTransform: Position ({matrix.GetElement(0,3):.3f}, {matrix.GetElement(1,3):.3f}, {matrix.GetElement(2,3):.3f})")
            
            # Test the mouse interaction function with this transform
            print("\n6. TESTING MOUSE INTERACTION WITH MANUAL TRANSFORM...")
            viewer.on_mouse_interaction_left(None, "ManualTest")

        else:
            print("   ❌ Could not retrieve UserTransform")

        print("\n7. CHECKING TRANSFORM IMMEDIATELY AFTER CREATION...")

        # Check the transform immediately after the part origin overlay was created
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            print("   Checking green sphere immediately after creation:")

            # Check UserTransform
            user_transform = renderer.part_origin_sphere.GetUserTransform()
            print(f"   UserTransform after creation: {user_transform}")
            if user_transform:
                matrix = user_transform.GetMatrix()
                print(f"   UserTransform Position: ({matrix.GetElement(0,3):.3f}, {matrix.GetElement(1,3):.3f}, {matrix.GetElement(2,3):.3f})")

            # Check Position and Orientation
            position = renderer.part_origin_sphere.GetPosition()
            orientation = renderer.part_origin_sphere.GetOrientation()
            print(f"   Actor Position: ({position[0]:.3f}, {position[1]:.3f}, {position[2]:.3f})")
            print(f"   Actor Orientation: ({orientation[0]:.3f}, {orientation[1]:.3f}, {orientation[2]:.3f})")

            # Check if direction vectors are stored
            if hasattr(renderer.part_origin_sphere, 'z_direction'):
                print(f"   Stored Z Direction: {renderer.part_origin_sphere.z_direction}")
            else:
                print("   ❌ No stored Z Direction")

            if hasattr(renderer.part_origin_sphere, 'x_direction'):
                print(f"   Stored X Direction: {renderer.part_origin_sphere.x_direction}")
            else:
                print("   ❌ No stored X Direction")

        print("\n" + "=" * 80)
        print("GREEN SPHERE TRANSFORM DEBUG COMPLETE")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ Debug test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_green_sphere_transform()
