#!/usr/bin/env python3
"""
Comprehensive debug program to analyze all rotation and positioning issues:
1. Initial loading - Local/Model vs Original Top numbers
2. Green ball disappearing on X+ button
3. World origin arrows moving incorrectly
4. Before/after location and rotation verification
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import time

def comprehensive_debug():
    """Comprehensive debug of all issues"""
    
    print("🔍 COMPREHENSIVE DEBUG: All rotation and positioning issues")
    print("="*100)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for UI to initialize
    app.processEvents()
    time.sleep(1)
    
    def capture_complete_state(label):
        """Capture complete state of everything"""
        print(f"\n{'='*100}")
        print(f"🔍 {label}")
        print(f"{'='*100}")
        
        # 1. TRACKING VARIABLES
        print(f"📊 TRACKING VARIABLES:")
        tracking_vars = {}
        
        if hasattr(viewer, 'current_pos_left'):
            tracking_vars['current_pos_left'] = viewer.current_pos_left
            print(f"   current_pos_left: {viewer.current_pos_left}")
        
        if hasattr(viewer, 'current_rot_left'):
            tracking_vars['current_rot_left'] = viewer.current_rot_left
            print(f"   current_rot_left: {viewer.current_rot_left}")
        
        if hasattr(viewer, 'orig_pos_left'):
            tracking_vars['orig_pos_left'] = viewer.orig_pos_left
            print(f"   orig_pos_left: {viewer.orig_pos_left}")
        
        if hasattr(viewer, 'orig_rot_left'):
            tracking_vars['orig_rot_left'] = viewer.orig_rot_left
            print(f"   orig_rot_left: {viewer.orig_rot_left}")
        
        # 2. DISPLAY TEXT ANALYSIS
        print(f"\n📺 DISPLAY TEXT ANALYSIS:")
        display_texts = {}
        
        # Get text from display labels
        if hasattr(viewer, 'model_text_left') and viewer.model_text_left:
            model_text = viewer.model_text_left.text()
            display_texts['model'] = model_text
            print(f"   Model text: {model_text}")
        
        if hasattr(viewer, 'local_origin_text_left') and viewer.local_origin_text_left:
            local_text = viewer.local_origin_text_left.text()
            display_texts['local_origin'] = local_text
            print(f"   Local Origin text: {local_text}")
        
        if hasattr(viewer, 'world_origin_text_left') and viewer.world_origin_text_left:
            world_text = viewer.world_origin_text_left.text()
            display_texts['world_origin'] = world_text
            print(f"   World Origin text: {world_text}")
        
        # 3. ORIGINAL TOP NUMBERS
        print(f"\n📋 ORIGINAL TOP NUMBERS:")
        original_numbers = {}
        
        if hasattr(viewer, 'original_top_label') and viewer.original_top_label:
            original_text = viewer.original_top_label.text()
            original_numbers['text'] = original_text
            print(f"   Original Top text: {original_text}")
            
            # Parse the numbers from Original Top
            try:
                # Extract position numbers (X= Y= Z=)
                import re
                pos_matches = re.findall(r'[XYZ]= ([-+]?\d*\.?\d+)', original_text)
                if len(pos_matches) >= 3:
                    original_numbers['position'] = {
                        'x': float(pos_matches[0]),
                        'y': float(pos_matches[1]),
                        'z': float(pos_matches[2])
                    }
                    print(f"   Original Top position: {original_numbers['position']}")
            except Exception as e:
                print(f"   Error parsing Original Top: {e}")
        
        # 4. ACTOR ANALYSIS
        print(f"\n📦 ACTOR ANALYSIS:")
        actors_info = {}
        
        if hasattr(viewer, 'vtk_renderer_left') and hasattr(viewer.vtk_renderer_left, 'renderer'):
            renderer = viewer.vtk_renderer_left.renderer
            actors = renderer.GetActors()
            actors.InitTraversal()
            
            print(f"   Total actors: {actors.GetNumberOfItems()}")
            
            model_actors = []
            green_ball_actors = []
            world_origin_actors = []
            other_actors = []
            
            for i in range(actors.GetNumberOfItems()):
                actor = actors.GetNextActor()
                if actor:
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    bounds = actor.GetBounds()
                    
                    # Calculate size
                    size_x = bounds[1] - bounds[0] if bounds[1] > bounds[0] else 0
                    size_y = bounds[3] - bounds[2] if bounds[3] > bounds[2] else 0
                    size_z = bounds[5] - bounds[4] if bounds[5] > bounds[4] else 0
                    max_size = max(size_x, size_y, size_z)
                    
                    # Classify actor
                    actor_info = {
                        'id': i,
                        'position': pos,
                        'orientation': orient,
                        'bounds': bounds,
                        'size': max_size
                    }
                    
                    if max_size > 5.0:
                        actor_info['type'] = 'MODEL'
                        model_actors.append(actor_info)
                    elif 0.1 < max_size < 1.0:
                        # Check if it's green (green ball)
                        try:
                            prop = actor.GetProperty()
                            color = prop.GetColor()
                            if color[1] > 0.8 and color[0] < 0.3 and color[2] < 0.3:  # Green-ish
                                actor_info['type'] = 'GREEN_BALL'
                                green_ball_actors.append(actor_info)
                            else:
                                actor_info['type'] = 'MARKER'
                                other_actors.append(actor_info)
                        except:
                            actor_info['type'] = 'MARKER'
                            other_actors.append(actor_info)
                    elif abs(pos[0]) < 0.1 and abs(pos[1]) < 0.1 and abs(pos[2]) < 0.1:
                        actor_info['type'] = 'WORLD_ORIGIN'
                        world_origin_actors.append(actor_info)
                    else:
                        actor_info['type'] = 'OTHER'
                        other_actors.append(actor_info)
            
            actors_info['model'] = model_actors
            actors_info['green_ball'] = green_ball_actors
            actors_info['world_origin'] = world_origin_actors
            actors_info['other'] = other_actors
            
            print(f"   Model actors: {len(model_actors)}")
            for actor in model_actors:
                print(f"     Actor {actor['id']}: pos={actor['position']}, size={actor['size']:.3f}")
            
            print(f"   Green ball actors: {len(green_ball_actors)}")
            for actor in green_ball_actors:
                print(f"     Actor {actor['id']}: pos={actor['position']}, size={actor['size']:.3f}")
            
            print(f"   World origin actors: {len(world_origin_actors)}")
            for actor in world_origin_actors:
                print(f"     Actor {actor['id']}: pos={actor['position']}, orient={actor['orientation']}")
        
        # 5. CALCULATED MODEL CENTER
        print(f"\n🎯 CALCULATED MODEL CENTER:")
        if model_actors:
            # Calculate actual model center
            min_x = min(actor['bounds'][0] for actor in model_actors)
            max_x = max(actor['bounds'][1] for actor in model_actors)
            min_y = min(actor['bounds'][2] for actor in model_actors)
            max_y = max(actor['bounds'][3] for actor in model_actors)
            min_z = min(actor['bounds'][4] for actor in model_actors)
            max_z = max(actor['bounds'][5] for actor in model_actors)
            
            actual_center = ((min_x + max_x) / 2, (min_y + max_y) / 2, (min_z + max_z) / 2)
            print(f"   Actual model center: {actual_center}")
            
            # Compare with tracking variables
            if 'current_pos_left' in tracking_vars:
                tracked_center = (
                    tracking_vars['current_pos_left']['x'],
                    tracking_vars['current_pos_left']['y'],
                    tracking_vars['current_pos_left']['z']
                )
                difference = (
                    actual_center[0] - tracked_center[0],
                    actual_center[1] - tracked_center[1],
                    actual_center[2] - tracked_center[2]
                )
                distance = (difference[0]**2 + difference[1]**2 + difference[2]**2)**0.5
                
                print(f"   Tracked center: {tracked_center}")
                print(f"   Difference: {difference}")
                print(f"   Distance: {distance:.6f}")
                
                if distance < 0.1:
                    print(f"   ✅ Tracking matches actual center")
                else:
                    print(f"   ❌ Tracking does NOT match actual center")
        
        return {
            'tracking_vars': tracking_vars,
            'display_texts': display_texts,
            'original_numbers': original_numbers,
            'actors_info': actors_info
        }
    
    def compare_states(before, after, operation):
        """Compare states and identify issues"""
        print(f"\n🔍 ANALYSIS: Changes after {operation}")
        print(f"{'='*100}")
        
        # Check green ball visibility
        before_green = len(before['actors_info']['green_ball'])
        after_green = len(after['actors_info']['green_ball'])
        
        print(f"🟢 GREEN BALL VISIBILITY:")
        print(f"   Before: {before_green} green ball actors")
        print(f"   After:  {after_green} green ball actors")
        
        if before_green > 0 and after_green == 0:
            print(f"   ❌ GREEN BALL DISAPPEARED!")
        elif before_green == 0 and after_green > 0:
            print(f"   ✅ Green ball appeared")
        elif before_green > 0 and after_green > 0:
            # Check if position changed correctly
            before_pos = before['actors_info']['green_ball'][0]['position']
            after_pos = after['actors_info']['green_ball'][0]['position']
            movement = (
                after_pos[0] - before_pos[0],
                after_pos[1] - before_pos[1],
                after_pos[2] - before_pos[2]
            )
            distance = (movement[0]**2 + movement[1]**2 + movement[2]**2)**0.5
            print(f"   Green ball moved: {movement} (distance: {distance:.6f})")
        
        # Check world origin arrows
        before_world = before['actors_info']['world_origin']
        after_world = after['actors_info']['world_origin']
        
        print(f"\n🌍 WORLD ORIGIN ARROWS:")
        print(f"   Before: {len(before_world)} world origin actors")
        print(f"   After:  {len(after_world)} world origin actors")
        
        if len(before_world) > 0 and len(after_world) > 0:
            for i, (before_actor, after_actor) in enumerate(zip(before_world, after_world)):
                pos_change = (
                    after_actor['position'][0] - before_actor['position'][0],
                    after_actor['position'][1] - before_actor['position'][1],
                    after_actor['position'][2] - before_actor['position'][2]
                )
                orient_change = (
                    after_actor['orientation'][0] - before_actor['orientation'][0],
                    after_actor['orientation'][1] - before_actor['orientation'][1],
                    after_actor['orientation'][2] - before_actor['orientation'][2]
                )
                
                pos_distance = (pos_change[0]**2 + pos_change[1]**2 + pos_change[2]**2)**0.5
                
                print(f"   Arrow {i}: pos_change={pos_change} (dist: {pos_distance:.6f}), orient_change={orient_change}")
                
                if pos_distance > 0.001:
                    print(f"     ❌ World origin arrow MOVED (should stay at 0,0,0)!")
                else:
                    print(f"     ✅ World origin arrow stayed at origin")
    
    print(f"🔍 Please load a STEP file, then press Enter...")
    input("Press Enter when STEP file is loaded...")
    
    # Capture initial state after loading
    initial_state = capture_complete_state("INITIAL STATE AFTER LOADING")
    
    # Check if initial numbers match Original Top
    print(f"\n🔍 INITIAL LOADING ANALYSIS:")
    print(f"{'='*100}")
    
    if 'original_numbers' in initial_state and 'position' in initial_state['original_numbers']:
        original_pos = initial_state['original_numbers']['position']
        
        if 'current_pos_left' in initial_state['tracking_vars']:
            current_pos = initial_state['tracking_vars']['current_pos_left']
            
            print(f"Original Top position: {original_pos}")
            print(f"Current tracked position: {current_pos}")
            
            diff_x = abs(original_pos['x'] - current_pos['x'])
            diff_y = abs(original_pos['y'] - current_pos['y'])
            diff_z = abs(original_pos['z'] - current_pos['z'])
            
            if diff_x < 0.01 and diff_y < 0.01 and diff_z < 0.01:
                print(f"✅ Initial loading: Numbers match Original Top")
            else:
                print(f"❌ Initial loading: Numbers do NOT match Original Top")
                print(f"   Differences: X={diff_x:.6f}, Y={diff_y:.6f}, Z={diff_z:.6f}")
    
    input("\nPress Enter to test X+ button...")
    
    # Test X+ button
    print(f"\n🔄 Testing X+ button...")
    before_x = capture_complete_state("BEFORE X+ BUTTON")
    
    viewer.rotate_shape('x', 15.0)
    app.processEvents()
    time.sleep(1)
    
    after_x = capture_complete_state("AFTER X+ BUTTON")
    compare_states(before_x, after_x, "X+ 15°")
    
    input("\nPress Enter to test Y+ button...")
    
    # Test Y+ button
    print(f"\n🔄 Testing Y+ button...")
    before_y = capture_complete_state("BEFORE Y+ BUTTON")
    
    viewer.rotate_shape('y', 15.0)
    app.processEvents()
    time.sleep(1)
    
    after_y = capture_complete_state("AFTER Y+ BUTTON")
    compare_states(before_y, after_y, "Y+ 15°")
    
    print(f"\n🔍 COMPREHENSIVE DEBUG COMPLETE")
    print(f"Press Ctrl+C to exit")
    
    try:
        app.exec_()
    except KeyboardInterrupt:
        print("Exiting...")

if __name__ == "__main__":
    comprehensive_debug()
