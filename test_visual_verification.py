#!/usr/bin/env python3
"""
Visual verification test - Load a footprint and show the origin numbers
This will open the GUI so you can see the actual numbers displayed
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK

def test_visual_verification():
    """Load a footprint and show the GUI for visual verification"""
    
    print("🔍 VISUAL VERIFICATION TEST")
    print("=" * 50)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create the viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    print("1. Opening GUI window...")
    
    # Wait a moment for GUI to initialize
    QTimer.singleShot(1000, lambda: load_footprint(viewer))
    
    def load_footprint(viewer):
        print("2. Loading footprint...")
        
        # Load a footprint file
        footprint_file = "e:/python/viewer/save/Footprint_BGA-256_17x17mm_1.0mm_REV001.step"
        
        if os.path.exists(footprint_file):
            print(f"   Loading: {footprint_file}")
            
            # Set active viewer to TOP and load
            viewer.active_viewer = "top"
            success = viewer.load_step_file_direct(footprint_file)
            
            if success:
                print("✅ Footprint loaded successfully!")
                print("\n📋 WHAT TO LOOK FOR:")
                print("   - Check the yellow text in the TOP viewer")
                print("   - Look for 'Origin (X = ... Y = ... Z = ...)' values")
                print("   - The numbers should NOT be all zeros")
                print("   - They should show the actual STEP file origin position")
                print("\n🔍 EXPECTED BEHAVIOR:")
                print("   - Main origin display: Shows actual STEP file values")
                print("   - Local origin display: Shows actual STEP file values") 
                print("   - World origin display: Shows (0.000, 0.000, 0.000)")
                print("\n💡 TIP: The GUI window is now open - you can see the numbers!")
                
                # Print the current values for reference
                if hasattr(viewer, 'current_pos_left'):
                    print(f"\n📊 CURRENT VALUES:")
                    print(f"   current_pos_left: {viewer.current_pos_left}")
                    if hasattr(viewer, 'orig_pos_left'):
                        print(f"   orig_pos_left: {viewer.orig_pos_left}")
                
            else:
                print("❌ Failed to load footprint")
                print("   Trying alternative file...")
                
                # Try alternative files
                alt_files = [
                    "e:/python/viewer/save/Footprint_BGA-256_17x17mm_1.0mm_REV002.step",
                    "e:/python/viewer/save/Footprint_BGA-256_17x17mm_1.0mm.step",
                ]
                
                for alt_file in alt_files:
                    if os.path.exists(alt_file):
                        print(f"   Trying: {alt_file}")
                        viewer.active_viewer = "top"
                        success = viewer.load_step_file_direct(alt_file)
                        if success:
                            print("✅ Alternative footprint loaded!")
                            break
                else:
                    print("❌ No footprint files found")
                    print("   Please check the save directory for STEP files")
        else:
            print(f"❌ File not found: {footprint_file}")
            print("   Listing available files...")
            
            save_dir = "e:/python/viewer/save"
            if os.path.exists(save_dir):
                files = [f for f in os.listdir(save_dir) if f.endswith('.step')]
                if files:
                    print(f"   Found {len(files)} STEP files:")
                    for f in files[:5]:  # Show first 5
                        print(f"     - {f}")
                    
                    # Try to load the first one
                    first_file = os.path.join(save_dir, files[0])
                    print(f"   Loading first file: {files[0]}")
                    viewer.active_viewer = "top"
                    success = viewer.load_step_file_direct(first_file)
                    if success:
                        print("✅ First available file loaded!")
                else:
                    print("   No STEP files found in save directory")
            else:
                print(f"   Save directory not found: {save_dir}")
    
    print("\n🖥️  GUI window should be opening...")
    print("   Close the window when you're done verifying the numbers")
    
    # Run the application
    app.exec_()
    
    print("\n✅ Visual verification test completed")

if __name__ == "__main__":
    test_visual_verification()
