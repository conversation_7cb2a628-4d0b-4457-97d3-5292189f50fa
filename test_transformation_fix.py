#!/usr/bin/env python3
"""
Test the transformation matrix fix
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import numpy as np
import vtk

print("=== TRANSFORMATION FIX TEST ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"🔧 Testing with: {test_file}")
    
    print("\n=== STEP 1: LOAD STEP FILE ===")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ STEP file loaded")
        print(f"   Initial rotation: {viewer.current_rot_left}")
        print(f"   Original rotation: {viewer.orig_rot_left}")
        
        print("\n=== STEP 2: APPLY ROTATION ===")
        print("🔄 Applying X+15° rotation")
        viewer._apply_model_rotation("top", "x", 15.0)
        print(f"   After rotation: {viewer.current_rot_left}")
        
        print("\n=== STEP 3: TEST TRANSFORMATION MATRIX CREATION ===")
        
        # Simulate the save method parameters
        current_pos = viewer._extract_position_from_display("top")
        current_rot = viewer._extract_rotation_from_vtk_actor("top")
        orig_pos = viewer.orig_pos_left
        orig_rot = viewer.orig_rot_left
        
        print(f"   current_pos: {current_pos}")
        print(f"   current_rot: {current_rot}")
        print(f"   orig_pos: {orig_pos}")
        print(f"   orig_rot: {orig_rot}")
        
        # Test the NEW transformation matrix creation (using total rotation)
        print(f"\n=== NEW TRANSFORMATION MATRIX (TOTAL ROTATION) ===")
        combined_transform = vtk.vtkTransform()
        combined_transform.Identity()
        
        # Use TOTAL rotation (current_rot already contains original + delta)
        total_rotation_x = current_rot['x']
        total_rotation_y = current_rot['y'] 
        total_rotation_z = current_rot['z']
        
        print(f"   total_rotation: X={total_rotation_x:.1f}° Y={total_rotation_y:.1f}° Z={total_rotation_z:.1f}°")
        
        # Apply the TOTAL rotation
        combined_transform.RotateX(total_rotation_x)
        combined_transform.RotateY(total_rotation_y)
        combined_transform.RotateZ(total_rotation_z)
        
        # Also include position
        total_position_x = current_pos['x']
        total_position_y = current_pos['y']
        total_position_z = current_pos['z']
        
        print(f"   total_position: X={total_position_x:.3f} Y={total_position_y:.3f} Z={total_position_z:.3f}")
        
        if abs(total_position_x) > 0.001 or abs(total_position_y) > 0.001 or abs(total_position_z) > 0.001:
            combined_transform.Translate(total_position_x, total_position_y, total_position_z)
        
        # Get the 4x4 matrix
        matrix_4x4 = vtk.vtkMatrix4x4()
        combined_transform.GetMatrix(matrix_4x4)
        
        # Convert to numpy
        transform_matrix = np.eye(4)
        for i in range(4):
            for j in range(4):
                transform_matrix[i, j] = matrix_4x4.GetElement(i, j)
        
        print(f"\n=== TOTAL TRANSFORMATION MATRIX ===")
        print("Matrix (4x4):")
        for i in range(4):
            row = [f"{transform_matrix[i,j]:8.4f}" for j in range(4)]
            print(f"   [{' '.join(row)}]")
        
        # Check if this contains the expected rotation
        identity_check = np.allclose(transform_matrix, np.eye(4), atol=1e-6)
        print(f"   Is identity matrix: {identity_check}")
        
        if not identity_check:
            print("✅ Matrix contains transformation data")
            
            # Extract rotation angles from the rotation matrix
            import math
            
            r11, r12, r13 = transform_matrix[0,0], transform_matrix[0,1], transform_matrix[0,2]
            r21, r22, r23 = transform_matrix[1,0], transform_matrix[1,1], transform_matrix[1,2]
            r31, r32, r33 = transform_matrix[2,0], transform_matrix[2,1], transform_matrix[2,2]
            
            # Simple extraction (may not be accurate for all cases)
            if abs(r31) < 0.99999:
                y_rot = math.asin(-r31)
                x_rot = math.atan2(r32, r33)
                z_rot = math.atan2(r21, r11)
            else:
                # Gimbal lock case
                y_rot = -math.pi/2 if r31 < 0 else math.pi/2
                x_rot = math.atan2(-r12, r22)
                z_rot = 0
            
            x_deg = math.degrees(x_rot)
            y_deg = math.degrees(y_rot)
            z_deg = math.degrees(z_rot)
            
            print(f"   Extracted rotation angles:")
            print(f"     X: {x_deg:.1f}°")
            print(f"     Y: {y_deg:.1f}°")
            print(f"     Z: {z_deg:.1f}°")
            
            expected_x = current_rot['x']
            print(f"   Expected X rotation: {expected_x:.1f}°")
            print(f"   Matrix X rotation:   {x_deg:.1f}°")
            print(f"   Difference:          {abs(expected_x - x_deg):.1f}°")
            
            if abs(expected_x - x_deg) < 5.0:
                print("🎉 SUCCESS: Matrix contains the correct total rotation!")
                print("   The transformation matrix fix is working correctly.")
                print("   The issue must be in the OpenCASCADE save process.")
            else:
                print("❌ FAILURE: Matrix does not contain the expected rotation")
        else:
            print("❌ PROBLEM: Matrix is still identity - no transformation!")
            
    else:
        print("❌ Failed to load STEP file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== TRANSFORMATION FIX TEST FINISHED ===")
