#!/usr/bin/env python3
"""
Comprehensive debug program to identify exactly what's happening with duplicate arrows.
This will load a STEP file, count all actors, perform X+ movement, and show exactly what changed.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK as StepViewer
import time

class ComprehensiveMovementDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewer()
        self.debug_data = {}
        
    def debug_all_actors(self, stage_name):
        """Debug all actors in both viewers"""
        print(f"\n{'='*60}")
        print(f"🔍 DEBUGGING ALL ACTORS - {stage_name}")
        print(f"{'='*60}")
        
        # Debug top viewer
        if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
            print(f"\n📊 TOP VIEWER ACTORS:")
            self.debug_renderer_actors(self.viewer.vtk_renderer_left, "TOP")
            
        # Debug bottom viewer  
        if hasattr(self.viewer, 'vtk_renderer_right') and self.viewer.vtk_renderer_right:
            print(f"\n📊 BOTTOM VIEWER ACTORS:")
            self.debug_renderer_actors(self.viewer.vtk_renderer_right, "BOTTOM")
            
        self.debug_data[stage_name] = time.time()
        
    def debug_renderer_actors(self, renderer, viewer_name):
        """Debug all actors in a specific renderer"""
        if not renderer or not hasattr(renderer, 'renderer'):
            print(f"   ❌ No renderer available for {viewer_name}")
            return
            
        # Get all actors from VTK renderer
        actor_collection = renderer.renderer.GetActors()
        if not actor_collection:
            print(f"   ❌ No actor collection for {viewer_name}")
            return
            
        actor_collection.InitTraversal()
        actor_count = 0
        
        print(f"   🎭 ALL ACTORS IN {viewer_name} VIEWER:")
        
        while True:
            actor = actor_collection.GetNextActor()
            if not actor:
                break
                
            actor_count += 1
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            visible = actor.GetVisibility()
            color = actor.GetProperty().GetColor()
            
            # Try to identify actor type
            actor_type = self.identify_actor_type(actor, renderer)
            
            print(f"      Actor {actor_count}: {actor_type}")
            print(f"         Position: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
            print(f"         Orientation: ({orient[0]:.3f}, {orient[1]:.3f}, {orient[2]:.3f})")
            print(f"         Visible: {visible}")
            print(f"         Color: ({color[0]:.3f}, {color[1]:.3f}, {color[2]:.3f})")
            
        print(f"   📈 TOTAL ACTORS: {actor_count}")
        
        # Also debug specific actor references
        self.debug_specific_actors(renderer, viewer_name)
        
    def identify_actor_type(self, actor, renderer):
        """Try to identify what type of actor this is"""
        # Check if it's a known actor
        if hasattr(renderer, 'step_actor') and actor == renderer.step_actor:
            return "STEP_ACTOR (main model)"
        if hasattr(renderer, 'bbox_actor') and actor == renderer.bbox_actor:
            return "BBOX_ACTOR (bounding box)"
        if hasattr(renderer, 'part_origin_sphere') and actor == renderer.part_origin_sphere:
            return "PART_ORIGIN_SPHERE (green sphere)"
        if hasattr(renderer, 'part_origin_x_arrow') and actor == renderer.part_origin_x_arrow:
            return "PART_ORIGIN_X_ARROW (yellow X)"
        if hasattr(renderer, 'part_origin_y_arrow') and actor == renderer.part_origin_y_arrow:
            return "PART_ORIGIN_Y_ARROW (yellow Y)"
        if hasattr(renderer, 'part_origin_z_arrow') and actor == renderer.part_origin_z_arrow:
            return "PART_ORIGIN_Z_ARROW (yellow Z)"
            
        # Check if it's in origin_actors list
        if hasattr(renderer, 'origin_actors') and actor in renderer.origin_actors:
            color = actor.GetProperty().GetColor()
            if color[0] > 0.8 and color[1] < 0.2 and color[2] < 0.2:
                return "WORLD_ORIGIN_X_ARROW (red X)"
            elif color[0] < 0.2 and color[1] > 0.8 and color[2] < 0.2:
                return "WORLD_ORIGIN_Y_ARROW (green Y)"
            elif color[0] < 0.2 and color[1] < 0.2 and color[2] > 0.8:
                return "WORLD_ORIGIN_Z_ARROW (blue Z)"
            else:
                return "WORLD_ORIGIN_UNKNOWN"
                
        # Check if it's in step_actors list
        if hasattr(renderer, 'step_actors') and actor in renderer.step_actors:
            return f"STEP_ACTORS[{renderer.step_actors.index(actor)}] (multi-actor)"
            
        return "UNKNOWN_ACTOR"
        
    def debug_specific_actors(self, renderer, viewer_name):
        """Debug specific actor references"""
        print(f"   🔗 SPECIFIC ACTOR REFERENCES:")
        
        # Check step_actor
        if hasattr(renderer, 'step_actor') and renderer.step_actor:
            pos = renderer.step_actor.GetPosition()
            print(f"      step_actor: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        else:
            print(f"      step_actor: None")
            
        # Check step_actors list
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            print(f"      step_actors: {len(renderer.step_actors)} actors")
            for i, actor in enumerate(renderer.step_actors):
                pos = actor.GetPosition()
                print(f"         [{i}]: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        else:
            print(f"      step_actors: None or empty")
            
        # Check origin_actors list
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            print(f"      origin_actors: {len(renderer.origin_actors)} actors")
            for i, actor in enumerate(renderer.origin_actors):
                pos = actor.GetPosition()
                color = actor.GetProperty().GetColor()
                print(f"         [{i}]: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) color=({color[0]:.1f},{color[1]:.1f},{color[2]:.1f})")
        else:
            print(f"      origin_actors: None or empty")
            
        # Check part origin actors
        part_actors = ['part_origin_sphere', 'part_origin_x_arrow', 'part_origin_y_arrow', 'part_origin_z_arrow']
        for actor_name in part_actors:
            if hasattr(renderer, actor_name):
                actor = getattr(renderer, actor_name)
                if actor:
                    pos = actor.GetPosition()
                    print(f"      {actor_name}: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
                else:
                    print(f"      {actor_name}: None")
            else:
                print(f"      {actor_name}: Not found")
                
    def run_comprehensive_test(self):
        """Run the comprehensive movement test"""
        print("🚀 STARTING COMPREHENSIVE MOVEMENT DEBUG")
        print("="*60)
        
        # Show the viewer
        self.viewer.show()
        
        # Wait for GUI to initialize
        QTimer.singleShot(1000, self.load_step_file)
        
        # Start the event loop
        self.app.exec_()
        
    def load_step_file(self):
        """Load a STEP file for testing"""
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            self.app.quit()
            return
            
        print(f"📂 Loading STEP file: {step_file}")
        
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        
        # Load the file
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print(f"❌ Failed to load STEP file")
            self.app.quit()
            return
            
        print(f"✅ STEP file loaded successfully")
        
        # Debug initial state
        QTimer.singleShot(2000, self.debug_initial_state)
        
    def debug_initial_state(self):
        """Debug the initial state after loading"""
        self.debug_all_actors("INITIAL_STATE")
        
        # Perform X+ movement
        QTimer.singleShot(1000, self.perform_x_movement)
        
    def perform_x_movement(self):
        """Perform X+ movement and debug the result"""
        print(f"\n🔄 PERFORMING X+ MOVEMENT (+2.0mm)")
        
        # Ensure top viewer is active
        self.viewer.active_viewer = "top"
        
        # Perform X+ movement
        self.viewer.move_shape('x', 2.0)
        
        # Debug after movement
        QTimer.singleShot(1000, self.debug_after_movement)
        
    def debug_after_movement(self):
        """Debug state after X+ movement"""
        self.debug_all_actors("AFTER_X_MOVEMENT")
        
        # Compare and analyze
        QTimer.singleShot(1000, self.analyze_results)
        
    def analyze_results(self):
        """Analyze the results and identify the problem"""
        print(f"\n🎯 ANALYSIS COMPLETE")
        print(f"="*60)
        print(f"✅ Debug data collected for comprehensive analysis")
        print(f"📋 Check the debug output above to identify:")
        print(f"   - Which actors are duplicated")
        print(f"   - Which actors moved incorrectly")
        print(f"   - Which actors should/shouldn't move together")
        print(f"   - Color conflicts between different arrow systems")
        
        # Close the application
        QTimer.singleShot(2000, self.app.quit)

if __name__ == "__main__":
    debugger = ComprehensiveMovementDebugger()
    debugger.run_comprehensive_test()
