#!/usr/bin/env python3
"""
Test what happens when we load the STEP file
"""

from step_loader import <PERSON><PERSON><PERSON>oader
import os

def test_step_loading():
    print("TESTING STEP FILE LOADING")
    print("=" * 40)
    
    loader = STEPLoader()
    step_file = "SOIC16P127_1270X940X610L89X51.STEP"
    
    if os.path.exists(step_file):
        print(f"Loading STEP file: {step_file}")
        success, message = loader.load_step_file(step_file)
        print(f"Result: success={success}, message='{message}'")
        
        if success and loader.current_polydata:
            points = loader.current_polydata.GetNumberOfPoints()
            cells = loader.current_polydata.GetNumberOfCells()
            print(f"Geometry: {points} points, {cells} cells")
            
            # Check if it's just a simple box (8 points, 6 faces)
            if points == 8 and cells == 6:
                print("WARNING: This is just a simple cube - not real STEP geometry")
            elif points > 8:
                print("SUCCESS: This appears to be real STEP geometry")
            else:
                print(f"INFO: Geometry has {points} points, {cells} cells")
        else:
            print("FAILED: No geometry loaded")
    else:
        print(f"ERROR: STEP file not found: {step_file}")

if __name__ == "__main__":
    test_step_loading()
