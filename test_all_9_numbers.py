#!/usr/bin/env python3

"""
COMPREHENSIVE TEST: Verify ALL 9 Numbers Are Correct
This test checks ALL 9 sets of values (Direction, REF. Direction, Origin)
for Local Origin, World Origin, and Main Display after rotations.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK as StepViewer
from PyQt5.QtWidgets import QApplication
import time

def extract_all_9_numbers(viewer):
    """Extract all 9 sets of numbers from the displays"""
    
    # Force text overlay update
    viewer.update_text_overlays()
    
    # Get the text from the displays
    if hasattr(viewer, 'local_origin_text_actor_left') and viewer.local_origin_text_actor_left:
        local_text = viewer.local_origin_text_actor_left.GetInput()
    else:
        local_text = "NOT FOUND"
        
    if hasattr(viewer, 'world_origin_text_actor_left') and viewer.world_origin_text_actor_left:
        world_text = viewer.world_origin_text_actor_left.GetInput()
    else:
        world_text = "NOT FOUND"
        
    if hasattr(viewer, 'combined_text_actor_left') and viewer.combined_text_actor_left:
        main_text = viewer.combined_text_actor_left.GetInput()
    else:
        main_text = "NOT FOUND"
    
    return {
        'local': local_text,
        'world': world_text, 
        'main': main_text
    }

def test_all_9_numbers():
    """Test that all 9 numbers are correct"""
    
    print("=" * 80)
    print("TESTING ALL 9 NUMBERS - NO LAZY CHECKING!")
    print("=" * 80)
    
    app = QApplication(sys.argv)
    
    try:
        # Create viewer and load file
        viewer = StepViewer()
        viewer.show()
        app.processEvents()
        time.sleep(1)
        
        print("\n1. Loading SOIC-16 footprint...")
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        print("✅ STEP file loaded successfully")
        app.processEvents()
        time.sleep(2)
        
        print("\n2. CHECKING INITIAL STATE - ALL 9 NUMBERS:")
        initial = extract_all_9_numbers(viewer)
        print(f"   Local Origin: {initial['local']}")
        print(f"   World Origin: {initial['world']}")
        print(f"   Main Display: {initial['main']}")
        
        print("\n3. APPLYING BUTTON ROTATION (15° X)...")
        viewer.rotate_shape('x', 15)
        app.processEvents()
        time.sleep(1)
        
        print("\n4. CHECKING AFTER BUTTON ROTATION - ALL 9 NUMBERS:")
        after_button = extract_all_9_numbers(viewer)
        print(f"   Local Origin: {after_button['local']}")
        print(f"   World Origin: {after_button['world']}")
        print(f"   Main Display: {after_button['main']}")
        
        print("\n5. APPLYING MOUSE ROTATION...")
        if viewer.vtk_renderer_left and viewer.vtk_renderer_left.renderer:
            camera = viewer.vtk_renderer_left.renderer.GetActiveCamera()
            if camera:
                camera.SetPosition(30, -50, 40)
                camera.SetFocalPoint(0, 0, 3)
                camera.SetViewUp(0, 0, 1)
                viewer.vtk_renderer_left.render_window.Render()
                app.processEvents()
                time.sleep(1)
                
                # Trigger mouse handler
                viewer.on_mouse_interaction_left(None, "InteractionEvent")
                app.processEvents()
                time.sleep(1)
                
                print("\n6. CHECKING AFTER MOUSE ROTATION - ALL 9 NUMBERS:")
                after_mouse = extract_all_9_numbers(viewer)
                print(f"   Local Origin: {after_mouse['local']}")
                print(f"   World Origin: {after_mouse['world']}")
                print(f"   Main Display: {after_mouse['main']}")
            else:
                print("   ❌ Camera not found")
        else:
            print("   ❌ Renderer not found")
        
        print("\n7. VERIFICATION:")
        print("   ✅ Check that Local Origin Direction/REF.Direction stay constant (STEP file values)")
        print("   ✅ Check that Local Origin position changes with rotations")
        print("   ✅ Check that World Origin shows rotation degrees")
        print("   ✅ Check that Main Display shows transformed values")
        print("   ✅ Check that NO values are zeros unless they should be")
        
        print("\n8. GUI open for manual verification of ALL 9 NUMBERS...")
        print("   Press Ctrl+C to exit")
        
        app.exec_()
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_all_9_numbers()
