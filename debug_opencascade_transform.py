#!/usr/bin/env python3
"""
Debug OpenCASCADE transformation directly
"""

import sys
import os
sys.path.append('.')

from step_loader import STEPLoader

print("=== DEBUG OPENCASCADE TRANSFORMATION ===")

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Testing with: {test_file}")
    
    # Load the STEP file
    loader = STEPLoader()
    success = loader.load_step_file(test_file)
    
    if success:
        print("✅ STEP file loaded")
        
        # Get original shape bounds
        if hasattr(loader, 'shape') and loader.shape:
            from OCC.Core.Bnd import Bnd_Box
            from OCC.Core.BRepBndLib import brepbndlib_Add
            
            # Get original bounds
            bbox = Bnd_Box()
            brepbndlib_Add(loader.shape, bbox)
            xmin, ymin, zmin, xmax, ymax, zmax = bbox.Get()
            original_bounds = (xmin, xmax, ymin, ymax, zmin, zmax)
            print(f"Original OpenCASCADE bounds: {original_bounds}")
            
            # Test the transformation method
            test_save_file = "debug_opencascade_transform.step"
            if os.path.exists(test_save_file):
                os.remove(test_save_file)
            
            print("\nTesting 90° X rotation...")
            success = loader.save_step_with_rotation(test_save_file, 90.0, 0.0, 0.0)
            
            if success and os.path.exists(test_save_file):
                print("✅ Transformation method completed")
                
                # Load the transformed file and check bounds
                loader2 = STEPLoader()
                success2 = loader2.load_step_file(test_save_file)
                
                if success2:
                    print("✅ Transformed file loaded")
                    
                    # Get transformed bounds
                    bbox2 = Bnd_Box()
                    brepbndlib_Add(loader2.shape, bbox2)
                    xmin2, ymin2, zmin2, xmax2, ymax2, zmax2 = bbox2.Get()
                    transformed_bounds = (xmin2, xmax2, ymin2, ymax2, zmin2, zmax2)
                    print(f"Transformed OpenCASCADE bounds: {transformed_bounds}")
                    
                    # Compare bounds
                    bounds_changed = any(abs(a - b) > 0.01 for a, b in zip(original_bounds, transformed_bounds))
                    print(f"OpenCASCADE bounds changed: {bounds_changed}")
                    
                    if bounds_changed:
                        print("✅ SUCCESS: OpenCASCADE transformation is working!")
                    else:
                        print("❌ FAILURE: OpenCASCADE transformation is NOT working!")
                        print("The transformation is not being applied to the shape")
                        
                        # Debug the transformation method
                        print("\nDEBUGGING TRANSFORMATION METHOD:")
                        
                        try:
                            from OCC.Core.gp import gp_Trsf, gp_Ax1, gp_Pnt, gp_Dir
                            from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_Transform
                            import math
                            
                            print("Creating transformation...")
                            trsf_x = gp_Trsf()
                            axis_x = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(1, 0, 0))
                            trsf_x.SetRotation(axis_x, math.radians(90.0))
                            print("✅ Transformation created")
                            
                            print("Applying transformation to shape...")
                            transform_builder = BRepBuilderAPI_Transform(loader.shape, trsf_x)
                            transform_builder.Build()
                            
                            if transform_builder.IsDone():
                                transformed_shape = transform_builder.Shape()
                                print("✅ Transformation applied")
                                
                                # Check transformed shape bounds
                                bbox3 = Bnd_Box()
                                brepbndlib_Add(transformed_shape, bbox3)
                                xmin3, ymin3, zmin3, xmax3, ymax3, zmax3 = bbox3.Get()
                                debug_bounds = (xmin3, xmax3, ymin3, ymax3, zmin3, zmax3)
                                print(f"Debug transformed bounds: {debug_bounds}")
                                
                                debug_changed = any(abs(a - b) > 0.01 for a, b in zip(original_bounds, debug_bounds))
                                print(f"Debug transformation working: {debug_changed}")
                                
                                if debug_changed:
                                    print("✅ The transformation itself works!")
                                    print("❌ The problem is in the save process")
                                else:
                                    print("❌ The transformation itself is not working")
                            else:
                                print("❌ Transformation failed")
                                
                        except Exception as e:
                            print(f"❌ Error in debug transformation: {e}")
                            import traceback
                            traceback.print_exc()
                else:
                    print("❌ Failed to load transformed file")
                
                # Clean up
                os.remove(test_save_file)
            else:
                print("❌ Transformation method failed")
        else:
            print("❌ No OpenCASCADE shape available")
    else:
        print("❌ Failed to load STEP file")
else:
    print("❌ No STEP files found")

print("=== DEBUG FINISHED ===")
