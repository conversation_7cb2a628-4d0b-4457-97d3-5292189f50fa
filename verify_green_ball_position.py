#!/usr/bin/env python3
"""
Verify the actual green ball position vs expected position.
This will help us see if the green ball is really following the model.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import time

def verify_green_ball():
    """Verify green ball position after rotations"""
    
    print("🔍 VERIFICATION: Testing green ball position")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for UI to initialize
    app.processEvents()
    time.sleep(1)
    
    print("🔍 Please load a STEP file, then press Enter...")
    input("Press Enter when STEP file is loaded...")
    
    def check_green_ball_position(step_name):
        """Check where the green ball actually is"""
        print(f"\n{'='*50}")
        print(f"🔍 {step_name}")
        print(f"{'='*50}")
        
        # Get all the position info
        if hasattr(viewer, 'current_pos_left'):
            print(f"📊 current_pos_left: {viewer.current_pos_left}")
        if hasattr(viewer, 'current_rot_left'):
            print(f"📊 current_rot_left: {viewer.current_rot_left}")
            
        # Find the green ball actor
        green_ball_found = False
        if (hasattr(viewer, 'vtk_renderer_left') and 
            hasattr(viewer.vtk_renderer_left, 'renderer')):
            
            renderer = viewer.vtk_renderer_left.renderer
            actors = renderer.GetActors()
            actors.InitTraversal()
            
            print(f"🔍 Searching through {actors.GetNumberOfItems()} actors...")
            
            for i in range(actors.GetNumberOfItems()):
                actor = actors.GetNextActor()
                if actor:
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    
                    # Check if this might be the green ball
                    # Green balls are usually small spheres
                    bounds = actor.GetBounds()
                    size = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])
                    
                    # Look for small actors that might be the green ball
                    if size < 1.0:  # Small actors
                        print(f"🟢 Small Actor {i}: pos={pos}, orient={orient}, size={size:.3f}")
                        
                        # Check if position matches current_pos_left
                        if hasattr(viewer, 'current_pos_left'):
                            expected_pos = (viewer.current_pos_left['x'], viewer.current_pos_left['y'], viewer.current_pos_left['z'])
                            diff_x = abs(pos[0] - expected_pos[0])
                            diff_y = abs(pos[1] - expected_pos[1]) 
                            diff_z = abs(pos[2] - expected_pos[2])
                            
                            if diff_x < 0.01 and diff_y < 0.01 and diff_z < 0.01:
                                print(f"✅ This appears to be the GREEN BALL!")
                                print(f"   Expected: {expected_pos}")
                                print(f"   Actual:   {pos}")
                                print(f"   Difference: ({diff_x:.6f}, {diff_y:.6f}, {diff_z:.6f})")
                                green_ball_found = True
                            else:
                                print(f"❌ Position doesn't match expected: {expected_pos}")
                                print(f"   Difference: ({diff_x:.3f}, {diff_y:.3f}, {diff_z:.3f})")
        
        if not green_ball_found:
            print("❌ GREEN BALL NOT FOUND or not at expected position!")
            
        return green_ball_found
    
    # Initial check
    check_green_ball_position("INITIAL STATE")
    
    # Do one rotation
    print(f"\n🔄 Performing X+ 15° rotation...")
    viewer.rotate_shape('x', 15.0)
    app.processEvents()
    time.sleep(1)
    
    check_green_ball_position("AFTER X+ 15°")
    
    # Do another rotation
    print(f"\n🔄 Performing Y+ 15° rotation...")
    viewer.rotate_shape('y', 15.0)
    app.processEvents()
    time.sleep(1)
    
    found = check_green_ball_position("AFTER Y+ 15°")
    
    if found:
        print(f"\n✅ SUCCESS: Green ball is following the model correctly!")
    else:
        print(f"\n❌ PROBLEM: Green ball is NOT at the expected position!")
        print(f"   This means the fix is not working as expected.")
    
    print(f"\n🔍 Visual inspection time - check if green ball moved with model...")
    print(f"Press Ctrl+C to exit")
    
    try:
        app.exec_()
    except KeyboardInterrupt:
        print("Exiting...")

if __name__ == "__main__":
    verify_green_ball()
