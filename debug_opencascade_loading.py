#!/usr/bin/env python3
"""
Debug OpenCASCADE loading to see why it's failing
"""

import sys
import os
sys.path.append('.')

def test_opencascade_imports():
    print("=== TESTING OPENCASCADE IMPORTS ===")
    
    try:
        print("1. Testing basic OpenCASCADE imports...")
        from OCC.Core.STEPCAFControl import STEPCAFControl_Reader
        print("   ✅ STEPCAFControl_Reader imported")
        
        from OCC.Core.XCAFApp import XCAFApp_Application
        print("   ✅ XCAFApp_Application imported")
        
        from OCC.Core.XCAFDoc import XCAFDoc_DocumentTool, XCAFDoc_ColorTool
        print("   ✅ XCAFDoc modules imported")
        
        from OCC.Core.TDocStd import TDocStd_Document
        print("   ✅ TDocStd_Document imported")
        
        from OCC.Core.IFSelect import IFSelect_RetDone
        print("   ✅ IFSelect_RetDone imported")
        
        print("✅ All OpenCASCADE imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ OpenCASCADE import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_opencascade_loading():
    print("\n=== TESTING OPENCASCADE STEP LOADING ===")
    
    # Find a STEP file to test with
    step_files = [f for f in os.listdir('.') if f.endswith('.step') or f.endswith('.STEP')]
    if not step_files:
        print("❌ No STEP files found")
        return False
        
    test_file = step_files[0]
    print(f"📁 Testing with: {test_file}")
    
    try:
        from OCC.Core.STEPCAFControl import STEPCAFControl_Reader
        from OCC.Core.XCAFApp import XCAFApp_Application
        from OCC.Core.XCAFDoc import XCAFDoc_DocumentTool, XCAFDoc_ColorTool
        from OCC.Core.TDocStd import TDocStd_Document
        from OCC.Core.IFSelect import IFSelect_RetDone
        
        print("2. Creating XCAF document...")
        app = XCAFApp_Application.GetApplication()
        doc = TDocStd_Document("MDTV-XCAF")
        print("   ✅ XCAF document created")
        
        print("3. Creating STEP reader...")
        reader = STEPCAFControl_Reader()
        reader.SetColorMode(True)
        reader.SetNameMode(True)
        reader.SetLayerMode(True)
        reader.SetPropsMode(True)
        print("   ✅ STEP reader created and configured")
        
        print(f"4. Reading STEP file: {test_file}")
        status = reader.ReadFile(test_file)
        print(f"   Read status: {status} (should be {IFSelect_RetDone})")
        
        if status != IFSelect_RetDone:
            print("❌ Failed to read STEP file")
            return False
            
        print("   ✅ STEP file read successfully")
        
        print("5. Transferring to XCAF document...")
        transfer_success = reader.Transfer(doc)
        print(f"   Transfer success: {transfer_success}")
        
        if not transfer_success:
            print("❌ Failed to transfer STEP data to XCAF document")
            return False
            
        print("   ✅ Transfer successful")
        
        print("6. Getting XCAF tools...")
        root_label = doc.Main()
        shape_tool = XCAFDoc_DocumentTool.ShapeTool(root_label)
        color_tool = XCAFDoc_DocumentTool.ColorTool(root_label)
        print("   ✅ XCAF tools obtained")
        
        print("7. Getting shapes...")
        from OCC.Core.TDF import TDF_LabelSequence
        free_shapes = TDF_LabelSequence()
        shape_tool.GetFreeShapes(free_shapes)
        
        print(f"   Number of free shapes: {free_shapes.Length()}")
        
        if free_shapes.Length() > 0:
            main_label = free_shapes.Value(1)
            shape = shape_tool.GetShape(main_label)
            print(f"   ✅ Got main shape: {shape}")
            print(f"   Shape is null: {shape.IsNull()}")
            return True
        else:
            print("❌ No free shapes found")
            return False
            
    except Exception as e:
        print(f"❌ OpenCASCADE loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_step_loader_directly():
    print("\n=== TESTING STEP LOADER DIRECTLY ===")
    
    from step_loader import STEPLoader
    
    # Find a STEP file to test with
    step_files = [f for f in os.listdir('.') if f.endswith('.step') or f.endswith('.STEP')]
    if not step_files:
        print("❌ No STEP files found")
        return False
        
    test_file = step_files[0]
    print(f"📁 Testing with: {test_file}")
    
    loader = STEPLoader()
    success, message = loader.load_step_file(test_file)
    
    print(f"Load result: {success}")
    print(f"Load message: {message}")
    print(f"Has shape: {hasattr(loader, 'shape') and loader.shape is not None}")
    
    if hasattr(loader, 'shape') and loader.shape:
        print(f"Shape is null: {loader.shape.IsNull()}")
    
    return success

if __name__ == "__main__":
    print("🔧 DEBUGGING OPENCASCADE LOADING ISSUES")
    
    imports_ok = test_opencascade_imports()
    if imports_ok:
        loading_ok = test_opencascade_loading()
        if loading_ok:
            loader_ok = test_step_loader_directly()
            if loader_ok:
                print("\n🎉 ALL TESTS PASSED - OpenCASCADE loading should work!")
            else:
                print("\n💥 STEP LOADER TEST FAILED")
        else:
            print("\n💥 OPENCASCADE LOADING TEST FAILED")
    else:
        print("\n💥 OPENCASCADE IMPORTS FAILED")
