#!/usr/bin/env python3
"""
Debug program to capture and display step_viewer output
"""

import subprocess
import sys
import threading
import time

def run_step_viewer():
    """Run step_viewer.py and capture its output"""
    print("🔧 DEBUG: Starting step_viewer.py with output capture...")
    
    try:
        # Start the process with output capture
        process = subprocess.Popen(
            [sys.executable, "step_viewer.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print("✅ step_viewer.py started, capturing output...")
        print("=" * 60)
        
        # Read output line by line
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(f"OUTPUT: {output.strip()}")
        
        # Get any remaining output
        remaining_output = process.stdout.read()
        if remaining_output:
            print(f"FINAL OUTPUT: {remaining_output}")
            
        return_code = process.poll()
        print(f"🏁 Process ended with return code: {return_code}")
        
    except Exception as e:
        print(f"❌ Error running step_viewer: {e}")

if __name__ == "__main__":
    run_step_viewer()
