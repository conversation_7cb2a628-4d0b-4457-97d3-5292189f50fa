#!/usr/bin/env python3
"""
Debug script to check the actual directions of the yellow arrows to see which ones are pointing in similar directions.
This will help identify why there appear to be duplicate arrows in the screenshot.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK as StepViewer
import math

class ArrowDirectionDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewer()
        
    def calculate_arrow_direction(self, actor):
        """Calculate the actual direction an arrow is pointing based on its orientation"""
        # Get the actor's orientation (in degrees)
        orientation = actor.GetOrientation()
        
        # Convert to radians
        rx = math.radians(orientation[0])
        ry = math.radians(orientation[1]) 
        rz = math.radians(orientation[2])
        
        # Default arrow points in +Y direction [0, 1, 0]
        # Apply rotations to get actual direction
        default_dir = [0, 1, 0]
        
        # Apply Z rotation
        cos_z = math.cos(rz)
        sin_z = math.sin(rz)
        x1 = default_dir[0] * cos_z - default_dir[1] * sin_z
        y1 = default_dir[0] * sin_z + default_dir[1] * cos_z
        z1 = default_dir[2]
        
        # Apply Y rotation  
        cos_y = math.cos(ry)
        sin_y = math.sin(ry)
        x2 = x1 * cos_y + z1 * sin_y
        y2 = y1
        z2 = -x1 * sin_y + z1 * cos_y
        
        # Apply X rotation
        cos_x = math.cos(rx)
        sin_x = math.sin(rx)
        x3 = x2
        y3 = y2 * cos_x - z2 * sin_x
        z3 = y2 * sin_x + z2 * cos_x
        
        return [x3, y3, z3]
        
    def debug_arrow_directions(self):
        """Debug the directions of all yellow arrows"""
        print(f"\n{'='*60}")
        print(f"🧭 ARROW DIRECTION ANALYSIS")
        print(f"{'='*60}")
        
        if not hasattr(self.viewer, 'vtk_renderer_left') or not self.viewer.vtk_renderer_left:
            print("❌ No top renderer available")
            return
            
        renderer = self.viewer.vtk_renderer_left
        
        # Check each part origin arrow
        arrows = [
            ('X Arrow', 'part_origin_x_arrow'),
            ('Y Arrow', 'part_origin_y_arrow'), 
            ('Z Arrow', 'part_origin_z_arrow')
        ]
        
        arrow_directions = []
        
        for arrow_name, arrow_attr in arrows:
            if hasattr(renderer, arrow_attr):
                actor = getattr(renderer, arrow_attr)
                if actor:
                    pos = actor.GetPosition()
                    orientation = actor.GetOrientation()
                    direction = self.calculate_arrow_direction(actor)
                    
                    print(f"\n🟡 {arrow_name}:")
                    print(f"   Position: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
                    print(f"   Orientation: ({orientation[0]:.1f}°, {orientation[1]:.1f}°, {orientation[2]:.1f}°)")
                    print(f"   Direction Vector: ({direction[0]:.3f}, {direction[1]:.3f}, {direction[2]:.3f})")
                    
                    # Determine primary direction
                    abs_dir = [abs(d) for d in direction]
                    max_component = max(abs_dir)
                    max_index = abs_dir.index(max_component)
                    
                    axis_names = ['X', 'Y', 'Z']
                    sign = '+' if direction[max_index] > 0 else '-'
                    primary_dir = f"{sign}{axis_names[max_index]}"
                    
                    print(f"   Primary Direction: {primary_dir} (strength: {max_component:.3f})")
                    
                    arrow_directions.append({
                        'name': arrow_name,
                        'direction': direction,
                        'primary': primary_dir,
                        'strength': max_component
                    })
                else:
                    print(f"\n❌ {arrow_name}: Actor not found")
            else:
                print(f"\n❌ {arrow_name}: Attribute not found")
                
        # Check for similar directions
        print(f"\n🔍 SIMILARITY ANALYSIS:")
        print(f"-" * 30)
        
        for i, arrow1 in enumerate(arrow_directions):
            for j, arrow2 in enumerate(arrow_directions):
                if i < j:  # Avoid duplicate comparisons
                    # Calculate dot product (similarity)
                    dot_product = sum(arrow1['direction'][k] * arrow2['direction'][k] for k in range(3))
                    angle_deg = math.degrees(math.acos(max(-1, min(1, abs(dot_product)))))
                    
                    print(f"   {arrow1['name']} vs {arrow2['name']}:")
                    print(f"      Angle between: {angle_deg:.1f}°")
                    print(f"      Dot product: {dot_product:.3f}")
                    
                    if angle_deg < 15:  # Very similar directions
                        print(f"      🚨 VERY SIMILAR DIRECTIONS! (< 15°)")
                    elif angle_deg < 30:  # Somewhat similar
                        print(f"      ⚠️  Similar directions (< 30°)")
                    else:
                        print(f"      ✅ Distinct directions")
                        
        # Check if any arrows point in the same primary direction
        print(f"\n📊 PRIMARY DIRECTION SUMMARY:")
        print(f"-" * 30)
        
        direction_groups = {}
        for arrow in arrow_directions:
            primary = arrow['primary']
            if primary not in direction_groups:
                direction_groups[primary] = []
            direction_groups[primary].append(arrow['name'])
            
        for direction, arrows in direction_groups.items():
            print(f"   {direction}: {', '.join(arrows)}")
            if len(arrows) > 1:
                print(f"      🚨 MULTIPLE ARROWS POINTING {direction}! This creates visual duplicates!")
                
        # Close the application
        QTimer.singleShot(2000, self.app.quit)
        
    def run_direction_test(self):
        """Run the arrow direction test"""
        print("🧭 STARTING ARROW DIRECTION ANALYSIS")
        print("="*60)
        
        # Show the viewer
        self.viewer.show()
        
        # Wait for GUI to initialize
        QTimer.singleShot(1000, self.load_step_file)
        
        # Start the event loop
        self.app.exec_()
        
    def load_step_file(self):
        """Load a STEP file for testing"""
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            self.app.quit()
            return
            
        print(f"📂 Loading STEP file: {step_file}")
        
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        
        # Load the file
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print(f"❌ Failed to load STEP file")
            self.app.quit()
            return
            
        print(f"✅ STEP file loaded successfully")
        
        # Debug arrow directions
        QTimer.singleShot(2000, self.debug_arrow_directions)

if __name__ == "__main__":
    debugger = ArrowDirectionDebugger()
    debugger.run_direction_test()
