#!/usr/bin/env python3
"""
Check syntax errors in all Python files at once
"""

import os
import ast
import py_compile
import traceback

def check_syntax_errors():
    """Check syntax errors in all Python files"""
    
    python_files = [
        'step_viewer.py',
        'step_loader.py', 
        'vtk_renderer.py',
        'gui_components.py'
    ]
    
    print("🔍 CHECKING SYNTAX ERRORS IN ALL FILES")
    print("=" * 50)
    
    all_good = True
    
    for filename in python_files:
        if not os.path.exists(filename):
            print(f"❌ {filename} - FILE NOT FOUND")
            all_good = False
            continue
            
        print(f"\n📁 Checking {filename}...")
        
        try:
            # Method 1: py_compile
            py_compile.compile(filename, doraise=True)
            print(f"✅ {filename} - SYNTAX OK (py_compile)")
            
        except py_compile.PyCompileError as e:
            print(f"❌ {filename} - SYNTAX ERROR (py_compile):")
            print(f"   {e}")
            all_good = False
            
            # Try AST parse for more details
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                ast.parse(content)
                print(f"   (But AST parse succeeded - might be import issue)")
            except SyntaxError as ast_e:
                print(f"   AST Error: Line {ast_e.lineno}: {ast_e.msg}")
                if ast_e.text:
                    print(f"   Code: {ast_e.text.strip()}")
            except Exception as ast_e:
                print(f"   AST Exception: {ast_e}")
                
        except Exception as e:
            print(f"❌ {filename} - COMPILE ERROR:")
            print(f"   {e}")
            all_good = False
    
    print("\n" + "=" * 50)
    if all_good:
        print("🎉 ALL FILES HAVE VALID SYNTAX!")
    else:
        print("❌ SOME FILES HAVE SYNTAX ERRORS")
        
    return all_good

def find_all_syntax_errors():
    """Find all syntax errors in step_viewer.py specifically"""
    
    filename = "step_viewer.py"
    print(f"\n🔍 DETAILED SYNTAX CHECK: {filename}")
    print("=" * 50)
    
    if not os.path.exists(filename):
        print(f"❌ {filename} not found")
        return False
        
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Try to parse with AST
        ast.parse(content)
        print(f"✅ {filename} - AST parse successful")
        return True
        
    except SyntaxError as e:
        print(f"❌ SYNTAX ERROR at line {e.lineno}:")
        print(f"   Message: {e.msg}")
        if e.text:
            print(f"   Code: {e.text.strip()}")
        print(f"   Position: column {e.offset}")
        
        # Show context around the error
        lines = content.split('\n')
        start_line = max(0, e.lineno - 3)
        end_line = min(len(lines), e.lineno + 2)
        
        print(f"\n📍 CONTEXT (lines {start_line+1}-{end_line}):")
        for i in range(start_line, end_line):
            marker = ">>> " if i == e.lineno - 1 else "    "
            line_num = str(i + 1).rjust(4)
            print(f"{marker}{line_num}: {lines[i]}")
            
        return False
        
    except Exception as e:
        print(f"❌ OTHER ERROR: {e}")
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🚀 COMPREHENSIVE SYNTAX CHECK")
    print("=" * 60)
    
    # Check all files
    all_syntax_ok = check_syntax_errors()
    
    # If step_viewer.py has issues, get detailed info
    if not all_syntax_ok:
        find_all_syntax_errors()
    
    return all_syntax_ok

if __name__ == "__main__":
    success = main()
    print(f"\n{'🎉 SUCCESS' if success else '❌ FAILED'}")
