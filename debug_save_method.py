#!/usr/bin/env python3
"""
Debug the save method to see what's happening
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

def test_save_method():
    print("=== DEBUG SAVE METHOD ===")
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    try:
        # Create viewer
        viewer = StepViewerTDK()
        print("✅ Created viewer")
        
        # Find a STEP file to test with
        step_files = [f for f in os.listdir('.') if f.endswith('.step') or f.endswith('.STEP')]
        if not step_files:
            print("❌ No STEP files found in current directory")
            return False
            
        test_file = step_files[0]
        print(f"📁 Using test file: {test_file}")
        
        # Step 1: Load the file
        print(f"\n1. LOADING: {test_file}")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(test_file)
        
        if not success:
            print("❌ Failed to load file")
            return False
            
        print("✅ File loaded successfully")
        
        # Check what we have
        loader = viewer.step_loader_left
        print(f"   Loader current_filename: {getattr(loader, 'current_filename', 'None')}")
        print(f"   Loader has shape: {hasattr(loader, 'shape') and loader.shape is not None}")
        
        # Step 2: Apply rotation
        print(f"\n2. ROTATING: Applying 90° X rotation")
        viewer._apply_model_rotation('top', 'x', 90.0)
        
        # Get current rotation state
        current_rot = viewer.current_rot_left.copy()
        print(f"   Current rotation state: {current_rot}")
        
        # Check original rotation state
        if hasattr(viewer, 'orig_rot_left'):
            orig_rot = viewer.orig_rot_left
            print(f"   Original rotation state: {orig_rot}")
        else:
            print("   No original rotation state found - setting to zero")
            viewer.orig_rot_left = {'x': 0, 'y': 0, 'z': 0}
            orig_rot = viewer.orig_rot_left
        
        # Step 3: Test the save method directly
        output_file = "debug_save_output.step"
        print(f"\n3. SAVING: Testing save method directly")

        # Remove existing file if it exists
        if os.path.exists(output_file):
            os.remove(output_file)
            print(f"   Removed existing {output_file}")

        # Make sure the loader points to the original file, not the output file
        loader.current_filename = test_file
        print(f"   Set loader current_filename to: {loader.current_filename}")
        
        # Call the save method directly with debug
        print("   Calling _save_step_with_current_rotation...")
        save_success = viewer._save_step_with_current_rotation(output_file, loader, current_rot)
        
        print(f"   Save method returned: {save_success}")
        
        # Check if file was created
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"✅ File created: {output_file} ({file_size} bytes)")
            
            # Compare with original
            with open(test_file, 'r', encoding='utf-8', errors='ignore') as f:
                original_content = f.read()
            
            with open(output_file, 'r', encoding='utf-8', errors='ignore') as f:
                saved_content = f.read()
            
            if original_content == saved_content:
                print("❌ File content is identical to original")
                return False
            else:
                print("✅ File content is different from original")
                return True
        else:
            print("❌ File was not created")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_save_method()
    if success:
        print("\n🎉 SAVE METHOD TEST PASSED!")
    else:
        print("\n💥 SAVE METHOD TEST FAILED!")
    
    sys.exit(0 if success else 1)
