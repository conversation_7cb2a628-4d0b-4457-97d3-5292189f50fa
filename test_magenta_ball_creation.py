#!/usr/bin/env python3
"""
Test magenta ball creation and positioning at STEP file origin
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.getcwd())

def test_magenta_ball_creation():
    """Test the magenta ball creation process"""
    print("🔍 TESTING MAGENTA BALL CREATION")
    print("="*50)
    
    try:
        # Import the main viewer
        from step_viewer import Step<PERSON>iewerTDK
        from PyQt5.QtWidgets import QApplication

        # Create Qt application
        app = QApplication(sys.argv)

        # Create viewer
        viewer = StepViewerTDK()
        print("✅ StepViewerTDK created successfully")
        
        # Check if we have a STEP file to load
        step_files = [f for f in os.listdir('.') if f.endswith('.STEP') or f.endswith('.step')]
        if not step_files:
            print("❌ No STEP files found in current directory")
            return False
            
        step_file = step_files[0]
        print(f"📁 Using STEP file: {step_file}")
        
        # Load the STEP file
        print(f"\n🔄 Loading STEP file: {step_file}")
        success = viewer.load_step_file_direct(step_file)
        
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        print("✅ STEP file loaded successfully")
        
        # Check if magenta ball was created
        print(f"\n🔍 CHECKING MAGENTA BALL CREATION:")
        print("-"*40)
        
        # Check top viewer
        if hasattr(viewer, 'vtk_renderer_left'):
            renderer = viewer.vtk_renderer_left
            print(f"✅ Top renderer exists")
            
            if hasattr(renderer, 'part_origin_sphere'):
                if renderer.part_origin_sphere:
                    ball = renderer.part_origin_sphere
                    pos = ball.GetPosition()
                    color = ball.GetProperty().GetColor()
                    
                    print(f"✅ Magenta ball EXISTS!")
                    print(f"   Position: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
                    print(f"   Color: RGB({color[0]:.3f}, {color[1]:.3f}, {color[2]:.3f})")
                    
                    # Check if it's magenta (1.0, 0.0, 1.0)
                    if abs(color[0] - 1.0) < 0.1 and abs(color[1] - 0.0) < 0.1 and abs(color[2] - 1.0) < 0.1:
                        print(f"✅ Color is MAGENTA - creation method was called!")
                    else:
                        print(f"⚠️  Color is NOT magenta - might be old ball")
                        
                    # Check if it's at STEP origin
                    if hasattr(viewer, 'orig_pos_left'):
                        orig_pos = viewer.orig_pos_left
                        print(f"📍 STEP origin should be: ({orig_pos['x']:.3f}, {orig_pos['y']:.3f}, {orig_pos['z']:.3f})")
                        
                        # Check if ball is at STEP origin (within tolerance)
                        distance = ((pos[0] - orig_pos['x'])**2 + (pos[1] - orig_pos['y'])**2 + (pos[2] - orig_pos['z'])**2)**0.5
                        if distance < 0.1:
                            print(f"✅ Ball is at STEP origin (distance: {distance:.3f})")
                        else:
                            print(f"❌ Ball is NOT at STEP origin (distance: {distance:.3f})")
                            print(f"   Ball position: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
                            print(f"   Should be at:  ({orig_pos['x']:.3f}, {orig_pos['y']:.3f}, {orig_pos['z']:.3f})")
                    else:
                        print("❌ No orig_pos_left found - STEP data not extracted")
                        
                else:
                    print("❌ part_origin_sphere is None")
            else:
                print("❌ part_origin_sphere attribute doesn't exist")
        else:
            print("❌ vtk_renderer_left doesn't exist")
            
        # Check if the creation function exists
        print(f"\n🔍 CHECKING CREATION FUNCTION:")
        print("-"*40)
        
        if hasattr(viewer, 'vtk_renderer_left') and hasattr(viewer.vtk_renderer_left, 'create_part_origin_overlay'):
            print("✅ create_part_origin_overlay method exists")
            
            # Try to call it manually to see if it works
            if hasattr(viewer, 'orig_pos_left'):
                print(f"🔧 Manually calling create_part_origin_overlay...")
                try:
                    viewer.vtk_renderer_left.create_part_origin_overlay(
                        viewer.orig_pos_left['x'],
                        viewer.orig_pos_left['y'],
                        viewer.orig_pos_left['z'],
                        getattr(viewer, 'orig_z_direction_left', None),
                        getattr(viewer, 'orig_x_direction_left', None)
                    )
                    print("✅ Manual call succeeded")
                except Exception as e:
                    print(f"❌ Manual call failed: {e}")
            else:
                print("❌ No orig_pos_left to use for manual call")
        else:
            print("❌ create_part_origin_overlay method doesn't exist")
            
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_magenta_ball_creation()
