#!/usr/bin/env python3
"""
Test just the file loading to see what's broken
"""

import sys
import os
sys.path.append('.')

def test_basic_loading():
    print("=== TESTING BASIC FILE LOADING ===")
    
    # Find STEP file
    step_files = [f for f in os.listdir('.') if f.endswith('.STEP') or f.endswith('.step')]
    if not step_files:
        print("❌ No STEP files found")
        return False
    
    test_file = step_files[0]
    print(f"📁 Testing with: {test_file}")
    
    # Test step_loader directly
    try:
        from step_loader import STEPLoader
        loader = STEPLoader()
        print("✅ STEPLoader created")
        
        success, message = loader.load_step_file(test_file)
        print(f"Load result: {success}")
        print(f"Load message: {message}")
        
        if success:
            print("✅ Basic loading works")
            return True
        else:
            print("❌ Basic loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Exception in basic loading: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_loading():
    print("\n=== TESTING GUI LOADING ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from step_viewer import StepViewerTDK
        
        app = QApplication(sys.argv)
        viewer = StepViewerTDK()
        print("✅ GUI created")
        
        # Find STEP file
        step_files = [f for f in os.listdir('.') if f.endswith('.STEP') or f.endswith('.step')]
        test_file = step_files[0]
        
        # Try to load file
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(test_file)
        
        print(f"GUI load result: {success}")
        
        if success:
            print("✅ GUI loading works")
            return True
        else:
            print("❌ GUI loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Exception in GUI loading: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 TESTING FILE LOADING ONLY")
    print("=" * 40)
    
    basic_ok = test_basic_loading()
    gui_ok = test_gui_loading()
    
    if basic_ok and gui_ok:
        print("\n✅ LOADING WORKS")
    else:
        print("\n❌ LOADING IS BROKEN")
        if not basic_ok:
            print("   - Basic loading failed")
        if not gui_ok:
            print("   - GUI loading failed")
