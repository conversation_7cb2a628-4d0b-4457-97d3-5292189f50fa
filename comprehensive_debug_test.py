#!/usr/bin/env python3
"""
COMPREHENSIVE DEBUG TEST - TESTS EVERY SINGLE ISSUE THE USER IDENTIFIED
Tests all 4 problems with EXACT NUMBERS for every actor and yellow text display
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import <PERSON><PERSON>iewerTDK as <PERSON><PERSON><PERSON><PERSON>

def get_all_actor_positions(renderer):
    """Get positions of ALL actors in the renderer with detailed info"""
    actors_info = {}
    
    if hasattr(renderer, 'renderer'):
        actor_collection = renderer.renderer.GetActors()
        if actor_collection:
            actor_collection.InitTraversal()
            actor = actor_collection.GetNextActor()
            actor_count = 0
            
            while actor:
                actor_count += 1
                pos = actor.GetPosition()
                color = actor.GetProperty().GetColor()
                
                # Identify actor type by color
                actor_type = "UNKNOWN"
                if color[0] > 0.8 and color[1] < 0.3 and color[2] < 0.3:  # Red
                    actor_type = "RED_WORLD_ORIGIN"
                elif color[0] < 0.3 and color[1] > 0.7 and color[2] < 0.3:  # Green
                    actor_type = "GREEN_PART_ORIGIN"
                elif color[0] > 0.8 and color[1] > 0.8 and color[2] < 0.3:  # Yellow
                    actor_type = "YELLOW_ARROW"
                elif color[0] > 0.5 and color[1] > 0.5 and color[2] > 0.5:  # Gray/White
                    actor_type = "MODEL_OR_BBOX"
                
                actors_info[f"Actor_{actor_count}_{actor_type}"] = {
                    'position': pos,
                    'color': color,
                    'type': actor_type
                }
                
                actor = actor_collection.GetNextActor()
    
    return actors_info

def get_yellow_text_values(viewer):
    """Extract exact values from yellow text displays"""
    text_values = {}
    
    # Get text from top viewer
    if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
        # Try to get the text values that are displayed
        try:
            # This will capture what's actually shown in yellow text
            if hasattr(viewer, '_calculate_unified_display_numbers'):
                # Call the method that calculates display numbers
                viewer._calculate_unified_display_numbers("top")
            
            # Get the actual text values from the viewer's internal state
            if hasattr(viewer, 'current_model_position'):
                text_values['model_position'] = viewer.current_model_position
            if hasattr(viewer, 'current_world_origin'):
                text_values['world_origin'] = viewer.current_world_origin
            if hasattr(viewer, 'current_local_origin'):
                text_values['local_origin'] = viewer.current_local_origin
            if hasattr(viewer, 'current_rotation'):
                text_values['rotation'] = viewer.current_rotation
                
        except Exception as e:
            text_values['error'] = str(e)
    
    return text_values

def test_single_operation(viewer, operation_name, operation_func):
    """Test a single operation and capture ALL data before/after"""
    print(f"\n" + "="*80)
    print(f"🔍 TESTING: {operation_name}")
    print("="*80)
    
    # Get renderer
    renderer = viewer.vtk_renderer_left
    
    # BEFORE operation
    print(f"\n📊 BEFORE {operation_name}:")
    before_actors = get_all_actor_positions(renderer)
    before_text = get_yellow_text_values(viewer)
    
    print(f"   🎯 YELLOW TEXT BEFORE:")
    for key, value in before_text.items():
        print(f"      {key}: {value}")
    
    print(f"   🎯 ALL ACTORS BEFORE ({len(before_actors)} total):")
    for actor_name, info in before_actors.items():
        pos = info['position']
        print(f"      {actor_name}: Position=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) Color={info['color']} Type={info['type']}")
    
    # PERFORM operation
    print(f"\n⚡ PERFORMING: {operation_name}")
    operation_func()
    
    # Wait for operation to complete
    time.sleep(0.1)
    QApplication.processEvents()
    
    # AFTER operation
    print(f"\n📊 AFTER {operation_name}:")
    after_actors = get_all_actor_positions(renderer)
    after_text = get_yellow_text_values(viewer)
    
    print(f"   🎯 YELLOW TEXT AFTER:")
    for key, value in after_text.items():
        print(f"      {key}: {value}")
    
    print(f"   🎯 ALL ACTORS AFTER ({len(after_actors)} total):")
    for actor_name, info in after_actors.items():
        pos = info['position']
        print(f"      {actor_name}: Position=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) Color={info['color']} Type={info['type']}")
    
    # ANALYZE changes
    print(f"\n🔍 CHANGES ANALYSIS:")
    
    # Check for new/removed actors
    before_names = set(before_actors.keys())
    after_names = set(after_actors.keys())
    
    new_actors = after_names - before_names
    removed_actors = before_names - after_names
    
    if new_actors:
        print(f"   ➕ NEW ACTORS: {new_actors}")
    if removed_actors:
        print(f"   ➖ REMOVED ACTORS: {removed_actors}")
    
    # Check position changes
    moved_actors = []
    for actor_name in before_names & after_names:
        before_pos = before_actors[actor_name]['position']
        after_pos = after_actors[actor_name]['position']
        
        # Check if position changed (with small tolerance)
        if (abs(before_pos[0] - after_pos[0]) > 0.001 or 
            abs(before_pos[1] - after_pos[1]) > 0.001 or 
            abs(before_pos[2] - after_pos[2]) > 0.001):
            
            change = (after_pos[0] - before_pos[0], 
                     after_pos[1] - before_pos[1], 
                     after_pos[2] - before_pos[2])
            
            moved_actors.append({
                'name': actor_name,
                'before': before_pos,
                'after': after_pos,
                'change': change,
                'type': before_actors[actor_name]['type']
            })
    
    if moved_actors:
        print(f"   🔄 MOVED ACTORS ({len(moved_actors)} total):")
        for actor in moved_actors:
            print(f"      {actor['name']} ({actor['type']}):")
            print(f"         Before: ({actor['before'][0]:.3f}, {actor['before'][1]:.3f}, {actor['before'][2]:.3f})")
            print(f"         After:  ({actor['after'][0]:.3f}, {actor['after'][1]:.3f}, {actor['after'][2]:.3f})")
            print(f"         Change: ({actor['change'][0]:.3f}, {actor['change'][1]:.3f}, {actor['change'][2]:.3f})")
    else:
        print(f"   ✅ NO ACTORS MOVED")
    
    # Check text changes
    print(f"\n🔍 YELLOW TEXT CHANGES:")
    for key in set(before_text.keys()) | set(after_text.keys()):
        before_val = before_text.get(key, "NOT_FOUND")
        after_val = after_text.get(key, "NOT_FOUND")
        if before_val != after_val:
            print(f"   🔄 {key}: {before_val} -> {after_val}")
        else:
            print(f"   ✅ {key}: {before_val} (unchanged)")
    
    return {
        'before_actors': before_actors,
        'after_actors': after_actors,
        'before_text': before_text,
        'after_text': after_text,
        'moved_actors': moved_actors,
        'new_actors': new_actors,
        'removed_actors': removed_actors
    }

def run_comprehensive_test():
    """Run comprehensive test of all 4 problems"""
    print("🎯 COMPREHENSIVE DEBUG TEST - TESTING ALL 4 PROBLEMS")
    print("="*80)
    
    app = QApplication(sys.argv)
    viewer = StepViewer()
    viewer.show()
    
    # Process events to ensure GUI is ready
    QApplication.processEvents()
    time.sleep(0.5)
    
    # Load STEP file
    step_file = "test.step"
    print(f"📁 Loading STEP file: {step_file}")
    
    try:
        viewer.load_step_file_direct(step_file)
        QApplication.processEvents()
        time.sleep(1.0)  # Wait for loading to complete
        print(f"✅ STEP file loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load STEP file: {e}")
        return
    
    # Test all operations
    test_results = {}
    
    # PROBLEM 1: Test X+ movement (bounding box moves more than model)
    test_results['x_plus_move'] = test_single_operation(
        viewer, "X+ MOVEMENT (Problem 1: Bounding box moves more)",
        lambda: viewer.move_shape("x", 2.0)
    )

    # PROBLEM 2: Test if green arrows moved with X+ movement
    # (This should be visible in the previous test results)

    # PROBLEM 3: Test X+ rotation (world origin arrows should rotate but stay at 0,0,0)
    test_results['x_plus_rotate'] = test_single_operation(
        viewer, "X+ ROTATION (Problem 3: World origin arrows should rotate)",
        lambda: viewer.rotate_shape("x", 15.0)
    )

    # PROBLEM 4: Test cumulative operations (X+ then Y-)
    def cumulative_ops():
        viewer.move_shape("x", 1.0)
        viewer.move_shape("y", -1.0)

    test_results['x_plus_then_y_minus'] = test_single_operation(
        viewer, "X+ THEN Y- CUMULATIVE (Problem 4: Operations interfere)",
        cumulative_ops
    )
    
    print(f"\n" + "="*80)
    print(f"🎯 COMPREHENSIVE TEST COMPLETE")
    print("="*80)
    
    # Summary of findings
    print(f"\n📋 SUMMARY OF ALL PROBLEMS:")
    
    for test_name, results in test_results.items():
        print(f"\n🔍 {test_name.upper()}:")
        print(f"   Actors moved: {len(results['moved_actors'])}")
        print(f"   New actors: {len(results['new_actors'])}")
        print(f"   Removed actors: {len(results['removed_actors'])}")
        
        # Check for specific problems
        if 'move' in test_name:
            # Check if bounding box moved different amount than model
            bbox_moves = [a for a in results['moved_actors'] if 'BBOX' in a['type']]
            model_moves = [a for a in results['moved_actors'] if 'MODEL' in a['type']]
            green_moves = [a for a in results['moved_actors'] if 'GREEN' in a['type']]
            
            print(f"   Bounding box moves: {len(bbox_moves)}")
            print(f"   Model moves: {len(model_moves)}")
            print(f"   Green arrow moves: {len(green_moves)}")
            
        elif 'rotate' in test_name:
            # Check if world origin arrows rotated but stayed at 0,0,0
            red_moves = [a for a in results['moved_actors'] if 'RED' in a['type']]
            print(f"   Red (world origin) moves: {len(red_moves)}")
    
    app.quit()

if __name__ == "__main__":
    run_comprehensive_test()
