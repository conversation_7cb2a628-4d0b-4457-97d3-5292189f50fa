#!/usr/bin/env python3
"""
Test OpenCASCADE transformation directly
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import vtk

print("=== OPENCASCADE TRANSFORMATION TEST ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"🔧 Testing with: {test_file}")
    
    print("\n=== STEP 1: LOAD STEP FILE ===")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ STEP file loaded")
        
        loader = viewer.step_loader_left
        if hasattr(loader, 'shape') and loader.shape:
            print("✅ OpenCASCADE shape available")
            
            print("\n=== STEP 2: CREATE VTK TRANSFORMATION ===")
            # Create a simple 90° X rotation
            vtk_transform = vtk.vtkTransform()
            vtk_transform.Identity()
            vtk_transform.RotateX(90.0)
            
            matrix = vtk_transform.GetMatrix()
            print("VTK transformation matrix:")
            for i in range(4):
                row = [matrix.GetElement(i, j) for j in range(4)]
                print(f"   [{row[0]:8.3f} {row[1]:8.3f} {row[2]:8.3f} {row[3]:8.3f}]")
            
            print("\n=== STEP 3: TEST OPENCASCADE TRANSFORMATION ===")
            test_save_file = "test_opencascade_transform.step"
            
            if os.path.exists(test_save_file):
                os.remove(test_save_file)
            
            # Call the OpenCASCADE transformation method directly
            success = loader.save_step_with_vtk_transform(test_save_file, matrix)
            
            if success and os.path.exists(test_save_file):
                file_size = os.path.getsize(test_save_file)
                print(f"✅ OpenCASCADE transformation successful: {test_save_file} ({file_size} bytes)")
                
                print("\n=== STEP 4: LOAD AND COMPARE ===")
                viewer.active_viewer = "bottom"
                load_success = viewer.load_step_file_direct(test_save_file)
                
                if load_success:
                    print("✅ Transformed file loaded")
                    
                    # Get original geometry bounds
                    top_renderer = viewer.vtk_renderer_left
                    if hasattr(top_renderer, 'step_actors') and top_renderer.step_actors:
                        original_actor = top_renderer.step_actors[0]
                        original_bounds = original_actor.GetBounds()
                        original_center = [
                            (original_bounds[0] + original_bounds[1]) / 2,
                            (original_bounds[2] + original_bounds[3]) / 2,
                            (original_bounds[4] + original_bounds[5]) / 2
                        ]
                        print(f"   Original geometry center: {original_center}")
                    
                    # Get transformed geometry bounds
                    bottom_renderer = viewer.vtk_renderer_right
                    if hasattr(bottom_renderer, 'step_actors') and bottom_renderer.step_actors:
                        transformed_actor = bottom_renderer.step_actors[0]
                        transformed_bounds = transformed_actor.GetBounds()
                        transformed_center = [
                            (transformed_bounds[0] + transformed_bounds[1]) / 2,
                            (transformed_bounds[2] + transformed_bounds[3]) / 2,
                            (transformed_bounds[4] + transformed_bounds[5]) / 2
                        ]
                        print(f"   Transformed geometry center: {transformed_center}")
                        
                        # Check if transformation was applied
                        import numpy as np
                        diff = np.array(transformed_center) - np.array(original_center)
                        transformation_applied = np.linalg.norm(diff) > 0.1
                        
                        print(f"   Center difference: {diff}")
                        print(f"   Transformation applied: {transformation_applied}")
                        
                        if transformation_applied:
                            print("✅ SUCCESS: OpenCASCADE transformation working!")
                        else:
                            print("❌ FAILURE: OpenCASCADE transformation not working!")
                            
                            # Let's check if the issue is with the SetValues method
                            print("\n=== DEBUGGING OPENCASCADE TRANSFORMATION ===")
                            try:
                                from OCC.Core.gp import gp_Trsf
                                
                                trsf = gp_Trsf()
                                print("Testing gp_Trsf.SetValues method...")
                                
                                # Try the SetValues method
                                trsf.SetValues(
                                    matrix.GetElement(0, 0), matrix.GetElement(0, 1), matrix.GetElement(0, 2), matrix.GetElement(0, 3),
                                    matrix.GetElement(1, 0), matrix.GetElement(1, 1), matrix.GetElement(1, 2), matrix.GetElement(1, 3),
                                    matrix.GetElement(2, 0), matrix.GetElement(2, 1), matrix.GetElement(2, 2), matrix.GetElement(2, 3)
                                )
                                print("✅ SetValues method works")
                                
                                # Check if the transformation is identity
                                is_identity = (
                                    abs(matrix.GetElement(0, 0) - 1.0) < 0.001 and
                                    abs(matrix.GetElement(1, 1) - 1.0) < 0.001 and
                                    abs(matrix.GetElement(2, 2) - 1.0) < 0.001 and
                                    abs(matrix.GetElement(0, 1)) < 0.001 and
                                    abs(matrix.GetElement(0, 2)) < 0.001 and
                                    abs(matrix.GetElement(1, 0)) < 0.001 and
                                    abs(matrix.GetElement(1, 2)) < 0.001 and
                                    abs(matrix.GetElement(2, 0)) < 0.001 and
                                    abs(matrix.GetElement(2, 1)) < 0.001
                                )
                                
                                print(f"   Matrix is identity: {is_identity}")
                                
                                if is_identity:
                                    print("❌ PROBLEM: VTK transformation matrix is identity (no rotation applied)")
                                else:
                                    print("✅ VTK transformation matrix contains rotation")
                                    
                            except Exception as e:
                                print(f"❌ OpenCASCADE transformation error: {e}")
                                import traceback
                                traceback.print_exc()
                else:
                    print("❌ Failed to load transformed file")
                    
                # Clean up
                os.remove(test_save_file)
                
            else:
                print("❌ OpenCASCADE transformation failed")
        else:
            print("❌ No OpenCASCADE shape available")
    else:
        print("❌ Failed to load STEP file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== OPENCASCADE TRANSFORMATION TEST FINISHED ===")
