#!/usr/bin/env python3
"""
Test script to debug rotation button issues and origin movement problems.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK as StepViewer

class RotationDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewer()
        
    def count_and_track_actors(self, stage_name):
        """Count and track all actors and their positions"""
        if not hasattr(self.viewer, 'vtk_renderer_left') or not self.viewer.vtk_renderer_left:
            print(f"   ❌ No top renderer available")
            return
            
        renderer = self.viewer.vtk_renderer_left.renderer
        if not renderer:
            print(f"   ❌ No VTK renderer available")
            return
            
        print(f"\n📊 {stage_name} - ACTOR TRACKING:")
        print(f"-" * 50)
        
        # Count different types of actors
        red_actors = []
        green_actors = []
        yellow_actors = []
        other_actors = []
        
        actors = renderer.GetActors()
        actors.InitTraversal()
        actor_count = 0
        
        while True:
            actor = actors.GetNextActor()
            if not actor:
                break
                
            actor_count += 1
            color = actor.GetProperty().GetColor()
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            
            # Classify by color
            if color[0] > 0.8 and color[1] < 0.3 and color[2] < 0.3:  # Red
                red_actors.append({'pos': pos, 'orient': orient})
            elif color[0] < 0.3 and color[1] > 0.8 and color[2] < 0.3:  # Green
                green_actors.append({'pos': pos, 'orient': orient})
            elif color[0] > 0.8 and color[1] > 0.8 and color[2] < 0.3:  # Yellow
                yellow_actors.append({'pos': pos, 'orient': orient})
            else:
                other_actors.append({'pos': pos, 'orient': orient, 'color': color})
                
        print(f"   🔴 Red actors (world origin): {len(red_actors)}")
        for i, actor in enumerate(red_actors):
            print(f"      Red {i+1}: pos=({actor['pos'][0]:.3f}, {actor['pos'][1]:.3f}, {actor['pos'][2]:.3f}), orient=({actor['orient'][0]:.1f}°, {actor['orient'][1]:.1f}°, {actor['orient'][2]:.1f}°)")
            
        print(f"   🟢 Green actors (part origin): {len(green_actors)}")
        for i, actor in enumerate(green_actors):
            print(f"      Green {i+1}: pos=({actor['pos'][0]:.3f}, {actor['pos'][1]:.3f}, {actor['pos'][2]:.3f}), orient=({actor['orient'][0]:.1f}°, {actor['orient'][1]:.1f}°, {actor['orient'][2]:.1f}°)")
            
        print(f"   🟡 Yellow actors (part arrows): {len(yellow_actors)}")
        for i, actor in enumerate(yellow_actors):
            print(f"      Yellow {i+1}: pos=({actor['pos'][0]:.3f}, {actor['pos'][1]:.3f}, {actor['pos'][2]:.3f}), orient=({actor['orient'][0]:.1f}°, {actor['orient'][1]:.1f}°, {actor['orient'][2]:.1f}°)")
            
        print(f"   ⚪ Other actors: {len(other_actors)}")
        
        # Check tracking variables
        if hasattr(self.viewer, 'current_pos_left'):
            print(f"   📍 Tracked position: {self.viewer.current_pos_left}")
        if hasattr(self.viewer, 'current_rot_left'):
            print(f"   🔄 Tracked rotation: {self.viewer.current_rot_left}")
            
    def run_rotation_test(self):
        """Run the rotation debugging test"""
        print("🔄 STARTING ROTATION DEBUG TEST")
        print("="*60)
        
        # Show the viewer
        self.viewer.show()
        
        # Wait for GUI to initialize
        QTimer.singleShot(1000, self.load_step_file)
        
        # Start the event loop
        self.app.exec_()
        
    def load_step_file(self):
        """Load a STEP file for testing"""
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            self.app.quit()
            return
            
        print(f"📂 Loading STEP file: {step_file}")
        
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        
        # Load the file
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print(f"❌ Failed to load STEP file")
            self.app.quit()
            return
            
        print(f"✅ STEP file loaded successfully")
        
        # Track initial state
        QTimer.singleShot(2000, self.track_initial_state)
        
    def track_initial_state(self):
        """Track initial state after loading"""
        self.count_and_track_actors("INITIAL STATE")
        
        # Perform X rotation
        QTimer.singleShot(1000, self.perform_x_rotation)
        
    def perform_x_rotation(self):
        """Perform X+ rotation"""
        print(f"\n🔄 PERFORMING X+ ROTATION (+15°)")
        
        # Ensure top viewer is active
        self.viewer.active_viewer = "top"
        
        # Perform X+ rotation
        self.viewer.rotate_shape('x', 15.0)
        
        # Track after rotation
        QTimer.singleShot(1000, self.track_after_rotation)
        
    def track_after_rotation(self):
        """Track state after rotation"""
        self.count_and_track_actors("AFTER X+ ROTATION")
        
        # Perform translation to see if origins move correctly
        QTimer.singleShot(1000, self.perform_translation)
        
    def perform_translation(self):
        """Perform X+ translation"""
        print(f"\n🔄 PERFORMING X+ TRANSLATION (+2.0mm)")
        
        # Perform X+ translation
        self.viewer.move_shape('x', 2.0)
        
        # Track after translation
        QTimer.singleShot(1000, self.track_after_translation)
        
    def track_after_translation(self):
        """Track state after translation and analyze"""
        self.count_and_track_actors("AFTER X+ TRANSLATION")
        
        print(f"\n🎯 ROTATION DEBUG ANALYSIS")
        print(f"="*60)
        print(f"Check the output above to see if:")
        print(f"1. ✅ Red actors (world origin) rotated correctly during X+ rotation")
        print(f"2. ✅ Green actors (part origin) rotated correctly during X+ rotation")
        print(f"3. ✅ Yellow actors (part arrows) rotated correctly during X+ rotation")
        print(f"4. ✅ All actors moved correctly during X+ translation")
        print(f"5. ✅ World origin moved with model center during translation")
        
        # Close the application
        QTimer.singleShot(3000, self.app.quit)

if __name__ == "__main__":
    debugger = RotationDebugger()
    debugger.run_rotation_test()
