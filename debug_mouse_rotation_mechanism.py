#!/usr/bin/env python3
"""
Debug script to understand exactly what happens during mouse rotation in VTK.
This will help identify why the Local Origin display doesn't update.
"""

import sys
import os
import vtk
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK as <PERSON><PERSON><PERSON><PERSON>

def debug_mouse_rotation():
    """Debug mouse rotation mechanism"""
    print("🔍 DEBUGGING MOUSE ROTATION MECHANISM")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    viewer = StepViewer()
    viewer.show()
    
    # Wait for GUI to initialize
    QTimer.singleShot(1000, lambda: load_and_debug(viewer, app))
    
    app.exec_()

def load_and_debug(viewer, app):
    """Load STEP file and debug rotation"""
    try:
        print("\n1. Loading STEP file...")
        
        # Load the STEP file
        step_file = "e:/Python/3d-view/SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            app.quit()
            return
            
        viewer.load_step_file_direct(step_file)
        print("✅ STEP file loaded")
        
        # Wait for loading to complete
        QTimer.singleShot(2000, lambda: analyze_initial_state(viewer, app))
        
    except Exception as e:
        print(f"❌ Error loading STEP file: {e}")
        app.quit()

def analyze_initial_state(viewer, app):
    """Analyze the initial state of all actors"""
    try:
        print("\n2. ANALYZING INITIAL STATE")
        print("-" * 40)
        
        # Get the VTK renderer
        renderer = viewer.vtk_renderer_left.renderer
        actors = renderer.GetActors()
        
        print(f"Total actors in renderer: {actors.GetNumberOfItems()}")
        
        # Iterate through all actors
        actors.InitTraversal()
        actor_count = 0
        green_sphere = None
        
        while True:
            actor = actors.GetNextItem()
            if not actor:
                break
                
            actor_count += 1
            print(f"\nActor {actor_count}:")
            print(f"  Type: {type(actor).__name__}")
            print(f"  Position: {actor.GetPosition()}")
            print(f"  Orientation: {actor.GetOrientation()}")
            
            # Check UserTransform
            user_transform = actor.GetUserTransform()
            if user_transform:
                matrix = user_transform.GetMatrix()
                pos = [matrix.GetElement(0, 3), matrix.GetElement(1, 3), matrix.GetElement(2, 3)]
                print(f"  UserTransform position: {pos}")
            else:
                print(f"  UserTransform: None")
                
            # Check if this is the green sphere
            if hasattr(viewer.vtk_renderer_left, 'part_origin_sphere'):
                if actor == viewer.vtk_renderer_left.part_origin_sphere:
                    green_sphere = actor
                    print(f"  *** THIS IS THE GREEN SPHERE ***")
                    
                    # Check stored attributes
                    if hasattr(actor, 'z_direction'):
                        print(f"  z_direction: {actor.z_direction}")
                    if hasattr(actor, 'x_direction'):
                        print(f"  x_direction: {actor.x_direction}")
        
        if green_sphere:
            print(f"\n✅ Found green sphere actor")
            QTimer.singleShot(1000, lambda: test_rotation_effects(viewer, app, green_sphere))
        else:
            print(f"\n❌ Green sphere not found")
            app.quit()
            
    except Exception as e:
        print(f"❌ Error analyzing initial state: {e}")
        app.quit()

def test_rotation_effects(viewer, app, green_sphere):
    """Test what happens to actors during rotation"""
    try:
        print("\n3. TESTING ROTATION EFFECTS")
        print("-" * 40)
        
        # Record initial state
        print("BEFORE ROTATION:")
        print(f"  Green sphere position: {green_sphere.GetPosition()}")
        print(f"  Green sphere orientation: {green_sphere.GetOrientation()}")
        
        user_transform = green_sphere.GetUserTransform()
        if user_transform:
            matrix = user_transform.GetMatrix()
            pos = [matrix.GetElement(0, 3), matrix.GetElement(1, 3), matrix.GetElement(2, 3)]
            print(f"  UserTransform position: {pos}")
            
            # Print full matrix
            print("  UserTransform matrix:")
            for i in range(4):
                row = [matrix.GetElement(i, j) for j in range(4)]
                print(f"    [{row[0]:8.3f} {row[1]:8.3f} {row[2]:8.3f} {row[3]:8.3f}]")
        
        # Simulate button rotation to see what changes
        print(f"\nPerforming button rotation (X axis, 15 degrees)...")
        viewer.rotate_shape('x', 15)
        
        print(f"\nAFTER BUTTON ROTATION:")
        print(f"  Green sphere position: {green_sphere.GetPosition()}")
        print(f"  Green sphere orientation: {green_sphere.GetOrientation()}")
        
        user_transform = green_sphere.GetUserTransform()
        if user_transform:
            matrix = user_transform.GetMatrix()
            pos = [matrix.GetElement(0, 3), matrix.GetElement(1, 3), matrix.GetElement(2, 3)]
            print(f"  UserTransform position: {pos}")
            
            # Print full matrix
            print("  UserTransform matrix:")
            for i in range(4):
                row = [matrix.GetElement(i, j) for j in range(4)]
                print(f"    [{row[0]:8.3f} {row[1]:8.3f} {row[2]:8.3f} {row[3]:8.3f}]")
        
        # Now test what the mouse interaction handler sees
        QTimer.singleShot(1000, lambda: test_mouse_handler(viewer, app, green_sphere))
        
    except Exception as e:
        print(f"❌ Error testing rotation effects: {e}")
        app.quit()

def test_mouse_handler(viewer, app, green_sphere):
    """Test what the mouse interaction handler actually sees"""
    try:
        print("\n4. TESTING MOUSE INTERACTION HANDLER")
        print("-" * 40)
        
        # Manually call the mouse interaction handler and trace what it does
        print("Calling mouse interaction handler...")
        
        # Add debug prints to trace the handler execution
        original_method = viewer.on_mouse_interaction_left
        
        def debug_mouse_handler():
            print("🔍 INSIDE MOUSE INTERACTION HANDLER")
            
            # Check if green sphere can be found
            if hasattr(viewer.vtk_renderer_left, 'part_origin_sphere'):
                sphere = viewer.vtk_renderer_left.part_origin_sphere
                print(f"✅ Green sphere found in handler")
                print(f"  Position: {sphere.GetPosition()}")
                print(f"  Orientation: {sphere.GetOrientation()}")
                
                user_transform = sphere.GetUserTransform()
                if user_transform:
                    matrix = user_transform.GetMatrix()
                    pos = [matrix.GetElement(0, 3), matrix.GetElement(1, 3), matrix.GetElement(2, 3)]
                    print(f"  UserTransform position: {pos}")
                else:
                    print(f"  ❌ No UserTransform in handler")
                    
                # Check stored attributes
                if hasattr(sphere, 'z_direction'):
                    print(f"  z_direction: {sphere.z_direction}")
                else:
                    print(f"  ❌ No z_direction stored")
                    
                if hasattr(sphere, 'x_direction'):
                    print(f"  x_direction: {sphere.x_direction}")
                else:
                    print(f"  ❌ No x_direction stored")
            else:
                print(f"❌ Green sphere NOT found in handler")
            
            # Call the original method
            return original_method()
        
        # Replace the method temporarily
        viewer.on_mouse_interaction_left = debug_mouse_handler
        
        # Call the handler
        result = viewer.on_mouse_interaction_left()
        
        # Restore original method
        viewer.on_mouse_interaction_left = original_method
        
        print(f"Handler result: {result}")
        
        # Show final analysis
        QTimer.singleShot(1000, lambda: final_analysis(viewer, app))
        
    except Exception as e:
        print(f"❌ Error testing mouse handler: {e}")
        app.quit()

def final_analysis(viewer, app):
    """Show final analysis and recommendations"""
    print("\n" + "=" * 60)
    print("🎯 FINAL ANALYSIS")
    print("=" * 60)
    
    print("\nKEY FINDINGS:")
    print("1. Checked all actors in the renderer")
    print("2. Analyzed green sphere state before/after rotation")
    print("3. Traced mouse interaction handler execution")
    print("4. Identified what data is available vs what's needed")
    
    print("\nNEXT STEPS:")
    print("1. The mouse interaction handler needs to read the CURRENT state")
    print("2. After mouse rotation, the actor positions/orientations change")
    print("3. The handler must extract the new values and update displays")
    print("4. The issue is likely in HOW the handler reads the transformed state")
    
    print(f"\n🔧 Debug complete. Check the output above for clues.")
    print(f"The program will continue running for manual testing.")

if __name__ == "__main__":
    debug_mouse_rotation()
