#!/usr/bin/env python3
"""
Test the fixed STEP file loading system
Verifies that STEP files can be loaded and displayed correctly
"""

import sys
import os
import traceback

def test_unified_system_fixed():
    """Test the fixed unified system"""
    print("\n=== TESTING FIXED UNIFIED SYSTEM ===")
    
    try:
        # Import PyQt5 first to create QApplication
        from PyQt5.QtWidgets import QApplication
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            print("✅ Created QApplication")
        else:
            print("✅ Using existing QApplication")
        
        # Import the viewer
        from step_viewer import StepViewerTDK
        
        # Create viewer instance
        viewer = StepViewerTDK()
        print("✅ StepViewerTDK created successfully")
        
        # Test STEP file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        print(f"📁 Testing with file: {step_file}")
        
        # Set active viewer to top
        viewer.active_viewer = "top"
        print("✅ Set active viewer to TOP")
        
        # Test the fixed load_step_file_direct method
        print("🔄 Calling load_step_file_direct()...")
        result = viewer.load_step_file_direct(step_file)
        print(f"Load result: {result}")
        
        if result:
            print("✅ STEP file loaded successfully!")
            
            # Check if polydata was created
            if hasattr(viewer.step_loader_left, 'current_polydata') and viewer.step_loader_left.current_polydata:
                polydata = viewer.step_loader_left.current_polydata
                print(f"✅ Polydata created: {polydata.GetNumberOfPoints()} points, {polydata.GetNumberOfCells()} cells")
                
                # Check if VTK renderer has the step actor
                if hasattr(viewer.vtk_renderer_left, 'step_actor') and viewer.vtk_renderer_left.step_actor:
                    print("✅ VTK step actor created")
                    print(f"✅ Actor visibility: {viewer.vtk_renderer_left.step_actor.GetVisibility()}")
                    return True
                else:
                    print("❌ VTK step actor not created")
                    return False
            else:
                print("❌ No polydata created")
                return False
        else:
            print("❌ STEP file loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Exception in fixed unified system test: {e}")
        traceback.print_exc()
        return False

def test_unified_load_method():
    """Test the _unified_load method directly"""
    print("\n=== TESTING _unified_load METHOD DIRECTLY ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from step_loader import STEPLoader
        from vtk_renderer import VTKRenderer
        
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create components
        step_loader = STEPLoader()
        vtk_renderer = VTKRenderer()
        
        print("✅ Components created")
        
        # Load STEP file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        success, message = step_loader.load_step_file(step_file)
        if not success:
            print(f"❌ Failed to load STEP file: {message}")
            return False
            
        print(f"✅ STEP file loaded: {message}")
        
        # Test the conversion method
        if hasattr(step_loader, 'current_polydata') and step_loader.current_polydata:
            vtk_data = step_loader.current_polydata
            print(f"✅ VTK data available: {vtk_data.GetNumberOfPoints()} points")
            
            # Test display_polydata method
            display_result = vtk_renderer.display_polydata(vtk_data)
            print(f"Display result: {display_result}")
            
            if display_result:
                print("✅ display_polydata() works correctly")
                return True
            else:
                print("❌ display_polydata() failed")
                return False
        else:
            print("❌ No VTK data available")
            return False
            
    except Exception as e:
        print(f"❌ Exception in _unified_load test: {e}")
        traceback.print_exc()
        return False

def test_step_file_formats():
    """Test different STEP file formats if available"""
    print("\n=== TESTING STEP FILE FORMATS ===")
    
    step_files = []
    
    # Look for STEP files in current directory
    for filename in os.listdir('.'):
        if filename.lower().endswith(('.step', '.stp')):
            step_files.append(filename)
    
    if not step_files:
        print("❌ No STEP files found in current directory")
        return False
        
    print(f"📁 Found {len(step_files)} STEP files: {step_files}")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from step_loader import STEPLoader
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        loader = STEPLoader()
        results = []
        
        for step_file in step_files[:3]:  # Test first 3 files
            print(f"\n🔄 Testing: {step_file}")
            success, message = loader.load_step_file(step_file)
            results.append((step_file, success, message))
            
            if success:
                print(f"✅ {step_file}: {message}")
                if hasattr(loader, 'current_polydata') and loader.current_polydata:
                    polydata = loader.current_polydata
                    print(f"   Polydata: {polydata.GetNumberOfPoints()} points, {polydata.GetNumberOfCells()} cells")
            else:
                print(f"❌ {step_file}: {message}")
        
        successful = sum(1 for _, success, _ in results if success)
        print(f"\n📊 Results: {successful}/{len(results)} files loaded successfully")
        
        return successful > 0
        
    except Exception as e:
        print(f"❌ Exception in STEP file format test: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 TESTING FIXED STEP FILE LOADING")
    print("=" * 50)
    
    tests = [
        ("Fixed Unified System", test_unified_system_fixed),
        ("_unified_load Method", test_unified_load_method),
        ("STEP File Formats", test_step_file_formats),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: CRASHED - {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
    
    print(f"\nResult: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ STEP file loading is now working correctly")
        print("✅ Unified system is functioning properly")
        print("✅ VTK display is working")
    else:
        print(f"\n⚠️  {total-passed} tests failed")
        print("Some issues may still exist")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
