#!/usr/bin/env python3
"""
Debug the unified rotation calculation system
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== UNIFIED ROTATION DEBUG TEST ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"🔧 Testing with: {test_file}")
    
    print("\n=== STEP 1: LOAD STEP FILE ===")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ STEP file loaded")
        
        # Check initial rotation tracking
        print(f"   Initial current_rot_left: {getattr(viewer, 'current_rot_left', 'NOT SET')}")
        print(f"   Initial orig_rot_left: {getattr(viewer, 'orig_rot_left', 'NOT SET')}")
        
        # Get initial unified calculation
        print("\n=== STEP 2: INITIAL UNIFIED CALCULATION ===")
        initial_display = viewer._calculate_unified_display_numbers("top")
        print(f"   Initial unified result: {initial_display}")
        
        print("\n=== STEP 3: APPLY ROTATION ===")
        print("🔄 Applying X+15° rotation")
        viewer._apply_model_rotation("top", "x", 15.0)
        
        # Check rotation tracking after rotation
        print(f"   After rotation current_rot_left: {getattr(viewer, 'current_rot_left', 'NOT SET')}")
        print(f"   After rotation orig_rot_left: {getattr(viewer, 'orig_rot_left', 'NOT SET')}")
        
        # Get unified calculation after rotation
        print("\n=== STEP 4: UNIFIED CALCULATION AFTER ROTATION ===")
        after_display = viewer._calculate_unified_display_numbers("top")
        print(f"   After rotation unified result: {after_display}")
        
        # Compare the results
        print("\n=== STEP 5: COMPARE RESULTS ===")
        if initial_display != after_display:
            print("✅ SUCCESS: Unified calculation changed after rotation!")
            print("   The unified system is working correctly.")
        else:
            print("❌ FAILURE: Unified calculation did NOT change after rotation!")
            print("   This is the bug - unified system not detecting rotation.")
            
        # Test the extraction methods
        print("\n=== STEP 6: TEST EXTRACTION METHODS ===")
        extracted_pos = viewer._extract_position_from_display("top")
        extracted_rot = viewer._extract_rotation_from_vtk_actor("top")
        print(f"   Extracted position: {extracted_pos}")
        print(f"   Extracted rotation: {extracted_rot}")
        
        # Check if extraction matches unified calculation
        if extracted_rot['x'] != 0 or extracted_rot['y'] != 0 or extracted_rot['z'] != 0:
            print("✅ SUCCESS: Extraction methods detect rotation!")
        else:
            print("❌ FAILURE: Extraction methods do NOT detect rotation!")
            
        print("\n=== STEP 7: DEBUG ROTATION TRACKING VARIABLES ===")
        print(f"   current_rot_left: {getattr(viewer, 'current_rot_left', 'NOT SET')}")
        print(f"   orig_rot_left: {getattr(viewer, 'orig_rot_left', 'NOT SET')}")
        
        # Calculate user rotation delta
        if hasattr(viewer, 'current_rot_left') and hasattr(viewer, 'orig_rot_left'):
            delta_rot = {
                'x': viewer.current_rot_left['x'] - viewer.orig_rot_left['x'],
                'y': viewer.current_rot_left['y'] - viewer.orig_rot_left['y'],
                'z': viewer.current_rot_left['z'] - viewer.orig_rot_left['z']
            }
            print(f"   User rotation delta: {delta_rot}")
            
            if abs(delta_rot['x']) > 0.1 or abs(delta_rot['y']) > 0.1 or abs(delta_rot['z']) > 0.1:
                print("✅ SUCCESS: User rotation delta detected!")
            else:
                print("❌ FAILURE: No user rotation delta detected!")
        else:
            print("❌ FAILURE: Rotation tracking variables not set!")
            
    else:
        print("❌ Failed to load STEP file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== UNIFIED ROTATION DEBUG TEST FINISHED ===")
