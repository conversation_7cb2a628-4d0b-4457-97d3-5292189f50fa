#!/usr/bin/env python3
"""
PROPER TEST: <PERSON> Angles to Expose Real Problems
Tests complex rotations that will reveal if elements are truly moving together
"""

import sys
import os
import time
import numpy as np
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

# Add current directory to path
sys.path.append('.')

def test_strange_angles():
    """Test with complex angles that will expose real movement issues"""
    
    print("🎯 PROPER TEST: Strange Angles to Expose Real Problems")
    print("=" * 70)
    
    # Import and create the application
    from step_viewer import StepViewerTDK
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for GUI to initialize
    QTest.qWait(2000)
    print("✅ GUI initialized")
    
    # Load a test STEP file
    test_files = [
        r"e:\python\viewer\save\test_part.step",
        r"e:\python\viewer\save\sample.step",
        r"e:\python\3d-view\test.step",
        r"e:\python\3d-view\sample.step"
    ]
    
    test_file = None
    for f in test_files:
        if os.path.exists(f):
            test_file = f
            break
    
    if test_file:
        print(f"📁 Loading test file: {test_file}")
        try:
            viewer.load_step_file_direct(test_file)
            QTest.qWait(3000)
            print("✅ STEP file loaded successfully")
        except Exception as e:
            print(f"❌ Error loading STEP file: {e}")
    else:
        print("⚠️  No test STEP file found - testing with default geometry")
    
    # Get TOP renderer and find all actors
    top_renderer = None
    if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
        top_renderer = viewer.vtk_renderer_left.renderer
    
    if not top_renderer:
        print("❌ Could not access TOP renderer")
        return False
    
    # Find all actors
    all_actors = []
    actor_collection = top_renderer.GetActors()
    actor_collection.InitTraversal()
    
    while True:
        actor = actor_collection.GetNextActor()
        if not actor:
            break
        all_actors.append(actor)
    
    print(f"✅ Found {len(all_actors)} actors to test")
    
    if len(all_actors) == 0:
        print("❌ No actors found to test!")
        return False
    
    # Test with STRANGE ANGLES that will expose problems
    strange_tests = [
        ("X+37.3°", "x", 37.3),
        ("Y-23.7°", "y", -23.7),
        ("Z+67.1°", "z", 67.1),
        ("X+127.5°", "x", 127.5),
        ("Y-156.8°", "y", -156.8),
        ("Z+203.4°", "z", 203.4),
    ]
    
    print(f"\n🧪 TESTING WITH STRANGE ANGLES...")
    print("-" * 60)
    
    all_results = []
    
    for test_name, axis, degrees in strange_tests:
        print(f"\n🔄 Testing {test_name} rotation...")
        
        # Capture BEFORE state with high precision
        before_states = []
        for i, actor in enumerate(all_actors):
            before_state = {
                'index': i,
                'position': tuple(actor.GetPosition()),
                'orientation': tuple(actor.GetOrientation()),
                'bounds': tuple(actor.GetBounds())
            }
            before_states.append(before_state)
        
        print(f"   📊 Captured BEFORE state for {len(before_states)} actors")
        
        try:
            # Apply the strange angle rotation
            print(f"   🔄 Applying {test_name} rotation...")
            viewer.rotate_shape(axis, degrees)
            QTest.qWait(2000)  # Wait for rotation and rendering
            
            # Force update
            if hasattr(viewer, 'update_text_overlays'):
                viewer.update_text_overlays()
            QTest.qWait(500)
            
        except Exception as e:
            print(f"   ❌ {test_name} FAILED with exception: {e}")
            continue
        
        # Capture AFTER state with high precision
        after_states = []
        for i, actor in enumerate(all_actors):
            after_state = {
                'index': i,
                'position': tuple(actor.GetPosition()),
                'orientation': tuple(actor.GetOrientation()),
                'bounds': tuple(actor.GetBounds())
            }
            after_states.append(after_state)
        
        print(f"   📊 Captured AFTER state for {len(after_states)} actors")
        
        # Detailed analysis of each actor
        test_results = {
            'test_name': test_name,
            'axis': axis,
            'degrees': degrees,
            'moved_actors': 0,
            'stationary_actors': 0,
            'inconsistent_actors': 0,
            'actor_details': []
        }
        
        print(f"   📈 DETAILED MOVEMENT ANALYSIS:")
        
        for before, after in zip(before_states, after_states):
            # Calculate precise differences
            pos_diff = sum(abs(a - b) for a, b in zip(after['position'], before['position']))
            orient_diff = sum(abs(a - b) for a, b in zip(after['orientation'], before['orientation']))
            
            # Calculate bounds difference (for detecting shape changes)
            bounds_diff = sum(abs(a - b) for a, b in zip(after['bounds'], before['bounds']))
            
            # Detailed analysis
            pos_moved = pos_diff > 0.0001
            orient_moved = orient_diff > 0.01  # 0.01 degrees threshold
            bounds_changed = bounds_diff > 0.0001
            
            index = before['index']
            
            actor_detail = {
                'index': index,
                'pos_diff': pos_diff,
                'orient_diff': orient_diff,
                'bounds_diff': bounds_diff,
                'pos_moved': pos_moved,
                'orient_moved': orient_moved,
                'bounds_changed': bounds_changed,
                'before_pos': before['position'],
                'after_pos': after['position'],
                'before_orient': before['orientation'],
                'after_orient': after['orientation']
            }
            
            if pos_moved or orient_moved or bounds_changed:
                print(f"     ✅ Actor {index+1}: MOVED")
                print(f"        Position: {before['position']} → {after['position']} (Δ={pos_diff:.6f})")
                print(f"        Orientation: {before['orientation']} → {after['orientation']} (Δ={orient_diff:.3f}°)")
                if bounds_changed:
                    print(f"        Bounds changed: Δ={bounds_diff:.6f}")
                test_results['moved_actors'] += 1
                actor_detail['status'] = 'MOVED'
            else:
                print(f"     ❌ Actor {index+1}: NO MOVEMENT DETECTED")
                print(f"        Position: {before['position']} (unchanged)")
                print(f"        Orientation: {before['orientation']} (unchanged)")
                test_results['stationary_actors'] += 1
                actor_detail['status'] = 'STATIONARY'
            
            test_results['actor_details'].append(actor_detail)
        
        # Check for consistency - all actors should move together
        if test_results['moved_actors'] > 0 and test_results['stationary_actors'] > 0:
            print(f"   ⚠️  INCONSISTENCY DETECTED: {test_results['moved_actors']} moved, {test_results['stationary_actors']} didn't move")
            test_results['inconsistent_actors'] = test_results['stationary_actors']
        
        # Summary for this test
        success_rate = test_results['moved_actors'] / len(all_actors) * 100 if all_actors else 0
        print(f"   📊 {test_name} Results:")
        print(f"      ✅ Moved: {test_results['moved_actors']}")
        print(f"      ❌ Stationary: {test_results['stationary_actors']}")
        print(f"      📈 Success rate: {success_rate:.1f}%")
        
        if test_results['inconsistent_actors'] > 0:
            print(f"      ⚠️  PROBLEM: Some elements not moving together!")
        elif test_results['moved_actors'] == len(all_actors):
            print(f"      ✅ PERFECT: All elements moved together")
        elif test_results['moved_actors'] == 0:
            print(f"      ❌ FAILURE: No elements moved at all")
        
        all_results.append(test_results)
    
    # Final comprehensive analysis
    print("\n" + "=" * 70)
    print("🏁 COMPREHENSIVE STRANGE ANGLE TEST RESULTS:")
    print("=" * 70)
    
    total_tests = len(all_results)
    perfect_tests = sum(1 for r in all_results if r['moved_actors'] == len(all_actors) and r['stationary_actors'] == 0)
    failed_tests = sum(1 for r in all_results if r['moved_actors'] == 0)
    inconsistent_tests = sum(1 for r in all_results if r['inconsistent_actors'] > 0)
    
    print(f"📊 SUMMARY:")
    print(f"   Total tests: {total_tests}")
    print(f"   Perfect tests (all elements moved): {perfect_tests}")
    print(f"   Failed tests (no movement): {failed_tests}")
    print(f"   Inconsistent tests (some moved, some didn't): {inconsistent_tests}")
    
    if perfect_tests == total_tests:
        print("🎉 EXCELLENT: All visual elements move together perfectly at ALL angles!")
        return True
    elif inconsistent_tests > 0:
        print("⚠️  PROBLEM DETECTED: Some visual elements are NOT moving together!")
        print("   This confirms your observation - there ARE issues with marker movement.")
        
        # Show which actors are problematic
        print(f"\n🔍 PROBLEMATIC ACTORS ANALYSIS:")
        actor_problems = {}
        for result in all_results:
            for actor_detail in result['actor_details']:
                actor_idx = actor_detail['index']
                if actor_idx not in actor_problems:
                    actor_problems[actor_idx] = {'moved': 0, 'stationary': 0}
                
                if actor_detail['status'] == 'MOVED':
                    actor_problems[actor_idx]['moved'] += 1
                else:
                    actor_problems[actor_idx]['stationary'] += 1
        
        for actor_idx, counts in actor_problems.items():
            total = counts['moved'] + counts['stationary']
            if counts['stationary'] > 0:
                print(f"   Actor {actor_idx+1}: Moved {counts['moved']}/{total} times - PROBLEMATIC!")
        
        return False
    else:
        print("⚠️  MIXED RESULTS: Some tests passed, some failed")
        return False

if __name__ == "__main__":
    success = test_strange_angles()
    sys.exit(0 if success else 1)
