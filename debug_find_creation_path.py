#!/usr/bin/env python3
"""
Debug program to find where model and green ball are actually created
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

print("🔍 DEBUG: Starting independent debug to find model/green ball creation path")

# Import the viewer
from step_viewer import StepViewerTDK

def debug_step_by_step():
    """Debug the STEP loading process step by step"""
    print("\n" + "="*80)
    print("🔍 STEP-BY-STEP DEBUG: Finding model and green ball creation")
    print("="*80)
    
    app = QApplication(sys.argv)
    
    # Create viewer
    print("\n📋 STEP 1: Creating viewer...")
    viewer = StepViewerTDK()
    viewer.show()
    print("✅ Viewer created and shown")
    
    # Wait for GUI to initialize
    app.processEvents()
    time.sleep(2)
    
    # Check initial state
    print("\n📋 STEP 2: Checking initial state...")
    check_actors(viewer, "INITIAL STATE")
    
    # Try to load STEP file using the GUI method
    print("\n📋 STEP 3: Loading STEP file using GUI method...")
    step_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if os.path.exists(step_file):
        print(f"Found STEP file: {step_file}")
        
        # Set active viewer to top
        viewer.active_viewer = "top"
        
        # Try the direct load method
        print("Calling load_step_file_direct()...")
        result = viewer.load_step_file_direct(step_file)
        print(f"load_step_file_direct() result: {result}")
        
        # Wait for processing
        app.processEvents()
        time.sleep(3)
        
        # Check state after load
        print("\n📋 STEP 4: Checking state after load...")
        check_actors(viewer, "AFTER LOAD")
        
    else:
        print(f"❌ STEP file not found: {step_file}")
    
    # Keep app running for inspection
    print("\n📋 STEP 5: Keeping app running for inspection...")
    print("Check the GUI window - do you see model and green ball?")
    
    # Exit after 10 seconds
    QTimer.singleShot(10000, app.quit)
    app.exec_()

def check_actors(viewer, stage):
    """Check what actors exist in the viewer"""
    print(f"\n🔍 CHECKING ACTORS - {stage}:")
    
    # Check TOP viewer
    if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
        print("✅ TOP vtk_renderer_left exists")
        
        # Check step_actor
        if hasattr(viewer.vtk_renderer_left, 'step_actor'):
            step_actor = viewer.vtk_renderer_left.step_actor
            print(f"   step_actor: {step_actor is not None}")
            if step_actor:
                print(f"   step_actor position: {step_actor.GetPosition()}")
                print(f"   step_actor visibility: {step_actor.GetVisibility()}")
        
        # Check step_actors (multi-actor)
        if hasattr(viewer.vtk_renderer_left, 'step_actors'):
            step_actors = viewer.vtk_renderer_left.step_actors
            print(f"   step_actors count: {len(step_actors) if step_actors else 0}")
        
        # Check green ball
        if hasattr(viewer.vtk_renderer_left, 'part_origin_sphere'):
            green_ball = viewer.vtk_renderer_left.part_origin_sphere
            print(f"   part_origin_sphere: {green_ball is not None}")
            if green_ball:
                print(f"   green_ball position: {green_ball.GetPosition()}")
                print(f"   green_ball visibility: {green_ball.GetVisibility()}")
        
        # Check total actors in renderer
        if hasattr(viewer.vtk_renderer_left, 'renderer'):
            actors = viewer.vtk_renderer_left.renderer.GetActors()
            print(f"   Total actors in renderer: {actors.GetNumberOfItems()}")
    else:
        print("❌ TOP vtk_renderer_left does not exist")

if __name__ == "__main__":
    debug_step_by_step()
