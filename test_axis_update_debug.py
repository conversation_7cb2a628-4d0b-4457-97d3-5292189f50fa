#!/usr/bin/env python3
"""
Test to debug the AXIS2_PLACEMENT_3D update process
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== AXIS UPDATE DEBUG TEST ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"🔧 Testing with: {test_file}")
    
    print("\n=== STEP 1: LOAD STEP FILE ===")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ STEP file loaded")
        print(f"   Initial rotation: {viewer.current_rot_left}")
        
        # Check if axis_data exists
        loader = viewer.step_loader_left
        if hasattr(loader, 'axis_data') and loader.axis_data:
            print(f"   Original axis_data: {loader.axis_data}")
        else:
            print("   ❌ No axis_data found in loader")
        
        print("\n=== STEP 2: APPLY ROTATION ===")
        print("🔄 Applying X+15° rotation")
        viewer._apply_model_rotation("top", "x", 15.0)
        print(f"   After rotation: {viewer.current_rot_left}")
        
        print("\n=== STEP 3: SAVE WITH AXIS UPDATE DEBUG ===")
        test_save_file = "test_axis_update_debug.step"
        
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
            
        print(f"🔧 Saving to: {test_save_file}")
        
        # Get save parameters
        current_pos = viewer._extract_position_from_display("top")
        current_rot = viewer._extract_rotation_from_vtk_actor("top")
        orig_pos = viewer.orig_pos_left
        orig_rot = viewer.orig_rot_left
        
        print(f"   Save parameters:")
        print(f"     current_rot: {current_rot}")
        print(f"     orig_rot: {orig_rot}")
        
        # Call save method
        save_success = viewer._save_step_with_transformations(
            test_save_file, loader, current_pos, current_rot, orig_pos, orig_rot
        )
        
        if save_success and os.path.exists(test_save_file):
            print("✅ Save completed successfully")
            
            print("\n=== STEP 4: CHECK SAVED FILE CONTENT ===")
            # Read the saved file and check for AXIS2_PLACEMENT_3D entries
            with open(test_save_file, 'r') as f:
                content = f.read()
            
            # Count AXIS2_PLACEMENT_3D entries
            axis_entries = [line for line in content.split('\n') if 'AXIS2_PLACEMENT_3D' in line]
            print(f"   Found {len(axis_entries)} AXIS2_PLACEMENT_3D entries in saved file")
            
            if axis_entries:
                print("   First few AXIS2_PLACEMENT_3D entries:")
                for i, entry in enumerate(axis_entries[:3]):
                    print(f"     {i+1}: {entry}")
            
            # Check if the coordinate system values match what we expect
            if hasattr(loader, 'axis_data') and loader.axis_data:
                original_point = loader.axis_data.get('point', [0, 0, 0])
                print(f"   Original coordinate system point: {original_point}")
                
                # Look for entries that might contain our coordinate system
                point_str = f"{original_point[0]:.3f}"
                matching_entries = [line for line in content.split('\n') if point_str in line]
                print(f"   Found {len(matching_entries)} entries containing original point coordinate")
                
                if matching_entries:
                    print("   Sample matching entries:")
                    for i, entry in enumerate(matching_entries[:2]):
                        print(f"     {i+1}: {entry}")
            
            print("\n=== STEP 5: LOAD SAVED FILE AND CHECK ===")
            viewer.active_viewer = "bottom"
            load_success = viewer.load_step_file_direct(test_save_file)
            
            if load_success:
                print("✅ Saved file loaded successfully")
                print(f"   Loaded rotation: {viewer.current_rot_right}")
                
                # Check if the loaded axis_data is different
                loader_bottom = viewer.step_loader_right
                if hasattr(loader_bottom, 'axis_data') and loader_bottom.axis_data:
                    print(f"   Loaded axis_data: {loader_bottom.axis_data}")
                    
                    # Compare original vs loaded axis data
                    if hasattr(loader, 'axis_data') and loader.axis_data:
                        orig_point = loader.axis_data.get('point', [0, 0, 0])
                        loaded_point = loader_bottom.axis_data.get('point', [0, 0, 0])
                        
                        print(f"   Original point: {orig_point}")
                        print(f"   Loaded point:   {loaded_point}")
                        
                        point_diff = [abs(orig_point[i] - loaded_point[i]) for i in range(3)]
                        print(f"   Point difference: {point_diff}")
                        
                        if max(point_diff) > 0.01:
                            print("✅ Coordinate system point was transformed in saved file")
                        else:
                            print("❌ Coordinate system point was NOT transformed in saved file")
                else:
                    print("   ❌ No axis_data found in loaded file")
                
                # Compare rotations
                expected_x = current_rot['x']
                actual_x = viewer.current_rot_right['x']
                x_diff = abs(expected_x - actual_x)
                
                print(f"\n=== ROTATION COMPARISON ===")
                print(f"   Expected X rotation: {expected_x:.1f}°")
                print(f"   Actual X rotation:   {actual_x:.1f}°")
                print(f"   Difference:          {x_diff:.1f}°")
                
                if x_diff < 1.0:
                    print("🎉 SUCCESS: Rotation preserved correctly!")
                else:
                    print("❌ FAILURE: Rotation not preserved")
                    print("   The OpenCASCADE transformation was applied but the coordinate system update failed")
            else:
                print("❌ Failed to load saved file")
        else:
            print("❌ Save failed")
        
        # Clean up
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
            
    else:
        print("❌ Failed to load STEP file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== AXIS UPDATE DEBUG TEST FINISHED ===")
