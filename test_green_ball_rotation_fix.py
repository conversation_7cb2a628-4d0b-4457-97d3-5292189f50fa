#!/usr/bin/env python3
"""
Test green ball rotation behavior and implement fix
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

print("🔍 TESTING GREEN BALL ROTATION BEHAVIOR")

# Import the viewer
from step_viewer import <PERSON><PERSON><PERSON><PERSON>TDK

def test_rotation_behavior():
    """Test the current rotation behavior of model and green ball"""
    print("\n" + "="*80)
    print("🔍 TESTING ROTATION BEHAVIOR: Model vs Green Ball")
    print("="*80)
    
    app = QApplication(sys.argv)
    
    # Create viewer
    print("\n📋 STEP 1: Creating viewer and loading STEP file...")
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for GUI to initialize
    app.processEvents()
    time.sleep(2)
    
    # Load STEP file
    step_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if os.path.exists(step_file):
        viewer.active_viewer = "top"
        result = viewer.load_step_file_direct(step_file)
        print(f"✅ STEP file loaded: {result}")
        
        # Wait for loading to complete
        app.processEvents()
        time.sleep(3)
        
        # Check initial state
        print("\n📋 STEP 2: Checking initial positions...")
        check_positions(viewer, "INITIAL")
        
        # Test Z-axis rotation (the problematic one)
        print("\n📋 STEP 3: Testing Z-axis rotation (+45°)...")
        print("This should make both model and green ball rotate in the SAME direction")

        # Apply Z rotation using the mouse rotation method
        viewer._apply_model_rotation("top", "z", 45.0)

        # Wait for rotation to complete
        app.processEvents()
        time.sleep(1)

        # Check positions after rotation
        print("\n📋 STEP 4: Checking positions after Z+45° rotation...")
        check_positions(viewer, "AFTER Z+45°")

        # Test another large rotation to see the pattern clearly
        print("\n📋 STEP 5: Testing another Z-axis rotation (+90°)...")
        viewer._apply_model_rotation("top", "z", 90.0)

        # Wait for rotation to complete
        app.processEvents()
        time.sleep(1)

        # Check positions after second rotation
        print("\n📋 STEP 6: Checking positions after Z+135° total...")
        check_positions(viewer, "AFTER Z+135° TOTAL")

        # Test one more large rotation to confirm the pattern
        print("\n📋 STEP 7: Testing final Z-axis rotation (+90°)...")
        viewer._apply_model_rotation("top", "z", 90.0)

        # Wait for rotation to complete
        app.processEvents()
        time.sleep(1)

        # Check positions after third rotation
        print("\n📋 STEP 8: Checking positions after Z+225° total...")
        check_positions(viewer, "AFTER Z+225° TOTAL")
        
    else:
        print(f"❌ STEP file not found: {step_file}")
    
    # Exit after 5 seconds
    QTimer.singleShot(5000, app.quit)
    app.exec_()

def check_positions(viewer, stage):
    """Check positions of model and green ball"""
    print(f"\n🔍 POSITIONS - {stage}:")

    renderer = viewer.vtk_renderer_left

    # Check model actors
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        print(f"   📦 Model actors ({len(renderer.step_actors)}):")
        for i, actor in enumerate(renderer.step_actors):
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            print(f"      Actor {i}: Pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) Orient=({orient[0]:.1f}, {orient[1]:.1f}, {orient[2]:.1f})")

    # Check green ball
    if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
        green_ball = renderer.part_origin_sphere
        pos = green_ball.GetPosition()
        orient = green_ball.GetOrientation()
        print(f"   🟢 Green ball: Pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) Orient=({orient[0]:.1f}, {orient[1]:.1f}, {orient[2]:.1f})")

        # Calculate distance from world origin
        distance = (pos[0]**2 + pos[1]**2 + pos[2]**2)**0.5
        print(f"      Distance from world origin: {distance:.3f}")

        # Calculate angle from X-axis (to track rotation direction)
        import math
        angle_rad = math.atan2(pos[1], pos[0])
        angle_deg = math.degrees(angle_rad)
        print(f"      Angle from X-axis: {angle_deg:.1f}°")
    else:
        print(f"   ❌ Green ball not found")

    # Check world origin markers (red markers)
    if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
        print(f"   🔴 World origin markers ({len(renderer.origin_actors)}):")
        for i, actor in enumerate(renderer.origin_actors):
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            print(f"      Marker {i}: Pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) Orient=({orient[0]:.1f}, {orient[1]:.1f}, {orient[2]:.1f})")

if __name__ == "__main__":
    test_rotation_behavior()
