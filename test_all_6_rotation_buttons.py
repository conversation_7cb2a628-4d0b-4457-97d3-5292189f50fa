#!/usr/bin/env python3
"""
COMPREHENSIVE TEST: ALL 6 ROTATION BUTTONS
Tests X+, X-, Y+, Y-, Z+, Z- rotation buttons to ensure:
1. World origin stays at (0,0,0) during ALL rotations
2. World origin arrows rotate correctly
3. Local origin moves with model during rotations
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK
import time

def check_world_origin_positions(viewer, test_name):
    """Check if all red actors (world origin) are at (0,0,0)"""
    print(f"\n🔴 {test_name} - World Origin Check:")
    
    renderer = viewer.vtk_renderer_left
    if not hasattr(renderer, 'renderer'):
        print("   ❌ No renderer found")
        return False
        
    red_actors_found = 0
    all_at_origin = True
    
    actor_collection = renderer.renderer.GetActors()
    if actor_collection:
        actor_collection.InitTraversal()
        actor = actor_collection.GetNextActor()
        while actor:
            color = actor.GetProperty().GetColor()
            # Check if this is a red actor (world origin marker)
            if color[0] > 0.8 and color[1] < 0.3 and color[2] < 0.3:
                pos = actor.GetPosition()
                red_actors_found += 1
                if abs(pos[0]) < 0.001 and abs(pos[1]) < 0.001 and abs(pos[2]) < 0.001:
                    print(f"   ✅ Red {red_actors_found}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) - AT ORIGIN")
                else:
                    print(f"   ❌ Red {red_actors_found}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) - NOT AT ORIGIN")
                    all_at_origin = False
            actor = actor_collection.GetNextActor()
    
    if red_actors_found == 0:
        print("   ❌ No red actors found!")
        return False
    elif all_at_origin:
        print(f"   ✅ {test_name}: World origin correctly stayed at (0,0,0)")
        return True
    else:
        print(f"   ❌ {test_name}: World origin moved away from (0,0,0) - FIX FAILED!")
        return False

def test_all_rotation_buttons():
    """Test all 6 rotation buttons comprehensively"""
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load a STEP file first
    step_file = "e:/python/viewer/save/sample.step"
    if os.path.exists(step_file):
        print(f"📁 Loading STEP file: {step_file}")
        viewer.load_step_file(step_file)
        time.sleep(1)  # Allow loading to complete
    else:
        print("❌ No STEP file found - creating test without file")
    
    print("\n" + "="*80)
    print("🎯 TESTING ALL 6 ROTATION BUTTONS")
    print("="*80)
    
    # Test all 6 rotation buttons
    rotation_tests = [
        ("X+ Rotation", lambda: viewer.rotate_shape('x', 15.0)),
        ("X- Rotation", lambda: viewer.rotate_shape('x', -15.0)),
        ("Y+ Rotation", lambda: viewer.rotate_shape('y', 15.0)),
        ("Y- Rotation", lambda: viewer.rotate_shape('y', -15.0)),
        ("Z+ Rotation", lambda: viewer.rotate_shape('z', 15.0)),
        ("Z- Rotation", lambda: viewer.rotate_shape('z', -15.0)),
    ]
    
    results = []
    
    for test_name, rotation_func in rotation_tests:
        print(f"\n🔄 PERFORMING: {test_name}")
        
        # Check BEFORE rotation
        check_world_origin_positions(viewer, f"BEFORE {test_name}")
        
        # Perform rotation
        print(f"🔄 BUTTON ROTATION: {test_name.lower()} -> calling UNIFIED rotation")
        rotation_func()
        time.sleep(0.5)  # Allow rotation to complete
        
        # Check AFTER rotation
        result = check_world_origin_positions(viewer, f"AFTER {test_name}")
        results.append((test_name, result))
        
        print(f"✅ {test_name} DEBUG COMPLETE")
    
    # Final summary
    print("\n" + "="*80)
    print("🎯 FINAL ROTATION TEST RESULTS")
    print("="*80)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {status}: {test_name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 ALL 6 ROTATION BUTTONS WORKING CORRECTLY!")
        print("   ✅ World origin stays at (0,0,0) during ALL rotations")
        print("   ✅ World origin arrows rotate correctly")
    else:
        print("\n❌ SOME ROTATION BUTTONS FAILED!")
        print("   ❌ World origin not staying at (0,0,0) during rotations")
    
    print("="*80)
    
    # Keep app running briefly to see results
    QTimer.singleShot(2000, app.quit)
    app.exec_()

if __name__ == "__main__":
    test_all_rotation_buttons()
