#!/usr/bin/env python3
"""
Test mouse direction fix and magenta ball rotation
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.getcwd())

def test_mouse_direction_fix():
    """Test that mouse direction and magenta ball rotation are fixed"""
    print("🖱️ TESTING MOUSE DIRECTION FIX")
    print("="*50)
    
    try:
        # Import the main viewer
        from step_viewer import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
        
        # Create Qt application
        app = QApplication(sys.argv)
        
        # Create viewer
        viewer = StepViewerTDK()
        print("✅ StepViewerTDK created successfully")
        
        # Check if we have a STEP file to load
        step_files = [f for f in os.listdir('.') if f.endswith('.STEP') or f.endswith('.step')]
        if not step_files:
            print("❌ No STEP files found in current directory")
            return False
            
        step_file = step_files[0]
        print(f"📁 Using STEP file: {step_file}")
        
        # Load the STEP file
        print(f"\n🔄 Loading STEP file: {step_file}")
        success = viewer.load_step_file_direct(step_file)
        
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        print("✅ STEP file loaded successfully")
        
        # Check initial magenta ball position
        print(f"\n🔍 INITIAL STATE:")
        print("-"*40)
        
        if hasattr(viewer, 'vtk_renderer_left') and hasattr(viewer.vtk_renderer_left, 'part_origin_sphere'):
            ball = viewer.vtk_renderer_left.part_origin_sphere
            if ball:
                initial_pos = ball.GetPosition()
                initial_orient = ball.GetOrientation()
                print(f"✅ Magenta ball initial position: ({initial_pos[0]:.3f}, {initial_pos[1]:.3f}, {initial_pos[2]:.3f})")
                print(f"✅ Magenta ball initial orientation: ({initial_orient[0]:.1f}°, {initial_orient[1]:.1f}°, {initial_orient[2]:.1f}°)")
            else:
                print("❌ Magenta ball not found")
                return False
        else:
            print("❌ Renderer or ball not found")
            return False
            
        # Test button rotation (should work correctly)
        print(f"\n🔄 TESTING BUTTON ROTATION (X+15°):")
        print("-"*40)
        
        # Apply button rotation
        viewer.rotate_shape('x', 15.0)
        
        # Check magenta ball after button rotation
        button_pos = ball.GetPosition()
        button_orient = ball.GetOrientation()
        print(f"✅ After button rotation - Position: ({button_pos[0]:.3f}, {button_pos[1]:.3f}, {button_pos[2]:.3f})")
        print(f"✅ After button rotation - Orientation: ({button_orient[0]:.1f}°, {button_orient[1]:.1f}°, {button_orient[2]:.1f}°)")
        
        # Calculate button rotation changes
        button_pos_change = [
            button_pos[0] - initial_pos[0],
            button_pos[1] - initial_pos[1], 
            button_pos[2] - initial_pos[2]
        ]
        button_orient_change = [
            button_orient[0] - initial_orient[0],
            button_orient[1] - initial_orient[1],
            button_orient[2] - initial_orient[2]
        ]
        
        print(f"📊 Button rotation changes:")
        print(f"   Position: ({button_pos_change[0]:.3f}, {button_pos_change[1]:.3f}, {button_pos_change[2]:.3f})")
        print(f"   Orientation: ({button_orient_change[0]:.1f}°, {button_orient_change[1]:.1f}°, {button_orient_change[2]:.1f}°)")
        
        # Test simulated mouse rotation (should now work in same direction)
        print(f"\n🖱️ TESTING SIMULATED MOUSE ROTATION:")
        print("-"*40)
        print("🔧 Simulating mouse drag (dx=50, dy=0) - should rotate Y-axis in SAME direction as Y+15° button...")
        
        # Simulate mouse rotation using the same method as the mouse handler
        # dx=50 (horizontal movement) should cause Y rotation
        rotation_scale = 0.2
        degrees = -50 * rotation_scale  # This should be -10° Y rotation (same direction as button)
        print(f"🔧 Calculated mouse rotation: Y-axis {degrees}°")
        
        # Apply mouse rotation
        viewer._apply_model_rotation("top", 'y', degrees, update_text=False)
        
        # Check magenta ball after mouse rotation
        mouse_pos = ball.GetPosition()
        mouse_orient = ball.GetOrientation()
        print(f"✅ After mouse rotation - Position: ({mouse_pos[0]:.3f}, {mouse_pos[1]:.3f}, {mouse_pos[2]:.3f})")
        print(f"✅ After mouse rotation - Orientation: ({mouse_orient[0]:.1f}°, {mouse_orient[1]:.1f}°, {mouse_orient[2]:.1f}°)")
        
        # Calculate mouse rotation changes (from button state)
        mouse_pos_change = [
            mouse_pos[0] - button_pos[0],
            mouse_pos[1] - button_pos[1], 
            mouse_pos[2] - button_pos[2]
        ]
        mouse_orient_change = [
            mouse_orient[0] - button_orient[0],
            mouse_orient[1] - button_orient[1],
            mouse_orient[2] - button_orient[2]
        ]
        
        print(f"📊 Mouse rotation changes:")
        print(f"   Position: ({mouse_pos_change[0]:.3f}, {mouse_pos_change[1]:.3f}, {mouse_pos_change[2]:.3f})")
        print(f"   Orientation: ({mouse_orient_change[0]:.1f}°, {mouse_orient_change[1]:.1f}°, {mouse_orient_change[2]:.1f}°)")
        
        # Check if mouse and button rotations are in same direction
        print(f"\n🎯 DIRECTION CONSISTENCY CHECK:")
        print("-"*40)
        
        # For Y rotation, we expect similar changes in position/orientation
        mouse_moved = any(abs(change) > 0.001 for change in mouse_pos_change)
        mouse_rotated = any(abs(change) > 0.1 for change in mouse_orient_change)
        
        if mouse_moved or mouse_rotated:
            print("✅ Mouse rotation DOES affect magenta ball")
            
            # Check if directions are consistent (both should move ball in similar way)
            # This is a rough check - if both rotations are in same direction, 
            # the changes should have similar signs
            consistent = True
            if abs(mouse_pos_change[0]) > 0.001 and abs(button_pos_change[0]) > 0.001:
                if (mouse_pos_change[0] > 0) != (button_pos_change[0] > 0):
                    consistent = False
                    
            if consistent:
                print("✅ SUCCESS: Mouse and button rotations appear to be in SAME direction!")
                print("🎉 The mouse direction fix is working!")
            else:
                print("❌ Mouse and button rotations are in OPPOSITE directions")
                print("🔧 The fix may need adjustment")
        else:
            print("❌ Mouse rotation does NOT affect magenta ball")
            print("🔧 The magenta ball is not following mouse rotation")
            
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_mouse_direction_fix()
