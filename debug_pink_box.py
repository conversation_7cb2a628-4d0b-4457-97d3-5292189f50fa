#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

from step_loader import ST<PERSON><PERSON>oader
import vtk

def debug_step_loading():
    """Debug why we get pink box instead of STEP geometry"""
    
    # Find a STEP file
    step_files = []
    for file in os.listdir('.'):
        if file.endswith('.step') or file.endswith('.stp'):
            step_files.append(file)
    
    if not step_files:
        print("❌ No STEP files found")
        return
    
    test_file = step_files[0]
    print(f"🔍 Debugging STEP loading with: {test_file}")
    
    # Create loader and load file
    loader = STEPLoader()
    success, message = loader.load_step_file(test_file)
    
    print(f"📊 Load result: success={success}, message='{message}'")
    
    # Check what the loader actually created
    print(f"📊 Loader has current_polydata: {hasattr(loader, 'current_polydata')}")
    if hasattr(loader, 'current_polydata'):
        polydata = loader.current_polydata
        if polydata:
            print(f"📊 Polydata exists: {polydata}")
            print(f"📊 Polydata points: {polydata.GetNumberOfPoints()}")
            print(f"📊 Polydata cells: {polydata.GetNumberOfCells()}")
            
            # Check if it's just a simple cube (pink box)
            if polydata.GetNumberOfPoints() == 24 and polydata.GetNumberOfCells() == 6:
                print("🔍 This looks like a simple VTK cube - explains the pink box!")
                print("🔍 The STEP file geometry is NOT being loaded properly")
            else:
                print("🔍 This looks like actual geometry data")
        else:
            print("❌ current_polydata is None")
    
    # Check if OpenCASCADE loading was attempted
    print(f"\n🔍 Checking OpenCASCADE loading path...")
    
    # Try to manually test OpenCASCADE loading
    try:
        result = loader._load_with_opencascade(test_file)
        print(f"📊 OpenCASCADE loading result: {result}")
    except Exception as e:
        print(f"❌ OpenCASCADE loading failed: {e}")
    
    # Check what fallback geometry was created
    print(f"\n🔍 Checking fallback geometry creation...")
    try:
        result = loader._create_step_geometry(test_file)
        print(f"📊 Fallback geometry result: {result}")
        
        if hasattr(loader, 'current_polydata') and loader.current_polydata:
            polydata = loader.current_polydata
            print(f"📊 Fallback polydata points: {polydata.GetNumberOfPoints()}")
            print(f"📊 Fallback polydata cells: {polydata.GetNumberOfCells()}")
            
            # This is likely what's creating the pink box
            if polydata.GetNumberOfPoints() == 24 and polydata.GetNumberOfCells() == 6:
                print("🎯 FOUND THE PROBLEM: Fallback geometry is creating a simple cube!")
                print("🎯 This cube becomes the pink box in the GUI")
                print("🎯 The real STEP geometry is not being loaded")
    except Exception as e:
        print(f"❌ Fallback geometry creation failed: {e}")

def test_opencascade_directly():
    """Test if OpenCASCADE is actually available"""
    print(f"\n🔍 Testing OpenCASCADE availability...")
    
    try:
        import OCC
        print("✅ OCC module imports successfully")
        
        try:
            from OCC.Core import STEPControl_Reader
            print("✅ STEPControl_Reader imports successfully")
            
            try:
                from OCC.Core import BRep_Tool, TopExp_Explorer, TopAbs_FACE
                print("✅ BRep tools import successfully")
                print("🎯 OpenCASCADE should be working!")
                return True
            except Exception as e:
                print(f"❌ BRep tools import failed: {e}")
                
        except Exception as e:
            print(f"❌ STEPControl_Reader import failed: {e}")
            
    except Exception as e:
        print(f"❌ OCC module import failed: {e}")
        print("🎯 This is why we get fallback geometry (pink box)!")
        
    return False

if __name__ == "__main__":
    print("🚀 Pink Box Debug Analysis")
    print("=" * 50)
    
    debug_step_loading()
    opencascade_available = test_opencascade_directly()
    
    print("\n" + "=" * 50)
    print("🎯 DIAGNOSIS:")
    if not opencascade_available:
        print("❌ OpenCASCADE is NOT available")
        print("🔧 This forces the code to use fallback geometry")
        print("🔧 Fallback geometry = simple VTK cube = pink box")
        print("🔧 SOLUTION: Install/fix OpenCASCADE Python bindings")
    else:
        print("✅ OpenCASCADE appears available")
        print("🔍 Issue may be in the STEP loading logic")
