#!/usr/bin/env python3
"""
Test the hypothesis that the model center calculation is wrong.
"""

import vtk
import numpy as np

def test_rotation_methods():
    """Test different VTK rotation methods to understand the behavior"""
    
    print("🔍 TESTING VTK ROTATION METHODS")
    print("="*80)
    
    # Create a simple cube actor
    cube_source = vtk.vtkCubeSource()
    cube_source.SetXLength(2.0)
    cube_source.SetYLength(2.0) 
    cube_source.SetZLength(2.0)
    
    mapper = vtk.vtkPolyDataMapper()
    mapper.SetInputConnection(cube_source.GetOutputPort())
    
    actor = vtk.vtkActor()
    actor.SetMapper(mapper)
    
    # Position the cube away from origin
    initial_pos = (5.0, 3.0, 1.0)
    actor.SetPosition(initial_pos[0], initial_pos[1], initial_pos[2])
    
    print(f"📦 INITIAL STATE:")
    print(f"   Position: {actor.GetPosition()}")
    print(f"   Orientation: {actor.GetOrientation()}")
    print(f"   Bounds: {actor.GetBounds()}")
    
    # Calculate initial center
    bounds = actor.GetBounds()
    initial_center = (
        (bounds[0] + bounds[1]) / 2,
        (bounds[2] + bounds[3]) / 2,
        (bounds[4] + bounds[5]) / 2
    )
    print(f"   Calculated center: {initial_center}")
    
    print(f"\n🔄 TEST 1: RotateX(15°)")
    print("-" * 40)
    
    # Method 1: RotateX (rotates around actor center)
    actor.RotateX(15.0)
    
    print(f"   After RotateX(15°):")
    print(f"   Position: {actor.GetPosition()}")
    print(f"   Orientation: {actor.GetOrientation()}")
    
    # Calculate new center
    bounds = actor.GetBounds()
    new_center_method1 = (
        (bounds[0] + bounds[1]) / 2,
        (bounds[2] + bounds[3]) / 2,
        (bounds[4] + bounds[5]) / 2
    )
    print(f"   New center: {new_center_method1}")
    
    center_movement_method1 = (
        new_center_method1[0] - initial_center[0],
        new_center_method1[1] - initial_center[1],
        new_center_method1[2] - initial_center[2]
    )
    print(f"   Center movement: {center_movement_method1}")
    
    # Reset actor
    actor.SetPosition(initial_pos[0], initial_pos[1], initial_pos[2])
    actor.SetOrientation(0, 0, 0)
    
    print(f"\n🔄 TEST 2: Manual rotation around world origin")
    print("-" * 40)
    
    # Method 2: Manual rotation around world origin
    # Step 1: Move to origin
    actor.SetPosition(0, 0, 0)
    
    # Step 2: Rotate
    actor.RotateX(15.0)
    
    # Step 3: Calculate new position
    transform = vtk.vtkTransform()
    transform.RotateX(15.0)
    new_pos = transform.TransformPoint(initial_pos)
    
    # Step 4: Move to new position
    actor.SetPosition(new_pos[0], new_pos[1], new_pos[2])
    
    print(f"   After manual world-origin rotation:")
    print(f"   Position: {actor.GetPosition()}")
    print(f"   Orientation: {actor.GetOrientation()}")
    
    # Calculate new center
    bounds = actor.GetBounds()
    new_center_method2 = (
        (bounds[0] + bounds[1]) / 2,
        (bounds[2] + bounds[3]) / 2,
        (bounds[4] + bounds[5]) / 2
    )
    print(f"   New center: {new_center_method2}")
    
    center_movement_method2 = (
        new_center_method2[0] - initial_center[0],
        new_center_method2[1] - initial_center[1],
        new_center_method2[2] - initial_center[2]
    )
    print(f"   Center movement: {center_movement_method2}")
    
    print(f"\n🔄 TEST 3: Transform-based calculation (what the code does)")
    print("-" * 40)
    
    # Method 3: What the current code does
    transform = vtk.vtkTransform()
    transform.RotateX(15.0)
    calculated_center = transform.TransformPoint(initial_center)
    
    print(f"   Original center: {initial_center}")
    print(f"   Calculated center (transform): {calculated_center}")
    
    calculated_movement = (
        calculated_center[0] - initial_center[0],
        calculated_center[1] - initial_center[1],
        calculated_center[2] - initial_center[2]
    )
    print(f"   Calculated movement: {calculated_movement}")
    
    print(f"\n📊 COMPARISON:")
    print("="*80)
    print(f"Method 1 (RotateX):           {center_movement_method1}")
    print(f"Method 2 (World origin):      {center_movement_method2}")
    print(f"Method 3 (Transform calc):    {calculated_movement}")
    
    # Check which methods match
    def vectors_match(v1, v2, tolerance=0.001):
        return (abs(v1[0] - v2[0]) < tolerance and 
                abs(v1[1] - v2[1]) < tolerance and 
                abs(v1[2] - v2[2]) < tolerance)
    
    print(f"\n🔍 ANALYSIS:")
    if vectors_match(center_movement_method1, calculated_movement):
        print(f"✅ Method 1 (RotateX) matches transform calculation")
        print(f"   This means the current code is CORRECT for RotateX/Y/Z")
    else:
        print(f"❌ Method 1 (RotateX) does NOT match transform calculation")
        print(f"   This means the current code is WRONG for RotateX/Y/Z")
    
    if vectors_match(center_movement_method2, calculated_movement):
        print(f"✅ Method 2 (World origin) matches transform calculation")
        print(f"   This means the current code would be CORRECT for world-origin rotation")
    else:
        print(f"❌ Method 2 (World origin) does NOT match transform calculation")
        print(f"   This means the current code would be WRONG for world-origin rotation")

if __name__ == "__main__":
    test_rotation_methods()
