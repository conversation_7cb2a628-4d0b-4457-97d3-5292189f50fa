#!/usr/bin/env python3
"""
Debug the simple save method directly
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== DEBUG SIMPLE SAVE METHOD ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Testing with: {test_file}")
    
    # Load file
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ File loaded")
        
        # Apply rotation
        print("Applying 90° X rotation...")
        viewer._apply_model_rotation("top", "x", 90.0)
        
        # Get current values
        loader = viewer.step_loader_left
        current_rot = viewer._extract_rotation_from_vtk_actor("top")
        orig_rot = viewer.orig_rot_left if hasattr(viewer, 'orig_rot_left') else {'x': 0, 'y': 0, 'z': 0}
        
        print(f"Current rotation: {current_rot}")
        print(f"Original rotation: {orig_rot}")
        
        # Test if simple save method exists
        print(f"_save_step_simple method exists: {hasattr(viewer, '_save_step_simple')}")
        
        # Call the simple save method directly
        test_save_file = "debug_simple_save.step"
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
            
        print("Calling _save_step_simple directly...")
        try:
            success = viewer._save_step_simple(test_save_file, loader, current_rot, orig_rot)
            print(f"Save returned: {success}")
            
            if os.path.exists(test_save_file):
                file_size = os.path.getsize(test_save_file)
                print(f"File created: {file_size} bytes")
                os.remove(test_save_file)
            else:
                print("No file created")
                
        except Exception as e:
            print(f"Error calling save method: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ Failed to load file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== DEBUG FINISHED ===")
