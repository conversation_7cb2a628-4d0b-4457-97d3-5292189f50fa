#!/usr/bin/env python3
"""
Test the Open STEP File button functionality
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

def test_button_functionality():
    """Test if the Open STEP File button works"""
    print("🔍 TESTING OPEN STEP FILE BUTTON")
    print("=" * 50)
    
    try:
        # Create QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Import and create viewer
        from step_viewer import StepViewerTDK
        viewer = StepViewerTDK()
        print("✅ Created viewer")
        
        # Test the load_step_file method directly
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if os.path.exists(step_file):
            print(f"✅ Found STEP file: {step_file}")
            
            # Test direct loading (what the button should do)
            print("🔄 Testing load_step_file_direct...")
            result = viewer.load_step_file_direct(step_file)
            print(f"📊 Load result: {result}")
            
            # Check if polydata was created
            if hasattr(viewer, 'step_loader_left') and viewer.step_loader_left:
                if hasattr(viewer.step_loader_left, 'current_polydata'):
                    polydata = viewer.step_loader_left.current_polydata
                    if polydata:
                        points = polydata.GetNumberOfPoints()
                        cells = polydata.GetNumberOfCells()
                        print(f"✅ Polydata created: {points} points, {cells} cells")
                    else:
                        print("❌ No polydata created")
                else:
                    print("❌ No current_polydata attribute")
            else:
                print("❌ No step_loader_left")
                
            # Check if VTK actors were created
            if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
                if hasattr(viewer.vtk_renderer_left, 'step_actors'):
                    actors = viewer.vtk_renderer_left.step_actors
                    print(f"✅ VTK actors: {len(actors) if actors else 0}")
                    if actors:
                        for i, actor in enumerate(actors):
                            if actor:
                                visibility = actor.GetVisibility()
                                print(f"  Actor {i+1}: visible={visibility}")
                elif hasattr(viewer.vtk_renderer_left, 'step_actor'):
                    actor = viewer.vtk_renderer_left.step_actor
                    if actor:
                        visibility = actor.GetVisibility()
                        print(f"✅ Single VTK actor: visible={visibility}")
                    else:
                        print("❌ No step_actor created")
                else:
                    print("❌ No step actors found")
            else:
                print("❌ No vtk_renderer_left")
        else:
            print(f"❌ STEP file not found: {step_file}")
            
        print("\n" + "=" * 50)
        print("📋 SUMMARY:")
        print("If you see polydata and actors created above,")
        print("then the loading works but display might be the issue.")
        print("If not, then the loading itself is broken.")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_button_functionality()
