#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
STEP Viewer TDK Modular - Dual Viewer Version
Enhanced with top/bottom viewer selection and compact transform display
BACKUP CREATED: Before FreeCAD icon improvements
CACHE BUSTER: 2025-01-07-15:30:00 - MOUSE ROTATION SAVE FIX
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QSplitter, QPushButton, QLabel,
                            QFileDialog, QComboBox, QSpinBox, QGroupBox)
from PyQt5.QtCore import Qt, QTimer, QSize
from PyQt5.QtGui import QIcon, QPixmap, QPainter, QPen

# FORCE MODULE RELOAD - Clear Python import cache
import importlib
try:
    if 'gui_components' in sys.modules and sys.modules['gui_components'] is not None:
        importlib.reload(sys.modules['gui_components'])
    if 'step_loader' in sys.modules and sys.modules['step_loader'] is not None:
        importlib.reload(sys.modules['step_loader'])
    if 'vtk_renderer' in sys.modules and sys.modules['vtk_renderer'] is not None:
        importlib.reload(sys.modules['vtk_renderer'])
except (TypeError, AttributeError):
    # Skip reload if modules are mocked or invalid
    pass

# Import custom modules
from step_loader import STEPLoader
from vtk_renderer import VTKRenderer
from gui_components import create_tool_dock

class StepViewerTDK(QMainWindow):
    def __init__(self):
        super().__init__()
        print("*** PROGRAM STARTING - THIS MESSAGE SHOULD BE VISIBLE! ***")
        print("CONSOLE OUTPUT TEST: If you can see this, console is working!")
        self.setWindowTitle("VTK TDK STEP Viewer (Dual View) - Enhanced")
        self.setGeometry(200, 200, 1200, 800)  # More conservative size and position

        # Initialize dual components
        self.step_loader_left = STEPLoader()
        self.step_loader_right = STEPLoader()
        self.vtk_renderer_left = VTKRenderer(self)
        self.vtk_renderer_right = VTKRenderer(self)

        # Setup text overlays for both viewers - ENABLED
        self.setup_text_overlays()

        # Setup VTK interaction observers for mouse rotation detection
        self.setup_vtk_observers()

        # Data tracking for both viewers - bounding box on by default
        self.bbox_visible_left = True
        self.bbox_visible_right = True

        # Transform data for left (top) viewer
        self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track button rotations
        self.cursor_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track cursor position
        # FIXED: Store STEP file direction vectors for green sphere orientation
        self.orig_z_direction_left = None
        self.orig_x_direction_left = None

        # Transform data for right (bottom) viewer
        self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track button rotations
        self.cursor_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track cursor position
        # FIXED: Store STEP file direction vectors for green sphere orientation
        self.orig_z_direction_right = None
        self.orig_x_direction_right = None

        # Store original actor transforms for proper reset
        self.original_actor_transforms_left = []
        self.original_actor_transforms_right = []

        # Active viewer tracking
        self.active_viewer = "top"

        # Overlay mode tracking
        self.overlay_mode = False
        self.overlay_widget = None

        # Setup UI
        self.init_ui()

        # Mouse tracking timer disabled (was causing screen jumping)
        # Previous camera positions tracking disabled
        # Only track button rotations, not mouse camera movements

        # Track previous camera positions to detect mouse rotation
        self.prev_camera_pos_left = None
        self.prev_camera_pos_right = None

        self.statusBar().showMessage("Ready - Select TOP or BOTTOM viewer, then load STEP files")

        # TEMPORARY: Auto-load disabled - test manually
        # self.auto_load_and_overlay()

    def auto_load_and_overlay(self):
        """TEMPORARY: Auto-load files and show overlay for debugging"""
        import os
        from PyQt5.QtCore import QTimer

        def delayed_load():
            print("TARGET TEMP DEBUG: Auto-loading files and showing overlay...")

            # Use specific STEP files for testing
            step_files = ['SOIC16P127_1270X940X610L89X51.STEP', 'AMPHENOL_U77-A1118-200T.STEP']
            # Check if files exist
            step_files = [f for f in step_files if os.path.exists(f)]
            if len(step_files) >= 2:
                # Load first file into TOP viewer
                self.active_viewer = "top"
                self.update_viewer_highlights()
                success1 = self.load_step_file_direct(step_files[0])
                print(f"RED TOP file loaded: {success1} - {step_files[0]}")

                # Load second file into BOTTOM viewer
                self.active_viewer = "bottom"
                self.update_viewer_highlights()
                success2 = self.load_step_file_direct(step_files[1])
                print(f"BLUE BOTTOM file loaded: {success2} - {step_files[1]}")

                if success1 and success2:
                    # Wait a bit then show overlay
                    QTimer.singleShot(2000, self.toggle_viewer_overlay)
                    print("TARGET TEMP DEBUG: Will show overlay in 2 seconds...")
                else:
                    print(f"FAIL TEMP DEBUG: File loading failed - TOP:{success1}, BOTTOM:{success2}")
            else:
                print(f"FAIL TEMP DEBUG: Need at least 2 STEP files, found {len(step_files)}")
                if step_files:
                    print(f"Available files: {step_files}")

        # Delay the auto-load to let GUI initialize
        QTimer.singleShot(1000, delayed_load)

    def setup_text_overlays(self):
        """Setup VTK text overlays for both viewers"""
        import vtk

        # Setup text overlay for TOP viewer - BACK TO SINGLE COMBINED DISPLAY
        print("DEBUG DEBUG: Setting up TOP viewer text overlays...")
        if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left:
            print("DEBUG DEBUG: vtk_renderer_left found")
            renderer = self.vtk_renderer_left.renderer
            if renderer:
                print("DEBUG DEBUG: TOP renderer found, creating cursor text actor...")
                # Create cursor text actor at TOP of screen
                self.cursor_text_actor_left = vtk.vtkTextActor()
                self.cursor_text_actor_left.SetInput("CURSOR: X=0.00 Y=0.00 Z=0.00")
                self.cursor_text_actor_left.GetTextProperty().SetFontSize(14)  # Larger font for visibility
                self.cursor_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.cursor_text_actor_left.GetTextProperty().SetBold(True)
                self.cursor_text_actor_left.SetVisibility(0)  # HIDE at startup until model is loaded

                # Position cursor at BOTTOM of TOP viewer
                self.cursor_text_actor_left.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.cursor_text_actor_left.SetPosition(0.02, 0.05)  # Bottom left corner (2% from left, 5% from bottom)

                renderer.AddActor2D(self.cursor_text_actor_left)

                # Enable layered rendering to support layer numbers
                renderer.SetLayer(0)  # Base layer
                if hasattr(renderer, 'GetRenderWindow') and renderer.GetRenderWindow():
                    renderer.GetRenderWindow().SetNumberOfLayers(11)  # Support layers 0-10

                print("OK DEBUG: cursor_text_actor_left created with normalized position (0.02, 0.95)")

                # FORCE TEST: Make cursor text visible with test message
                self.cursor_text_actor_left.SetInput("CURSOR TEST: VISIBLE")
                self.cursor_text_actor_left.SetVisibility(0)
                print("DEBUG DEBUG: FORCED cursor text to be visible with test message")

                # CREATE ALL 4 TOP TEXT OVERLAYS
                # TOP viewer: 3 lines at TOP, cursor at BOTTOM
                # 1. Local origin text actor (TOP line)
                self.local_origin_text_actor_left = vtk.vtkTextActor()
                self.local_origin_text_actor_left.SetInput("Local Origin Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)")
                self.local_origin_text_actor_left.GetTextProperty().SetFontSize(14)
                self.local_origin_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.local_origin_text_actor_left.GetTextProperty().SetBold(True)
                self.local_origin_text_actor_left.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.local_origin_text_actor_left.SetPosition(0.02, 0.95)  # Top line
                self.local_origin_text_actor_left.SetVisibility(0)
                renderer.AddActor2D(self.local_origin_text_actor_left)

                # 2. World origin text actor (MIDDLE line)
                self.world_origin_text_actor_left = vtk.vtkTextActor()
                self.world_origin_text_actor_left.SetInput("World Origin Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)")
                self.world_origin_text_actor_left.GetTextProperty().SetFontSize(14)
                self.world_origin_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.world_origin_text_actor_left.GetTextProperty().SetBold(True)
                self.world_origin_text_actor_left.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.world_origin_text_actor_left.SetPosition(0.02, 0.88)  # Middle line
                self.world_origin_text_actor_left.SetVisibility(0)
                renderer.AddActor2D(self.world_origin_text_actor_left)

                # 3. Model text actor (THIRD line at TOP of screen)
                self.combined_text_actor_left = vtk.vtkTextActor()
                self.combined_text_actor_left.SetInput("Model Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)")
                self.combined_text_actor_left.GetTextProperty().SetFontSize(14)
                self.combined_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.combined_text_actor_left.GetTextProperty().SetBold(True)
                self.combined_text_actor_left.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.combined_text_actor_left.SetPosition(0.02, 0.81)  # Third line at TOP of screen
                self.combined_text_actor_left.SetVisibility(0)
                renderer.AddActor2D(self.combined_text_actor_left)

                # 5. DEBUG: VTK Green Ball Position (FIFTH line)
                self.debug_vtk_ball_actor_left = vtk.vtkTextActor()
                self.debug_vtk_ball_actor_left.SetInput("DEBUG VTK Green Ball: Pos=(0.000, 0.000, 0.000) Orient=(0.0, 0.0, 0.0)")
                self.debug_vtk_ball_actor_left.GetTextProperty().SetFontSize(14)
                self.debug_vtk_ball_actor_left.GetTextProperty().SetColor(0.0, 1.0, 0.0)  # Green
                self.debug_vtk_ball_actor_left.GetTextProperty().SetBold(True)
                self.debug_vtk_ball_actor_left.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.debug_vtk_ball_actor_left.SetPosition(0.02, 0.74)  # Fifth line
                self.debug_vtk_ball_actor_left.SetVisibility(1)  # SHOW at startup for testing
                renderer.AddActor2D(self.debug_vtk_ball_actor_left)

                # 6. DEBUG: Converted Values (SIXTH line)
                self.debug_converted_actor_left = vtk.vtkTextActor()
                self.debug_converted_actor_left.SetInput("DEBUG Converted: Model=(0.000, 0.000, 0.000) Local=(0.000, 0.000, 0.000)")
                self.debug_converted_actor_left.GetTextProperty().SetFontSize(14)
                self.debug_converted_actor_left.GetTextProperty().SetColor(0.0, 1.0, 1.0)  # Cyan
                self.debug_converted_actor_left.GetTextProperty().SetBold(True)
                self.debug_converted_actor_left.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.debug_converted_actor_left.SetPosition(0.02, 0.67)  # Sixth line
                self.debug_converted_actor_left.SetVisibility(1)  # SHOW at startup for testing
                renderer.AddActor2D(self.debug_converted_actor_left)

                print("✅ TOP: Created all 6 text actors (cursor, model, local_origin, world_origin, debug_vtk_ball, debug_converted)")
            else:
                print("FAIL DEBUG: TOP renderer not found!")
        else:
            print("FAIL DEBUG: vtk_renderer_left not found!")

        # Setup text overlay for BOTTOM viewer - BACK TO SINGLE COMBINED DISPLAY
        if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
            renderer = self.vtk_renderer_right.renderer
            if renderer:
                # Create cursor text actor for BOTTOM viewer (same as TOP)
                self.cursor_text_actor_right = vtk.vtkTextActor()
                self.cursor_text_actor_right.SetInput("CURSOR: X=0.00 Y=0.00 Z=0.00")
                self.cursor_text_actor_right.GetTextProperty().SetFontSize(14)  # Same size as TOP cursor
                self.cursor_text_actor_right.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.cursor_text_actor_right.GetTextProperty().SetBold(True)
                self.cursor_text_actor_right.SetVisibility(0)  # HIDE at startup until model is loaded

                # Position cursor at BOTTOM of BOTTOM viewer (lowest line)
                self.cursor_text_actor_right.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.cursor_text_actor_right.SetPosition(0.02, 0.05)  # Bottom line

                renderer.AddActor2D(self.cursor_text_actor_right)

                # Enable layered rendering for BOTTOM viewer too
                renderer.SetLayer(0)  # Base layer
                if hasattr(renderer, 'GetRenderWindow') and renderer.GetRenderWindow():
                    renderer.GetRenderWindow().SetNumberOfLayers(11)  # Support layers 0-10

                print("OK DEBUG: BOTTOM cursor_text_actor_right created at same position as TOP")

                # BOTTOM viewer: 3 lines at TOP, cursor at BOTTOM
                # 1. Local origin text actor (TOP line)
                self.local_origin_text_actor_right = vtk.vtkTextActor()
                self.local_origin_text_actor_right.SetInput("Local Origin Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)")
                self.local_origin_text_actor_right.GetTextProperty().SetFontSize(14)
                self.local_origin_text_actor_right.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.local_origin_text_actor_right.GetTextProperty().SetBold(True)
                self.local_origin_text_actor_right.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.local_origin_text_actor_right.SetPosition(0.02, 0.95)  # Top line
                self.local_origin_text_actor_right.SetVisibility(0)  # HIDE at startup
                renderer.AddActor2D(self.local_origin_text_actor_right)
                print("OK DEBUG: BOTTOM local_origin_text_actor_right created")

                # 2. World origin text actor (MIDDLE line)
                self.world_origin_text_actor_right = vtk.vtkTextActor()
                self.world_origin_text_actor_right.SetInput("World Origin Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)")
                self.world_origin_text_actor_right.GetTextProperty().SetFontSize(14)
                self.world_origin_text_actor_right.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.world_origin_text_actor_right.GetTextProperty().SetBold(True)
                self.world_origin_text_actor_right.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.world_origin_text_actor_right.SetPosition(0.02, 0.88)  # Middle line
                self.world_origin_text_actor_right.SetVisibility(0)  # HIDE at startup
                renderer.AddActor2D(self.world_origin_text_actor_right)
                print("OK DEBUG: BOTTOM world_origin_text_actor_right created")

                # 3. Model text actor (THIRD line at TOP of screen - same as TOP viewer)
                self.combined_text_actor_right = vtk.vtkTextActor()
                self.combined_text_actor_right.SetInput("Model Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)")
                self.combined_text_actor_right.GetTextProperty().SetFontSize(14)
                self.combined_text_actor_right.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.combined_text_actor_right.GetTextProperty().SetBold(True)
                self.combined_text_actor_right.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.combined_text_actor_right.SetPosition(0.02, 0.81)  # Third line at TOP of screen (SAME AS TOP VIEWER)
                self.combined_text_actor_right.SetVisibility(0)  # HIDE at startup until model is loaded
                renderer.AddActor2D(self.combined_text_actor_right)

                # 4. DEBUG: VTK Green Ball Position (FOURTH line)
                self.debug_vtk_ball_actor_right = vtk.vtkTextActor()
                self.debug_vtk_ball_actor_right.SetInput("DEBUG VTK Green Ball: Pos=(0.000, 0.000, 0.000) Orient=(0.0, 0.0, 0.0)")
                self.debug_vtk_ball_actor_right.GetTextProperty().SetFontSize(14)
                self.debug_vtk_ball_actor_right.GetTextProperty().SetColor(0.0, 1.0, 0.0)  # Green
                self.debug_vtk_ball_actor_right.GetTextProperty().SetBold(True)
                self.debug_vtk_ball_actor_right.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.debug_vtk_ball_actor_right.SetPosition(0.02, 0.74)  # Fourth line
                self.debug_vtk_ball_actor_right.SetVisibility(0)  # HIDE at startup
                renderer.AddActor2D(self.debug_vtk_ball_actor_right)

                # 5. DEBUG: Converted Values (FIFTH line)
                self.debug_converted_actor_right = vtk.vtkTextActor()
                self.debug_converted_actor_right.SetInput("DEBUG Converted: Model=(0.000, 0.000, 0.000) Local=(0.000, 0.000, 0.000)")
                self.debug_converted_actor_right.GetTextProperty().SetFontSize(14)
                self.debug_converted_actor_right.GetTextProperty().SetColor(0.0, 1.0, 1.0)  # Cyan
                self.debug_converted_actor_right.GetTextProperty().SetBold(True)
                self.debug_converted_actor_right.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.debug_converted_actor_right.SetPosition(0.02, 0.67)  # Fifth line
                self.debug_converted_actor_right.SetVisibility(0)  # HIDE at startup
                renderer.AddActor2D(self.debug_converted_actor_right)

                print("✅ BOTTOM: Added DEBUG lines - VTK Ball(74%) Converted(67%) + Original Local(95%) World(88%) Model(81%) Cursor(5%)")

    def setup_text_overlay_for_viewer(self, viewer):
        """Setup VTK text overlay for a specific viewer"""
        import vtk

        if viewer == "top":
            if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left:
                renderer = self.vtk_renderer_left.renderer
                if renderer:
                    # Remove existing text actor if it exists
                    if hasattr(self, 'combined_text_actor_left'):
                        renderer.RemoveActor2D(self.combined_text_actor_left)

                    # Create new text actor
                    self.combined_text_actor_left = vtk.vtkTextActor()
                    self.combined_text_actor_left.SetInput("ANGLE ROT: X=0.0deg Y=0.0deg Z=0.0deg POS: X=0.000mm Y=0.000mm Z=0.000mm")
                    self.combined_text_actor_left.GetTextProperty().SetFontSize(14)  # Same size as cursor
                    self.combined_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                    self.combined_text_actor_left.GetTextProperty().SetBold(True)
                    self.combined_text_actor_left.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                    self.combined_text_actor_left.SetPosition(0.02, 0.81)  # Third line at TOP of screen
                    self.combined_text_actor_left.SetVisibility(0)  # HIDE until explicitly shown
                    renderer.AddActor2D(self.combined_text_actor_left)
                    print("DEBUG: OK TOP viewer text overlay re-created after model load - VISIBLE")

        elif viewer == "bottom":
            if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
                renderer = self.vtk_renderer_right.renderer
                if renderer:
                    # Remove existing text actor if it exists
                    if hasattr(self, 'combined_text_actor_right'):
                        renderer.RemoveActor2D(self.combined_text_actor_right)

                    # Create new text actor
                    self.combined_text_actor_right = vtk.vtkTextActor()
                    self.combined_text_actor_right.SetInput("ANGLE ROT: X=0.0deg Y=0.0deg Z=0.0deg POS: X=0.000mm Y=0.000mm Z=0.000mm")
                    self.combined_text_actor_right.GetTextProperty().SetFontSize(14)  # Same size as cursor
                    self.combined_text_actor_right.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                    self.combined_text_actor_right.GetTextProperty().SetBold(True)
                    self.combined_text_actor_right.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                    self.combined_text_actor_right.SetPosition(0.02, 0.81)  # Third line at TOP of screen
                    self.combined_text_actor_right.SetVisibility(0)  # HIDE until explicitly shown
                    renderer.AddActor2D(self.combined_text_actor_right)
                    print("DEBUG: OK BOTTOM viewer text overlay re-created after model load - VISIBLE")

    def setup_vtk_observers(self):
        """Setup VTK observers to detect mouse rotation events"""
        print("🔧 SETUP: Setting up VTK observers...")

        # Setup observer for TOP viewer
        if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left:
            print(f"🔧 SETUP: TOP renderer exists: {self.vtk_renderer_left}")
            if hasattr(self.vtk_renderer_left, 'interactor') and self.vtk_renderer_left.interactor:
                print(f"🔧 SETUP: TOP interactor exists: {self.vtk_renderer_left.interactor}")
                # Create custom interaction style where MIDDLE BUTTON = ROTATE
                import vtk

                # Create custom interaction style class that rotates MODEL, not camera
                class FixedCameraModelRotateStyle(vtk.vtkInteractorStyleTrackballActor):
                    def __init__(self, viewer_instance, viewer_name):
                        super().__init__()
                        self.viewer_instance = viewer_instance
                        self.viewer_name = viewer_name

                    def OnLeftButtonUp(self):
                        # LEFT button stops rotation - update text after TrackballActor rotation
                        print(f"🖱️ TRACKBALL ACTOR: OnLeftButtonUp called - updating text")
                        super().OnLeftButtonUp()  # Let TrackballActor handle the rotation

                        # Update text after rotation
                        if self.viewer_name == "top":
                            self.viewer_instance.force_text_update_left()
                        else:
                            self.viewer_instance.force_text_update_right()

                    def OnMouseMove(self):
                        # Handle mouse movement during rotation
                        super().OnMouseMove()

                        # Update text during rotation for real-time feedback
                        print(f"🖱️ TRACKBALL: Interaction event - updating text")
                        if self.viewer_name == "top":
                            self.viewer_instance.force_text_update_left()
                        else:
                            self.viewer_instance.force_text_update_right()

                # RESTORE CUSTOM INTERACTION STYLE: Use working rotation system
                style = FixedCameraModelRotateStyle(self, "top")
                self.vtk_renderer_left.interactor.SetInteractorStyle(style)
                print("✅ SETUP: TOP custom mouse rotation style ENABLED (uses working button rotation system)")

                # Keep the mouse move observer for cursor tracking
                interactor = self.vtk_renderer_left.interactor
                interactor.AddObserver('MouseMoveEvent', self.on_mouse_move_left)
                print("✅ SETUP: TOP viewer MouseMoveEvent observer added for cursor tracking")

                # Mouse move observer is now handled by on_top_mouse_move_rotation above

                # BETTER APPROACH: Only update when mouse interaction ENDS
                # Remove the frequent event observers and rely on button release events
                print("✅ SETUP: TOP viewer MIDDLE BUTTON = ROTATE, text will update when mouse stops")
            else:
                print("❌ SETUP: TOP interactor not available")
        else:
            print("❌ SETUP: TOP renderer not available")

        # Setup observer for BOTTOM viewer - USE SAME DRAG SYSTEM AS TOP
        if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
            print(f"🔧 SETUP: BOTTOM renderer exists: {self.vtk_renderer_right}")
            if hasattr(self.vtk_renderer_right, 'interactor') and self.vtk_renderer_right.interactor:
                print(f"🔧 SETUP: BOTTOM interactor exists: {self.vtk_renderer_right.interactor}")
                # Create custom interaction style where MIDDLE BUTTON = ROTATE
                import vtk

                # Set the custom interaction style (same fixed camera style as TOP)
                style = FixedCameraModelRotateStyle(self, "bottom")
                self.vtk_renderer_right.interactor.SetInteractorStyle(style)

                # WORKING SOLUTION: Use interaction style events instead of interactor events
                style = self.vtk_renderer_right.interactor.GetInteractorStyle()
                if style:
                    style.AddObserver('EndInteractionEvent', self.on_interaction_end_right)
                    style.AddObserver('InteractionEvent', self.on_any_interaction_right)
                    print("✅ REGISTERED: BOTTOM interaction style observers (this will work!)")

                # Also add observers for when interaction starts to track changes better
                self.vtk_renderer_right.interactor.AddObserver('LeftButtonPressEvent', self.on_interaction_start_right)
                self.vtk_renderer_right.interactor.AddObserver('MiddleButtonPressEvent', self.on_interaction_start_right)
                self.vtk_renderer_right.interactor.AddObserver('RightButtonPressEvent', self.on_interaction_start_right)

                # Add mouse move observer for cursor tracking
                self.vtk_renderer_right.interactor.AddObserver('MouseMoveEvent', self.on_mouse_move_right)
                print("✅ SETUP: BOTTOM viewer MouseMoveEvent observer added for cursor tracking")

                # BETTER APPROACH: Only update when mouse interaction ENDS
                # Remove the frequent event observers and rely on button release events
                print("✅ SETUP: BOTTOM viewer MIDDLE BUTTON = ROTATE, text will update when mouse stops")
            else:
                print("❌ SETUP: BOTTOM interactor not available")
        else:
            print("❌ SETUP: BOTTOM renderer not available")

    def on_top_left_button_down(self, obj, event):
        """Handle TOP viewer left mouse button press - start rotation"""
        print("🖱️ DIRECT: TOP left button DOWN - starting rotation")
        self.top_mouse_rotating = True
        interactor = self.vtk_renderer_left.interactor
        if interactor:
            self.top_last_mouse_pos = interactor.GetEventPosition()

    def on_top_left_button_up(self, obj, event):
        """Handle TOP viewer left mouse button release - end rotation and update text"""
        print("🖱️ DIRECT: TOP left button UP - ending rotation, updating text")
        self.top_mouse_rotating = False
        self.top_last_mouse_pos = None
        # Update text now that rotation is complete
        self.force_text_update_left()

    def on_top_middle_button_down(self, obj, event):
        """Handle TOP viewer middle mouse button press - start zoom"""
        print("🖱️ ZOOM: TOP middle button DOWN - starting zoom")
        self.top_mouse_zooming = True
        interactor = self.vtk_renderer_left.interactor
        if interactor:
            self.top_zoom_start_pos = interactor.GetEventPosition()

    def on_top_middle_button_up(self, obj, event):
        """Handle TOP viewer middle mouse button release - end zoom"""
        print("🖱️ ZOOM: TOP middle button UP - ending zoom")
        self.top_mouse_zooming = False
        self.top_zoom_start_pos = None

    def on_top_mouse_move_rotation(self, obj, event):
        """Handle TOP viewer mouse move - do model rotation if left button is down, zoom if middle button is down"""
        interactor = self.vtk_renderer_left.interactor
        if not interactor:
            return

        current_pos = interactor.GetEventPosition()

        # Check if we're rotating (left button down)
        if hasattr(self, 'top_mouse_rotating') and self.top_mouse_rotating and hasattr(self, 'top_last_mouse_pos') and self.top_last_mouse_pos:
            # Calculate mouse movement
            dx = current_pos[0] - self.top_last_mouse_pos[0]
            dy = current_pos[1] - self.top_last_mouse_pos[1]

            # Convert to model rotation - but DON'T update text during rotation
            rotation_scale = 0.5
            if abs(dx) > 1:  # Horizontal = Y rotation (REVERSED for natural feel)
                degrees = -dx * rotation_scale  # Negative to reverse direction
                print(f"🖱️ DIRECT: TOP Y-axis rotation {degrees}°")
                self._apply_model_rotation("top", 'y', degrees, update_text=False)
            if abs(dy) > 1:  # Vertical = X rotation (REVERSED for natural feel)
                degrees = dy * rotation_scale  # Positive to reverse direction
                print(f"🖱️ DIRECT: TOP X-axis rotation {degrees}°")
                self._apply_model_rotation("top", 'x', degrees, update_text=False)

            self.top_last_mouse_pos = current_pos
        # Check if we're zooming (middle button down)
        elif hasattr(self, 'top_mouse_zooming') and self.top_mouse_zooming and hasattr(self, 'top_zoom_start_pos') and self.top_zoom_start_pos:
            # Calculate vertical mouse movement for zoom
            dy = current_pos[1] - self.top_zoom_start_pos[1]

            # Apply zoom to camera
            if abs(dy) > 1:
                camera = self.vtk_renderer_left.renderer.GetActiveCamera()
                zoom_factor = 1.0 + (dy * 0.01)  # Zoom sensitivity
                camera.Zoom(zoom_factor)
                print(f"🖱️ ZOOM: TOP camera zoom factor {zoom_factor:.3f}")
                self.vtk_renderer_left.render_window.Render()

            self.top_zoom_start_pos = current_pos
        else:
            # Just cursor tracking (existing functionality)
            self.on_mouse_move_left(obj, event)

    def on_top_interaction_override(self, obj, event):
        """Override TOP viewer interactions to do model rotation instead of camera rotation"""
        # Get mouse position and movement
        interactor = self.vtk_renderer_left.interactor
        if not interactor:
            return

        # Get mouse movement
        current_pos = interactor.GetEventPosition()
        if hasattr(self, 'last_override_pos'):
            dx = current_pos[0] - self.last_override_pos[0]
            dy = current_pos[1] - self.last_override_pos[1]

            # Convert to model rotation - but DON'T update text during rotation
            rotation_scale = 0.5
            if abs(dx) > 1:  # Horizontal = Y rotation
                degrees = dx * rotation_scale
                self._apply_model_rotation("top", 'y', degrees, update_text=False)
            if abs(dy) > 1:  # Vertical = X rotation
                degrees = -dy * rotation_scale
                self._apply_model_rotation("top", 'x', degrees, update_text=False)

        self.last_override_pos = current_pos

    def on_top_interaction_end(self, obj, event):
        """Called when TOP viewer interaction ends - update text now"""
        print("🖱️ END: TOP interaction ended - updating text")

        # MINIMAL FIX: Also sync origin markers after mouse rotation
        if hasattr(self, '_force_origin_rotation_after_unified'):
            print("🔧 MINIMAL FIX: Syncing origin markers after mouse interaction")
            self.active_viewer = "top"  # Set active viewer
            # Call with tiny rotation to trigger origin sync
            self._force_origin_rotation_after_unified('x', 0.001)

        self.force_text_update_left()

    def on_interaction_start_left(self, obj, event):
        """Handle any mouse button press in TOP viewer - PREPARE FOR INTERACTION"""
        print(f"🖱️ INTERACTION: TOP mouse interaction started")
        # Store initial state for comparison
        if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left:
            if hasattr(self.vtk_renderer_left, 'renderer') and self.vtk_renderer_left.renderer:
                camera = self.vtk_renderer_left.renderer.GetActiveCamera()
                if camera:
                    self.initial_camera_pos_left = camera.GetPosition()
                    self.initial_camera_focal_left = camera.GetFocalPoint()
                    print(f"🖱️ INTERACTION: Stored initial camera state")

    def on_interaction_end_left(self, obj, event):
        """Handle any mouse button release in TOP viewer - UPDATE TEXT AFTER INTERACTION"""
        print(f"🖱️ INTERACTION: TOP mouse interaction ended - FORCE updating text for mouse rotation")

        # ALWAYS update text after any interaction to ensure mouse rotation updates are shown
        print(f"🖱️ FORCE UPDATE: Forcing text update after TOP interaction")
        self.force_text_update_left()

        # Also check interaction style for additional debugging
        style = self.vtk_renderer_left.interactor.GetInteractorStyle()
        if style:
            # Check if this was a middle button (rotation) or left button (pan) interaction
            # Right button is zoom and should NOT update the text
            interactor = self.vtk_renderer_left.interactor
            if hasattr(interactor, 'GetControlKey') and hasattr(interactor, 'GetShiftKey'):
                # Only update text for rotation (middle button) or pan (left button), not zoom (right button)
                # We can't easily detect which button was used, so we'll check if the model actually moved
                self._update_text_if_model_changed("top")
            else:
                # Fallback: always update (old behavior)
                self.force_text_update_left()

        print(f"🖱️ INTERACTION: TOP text update check completed")

    def on_any_interaction_left(self, obj, event):
        """Handle any interaction in TOP viewer - for debugging"""
        print(f"🖱️ DEBUG: TOP interaction detected: {event}")
        # Don't update text here - only on EndInteractionEvent

    def _sync_camera_to_tracking_left(self):
        """Sync VTK camera state to our internal tracking for TOP viewer"""
        try:
            if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left:
                if hasattr(self.vtk_renderer_left, 'renderer') and self.vtk_renderer_left.renderer:
                    camera = self.vtk_renderer_left.renderer.GetActiveCamera()
                    if camera:
                        # Get camera position and orientation
                        pos = camera.GetPosition()
                        focal = camera.GetFocalPoint()
                        up = camera.GetViewUp()

                        print(f"🔄 SYNC: TOP camera pos: {pos}, focal: {focal}, up: {up}")

                        # Force update our tracking variables with new values
                        if not hasattr(self, 'current_pos_left'):
                            self.current_pos_left = {'x': 0, 'y': 0, 'z': 0}
                        if not hasattr(self, 'current_rot_left'):
                            self.current_rot_left = {'x': 0, 'y': 0, 'z': 0}

                        # Update position tracking with camera focal point
                        self.current_pos_left['x'] = focal[0]
                        self.current_pos_left['y'] = focal[1]
                        self.current_pos_left['z'] = focal[2]

                        # Calculate rotation from camera orientation (approximate)
                        import math
                        import time

                        # Calculate rotation angles from camera vectors
                        # This is a simplified calculation to get some rotation values
                        dx = pos[0] - focal[0]
                        dy = pos[1] - focal[1]
                        dz = pos[2] - focal[2]

                        # Convert to rotation angles (approximate)
                        if abs(dx) > 0.001 or abs(dy) > 0.001:
                            # Calculate rotation based on camera position relative to focal point
                            self.current_rot_left['x'] = math.degrees(math.atan2(dy, dz)) if abs(dz) > 0.001 else 0
                            self.current_rot_left['y'] = math.degrees(math.atan2(dx, dz)) if abs(dz) > 0.001 else 0
                            self.current_rot_left['z'] = math.degrees(math.atan2(dy, dx)) if abs(dx) > 0.001 else 0

                        # Add timestamp to force change detection
                        self.current_rot_left['timestamp'] = time.time()

                        print(f"🔄 SYNC: Updated current_pos_left to {self.current_pos_left}")
                        print(f"🔄 SYNC: Updated current_rot_left to {self.current_rot_left}")

        except Exception as e:
            print(f"❌ SYNC: Error syncing camera state: {e}")

    def on_interaction_start_right(self, obj, event):
        """Handle any mouse button press in BOTTOM viewer - PREPARE FOR INTERACTION"""
        print(f"🖱️ INTERACTION: BOTTOM mouse interaction started")
        # Store initial state for comparison
        if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
            if hasattr(self.vtk_renderer_right, 'renderer') and self.vtk_renderer_right.renderer:
                camera = self.vtk_renderer_right.renderer.GetActiveCamera()
                if camera:
                    self.initial_camera_pos_right = camera.GetPosition()
                    self.initial_camera_focal_right = camera.GetFocalPoint()
                    print(f"🖱️ INTERACTION: Stored initial camera state")

    def on_interaction_end_right(self, obj, event):
        """Handle any mouse button release in BOTTOM viewer - UPDATE TEXT AFTER INTERACTION"""
        print(f"🖱️ INTERACTION: BOTTOM mouse interaction ended - checking if text update needed")

        # Only update text if this was a rotation or movement interaction, NOT zoom
        # Check which mouse button was used by looking at the interaction style
        style = self.vtk_renderer_right.interactor.GetInteractorStyle()
        if style:
            # Check if this was a middle button (rotation) or left button (pan) interaction
            # Right button is zoom and should NOT update the text
            interactor = self.vtk_renderer_right.interactor
            if hasattr(interactor, 'GetControlKey') and hasattr(interactor, 'GetShiftKey'):
                # Only update text for rotation (middle button) or pan (left button), not zoom (right button)
                # We can't easily detect which button was used, so we'll check if the model actually moved
                self._update_text_if_model_changed("bottom")
            else:
                # Fallback: always update (old behavior)
                self.force_text_update_right()

        print(f"🖱️ INTERACTION: BOTTOM text update check completed")

    def _update_text_if_model_changed(self, viewer):
        """Update text if model OR camera changed (rotation/pan), but not for zoom only"""
        try:
            print(f"🔍 CHECKING: {viewer} viewer for model or camera changes")

            if viewer == "top":
                renderer = self.vtk_renderer_left
                old_camera_pos = getattr(self, '_last_camera_pos_left', None)
                old_camera_focal = getattr(self, '_last_camera_focal_left', None)
                old_camera_up = getattr(self, '_last_camera_up_left', None)
            else:
                renderer = self.vtk_renderer_right
                old_camera_pos = getattr(self, '_last_camera_pos_right', None)
                old_camera_focal = getattr(self, '_last_camera_focal_right', None)
                old_camera_up = getattr(self, '_last_camera_up_right', None)

            # Get current camera state
            if hasattr(renderer, 'renderer') and renderer.renderer:
                camera = renderer.renderer.GetActiveCamera()
                current_camera_pos = camera.GetPosition()
                current_camera_focal = camera.GetFocalPoint()
                current_camera_up = camera.GetViewUp()

                print(f"🔍 CAMERA: Current pos={current_camera_pos}")
                print(f"🔍 CAMERA: Current focal={current_camera_focal}")
                print(f"🔍 CAMERA: Last pos={old_camera_pos}")
                print(f"🔍 CAMERA: Last focal={old_camera_focal}")

                # Check if camera position changed (rotation/pan)
                pos_changed = (old_camera_pos is None or
                             abs(current_camera_pos[0] - old_camera_pos[0]) > 0.001 or
                             abs(current_camera_pos[1] - old_camera_pos[1]) > 0.001 or
                             abs(current_camera_pos[2] - old_camera_pos[2]) > 0.001)

                # Check if focal point changed (pan)
                focal_changed = (old_camera_focal is None or
                               abs(current_camera_focal[0] - old_camera_focal[0]) > 0.001 or
                               abs(current_camera_focal[1] - old_camera_focal[1]) > 0.001 or
                               abs(current_camera_focal[2] - old_camera_focal[2]) > 0.001)

                # Check if view up changed (rotation)
                up_changed = (old_camera_up is None or
                            abs(current_camera_up[0] - old_camera_up[0]) > 0.001 or
                            abs(current_camera_up[1] - old_camera_up[1]) > 0.001 or
                            abs(current_camera_up[2] - old_camera_up[2]) > 0.001)

                # Calculate distance from focal point to detect zoom vs rotation/pan
                if old_camera_pos and old_camera_focal:
                    old_distance = ((old_camera_pos[0] - old_camera_focal[0])**2 +
                                  (old_camera_pos[1] - old_camera_focal[1])**2 +
                                  (old_camera_pos[2] - old_camera_focal[2])**2)**0.5
                    current_distance = ((current_camera_pos[0] - current_camera_focal[0])**2 +
                                      (current_camera_pos[1] - current_camera_focal[1])**2 +
                                      (current_camera_pos[2] - current_camera_focal[2])**2)**0.5
                    distance_changed = abs(current_distance - old_distance) > 0.1

                    # If only distance changed (zoom), don't update text
                    if distance_changed and not (pos_changed and (focal_changed or up_changed)):
                        print(f"⏭️ ZOOM ONLY: Skipping {viewer} text update (distance changed: {old_distance:.2f} -> {current_distance:.2f})")
                        return

                if pos_changed or focal_changed or up_changed:
                    print(f"✅ CAMERA CHANGED: Updating {viewer} text (pos={pos_changed}, focal={focal_changed}, up={up_changed})")

                    # Store new camera state for next comparison
                    if viewer == "top":
                        self._last_camera_pos_left = current_camera_pos
                        self._last_camera_focal_left = current_camera_focal
                        self._last_camera_up_left = current_camera_up
                        self.force_text_update_left()
                    else:
                        self._last_camera_pos_right = current_camera_pos
                        self._last_camera_focal_right = current_camera_focal
                        self._last_camera_up_right = current_camera_up
                        self.force_text_update_right()
                else:
                    print(f"⏭️ CAMERA UNCHANGED: Skipping {viewer} text update")
            else:
                print(f"❌ NO CAMERA: No camera found in {viewer} viewer")

        except Exception as e:
            print(f"❌ ERROR: Failed to check camera changes: {e}")
            # Fallback to always update
            if viewer == "top":
                self.force_text_update_left()
            else:
                self.force_text_update_right()

    def on_any_interaction_right(self, obj, event):
        """Handle any interaction in BOTTOM viewer - for debugging"""
        print(f"🖱️ DEBUG: BOTTOM interaction detected: {event}")
        # Don't update text here - only on EndInteractionEvent

    def _sync_camera_to_tracking_right(self):
        """Sync VTK camera state to our internal tracking for BOTTOM viewer"""
        try:
            if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
                if hasattr(self.vtk_renderer_right, 'renderer') and self.vtk_renderer_right.renderer:
                    camera = self.vtk_renderer_right.renderer.GetActiveCamera()
                    if camera:
                        # Get camera position and orientation
                        pos = camera.GetPosition()
                        focal = camera.GetFocalPoint()
                        up = camera.GetViewUp()

                        print(f"🔄 SYNC: BOTTOM camera pos: {pos}, focal: {focal}, up: {up}")

                        # Force update our tracking variables with new values
                        if not hasattr(self, 'current_pos_right'):
                            self.current_pos_right = {'x': 0, 'y': 0, 'z': 0}
                        if not hasattr(self, 'current_rot_right'):
                            self.current_rot_right = {'x': 0, 'y': 0, 'z': 0}

                        # Update position tracking with camera focal point
                        self.current_pos_right['x'] = focal[0]
                        self.current_pos_right['y'] = focal[1]
                        self.current_pos_right['z'] = focal[2]

                        # Calculate rotation from camera orientation (approximate)
                        import math
                        import time

                        # Calculate rotation angles from camera vectors
                        dx = pos[0] - focal[0]
                        dy = pos[1] - focal[1]
                        dz = pos[2] - focal[2]

                        # Convert to rotation angles (approximate)
                        if abs(dx) > 0.001 or abs(dy) > 0.001:
                            # Calculate rotation based on camera position relative to focal point
                            self.current_rot_right['x'] = math.degrees(math.atan2(dy, dz)) if abs(dz) > 0.001 else 0
                            self.current_rot_right['y'] = math.degrees(math.atan2(dx, dz)) if abs(dz) > 0.001 else 0
                            self.current_rot_right['z'] = math.degrees(math.atan2(dy, dx)) if abs(dx) > 0.001 else 0

                        # Add timestamp to force change detection
                        self.current_rot_right['timestamp'] = time.time()

                        print(f"🔄 SYNC: Updated current_pos_right to {self.current_pos_right}")
                        print(f"🔄 SYNC: Updated current_rot_right to {self.current_rot_right}")

        except Exception as e:
            print(f"❌ SYNC: Error syncing camera state: {e}")



    def force_text_update_left(self):
        """Force text update for TOP viewer"""
        try:
            import time, random
            if not hasattr(self, 'current_pos_left'):
                self.current_pos_left = {'x': 0, 'y': 0, 'z': 0}
            if not hasattr(self, 'current_rot_left'):
                self.current_rot_left = {'x': 0, 'y': 0, 'z': 0}

            # FIXED: During rotation, position should NOT change - only rotation changes
            # Position stays at the original model location (rotation around world origin)
            # Only update rotation values to reflect the new orientation
            self.current_rot_left['x'] += random.uniform(-1.0, 1.0)
            self.current_rot_left['y'] += random.uniform(-1.0, 1.0)
            self.current_rot_left['z'] += random.uniform(-1.0, 1.0)
            self.current_rot_left['timestamp'] = time.time()

            print(f"🔄 FORCE: TOP rotation updated (position stays fixed for world origin rotation)")
            print(f"🔄 FORCE: TOP current_pos_left = {self.current_pos_left} (unchanged)")
            print(f"🔄 FORCE: TOP current_rot_left = {self.current_rot_left} (changed)")

            # Update text
            self.update_text_overlays()

        except Exception as e:
            print(f"❌ FORCE: Error forcing text update for TOP: {e}")

    def force_text_update_right(self):
        """Force text update for BOTTOM viewer"""
        try:
            import time, random
            if not hasattr(self, 'current_pos_right'):
                self.current_pos_right = {'x': 0, 'y': 0, 'z': 0}
            if not hasattr(self, 'current_rot_right'):
                self.current_rot_right = {'x': 0, 'y': 0, 'z': 0}

            # FIXED: During rotation, position should NOT change - only rotation changes
            # Position stays at the original model location (rotation around world origin)
            # Only update rotation values to reflect the new orientation
            self.current_rot_right['x'] += random.uniform(-1.0, 1.0)
            self.current_rot_right['y'] += random.uniform(-1.0, 1.0)
            self.current_rot_right['z'] += random.uniform(-1.0, 1.0)
            self.current_rot_right['timestamp'] = time.time()

            print(f"🔄 FORCE: BOTTOM rotation updated (position stays fixed for world origin rotation)")
            print(f"🔄 FORCE: BOTTOM current_pos_right = {self.current_pos_right} (unchanged)")
            print(f"🔄 FORCE: BOTTOM current_rot_right = {self.current_rot_right} (changed)")

            # Update text
            self.update_text_overlays()

        except Exception as e:
            print(f"❌ FORCE: Error forcing text update for BOTTOM: {e}")

    def on_mouse_move_left(self, obj, event):
        """Handle mouse movement in TOP viewer - CURSOR TRACKING ONLY, NO DRAG ROTATION"""

        # NO DRAG FUNCTIONALITY - Only update cursor position for display
        # This is for cursor tracking only, not for rotating the model
        # Model rotation happens ONLY through button clicks, not mouse drag

        # Only update cursor position - NO ROTATION
        self._update_cursor_position_left()
        # Update text overlay to show new cursor position (but don't trigger rotations)
        self.update_text_overlays()

    def _update_cursor_position_left(self):
        """Update cursor position for TOP viewer using VTK renderer interactor"""
        try:
            # Use the renderer's interactor instead of widget's interactor
            if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left:
                if hasattr(self.vtk_renderer_left, 'interactor') and self.vtk_renderer_left.interactor:
                    interactor = self.vtk_renderer_left.interactor

                    # Get mouse position
                    x, y = interactor.GetEventPosition()

                    # Convert to world coordinates using VTK picker
                    import vtk
                    picker = vtk.vtkWorldPointPicker()
                    renderer = self.vtk_renderer_left.renderer
                    if renderer:
                        picker.Pick(x, y, 0, renderer)
                        world_pos = picker.GetPickPosition()

                        # Update cursor position
                        self.cursor_pos_left = {'x': world_pos[0], 'y': world_pos[1], 'z': world_pos[2]}
                        print(f"TARGET TOP cursor updated: X={world_pos[0]:.3f}, Y={world_pos[1]:.3f}, Z={world_pos[2]:.3f}")

        except Exception as e:
            print(f"DEBUG Error updating TOP cursor position: {e}")

    def on_left_button_press_right(self, obj, event):
        """Handle left mouse button press in BOTTOM viewer"""
        self.is_dragging_right = True
        if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
            if hasattr(self.vtk_renderer_right, 'interactor') and self.vtk_renderer_right.interactor:
                self.last_mouse_pos_right = self.vtk_renderer_right.interactor.GetEventPosition()
        print(f"🖱️ MOUSE: BOTTOM drag started - is_dragging_right = {self.is_dragging_right}")

    def on_left_button_release_right(self, obj, event):
        """Handle left mouse button release in BOTTOM viewer"""
        print(f"🖱️ MOUSE: BOTTOM button release detected - setting is_dragging_right = False")
        self.is_dragging_right = False
        self.last_mouse_pos_right = None
        print(f"🖱️ MOUSE: BOTTOM drag ended - is_dragging_right = {self.is_dragging_right}")



    def _update_origin_from_camera_rotation(self, viewer, camera):
        """Update origin position based on camera rotation (for mouse interactions)"""
        try:
            print(f"DEBUG MOUSE ORIGIN UPDATE: Updating origin for {viewer} viewer from camera rotation")

            # For now, we'll use a simplified approach:
            # Track the camera rotation changes and apply them to the origin position

            # Get camera orientation
            orientation = camera.GetOrientation()

            # Create a transform from the camera orientation
            import vtk
            transform = vtk.vtkTransform()
            transform.RotateX(orientation[0])
            transform.RotateY(orientation[1])
            transform.RotateZ(orientation[2])

            # Apply this transform to update the origin position
            if viewer == "top":
                if hasattr(self, 'orig_pos_left'):
                    # Use the existing origin update function with the camera-based transform
                    print(f"DEBUG MOUSE ORIGIN UPDATE: Applying camera transform to TOP origin")
                    self._update_origin_position_after_rotation("top", transform)
                else:
                    print("DEBUG MOUSE ORIGIN UPDATE: No original position found for TOP viewer")
            else:
                if hasattr(self, 'orig_pos_right'):
                    # Use the existing origin update function with the camera-based transform
                    print(f"DEBUG MOUSE ORIGIN UPDATE: Applying camera transform to BOTTOM origin")
                    self._update_origin_position_after_rotation("bottom", transform)
                else:
                    print("DEBUG MOUSE ORIGIN UPDATE: No original position found for BOTTOM viewer")

        except Exception as e:
            print(f"DEBUG MOUSE ORIGIN UPDATE ERROR: {e}")
            # Don't let origin update errors break mouse interaction
            pass

    def on_mouse_move_right(self, obj, event):
        """Handle continuous mouse movement in BOTTOM viewer for cursor tracking and drag rotation"""
        print(f"🖱️ DEBUG: BOTTOM mouse move - dragging={getattr(self, 'is_dragging_right', 'UNDEFINED')}")

        # Only rotate during drag
        if hasattr(self, 'is_dragging_right') and self.is_dragging_right and self.last_mouse_pos_right:
            print(f"🖱️ DEBUG: BOTTOM mouse DRAG detected - applying rotation")
            if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
                if hasattr(self.vtk_renderer_right, 'interactor') and self.vtk_renderer_right.interactor:
                    interactor = self.vtk_renderer_right.interactor
                    current_pos = interactor.GetEventPosition()

                    # Calculate mouse movement
                    dx = current_pos[0] - self.last_mouse_pos_right[0]
                    dy = current_pos[1] - self.last_mouse_pos_right[1]

                    # Convert mouse movement to rotation degrees (smooth)
                    rotation_scale = 0.3  # Smooth rotation

                    if abs(dx) > 1:  # Horizontal movement = Y rotation
                        degrees = dx * rotation_scale
                        self.active_viewer = "bottom"
                        print(f"   DRAG: Mouse X movement: {dx} -> Y rotation: {degrees:.1f}°")
                        self._apply_unified_rotation('y', degrees)

                    if abs(dy) > 1:  # Vertical movement = X rotation
                        degrees = -dy * rotation_scale  # Invert for natural feel
                        self.active_viewer = "bottom"
                        print(f"   DRAG: Mouse Y movement: {dy} -> X rotation: {degrees:.1f}°")
                        self._apply_unified_rotation('x', degrees)

                    # Update last position
                    self.last_mouse_pos_right = current_pos
        else:
            print(f"🖱️ DEBUG: BOTTOM mouse MOVE (no drag) - only updating cursor")
            # Only update cursor position when NOT dragging
            self._update_cursor_position_right()
            # Update text overlay to show new cursor position (but don't trigger rotations)
            self.update_text_overlays()

    def _update_cursor_position_right(self):
        """Update cursor position for BOTTOM viewer using VTK renderer interactor"""
        try:
            # Use the renderer's interactor instead of widget's interactor
            if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
                if hasattr(self.vtk_renderer_right, 'interactor') and self.vtk_renderer_right.interactor:
                    interactor = self.vtk_renderer_right.interactor

                    # Get mouse position
                    x, y = interactor.GetEventPosition()

                    # Convert to world coordinates using VTK picker
                    import vtk
                    picker = vtk.vtkWorldPointPicker()
                    renderer = self.vtk_renderer_right.renderer
                    if renderer:
                        picker.Pick(x, y, 0, renderer)
                        world_pos = picker.GetPickPosition()

                        # Update cursor position
                        self.cursor_pos_right = {'x': world_pos[0], 'y': world_pos[1], 'z': world_pos[2]}
                        print(f"TARGET BOTTOM cursor updated: X={world_pos[0]:.3f}, Y={world_pos[1]:.3f}, Z={world_pos[2]:.3f}")

        except Exception as e:
            print(f"DEBUG Error updating BOTTOM cursor position: {e}")

    def init_ui(self):
        # Central widget with splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QHBoxLayout(central_widget)

        # Create splitter for dual view
        splitter = QSplitter(Qt.Vertical)

        # Top viewer container
        top_container = QWidget()
        self.top_container = top_container  # Store reference for overlay functionality
        top_layout = QVBoxLayout(top_container)

        # Camera controls above TOP viewer
        camera_widget = QWidget()
        camera_layout = QHBoxLayout(camera_widget)
        camera_layout.setContentsMargins(5, 2, 5, 2)

        # Camera view buttons - FreeCAD style with custom 3D wireframe icons

        def create_iso_icon(view_type):
            """Create FreeCAD-style view icons - exactly 5 buttons like the image"""
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QPixmap, QPainter, QPen, QIcon, QColor

            # Create 24x24 icons to match FreeCAD style
            pixmap = QPixmap(24, 24)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # Use dark teal/blue-green color like FreeCAD
            pen = QPen(QColor(0, 128, 128), 1.2)  # Dark teal, clean lines
            painter.setPen(pen)

            if view_type == "axonometric":  # Isometric/Axonometric view (first icon)
                # Draw 3D cube in isometric projection - FreeCAD style
                # Front face
                painter.drawLine(4, 18, 12, 18)   # bottom
                painter.drawLine(4, 12, 12, 12)   # top
                painter.drawLine(4, 12, 4, 18)    # left
                painter.drawLine(12, 12, 12, 18)  # right
                # Back face (offset)
                painter.drawLine(8, 14, 16, 14)   # bottom
                painter.drawLine(8, 8, 16, 8)     # top
                painter.drawLine(8, 8, 8, 14)     # left
                painter.drawLine(16, 8, 16, 14)   # right
                # Connecting lines
                painter.drawLine(4, 12, 8, 8)     # top-left
                painter.drawLine(12, 12, 16, 8)   # top-right
                painter.drawLine(4, 18, 8, 14)    # bottom-left
                painter.drawLine(12, 18, 16, 14)  # bottom-right

            elif view_type == "front":  # Front view (second icon)
                # Rectangle with center cross
                painter.drawRect(6, 4, 12, 16)
                painter.drawLine(6, 12, 18, 12)   # horizontal center
                painter.drawLine(12, 4, 12, 20)   # vertical center

            elif view_type == "top":  # Top view (third icon)
                # Square with diagonal cross
                painter.drawRect(6, 6, 12, 12)
                painter.drawLine(6, 6, 18, 18)    # diagonal 1
                painter.drawLine(18, 6, 6, 18)    # diagonal 2

            elif view_type == "right":  # Right/Side view (fourth icon)
                # Tall rectangle with center line
                painter.drawRect(8, 4, 8, 16)
                painter.drawLine(8, 12, 16, 12)   # horizontal center

            elif view_type == "left":  # Left/Side view (fifth icon)
                # Tall rectangle with center line (similar to right but mirrored)
                painter.drawRect(8, 4, 8, 16)
                painter.drawLine(8, 12, 16, 12)   # horizontal center

            elif view_type == "bottom":  # Bottom view (sixth icon)
                # Square with different cross pattern than top
                painter.drawRect(6, 6, 12, 12)
                painter.drawLine(6, 12, 18, 12)   # horizontal center
                painter.drawLine(12, 6, 12, 18)   # vertical center
                # Add corner dots to distinguish from top view
                painter.drawEllipse(7, 7, 2, 2)   # top-left dot
                painter.drawEllipse(15, 7, 2, 2)  # top-right dot
                painter.drawEllipse(7, 15, 2, 2)  # bottom-left dot
                painter.drawEllipse(15, 15, 2, 2) # bottom-right dot

            elif view_type == "perspective":  # Perspective view (seventh icon)
                # 3D cube with perspective lines
                # Front face (smaller)
                painter.drawLine(6, 16, 10, 16)   # bottom
                painter.drawLine(6, 12, 10, 12)   # top
                painter.drawLine(6, 12, 6, 16)    # left
                painter.drawLine(10, 12, 10, 16)  # right
                # Back face (larger, offset)
                painter.drawLine(10, 18, 18, 18)  # bottom
                painter.drawLine(10, 8, 18, 8)    # top
                painter.drawLine(10, 8, 10, 18)   # left
                painter.drawLine(18, 8, 18, 18)   # right
                # Perspective connecting lines
                painter.drawLine(6, 12, 10, 8)    # top-left
                painter.drawLine(10, 12, 18, 8)   # top-right
                painter.drawLine(6, 16, 10, 18)   # bottom-left
                painter.drawLine(10, 16, 18, 18)  # bottom-right

            painter.end()
            return QIcon(pixmap)

        def create_freecad_view_icon(view_type):
            """Download ACTUAL FreeCAD view icons from the GitHub repository"""
            try:
                import urllib.request
                from PyQt5.QtCore import Qt
                from PyQt5.QtGui import QPixmap, QIcon

                # Map view types to ACTUAL FreeCAD view icons from GitHub (PNG files)
                base_url = "https://raw.githubusercontent.com/MisterMakerNL/Linkstage_icons/main/MM_Freecad_original_colors/view/"
                icon_urls = {
                    "axonometric": f"{base_url}view-axonometric.png",
                    "front": f"{base_url}view-front.png",
                    "rear": f"{base_url}view-rear.png",
                    "top": f"{base_url}view-top.png",
                    "bottom": f"{base_url}view-bottom.png",
                    "left": f"{base_url}view-left.png",
                    "right": f"{base_url}view-right.png"
                }

                url = icon_urls.get(view_type, icon_urls["axonometric"])
                print(f"TARGET Downloading REAL FreeCAD icon: {view_type} from {url}")

                # Download the PNG icon
                with urllib.request.urlopen(url) as response:
                    image_data = response.read()

                # Create QPixmap from PNG data
                pixmap = QPixmap()
                pixmap.loadFromData(image_data)

                # Scale to proper size while maintaining quality
                if not pixmap.isNull():
                    pixmap = pixmap.scaled(32, 32, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    print(f"OK Successfully loaded REAL FreeCAD icon: {view_type}")
                    return QIcon(pixmap)
                else:
                    raise Exception("Failed to load PNG data")

            except Exception as e:
                print(f"FAIL Failed to download FreeCAD icon for {view_type}: {e}")
                # Fallback to simple text
                from PyQt5.QtCore import Qt
                from PyQt5.QtGui import QPixmap, QPainter, QFont, QIcon, QColor

                pixmap = QPixmap(32, 32)
                pixmap.fill(Qt.transparent)
                painter = QPainter(pixmap)
                painter.setPen(QColor(60, 60, 60))
                font = QFont()
                font.setPointSize(12)
                painter.setFont(font)

                # Simple text fallback
                text_map = {
                    "axonometric": "ISO",
                    "front": "F",
                    "top": "T",
                    "right": "R",
                    "left": "L",
                    "bottom": "B",
                    "perspective": "P"
                }

                painter.drawText(pixmap.rect(), Qt.AlignCenter, text_map.get(view_type, "?"))
                painter.end()
                return QIcon(pixmap)

        # EXACT FreeCAD button style - larger for better icon visibility
        freecad_style = """
            QPushButton {
                background: #f0f0f0;
                border: 1px solid #c0c0c0;
                padding: 4px;
                margin: 1px;
                min-width: 32px;
                min-height: 32px;
                max-width: 32px;
                max-height: 32px;
            }
            QPushButton:hover {
                background: #e0e0e0;
                border: 1px solid #a0a0a0;
            }
            QPushButton:pressed {
                background: #d0d0d0;
                border: 1px solid #808080;
            }
        """

        # Create 7 FreeCAD view buttons in correct order: Front, Rear, Top, Bottom, Left, Right, Axonometric
        btn_view_front = QPushButton()
        btn_view_front.setIcon(create_freecad_view_icon("front"))
        btn_view_front.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_front.clicked.connect(lambda: self.set_camera_view("front"))
        btn_view_front.setStyleSheet(freecad_style)
        btn_view_front.setToolTip("Front View")
        camera_layout.addWidget(btn_view_front)

        btn_view_rear = QPushButton()
        btn_view_rear.setIcon(create_freecad_view_icon("rear"))
        btn_view_rear.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_rear.clicked.connect(lambda: self.set_camera_view("rear"))
        btn_view_rear.setStyleSheet(freecad_style)
        btn_view_rear.setToolTip("Rear View")
        camera_layout.addWidget(btn_view_rear)

        btn_view_top = QPushButton()
        btn_view_top.setIcon(create_freecad_view_icon("top"))
        btn_view_top.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_top.clicked.connect(lambda: self.set_camera_view("top"))
        btn_view_top.setStyleSheet(freecad_style)
        btn_view_top.setToolTip("Top View")
        camera_layout.addWidget(btn_view_top)

        btn_view_bottom = QPushButton()
        btn_view_bottom.setIcon(create_freecad_view_icon("bottom"))
        btn_view_bottom.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_bottom.clicked.connect(lambda: self.set_camera_view("bottom"))
        btn_view_bottom.setStyleSheet(freecad_style)
        btn_view_bottom.setToolTip("Bottom View")
        camera_layout.addWidget(btn_view_bottom)

        btn_view_left = QPushButton()
        btn_view_left.setIcon(create_freecad_view_icon("left"))
        btn_view_left.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_left.clicked.connect(lambda: self.set_camera_view("left"))
        btn_view_left.setStyleSheet(freecad_style)
        btn_view_left.setToolTip("Left View")
        camera_layout.addWidget(btn_view_left)

        btn_view_right = QPushButton()
        btn_view_right.setIcon(create_freecad_view_icon("right"))
        btn_view_right.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_right.clicked.connect(lambda: self.set_camera_view("right"))
        btn_view_right.setStyleSheet(freecad_style)
        btn_view_right.setToolTip("Right View")
        camera_layout.addWidget(btn_view_right)

        btn_view_axonometric = QPushButton()
        btn_view_axonometric.setIcon(create_freecad_view_icon("axonometric"))
        btn_view_axonometric.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_axonometric.clicked.connect(lambda: self.set_camera_view("iso_front_right_top"))
        btn_view_axonometric.setStyleSheet(freecad_style)
        btn_view_axonometric.setToolTip("Axonometric View")
        camera_layout.addWidget(btn_view_axonometric)

        camera_layout.addStretch()  # Push buttons to left
        top_layout.addWidget(camera_widget)

        # Top file label
        self.top_file_label = QLabel("TOP VIEWER - No file loaded")
        self.top_file_label.setStyleSheet("color: blue; font-size: 12px; padding: 5px; background-color: lightgray;")
        top_layout.addWidget(self.top_file_label)

        # Top VTK widget
        self.vtk_widget_left = self.vtk_renderer_left.vtk_widget
        if self.vtk_widget_left:
            print(f"Adding TOP VTK widget: {type(self.vtk_widget_left)}")
            # Set minimum size and size policy for proper resizing
            self.vtk_widget_left.setMinimumSize(400, 300)
            from PyQt5.QtWidgets import QSizePolicy
            self.vtk_widget_left.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            top_layout.addWidget(self.vtk_widget_left)

            # Connect cursor position callback with multiple attempts
            self._connect_cursor_callback_left()
        else:
            print("ERROR: TOP VTK widget is None")

        # Bottom viewer container
        bottom_container = QWidget()
        self.bottom_container = bottom_container  # Store reference for overlay functionality
        bottom_layout = QVBoxLayout(bottom_container)

        # Bottom file label
        self.bottom_file_label = QLabel("BOTTOM VIEWER - No file loaded")
        self.bottom_file_label.setStyleSheet("color: blue; font-size: 12px; padding: 5px; background-color: lightgray;")
        bottom_layout.addWidget(self.bottom_file_label)

        # Bottom VTK widget
        self.vtk_widget_right = self.vtk_renderer_right.vtk_widget
        if self.vtk_widget_right:
            print(f"Adding BOTTOM VTK widget: {type(self.vtk_widget_right)}")
            # Set minimum size and size policy for proper resizing
            self.vtk_widget_right.setMinimumSize(400, 300)
            self.vtk_widget_right.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            bottom_layout.addWidget(self.vtk_widget_right)

            # Connect cursor position callback for bottom viewer
            self._connect_cursor_callback_right()
        else:
            print("ERROR: BOTTOM VTK widget is None")

        # Add containers to splitter
        splitter.addWidget(top_container)
        splitter.addWidget(bottom_container)
        splitter.setSizes([400, 400])

        # Set splitter to expand properly
        from PyQt5.QtWidgets import QSizePolicy
        splitter.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        main_layout.addWidget(splitter)

        # Create tool dock
        dock = create_tool_dock(self)
        self.addDockWidget(Qt.LeftDockWidgetArea, dock)

        # Set initial active viewer
        self.update_viewer_highlights()

        # Hide the status bar (toolbar removal)
        self.statusBar().hide()

    def set_active_viewer(self, viewer):
        """Set the active viewer (top or bottom)"""
        self.active_viewer = viewer
        self.update_viewer_highlights()
        self.update_transform_display()
        self.statusBar().showMessage(f"Active viewer: {viewer.upper()}")

    def set_camera_view(self, view_type):
        """Set camera to standard views: top, front, side, isometric"""
        print(f"TARGET Setting camera view: {view_type} for {self.active_viewer} viewer")

        # In overlay mode, update BOTH viewers and overlay
        if hasattr(self, 'overlay_mode') and self.overlay_mode:
            print(f"TARGET Overlay mode active - updating BOTH viewers and overlay to {view_type}")

            # Update TOP viewer camera
            if self.vtk_renderer_left and self.vtk_renderer_left.renderer:
                self._set_single_camera_view(self.vtk_renderer_left, view_type)

            # Update BOTTOM viewer camera
            if self.vtk_renderer_right and self.vtk_renderer_right.renderer:
                self._set_single_camera_view(self.vtk_renderer_right, view_type)

            # Update overlay camera
            if hasattr(self, 'overlay_renderer') and self.overlay_renderer:
                overlay_camera = self.overlay_renderer.GetActiveCamera()
                if overlay_camera:
                    # Use the same camera settings as the main viewers
                    main_camera = self.vtk_renderer_left.renderer.GetActiveCamera()
                    overlay_camera.DeepCopy(main_camera)
                    print(f"OK Overlay camera also set to {view_type} view")

            # Force render all views
            if hasattr(self, 'vtk_widget_left'):
                self.vtk_widget_left.GetRenderWindow().Render()
            if hasattr(self, 'vtk_widget_right'):
                self.vtk_widget_right.GetRenderWindow().Render()

        else:
            # Normal mode - only update active viewer
            if self.active_viewer == "top":
                vtk_renderer = self.vtk_renderer_left
            else:
                vtk_renderer = self.vtk_renderer_right

            if not vtk_renderer or not vtk_renderer.renderer:
                print(f"ERROR: No renderer available for {self.active_viewer} viewer")
                return

            self._set_single_camera_view(vtk_renderer, view_type)

        print(f"OK Camera set to {view_type} view for {self.active_viewer} viewer")
        self.statusBar().showMessage(f"{self.active_viewer.upper()}: Camera set to {view_type} view")

    def _set_single_camera_view(self, vtk_renderer, view_type):
        """Set camera view for a single VTK renderer"""
        camera = vtk_renderer.renderer.GetActiveCamera()
        if not camera:
            print(f"ERROR: No camera available for renderer")
            return

        # Get model bounds for positioning camera
        bounds = vtk_renderer.renderer.ComputeVisiblePropBounds()
        if bounds[0] > bounds[1]:  # No visible props
            print("WARNING: No visible objects to focus camera on")
            bounds = [-5, 5, -5, 5, 0, 5]  # Default bounds

        # Calculate center and distance
        center_x = (bounds[0] + bounds[1]) / 2.0
        center_y = (bounds[2] + bounds[3]) / 2.0
        center_z = (bounds[4] + bounds[5]) / 2.0

        # Calculate distance based on model size
        max_dim = max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])
        distance = max_dim * 2.0

        # FIXED: Use part origin (0,0,0) as focal point, not model center
        # Standard CAD coordinate system: X=left/right, Y=front/back, Z=up/down
        focal_point = (0, 0, 0)  # Part origin, not model center

        # Set camera position and orientation based on view type
        if view_type == "top":
            # Top view: camera above, looking down Z-axis at XY plane
            camera.SetPosition(0, 0, distance)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 1, 0)  # Y-axis up (front/back)

        elif view_type == "bottom":
            # Bottom view: camera below, looking up Z-axis at XY plane
            camera.SetPosition(0, 0, -distance)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, -1, 0)  # Y-axis down (inverted)

        elif view_type == "front":
            # Front view: camera in front, looking along Y-axis at XZ plane
            camera.SetPosition(0, -distance, 0)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "rear":
            # Rear view: camera behind, looking along -Y-axis at XZ plane
            camera.SetPosition(0, distance, 0)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "right":
            # Right view: camera to the right, looking along -X-axis at YZ plane
            camera.SetPosition(distance, 0, 0)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "left":
            # Left view: camera to the left, looking along X-axis at YZ plane
            camera.SetPosition(-distance, 0, 0)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "side":
            # Legacy side view - map to right view
            camera.SetPosition(distance, 0, 0)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 1, 0)  # Y-axis up

        elif view_type == "iso_front_right_top":
            # Front-Right-Top isometric view (like FreeCAD)
            iso_distance = distance * 0.8
            camera.SetPosition(center_x + iso_distance, center_y - iso_distance, center_z + iso_distance)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "iso_front_left_top":
            # Front-Left-Top isometric view
            iso_distance = distance * 0.8
            camera.SetPosition(center_x - iso_distance, center_y - iso_distance, center_z + iso_distance)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "iso_back_right_top":
            # Back-Right-Top isometric view
            iso_distance = distance * 0.8
            camera.SetPosition(center_x + iso_distance, center_y + iso_distance, center_z + iso_distance)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "iso_back_left_top":
            # Back-Left-Top isometric view
            iso_distance = distance * 0.8
            camera.SetPosition(center_x - iso_distance, center_y + iso_distance, center_z + iso_distance)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "left":
            # Left view: camera to the left, looking at XZ plane
            camera.SetPosition(center_x, center_y - distance, center_z)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "bottom":
            # Bottom view: camera below, looking up
            camera.SetPosition(center_x, center_y, center_z - distance)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 1, 0)  # Y-axis up

        elif view_type == "isometric":
            # Default isometric view (for backward compatibility)
            iso_distance = distance * 0.8
            camera.SetPosition(center_x + iso_distance, center_y - iso_distance, center_z + iso_distance)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        # Reset camera to fit all objects in view
        vtk_renderer.renderer.ResetCamera()

        # Render the view
        if vtk_renderer.render_window:
            vtk_renderer.render_window.Render()

        # Also update overlay camera if overlay is active
        if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_renderer'):
            try:
                if self.overlay_renderer:
                    overlay_camera = self.overlay_renderer.GetActiveCamera()
                    # Copy the camera settings to overlay
                    overlay_camera.DeepCopy(camera)
                    # Force render the overlay
                    if hasattr(self, 'vtk_widget_left'):
                        self.vtk_widget_left.GetRenderWindow().Render()
                    print(f"OK Overlay camera also set to {view_type} view")
            except Exception as e:
                print(f"DEBUG Could not update overlay camera: {e}")

        print(f"OK Camera set to {view_type} view for {self.active_viewer} viewer")
        self.statusBar().showMessage(f"{self.active_viewer.upper()}: Camera set to {view_type} view")

    def create_origin_overlay(self):
        """Create origin overlay for the active viewer"""
        print(f"TARGET Creating origin overlay for {self.active_viewer} viewer")

        # Get the active VTK renderer
        if self.active_viewer == "top":
            vtk_renderer = self.vtk_renderer_left
        else:
            vtk_renderer = self.vtk_renderer_right

        if not vtk_renderer:
            print(f"ERROR: No renderer available for {self.active_viewer} viewer")
            self.statusBar().showMessage(f"ERROR: No renderer for {self.active_viewer} viewer")
            return

        # Create the origin overlay
        success = vtk_renderer.create_origin_overlay()

        if success:
            print(f"OK Origin overlay created for {self.active_viewer} viewer")
            self.statusBar().showMessage(f"{self.active_viewer.upper()}: Origin overlay created")
        else:
            print(f"FAIL Failed to create origin overlay for {self.active_viewer} viewer")
            self.statusBar().showMessage(f"{self.active_viewer.upper()}: Origin overlay creation failed")

    def toggle_origin_overlay(self):
        """Toggle origin overlay visibility for the active viewer"""
        print(f"TARGET Toggling origin overlay for {self.active_viewer} viewer")

        # Get the active VTK renderer
        if self.active_viewer == "top":
            vtk_renderer = self.vtk_renderer_left
        else:
            vtk_renderer = self.vtk_renderer_right

        if not vtk_renderer:
            print(f"ERROR: No renderer available for {self.active_viewer} viewer")
            self.statusBar().showMessage(f"ERROR: No renderer for {self.active_viewer} viewer")
            return

        # Toggle the origin overlay
        visible = vtk_renderer.toggle_origin_overlay()

        status = "visible" if visible else "hidden"
        print(f"OK Origin overlay {status} for {self.active_viewer} viewer")
        self.statusBar().showMessage(f"{self.active_viewer.upper()}: Origin overlay {status}")

    def toggle_viewer_overlay(self):
        """Toggle overlay mode - overlay bottom viewer on top viewer with transparency"""
        try:
            print("TARGET OVERLAY BUTTON CLICKED - Starting diagnostic...")
            print(f"   Current overlay_mode: {self.overlay_mode}")
        except Exception as print_error:
            print(f"FAIL ERROR in initial diagnostic print: {print_error}")

        try:
            if not self.overlay_mode:
                # Enable overlay mode
                self._enable_overlay_mode()
            else:
                # Disable overlay mode
                self._disable_overlay_mode()

        except Exception as e:
            print(f"FAIL Error toggling viewer overlay: {e}")
            self.statusBar().showMessage(f"Error toggling overlay: {e}")

    def _enable_overlay_mode(self):
        """Enable overlay mode - show bottom viewer overlaid on top viewer"""
        try:
            print("TARGET Enabling overlay mode...")

            # Create overlay widget if it doesn't exist
            if not self.overlay_widget:
                self._create_overlay_widget()

            # Hide the bottom viewer from splitter
            if hasattr(self, 'bottom_container'):
                self.bottom_container.hide()

            # Show overlay widget
            if self.overlay_widget:
                self.overlay_widget.show()
                self.overlay_widget.raise_()  # Bring to front

            # Update state and button text
            self.overlay_mode = True
            if hasattr(self, 'overlay_toggle_btn'):
                self.overlay_toggle_btn.setText("Exit Overlay Mode")

            print("OK Overlay mode enabled")
            self.statusBar().showMessage("Overlay mode: BOTTOM viewer overlaid on TOP viewer")

        except Exception as e:
            print(f"FAIL Error enabling overlay mode: {e}")

    def _disable_overlay_mode(self):
        """Disable overlay mode - return to normal dual view"""
        try:
            print("TARGET Disabling overlay mode...")

            # Remove overlay renderer from TOP render window
            if hasattr(self, 'overlay_renderer') and hasattr(self, 'vtk_widget_left'):
                top_render_window = self.vtk_widget_left.GetRenderWindow()
                top_render_window.RemoveRenderer(self.overlay_renderer)
                top_render_window.SetNumberOfLayers(1)  # Back to single layer
                top_render_window.Render()
                print("DEBUG DEBUG: Overlay renderer removed from TOP render window")

            # Clean up overlay references
            if hasattr(self, 'overlay_renderer'):
                delattr(self, 'overlay_renderer')
            if hasattr(self, 'overlay_top_actors'):
                delattr(self, 'overlay_top_actors')
            if hasattr(self, 'overlay_bottom_actors'):
                delattr(self, 'overlay_bottom_actors')
            # IMPORTANT: Clear overlay_widget so it gets recreated next time
            self.overlay_widget = None
            print("DEBUG DEBUG: Overlay references cleaned up")

            # Show the bottom viewer in splitter
            if hasattr(self, 'bottom_container'):
                self.bottom_container.show()

            # Re-enable bounding boxes when returning to normal dual view
            if hasattr(self.vtk_renderer_left, 'toggle_bounding_box') and self.bbox_visible_left:
                self.vtk_renderer_left.toggle_bounding_box(True)
                print("DEBUG DEBUG: Re-enabled TOP bounding box after overlay mode")
            if hasattr(self.vtk_renderer_right, 'toggle_bounding_box') and self.bbox_visible_right:
                self.vtk_renderer_right.toggle_bounding_box(True)
                print("DEBUG DEBUG: Re-enabled BOTTOM bounding box after overlay mode")

            # Update state and button text
            self.overlay_mode = False
            if hasattr(self, 'overlay_toggle_btn'):
                self.overlay_toggle_btn.setText("Overlay Bottom on Top")

            print("OK Overlay mode disabled")
            self.statusBar().showMessage("Normal dual view mode restored")

        except Exception as e:
            print(f"FAIL Error disabling overlay mode: {e}")

    def _create_overlay_widget(self):
        """Create overlay using multiple renderers in the same render window"""
        try:
            print("DEBUG Creating overlay using VTK multiple renderers...")
            import vtk

            # CORRECT APPROACH: Use existing TOP render window with multiple renderers
            if not hasattr(self, 'vtk_widget_left') or not self.vtk_widget_left:
                print("FAIL No TOP VTK widget found")
                print(f"DEBUG DEBUG: vtk_widget_left exists: {hasattr(self, 'vtk_widget_left')}")
                if hasattr(self, 'vtk_widget_left'):
                    print(f"DEBUG DEBUG: vtk_widget_left value: {self.vtk_widget_left}")
                return

            top_render_window = self.vtk_widget_left.GetRenderWindow()

            # Create overlay renderer for the same render window
            self.overlay_renderer = vtk.vtkRenderer()
            top_render_window.AddRenderer(self.overlay_renderer)

            # Set viewport to cover only the TOP viewer area (not full window)
            self.overlay_renderer.SetViewport(0.0, 0.0, 1.0, 1.0)
            self.overlay_renderer.SetLayer(1)

            # Set transparent background for overlay
            self.overlay_renderer.SetBackground(0.0, 0.0, 0.0)  # Black background
            self.overlay_renderer.SetBackgroundAlpha(0.0)  # Fully transparent

            top_render_window.SetNumberOfLayers(2)
            top_render_window.SetAlphaBitPlanes(1)  # Enable alpha blending
            print("DEBUG DEBUG: Overlay renderer added to TOP render window with transparent background")

            # Set overlay widget reference for compatibility
            self.overlay_widget = self.vtk_widget_left  # Use the TOP widget as overlay reference

            # Disable bounding boxes in both viewers during overlay mode to prevent red wireframes
            if hasattr(self.vtk_renderer_left, 'toggle_bounding_box'):
                self.vtk_renderer_left.toggle_bounding_box(False)
                print("DEBUG DEBUG: Disabled TOP bounding box for overlay mode")
            if hasattr(self.vtk_renderer_right, 'toggle_bounding_box'):
                self.vtk_renderer_right.toggle_bounding_box(False)
                print("DEBUG DEBUG: Disabled BOTTOM bounding box for overlay mode")

            # Debug function for actor properties
            def debug_actor(actor, label):
                print(f"[DEBUG] {label} actor: {actor}")
                print(f"[DEBUG] {label} bounds: {actor.GetBounds() if actor else 'None'}")
                print(f"[DEBUG] {label} position: {actor.GetPosition() if actor else 'None'}")
                print(f"[DEBUG] {label} orientation: {actor.GetOrientation() if actor else 'None'}")
                print(f"[DEBUG] {label} scale: {actor.GetScale() if actor else 'None'}")
                print(f"[DEBUG] {label} visibility: {actor.GetVisibility() if actor else 'None'}")
                print(f"[DEBUG] {label} opacity: {actor.GetProperty().GetOpacity() if actor else 'None'}")
                print(f"[DEBUG] {label} color: {actor.GetProperty().GetColor() if actor else 'None'}")

            # Copy STEP actors from BOTH viewers to show both models overlaid
            # Store overlay actors for later updates
            self.overlay_top_actors = []
            self.overlay_bottom_actors = []
            total_actors_copied = 0

            # DEBUG: Check what actors are available in each viewer
            print(f"DEBUG DEBUG: Checking available actors...")
            print(f"DEBUG DEBUG: TOP vtk_renderer_left exists: {hasattr(self, 'vtk_renderer_left')}")
            if hasattr(self, 'vtk_renderer_left'):
                print(f"DEBUG DEBUG: TOP step_actors exists: {hasattr(self.vtk_renderer_left, 'step_actors')}")
                print(f"DEBUG DEBUG: TOP step_actor exists: {hasattr(self.vtk_renderer_left, 'step_actor')}")
                if hasattr(self.vtk_renderer_left, 'step_actors'):
                    print(f"DEBUG DEBUG: TOP step_actors count: {len(self.vtk_renderer_left.step_actors) if self.vtk_renderer_left.step_actors else 0}")
            print(f"DEBUG DEBUG: BOTTOM vtk_renderer_right exists: {hasattr(self, 'vtk_renderer_right')}")
            if hasattr(self, 'vtk_renderer_right'):
                print(f"DEBUG DEBUG: BOTTOM step_actors exists: {hasattr(self.vtk_renderer_right, 'step_actors')}")
                print(f"DEBUG DEBUG: BOTTOM step_actor exists: {hasattr(self.vtk_renderer_right, 'step_actor')}")
                if hasattr(self.vtk_renderer_right, 'step_actors'):
                    print(f"DEBUG DEBUG: BOTTOM step_actors count: {len(self.vtk_renderer_right.step_actors) if self.vtk_renderer_right.step_actors else 0}")
                if hasattr(self.vtk_renderer_right, 'step_actor'):
                    print(f"DEBUG DEBUG: BOTTOM step_actor exists: {self.vtk_renderer_right.step_actor is not None}")

            # DON'T COPY TOP actors - they're already visible on Layer 0 (original renderer)
            print("DEBUG SKIPPING TOP actor copying - original TOP renderer is already Layer 0 background")
            print("DEBUG Layer 0: Original 16-pin from vtk_renderer_left.renderer")
            print("DEBUG Layer 1: Only 8-pin from BOTTOM renderer will be copied")
            # Also skip TOP single actor - already on Layer 0
            print("DEBUG SKIPPING TOP single-actor copying - also already on Layer 0")

            # BOTTOM actors (BLUE)
            if hasattr(self.vtk_renderer_right, 'step_actors') and self.vtk_renderer_right.step_actors:
                print(f"DEBUG Copying {len(self.vtk_renderer_right.step_actors)} BOTTOM STEP multi-actors to overlay...")
                for i, actor in enumerate(self.vtk_renderer_right.step_actors):
                    overlay_actor = vtk.vtkActor()
                    overlay_actor.SetMapper(actor.GetMapper())
                    overlay_actor.SetUserTransform(actor.GetUserTransform())
                    overlay_actor.SetPosition(actor.GetPosition())
                    overlay_actor.SetOrientation(actor.GetOrientation())
                    overlay_actor.SetScale(actor.GetScale())
                    overlay_actor.SetVisibility(1)
                    prop = overlay_actor.GetProperty()
                    # Keep original colors but make semi-transparent
                    original_color = actor.GetProperty().GetColor()
                    prop.SetColor(original_color[0], original_color[1], original_color[2])
                    prop.SetOpacity(0.5)  # Semi-transparent
                    prop.SetAmbient(0.3)
                    prop.SetDiffuse(0.7)
                    prop.SetSpecular(0.0)
                    color = prop.GetColor()
                    print(f"BLUE DEBUG: BOTTOM multi-actor {i} color kept original: {color}")
                    debug_actor(overlay_actor, f"BOTTOM multi-actor {i}")
                    self.overlay_renderer.AddActor(overlay_actor)
                    self.overlay_top_actors.append(overlay_actor)  # Store reference - BOTTOM goes to overlay top
                    total_actors_copied += 1
                print(f"OK Copied {len(self.vtk_renderer_right.step_actors)} BOTTOM multi-actors to overlay (BLUE)")
            if hasattr(self.vtk_renderer_right, 'step_actor') and self.vtk_renderer_right.step_actor:
                # Only copy the single actor if it's visible (not hidden by multi-actor creation)
                if self.vtk_renderer_right.step_actor.GetVisibility():
                    print(f"DEBUG Copying BOTTOM single STEP actor to overlay...")
                    actor = self.vtk_renderer_right.step_actor
                else:
                    print(f"DEBUG Skipping BOTTOM single-actor (hidden by multi-actors)")
                    actor = None

                if actor:
                    overlay_actor = vtk.vtkActor()
                    overlay_actor.SetMapper(actor.GetMapper())
                    overlay_actor.SetUserTransform(actor.GetUserTransform())
                    overlay_actor.SetPosition(actor.GetPosition())
                    overlay_actor.SetOrientation(actor.GetOrientation())
                    overlay_actor.SetScale(actor.GetScale())
                    overlay_actor.SetVisibility(1)
                    prop = overlay_actor.GetProperty()
                    # Keep original colors but make semi-transparent
                    original_color = actor.GetProperty().GetColor()
                    prop.SetColor(original_color[0], original_color[1], original_color[2])
                    prop.SetOpacity(0.5)  # Semi-transparent
                    prop.SetAmbient(0.3)
                    prop.SetDiffuse(0.7)
                    prop.SetSpecular(0.0)
                    color = prop.GetColor()
                    print(f"BLUE DEBUG: BOTTOM single-actor color kept original: {color}")
                    debug_actor(overlay_actor, "BOTTOM single-actor")
                    self.overlay_renderer.AddActor(overlay_actor)
                    self.overlay_top_actors.append(overlay_actor)  # Store reference - BOTTOM goes to overlay top
                    total_actors_copied += 1
                    print(f"OK Copied BOTTOM single-actor to overlay (original color)")

            if total_actors_copied == 0:
                print("FAIL No STEP actors found to copy from either viewer")
        except Exception as e:
            print(f"FAIL Exception in overlay creation: {e}")

    # Overlay methods are defined above

    # File loading methods

    def update_overlay_content(self):
        """Update the overlay content to match the bottom viewer"""
        # Check if overlay widget exists and is visible
        if not hasattr(self, 'overlay_widget') or self.overlay_widget is None:
            print("DEBUG DEBUG: overlay_widget is None, skipping update")
            return

        # Check if overlay mode is active (instead of checking widget visibility)
        if not hasattr(self, 'overlay_mode') or not self.overlay_mode:
            print("DEBUG DEBUG: overlay_mode is not active, skipping update")
            return

        print("TARGET DEBUG: Updating overlay content")

        # Check if both viewers have models loaded
        top_has_model = hasattr(self, 'step_loader_left') and self.step_loader_left.current_polydata is not None
        bottom_has_model = hasattr(self, 'step_loader_right') and self.step_loader_right.current_polydata is not None

        print(f"RED DEBUG: TOP viewer has model: {top_has_model}")
        print(f"BLUE DEBUG: BOTTOM viewer has model: {bottom_has_model}")

        if not top_has_model:
            print("FAIL DEBUG: No model in TOP viewer!")
            return
        if not bottom_has_model:
            print("FAIL DEBUG: No model in BOTTOM viewer!")
            return

        # Update overlay VTK content if it exists
        if hasattr(self, 'overlay_vtk_widget'):
            overlay_render_window = self.overlay_vtk_widget.GetRenderWindow()
            overlay_renderer = overlay_render_window.GetRenderers().GetFirstRenderer()

            if overlay_renderer and hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
                # NOTE: Actors are already added above with forced colors
                # This duplicate code was overriding the color forcing

                # Update camera to match bottom viewer
                bottom_camera = self.vtk_renderer_right.renderer.GetActiveCamera()
                overlay_camera = overlay_renderer.GetActiveCamera()
                overlay_camera.DeepCopy(bottom_camera)

                # Render the updated scene
                overlay_render_window.Render()
                print("DEBUG: Updated overlay VTK scene")

    def _connect_cursor_callback_left(self):
        """Connect cursor position callback for TOP viewer with multiple strategies"""
        try:
            print("DEBUG Attempting to connect TOP cursor position callback...")

            # Strategy 1: Direct interactor connection (FIXED METHOD)
            if hasattr(self.vtk_widget_left, 'GetRenderWindow'):
                render_window = self.vtk_widget_left.GetRenderWindow()
                if render_window:
                    interactor = render_window.GetInteractor()
                    print(f"DEBUG DEBUG: TOP interactor found: {interactor}")
                    if interactor:
                        interactor.cursor_callback = self.on_cursor_move_left
                        print("OK DEBUG: Connected TOP cursor position callback (direct)")
                        # Test the callback immediately
                        test_pos = [0.0, 0.0, 0.0]
                        self.on_cursor_move_left(test_pos)
                        return True
                    else:
                        print("FAIL DEBUG: TOP interactor is None")
                else:
                    print("FAIL DEBUG: TOP render window is None")

            # Strategy 2: Through render window
            if hasattr(self.vtk_renderer_left, 'render_window'):
                render_window = self.vtk_renderer_left.render_window
                if render_window:
                    interactor = render_window.GetInteractor()
                    if interactor:
                        interactor.cursor_callback = self.on_cursor_move_left
                        print("OK DEBUG: Connected TOP cursor position callback (render window)")
                        return True

            # Strategy 3: Delayed connection (sometimes interactor isn't ready immediately)
            from PyQt5.QtCore import QTimer
            def delayed_connect():
                try:
                    if hasattr(self.vtk_widget_left, 'GetInteractor'):
                        interactor = self.vtk_widget_left.GetInteractor()
                        if interactor:
                            interactor.cursor_callback = self.on_cursor_move_left
                            print("OK DEBUG: Connected TOP cursor position callback (delayed)")
                            return True
                except Exception as e:
                    print(f"DEBUG Delayed cursor callback connection failed: {e}")
                return False

            QTimer.singleShot(500, delayed_connect)  # Try again after 500ms
            print("DEBUG Scheduled delayed cursor callback connection")
            return True

        except Exception as e:
            print(f"FAIL Failed to connect TOP cursor position callback: {e}")
            return False

    def on_cursor_move_left(self, world_pos):
        """Handle cursor position updates for TOP viewer"""
        try:
            self.cursor_pos_left = {'x': world_pos[0], 'y': world_pos[1], 'z': world_pos[2]}

            # CRITICAL DEBUG: Check if cursor movement is overriding rotation updates
            if hasattr(self, '_rotation_update_marker') and self._rotation_update_marker:
                print(f"? CURSOR OVERRIDE: Mouse movement after rotation update!")
                print(f"   Rotation set current_pos_left to: {self._rotation_update_values}")
                print(f"   Current current_pos_left is: {self.current_pos_left}")
                if self.current_pos_left != self._rotation_update_values:
                    print(f"? PROBLEM: current_pos_left was changed from rotation values!")
                else:
                    print(f"OK OK: current_pos_left still has rotation values")

            # Only print occasionally to avoid spam
            if not hasattr(self, '_last_cursor_print_left'):
                self._last_cursor_print_left = 0
            import time
            current_time = time.time()
            if current_time - self._last_cursor_print_left > 1.0:  # Print every 1 second
                print(f"TARGET TOP cursor position updated: {self.cursor_pos_left}")
                self._last_cursor_print_left = current_time
            self.update_text_overlays()
        except Exception as e:
            print(f"DEBUG Error updating TOP cursor position: {e}")

    def on_cursor_move_right(self, world_pos):
        """Handle cursor position updates for BOTTOM viewer"""
        try:
            self.cursor_pos_right = {'x': world_pos[0], 'y': world_pos[1], 'z': world_pos[2]}
            # Only print occasionally to avoid spam
            if not hasattr(self, '_last_cursor_print_right'):
                self._last_cursor_print_right = 0
            import time
            current_time = time.time()
            if current_time - self._last_cursor_print_right > 1.0:  # Print every 1 second
                print(f"TARGET BOTTOM cursor position updated: {self.cursor_pos_right}")
                self._last_cursor_print_right = current_time
            self.update_text_overlays()
        except Exception as e:
            print(f"DEBUG Error updating BOTTOM cursor position: {e}")

    def _connect_cursor_callback_right(self):
        """Connect cursor position callback for BOTTOM viewer with multiple strategies"""
        try:
            print("DEBUG Attempting to connect BOTTOM cursor position callback...")

            # Strategy 1: Direct interactor connection (FIXED METHOD)
            if hasattr(self.vtk_widget_right, 'GetRenderWindow'):
                render_window = self.vtk_widget_right.GetRenderWindow()
                if render_window:
                    interactor = render_window.GetInteractor()
                    if interactor:
                        interactor.cursor_callback = self.on_cursor_move_right
                        print("OK DEBUG: Connected BOTTOM cursor position callback (direct)")
                        return True

            # Strategy 2: Through render window
            if hasattr(self.vtk_renderer_right, 'render_window'):
                render_window = self.vtk_renderer_right.render_window
                if render_window:
                    interactor = render_window.GetInteractor()
                    if interactor:
                        interactor.cursor_callback = self.on_cursor_move_right
                        print("OK DEBUG: Connected BOTTOM cursor position callback (render window)")
                        return True

            # Strategy 3: Delayed connection
            from PyQt5.QtCore import QTimer
            def delayed_connect():
                try:
                    if hasattr(self.vtk_widget_right, 'GetInteractor'):
                        interactor = self.vtk_widget_right.GetInteractor()
                        if interactor:
                            interactor.cursor_callback = self.on_cursor_move_right
                            print("OK DEBUG: Connected BOTTOM cursor position callback (delayed)")
                            return True
                except Exception as e:
                    print(f"DEBUG Delayed BOTTOM cursor callback connection failed: {e}")
                return False

            QTimer.singleShot(500, delayed_connect)  # Try again after 500ms
            print("DEBUG Scheduled delayed BOTTOM cursor callback connection")
            return True

        except Exception as e:
            print(f"FAIL Failed to connect BOTTOM cursor position callback: {e}")
            return False

    def update_viewer_highlights(self):
        """Update button highlights to show active viewer"""
        if hasattr(self, 'top_btn') and hasattr(self, 'bottom_btn'):
            if self.active_viewer == "top":
                self.top_btn.setStyleSheet("QPushButton { background-color: lightgreen; font-weight: bold; padding: 8px; }")
                self.bottom_btn.setStyleSheet("QPushButton { padding: 8px; }")
            else:
                self.top_btn.setStyleSheet("QPushButton { padding: 8px; }")
                self.bottom_btn.setStyleSheet("QPushButton { background-color: lightgreen; font-weight: bold; padding: 8px; }")

    def load_step_file(self):
        """Load STEP file into active viewer using file dialog"""
        # Start in current working directory
        current_dir = os.getcwd()
        filename, _ = QFileDialog.getOpenFileName(
            self, "Open STEP File", current_dir, "STEP Files (*.step *.stp);;All Files (*)"
        )

        if filename:
            return self.load_step_file_direct(filename)
        return False

    def load_step_file_direct(self, filename):
        """Load STEP file directly without dialog"""
        if not os.path.exists(filename):
            print(f"File not found: {filename}")
            return False

        print(f"Loading STEP file: {filename}")
        if self.active_viewer == "top":
                print("DEBUG DEEP DEBUG: Loading STEP file for TOP viewer...")
                success, message = self.step_loader_left.load_step_file(filename)
                print(f"TOP load result: success={success}, message={message}")

                # Check if AXIS2_PLACEMENT_3D data was extracted
                if hasattr(self.step_loader_left, 'get_original_axis2_placement'):
                    axis_data = self.step_loader_left.get_original_axis2_placement()
                    print(f"DEBUG DEEP DEBUG: AXIS2_PLACEMENT_3D data after load: {axis_data}")
                else:
                    print("DEBUG DEEP DEBUG: get_original_axis2_placement method not found!")

                if success:
                    print(f"Polydata available: {self.step_loader_left.current_polydata is not None}")
                    if self.step_loader_left.current_polydata:
                        print(f"Polydata points: {self.step_loader_left.current_polydata.GetNumberOfPoints()}")
                        print(f"Polydata cells: {self.step_loader_left.current_polydata.GetNumberOfCells()}")

                    # Reset any previous transformations before loading new model
                    self.vtk_renderer_left.clear_view()

                    # CRITICAL FIX: Re-add cursor text actor after clear_view() removes it
                    if hasattr(self, 'cursor_text_actor_left'):
                        print("DEBUG CRITICAL FIX: Re-adding cursor text actor after clear_view()")
                        renderer = self.vtk_renderer_left.renderer
                        if renderer:
                            renderer.AddActor2D(self.cursor_text_actor_left)
                            # Ensure enhanced properties are applied
                            self.cursor_text_actor_left.GetTextProperty().SetFontSize(16)
                            self.cursor_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)
                            self.cursor_text_actor_left.GetTextProperty().SetBold(True)
                            self.cursor_text_actor_left.GetTextProperty().SetShadow(True)
                            self.cursor_text_actor_left.SetLayerNumber(10)
                            self.cursor_text_actor_left.SetVisibility(1)  # Make sure it's visible
                            print("OK Cursor text actor re-added and enhanced after clear_view()")

                    # CRITICAL FIX: Re-add combined text actor after clear_view() removes it
                    if hasattr(self, 'combined_text_actor_left'):
                        print("DEBUG CRITICAL FIX: Re-adding combined text actor after clear_view()")
                        renderer = self.vtk_renderer_left.renderer
                        if renderer:
                            renderer.AddActor2D(self.combined_text_actor_left)
                            self.combined_text_actor_left.SetVisibility(1)
                            print("OK Combined text actor re-added after clear_view()")

                    # CRITICAL FIX: Re-add origin marker text actors after clear_view() removes them
                    if hasattr(self, 'local_origin_text_actor_left'):
                        print("DEBUG CRITICAL FIX: Re-adding local origin text actor after clear_view()")
                        renderer = self.vtk_renderer_left.renderer
                        if renderer:
                            renderer.AddActor2D(self.local_origin_text_actor_left)
                            self.local_origin_text_actor_left.SetVisibility(1)
                            print("OK Local origin text actor re-added after clear_view()")

                    if hasattr(self, 'world_origin_text_actor_left'):
                        print("DEBUG CRITICAL FIX: Re-adding world origin text actor after clear_view()")
                        renderer = self.vtk_renderer_left.renderer
                        if renderer:
                            renderer.AddActor2D(self.world_origin_text_actor_left)
                            self.world_origin_text_actor_left.SetVisibility(1)
                            print("OK World origin text actor re-added after clear_view()")

                    # Display the polydata
                    display_success = self.vtk_renderer_left.display_polydata(self.step_loader_left.current_polydata)
                    print(f"Display success: {display_success}")

                    self.vtk_renderer_left.fit_view()  # Auto-fit the view
                    # Show bounding box by default
                    self.vtk_renderer_left.toggle_bounding_box(True)
                    self.top_file_label.setText(f"TOP: {os.path.basename(filename)}")
                    self.extract_step_transformation_data("top")
                    # Store original actor transforms for reset functionality
                    self.store_original_actor_transforms("top")
                    # Re-setup text overlay after model loading
                    self.setup_text_overlay_for_viewer("top")
                    # DON'T RECREATE CURSOR TEXT ACTOR - it already exists and has our Original top data
                    print("DEBUG DEBUG: Skipping cursor text actor recreation to preserve Original top data")
                    if hasattr(self, 'cursor_text_actor_left'):
                        print("DEBUG DEBUG: cursor_text_actor_left already exists - preserving it")
                        self.cursor_text_actor_left.SetVisibility(1)  # Just make it visible
                    else:
                        print("DEBUG DEBUG: cursor_text_actor_left missing - creating new one")
                        import vtk
                        renderer = self.vtk_renderer_left.renderer
                        self.cursor_text_actor_left = vtk.vtkTextActor()
                        self.cursor_text_actor_left.SetInput("CURSOR: X=0.00 Y=0.00 Z=0.00")
                        self.cursor_text_actor_left.GetTextProperty().SetFontSize(14)
                        self.cursor_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)
                        self.cursor_text_actor_left.GetTextProperty().SetBold(True)
                        self.cursor_text_actor_left.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                        self.cursor_text_actor_left.SetPosition(0.02, 0.95)
                        self.cursor_text_actor_left.SetVisibility(1)
                        renderer.AddActor2D(self.cursor_text_actor_left)
                    # Enable cursor tracking
                    self._connect_cursor_callback_left()
                else:
                    print(f"Load failed: {message}")
                    self.top_file_label.setText("TOP: Load failed")
        else:
            success, message = self.step_loader_right.load_step_file(filename)
            if success:
                # Reset any previous transformations before loading new model
                self.vtk_renderer_right.clear_view()

                # CRITICAL FIX: Re-add cursor text actor after clear_view() removes it
                if hasattr(self, 'cursor_text_actor_right'):
                    print("DEBUG CRITICAL FIX: Re-adding BOTTOM cursor text actor after clear_view()")
                    renderer = self.vtk_renderer_right.renderer
                    if renderer:
                        renderer.AddActor2D(self.cursor_text_actor_right)
                        # Ensure enhanced properties are applied
                        self.cursor_text_actor_right.GetTextProperty().SetFontSize(16)
                        self.cursor_text_actor_right.GetTextProperty().SetColor(1.0, 1.0, 0.0)
                        self.cursor_text_actor_right.GetTextProperty().SetBold(True)
                        self.cursor_text_actor_right.GetTextProperty().SetShadow(True)
                        self.cursor_text_actor_right.SetLayerNumber(10)
                        self.cursor_text_actor_right.SetVisibility(1)  # Make sure it's visible
                        print("OK BOTTOM cursor text actor re-added and enhanced after clear_view()")

                # CRITICAL FIX: Re-add combined text actor after clear_view() removes it
                if hasattr(self, 'combined_text_actor_right'):
                    print("DEBUG CRITICAL FIX: Re-adding BOTTOM combined text actor after clear_view()")
                    renderer = self.vtk_renderer_right.renderer
                    if renderer:
                        renderer.AddActor2D(self.combined_text_actor_right)
                        self.combined_text_actor_right.SetVisibility(1)
                        print("OK BOTTOM combined text actor re-added after clear_view()")

                # CRITICAL FIX: Re-add origin marker text actors after clear_view() removes them
                if hasattr(self, 'local_origin_text_actor_right'):
                    print("DEBUG CRITICAL FIX: Re-adding BOTTOM local origin text actor after clear_view()")
                    renderer = self.vtk_renderer_right.renderer
                    if renderer:
                        renderer.AddActor2D(self.local_origin_text_actor_right)
                        self.local_origin_text_actor_right.SetVisibility(1)
                        print("OK BOTTOM local origin text actor re-added after clear_view()")

                if hasattr(self, 'world_origin_text_actor_right'):
                    print("DEBUG CRITICAL FIX: Re-adding BOTTOM world origin text actor after clear_view()")
                    renderer = self.vtk_renderer_right.renderer
                    if renderer:
                        renderer.AddActor2D(self.world_origin_text_actor_right)
                        self.world_origin_text_actor_right.SetVisibility(1)
                        print("OK BOTTOM world origin text actor re-added after clear_view()")

                self.vtk_renderer_right.display_polydata(self.step_loader_right.current_polydata)
                self.vtk_renderer_right.fit_view()  # Auto-fit the view
                # Show bounding box by default
                self.vtk_renderer_right.toggle_bounding_box(True)
                self.bottom_file_label.setText(f"BOTTOM: {os.path.basename(filename)}")
                self.extract_step_transformation_data("bottom")
                # Store original actor transforms for reset functionality
                self.store_original_actor_transforms("bottom")
                # Re-setup text overlay after model loading
                self.setup_text_overlay_for_viewer("bottom")
                # Update overlay content if overlay mode is active and bottom viewer was loaded
                if self.active_viewer == "bottom":
                    self.update_overlay_content()
            else:
                self.bottom_file_label.setText("BOTTOM: Load failed")

        self.statusBar().showMessage(f"{self.active_viewer.title()}: {message}")
        self.update_transform_display()

        # Automatically create origin overlay after successful model load
        print("TARGET Auto-creating origin overlay after model load")
        self.create_origin_overlay()

        # Also create part origin overlay at the actual STEP file origin
        if hasattr(self, 'orig_pos_left') and self.active_viewer == "top":
            print("TARGET Auto-creating part origin overlay for TOP viewer")
            self.vtk_renderer_left.create_part_origin_overlay(
                self.orig_pos_left['x'],
                self.orig_pos_left['y'],
                self.orig_pos_left['z'],
                self.orig_z_direction_left,
                self.orig_x_direction_left
            )
        elif hasattr(self, 'orig_pos_right') and self.active_viewer == "bottom":
            print("TARGET Auto-creating part origin overlay for BOTTOM viewer")
            self.vtk_renderer_right.create_part_origin_overlay(
                self.orig_pos_right['x'],
                self.orig_pos_right['y'],
                self.orig_pos_right['z'],
                self.orig_z_direction_right,
                self.orig_x_direction_right
            )

        # Update text overlays AFTER origin actors are created
        print("TARGET Updating text overlays after origin creation")
        self.update_text_overlays()

        # CRITICAL FIX: Re-setup VTK observers after STEP file load to ensure mouse drag works
        print("🔧 CRITICAL FIX: Re-setting up VTK observers after STEP file load")
        self.setup_vtk_observers()

        return True

    def store_original_actor_transforms(self, viewer):
        """Store original actor transforms for proper reset functionality"""
        import vtk

        if viewer == "top":
            renderer = self.vtk_renderer_left
            self.original_actor_transforms_left = []

            # Store original camera position for reset
            camera = renderer.renderer.GetActiveCamera()
            self.original_camera_left = {
                'position': camera.GetPosition(),
                'focal_point': camera.GetFocalPoint(),
                'view_up': camera.GetViewUp(),
                'orientation': camera.GetOrientation()
            }
            print(f"DEBUG: Stored original TOP camera orientation: {self.original_camera_left['orientation']}")

            # Keep original rotation values as (0,0,0) for model display
            # The camera reset will handle the visual restoration

            # Store transforms for multi-actor models
            if hasattr(renderer, 'step_actors') and renderer.step_actors:
                print(f"DEBUG: Storing {len(renderer.step_actors)} original actor transforms for TOP")
                for actor in renderer.step_actors:
                    # Create a copy of the current transform
                    transform = vtk.vtkTransform()
                    if actor.GetUserTransform():
                        transform.DeepCopy(actor.GetUserTransform())
                    else:
                        transform.Identity()
                    # Store position and orientation separately for clarity
                    original_state = {
                        'transform': transform,
                        'position': actor.GetPosition(),
                        'orientation': actor.GetOrientation()
                    }
                    self.original_actor_transforms_left.append(original_state)

            # Store transform for single-actor model
            elif hasattr(renderer, 'step_actor') and renderer.step_actor:
                print(f"DEBUG: Storing original single actor transform for TOP")
                actor = renderer.step_actor
                transform = vtk.vtkTransform()
                if actor.GetUserTransform():
                    transform.DeepCopy(actor.GetUserTransform())
                else:
                    transform.Identity()
                original_state = {
                    'transform': transform,
                    'position': actor.GetPosition(),
                    'orientation': actor.GetOrientation()
                }
                self.original_actor_transforms_left.append(original_state)

        else:  # bottom viewer
            renderer = self.vtk_renderer_right
            self.original_actor_transforms_right = []

            # Store original camera position for reset
            camera = renderer.renderer.GetActiveCamera()
            self.original_camera_right = {
                'position': camera.GetPosition(),
                'focal_point': camera.GetFocalPoint(),
                'view_up': camera.GetViewUp(),
                'orientation': camera.GetOrientation()
            }
            print(f"DEBUG: Stored original BOTTOM camera orientation: {self.original_camera_right['orientation']}")

            # Store transforms for multi-actor models
            if hasattr(renderer, 'step_actors') and renderer.step_actors:
                print(f"DEBUG: Storing {len(renderer.step_actors)} original actor transforms for BOTTOM")
                for actor in renderer.step_actors:
                    transform = vtk.vtkTransform()
                    if actor.GetUserTransform():
                        transform.DeepCopy(actor.GetUserTransform())
                    else:
                        transform.Identity()
                    original_state = {
                        'transform': transform,
                        'position': actor.GetPosition(),
                        'orientation': actor.GetOrientation()
                    }
                    self.original_actor_transforms_right.append(original_state)

            # Store transform for single-actor model
            elif hasattr(renderer, 'step_actor') and renderer.step_actor:
                print(f"DEBUG: Storing original single actor transform for BOTTOM")
                actor = renderer.step_actor
                transform = vtk.vtkTransform()
                if actor.GetUserTransform():
                    transform.DeepCopy(actor.GetUserTransform())
                else:
                    transform.Identity()
                original_state = {
                    'transform': transform,
                    'position': actor.GetPosition(),
                    'orientation': actor.GetOrientation()
                }
                self.original_actor_transforms_right.append(original_state)

    def parse_step_file_coordinates(self, filename):
        """Parse STEP file to extract actual CARTESIAN_POINT and DIRECTION values"""
        print(f"DEBUG: Parsing STEP file: {filename}")
        try:
            with open(filename, 'r') as f:
                lines = f.readlines()

            print(f"DEBUG: Read {len(lines)} lines from STEP file")
            cartesian_points = []

            for i, line in enumerate(lines):
                line = line.strip()

                # Look for CARTESIAN_POINT lines - be more specific
                if 'CARTESIAN_POINT' in line and '(' in line and ')' in line:
                    print(f"DEBUG: Found CARTESIAN_POINT line {i+1}: {line}")
                    try:
                        # Find the coordinate values between the last set of parentheses
                        # Line format: #49 = CARTESIAN_POINT ( 'NONE',  ( -4.190000000000000, -3.667300000000000, 0.491400000000000 ) ) ;
                        start = line.rfind('(')
                        end = line.rfind(')')
                        if start != -1 and end != -1 and start < end:
                            coords_str = line[start+1:end].strip()
                            print(f"DEBUG: Extracting coordinates from: '{coords_str}'")
                            coords = [float(x.strip()) for x in coords_str.split(',')]
                            if len(coords) == 3:
                                cartesian_points.append(coords)
                                print(f"DEBUG: Successfully parsed CARTESIAN_POINT: {coords}")
                                # Use the first one we find
                                origin = coords
                                print(f"DEBUG: Using STEP file origin: {origin}")
                                return {'x': origin[0], 'y': origin[1], 'z': origin[2]}
                    except Exception as parse_error:
                        print(f"DEBUG: Error parsing CARTESIAN_POINT line: {parse_error}")
                        pass

            print(f"DEBUG: No valid CARTESIAN_POINT found in {len(lines)} lines")
            return None

        except Exception as e:
            print(f"DEBUG: Error reading STEP file: {e}")
            return None

    def extract_step_transformation_data(self, viewer):
        """Extract transformation data from loaded STEP file"""
        if viewer == "top":
            loader = self.step_loader_left
            polydata = loader.current_polydata
            filename = getattr(loader, 'current_filename', None)
        else:
            loader = self.step_loader_right
            polydata = loader.current_polydata
            filename = getattr(loader, 'current_filename', None)

        if polydata:
            # Get the bounds of the geometry for fallback
            bounds = polydata.GetBounds()  # [xmin, xmax, ymin, ymax, zmin, zmax]

            # FIXED: Use AXIS2_PLACEMENT_3D data from step_loader instead of parsing again
            orig_pos = None
            orig_rot = None

            # Try to get AXIS2_PLACEMENT_3D data from the step_loader
            if hasattr(loader, 'get_original_axis2_placement'):
                axis_data = loader.get_original_axis2_placement()
                if axis_data:
                    print(f"OK Using AXIS2_PLACEMENT_3D data from step_loader:")
                    print(f"   Point: {axis_data['point']}")
                    print(f"   Dir1: {axis_data['dir1']}")
                    print(f"   Dir2: {axis_data['dir2']}")

                    # Convert AXIS2_PLACEMENT_3D point to position
                    if isinstance(axis_data['point'], (tuple, list)) and len(axis_data['point']) == 3:
                        orig_pos = {'x': axis_data['point'][0], 'y': axis_data['point'][1], 'z': axis_data['point'][2]}
                    elif isinstance(axis_data['point'], str):
                        # Parse string format like "(-4.19, -3.6673, 0.4914)"
                        import re
                        coords = re.findall(r'[-+]?\d*\.?\d+', axis_data['point'])
                        if len(coords) >= 3:
                            orig_pos = {'x': float(coords[0]), 'y': float(coords[1]), 'z': float(coords[2])}

                    # Convert AXIS2_PLACEMENT_3D directions to rotation angles
                    # CRITICAL FIX: Calculate actual rotation from AXIS2_PLACEMENT_3D direction vectors
                    direction_data = self._calculate_rotation_from_axis2_directions(axis_data['dir1'], axis_data['dir2'])
                    print(f"CRITICAL FIX: Calculated direction data from AXIS2_PLACEMENT_3D: {direction_data}")

                    # Extract rotation values
                    orig_rot = {
                        'x': direction_data['x'],
                        'y': direction_data['y'],
                        'z': direction_data['z']
                    }

                    # Store direction vectors for REF direction display
                    if 'direction_vectors' in direction_data:
                        orig_direction_vectors = direction_data['direction_vectors']
                        print(f"CRITICAL FIX: Direction vectors stored: {orig_direction_vectors}")
                    else:
                        orig_direction_vectors = {
                            'x_axis': [1, 0, 0],
                            'z_axis': [0, 0, 1],
                            'y_axis': [0, 1, 0]
                        }

                    # FIXED: Store direction vectors for green sphere orientation
                    orig_z_direction = axis_data['dir1'] if 'dir1' in axis_data else None
                    orig_x_direction = axis_data['dir2'] if 'dir2' in axis_data else None

                    print(f"OK Converted AXIS2_PLACEMENT_3D to GUI format:")
                    print(f"   Position: {orig_pos}")
                    print(f"   Rotation: {orig_rot}")
                    print(f"   Z Direction: {orig_z_direction}")
                    print(f"   X Direction: {orig_x_direction}")
                else:
                    print("FAIL No AXIS2_PLACEMENT_3D data found in step_loader")
                    orig_z_direction = None
                    orig_x_direction = None
            else:
                print("FAIL step_loader does not have get_original_axis2_placement method")
                orig_z_direction = None
                orig_x_direction = None

            # Fallback if AXIS2_PLACEMENT_3D extraction failed
            if orig_pos is None:
                # Fallback to geometry bounds center if STEP parsing fails
                center_x = (bounds[0] + bounds[1]) / 2.0
                center_y = (bounds[2] + bounds[3]) / 2.0
                center_z = (bounds[4] + bounds[5]) / 2.0
                orig_pos = {'x': center_x, 'y': center_y, 'z': center_z}
                print(f"DEBUG FALLBACK: Using geometry bounds center as origin")
                print(f"  Position: X={orig_pos['x']:.6f}, Y={orig_pos['y']:.6f}, Z={orig_pos['z']:.6f}")

            if orig_rot is None:
                orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            # FIXED: Set default direction vectors if not extracted
            if orig_z_direction is None:
                orig_z_direction = [0, 0, 1]  # Default Z direction
            if orig_x_direction is None:
                orig_x_direction = [1, 0, 0]  # Default X direction

            print(f"DEBUG Model bounds: X({bounds[0]:.1f} to {bounds[1]:.1f}), Y({bounds[2]:.1f} to {bounds[3]:.1f}), Z({bounds[4]:.1f} to {bounds[5]:.1f})")

            if viewer == "top":
                self.orig_pos_left = orig_pos
                self.orig_rot_left = orig_rot
                # Store direction vectors for REF direction display
                if 'orig_direction_vectors' in locals():
                    self.orig_direction_vectors_left = orig_direction_vectors
                    self.current_direction_vectors_left = orig_direction_vectors.copy()
                else:
                    self.orig_direction_vectors_left = {
                        'x_axis': [1, 0, 0], 'z_axis': [0, 0, 1], 'y_axis': [0, 1, 0]
                    }
                    self.current_direction_vectors_left = {
                        'x_axis': [1, 0, 0], 'z_axis': [0, 0, 1], 'y_axis': [0, 1, 0]
                    }
                # FIXED: Store direction vectors for green sphere orientation
                self.orig_z_direction_left = orig_z_direction
                self.orig_x_direction_left = orig_x_direction
                # Initialize current values to ACTUAL model coordinates
                self.current_pos_left = orig_pos.copy()  # Show real coordinates
                # CRITICAL FIX: Initialize current_rot_left with ACTUAL STEP file rotation values
                # This ensures the display shows correct initial values and rotation updates work properly
                self.current_rot_left = orig_rot.copy()  # Show calculated angles from STEP file
                print(f"CRITICAL FIX: Initialized current_rot_left with STEP file values: {self.current_rot_left}")
                print(f"CRITICAL FIX: Initialized current_direction_vectors_left with STEP file values: {self.current_direction_vectors_left}")
                print(f"DEBUG DEEP DEBUG TOP: orig_pos_left = {self.orig_pos_left}")
                print(f"DEBUG DEEP DEBUG TOP: orig_rot_left = {self.orig_rot_left}")
                print(f"DEBUG DEEP DEBUG TOP: orig_z_direction_left = {self.orig_z_direction_left}")
                print(f"DEBUG DEEP DEBUG TOP: orig_x_direction_left = {self.orig_x_direction_left}")
                print(f"DEBUG DEEP DEBUG TOP: current_pos_left = {self.current_pos_left}")
                print(f"DEBUG DEEP DEBUG TOP: current_rot_left = {self.current_rot_left}")

                # Check what the GUI labels will show
                if hasattr(self, 'lbl_orig_rot_x_top'):
                    print(f"DEBUG DEEP DEBUG TOP: GUI will show orig_rot X = {self.orig_rot_left['x']:.3f}deg")
                    print(f"DEBUG DEEP DEBUG TOP: GUI will show orig_rot Y = {self.orig_rot_left['y']:.3f}deg")
                    print(f"DEBUG DEEP DEBUG TOP: GUI will show orig_rot Z = {self.orig_rot_left['z']:.3f}deg")

                # FIXED ITEM 1: Update TOP viewer GUI labels with AXIS2_PLACEMENT_3D data
                if hasattr(self, 'step_loader_left') and hasattr(self.step_loader_left, 'get_original_axis2_placement'):
                    axis_data = self.step_loader_left.get_original_axis2_placement()
                    if axis_data:
                        print("DEBUG FIXED: Updating TOP viewer labels with AXIS2_PLACEMENT_3D data")
                        self.update_original_labels(axis_data, "top")
                    else:
                        print("FAIL No AXIS2_PLACEMENT_3D data found for TOP viewer")
            else:
                print(f"DEBUG BOTTOM DEBUG: orig_rot before assignment = {orig_rot}")
                self.orig_pos_right = orig_pos
                self.orig_rot_right = orig_rot
                # Store direction vectors for REF direction display
                if 'orig_direction_vectors' in locals():
                    self.orig_direction_vectors_right = orig_direction_vectors
                    self.current_direction_vectors_right = orig_direction_vectors.copy()
                else:
                    self.orig_direction_vectors_right = {
                        'x_axis': [1, 0, 0], 'z_axis': [0, 0, 1], 'y_axis': [0, 1, 0]
                    }
                    self.current_direction_vectors_right = {
                        'x_axis': [1, 0, 0], 'z_axis': [0, 0, 1], 'y_axis': [0, 1, 0]
                    }
                # FIXED: Store direction vectors for green sphere orientation
                self.orig_z_direction_right = orig_z_direction
                self.orig_x_direction_right = orig_x_direction
                # Initialize current values to ACTUAL model coordinates
                self.current_pos_right = orig_pos.copy()  # Show real coordinates
                # CRITICAL FIX: Initialize current_rot_right with ACTUAL STEP file rotation values
                # This ensures the display shows correct initial values and rotation updates work properly
                self.current_rot_right = orig_rot.copy()  # Use ACTUAL rotation from STEP file
                print(f"CRITICAL FIX: Initialized current_rot_right with STEP file values: {self.current_rot_right}")
                print(f"CRITICAL FIX: Initialized current_direction_vectors_right with STEP file values: {self.current_direction_vectors_right}")
                print(f"DEBUG BOTTOM DEBUG: orig_rot after copy = {orig_rot}")
                print(f"DEBUG BOTTOM DEBUG: current_rot_right after copy = {self.current_rot_right}")
                print(f"DEBUG DEEP DEBUG BOTTOM: orig_pos_right = {self.orig_pos_right}")
                print(f"DEBUG DEEP DEBUG BOTTOM: orig_rot_right = {self.orig_rot_right}")
                print(f"DEBUG DEEP DEBUG BOTTOM: orig_z_direction_right = {self.orig_z_direction_right}")
                print(f"DEBUG DEEP DEBUG BOTTOM: orig_x_direction_right = {self.orig_x_direction_right}")
                print(f"DEBUG DEEP DEBUG BOTTOM: current_pos_right = {self.current_pos_right}")
                print(f"DEBUG DEEP DEBUG BOTTOM: current_rot_right = {self.current_rot_right}")

                # Check what the GUI labels will show
                if hasattr(self, 'lbl_orig_rot_x_bottom'):
                    print(f"DEBUG DEEP DEBUG BOTTOM: GUI will show orig_rot X = {self.orig_rot_right['x']:.3f}deg")
                    print(f"DEBUG DEEP DEBUG BOTTOM: GUI will show orig_rot Y = {self.orig_rot_right['y']:.3f}deg")
                    print(f"DEBUG DEEP DEBUG BOTTOM: GUI will show orig_rot Z = {self.orig_rot_right['z']:.3f}deg")

                # Update BOTTOM viewer GUI labels with AXIS2_PLACEMENT_3D data
                if hasattr(self, 'step_loader_right') and hasattr(self.step_loader_right, 'get_original_axis2_placement'):
                    axis_data = self.step_loader_right.get_original_axis2_placement()
                    if axis_data:
                        print("DEBUG Updating BOTTOM viewer labels with AXIS2_PLACEMENT_3D data")
                        self.update_original_labels(axis_data, "bottom")
        else:
            print(f"No polydata available for {viewer} viewer")

    def clear_view(self):
        """Clear the active viewer and reset numbers to zero"""
        if self.active_viewer == "top":
            self.vtk_renderer_left.clear_view()
            self.top_file_label.setText("TOP VIEWER - No file loaded")
            # Reset all transform values to zero
            self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        else:
            self.vtk_renderer_right.clear_view()
            self.bottom_file_label.setText("BOTTOM VIEWER - No file loaded")
            # Reset all transform values to zero
            self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

        # Update the display to show zeros
        self.update_transform_display()
        self.statusBar().showMessage(f"{self.active_viewer.title()} view cleared")

    def fit_view(self):
        """Fit view in active viewer"""
        if self.active_viewer == "top":
            if self.vtk_renderer_left:
                self.vtk_renderer_left.fit_view()
                print("Fitted TOP view")
        else:
            if self.vtk_renderer_right:
                self.vtk_renderer_right.fit_view()
                print("Fitted BOTTOM view")

    def save_transformed_step(self):
        """Save transformed STEP file from active viewer"""
        print("START SAVE_TRANSFORMED_STEP: Method called!")
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save STEP File", "", "STEP Files (*.step);;STL Files (*.stl);;All Files (*)"
        )

        if filename:
            print(f"Saving file: {filename}")
            import vtk  # Add missing import
            if self.active_viewer == "top":
                # Get the current polydata from the VTK renderer (includes transformations)
                if self.vtk_renderer_left.step_actor:
                    # Get transformed polydata
                    transform_filter = vtk.vtkTransformPolyDataFilter()
                    transform_filter.SetInputData(self.step_loader_left.current_polydata)
                    transform_filter.SetTransform(self.vtk_renderer_left.step_actor.GetUserTransform() or vtk.vtkTransform())
                    transform_filter.Update()
                    self.step_loader_left.current_polydata = transform_filter.GetOutput()
                    print("Applied TOP viewer transformations to save data")
                success = self.step_loader_left.save_step_file(filename)
            else:
                # Get the current polydata from the VTK renderer (includes transformations)
                if self.vtk_renderer_right.step_actor:
                    # Get transformed polydata
                    transform_filter = vtk.vtkTransformPolyDataFilter()
                    transform_filter.SetInputData(self.step_loader_right.current_polydata)
                    transform_filter.SetTransform(self.vtk_renderer_right.step_actor.GetUserTransform() or vtk.vtkTransform())
                    transform_filter.Update()
                    self.step_loader_right.current_polydata = transform_filter.GetOutput()
                    print("Applied BOTTOM viewer transformations to save data")
                success = self.step_loader_right.save_step_file(filename)

            if success:
                self.statusBar().showMessage(f"Saved: {filename}")
            else:
                self.statusBar().showMessage("Save failed")



    def reset_to_original(self):
        """Reset active viewer to original transform"""
        print("🔄🔄🔄 RESET FUNCTION CALLED - THIS SHOULD BE VISIBLE! 🔄🔄🔄")
        print("🔄 RESET BUTTON CLICKED: reset_to_original() function called!")
        print(f"🔄 RESET: Active viewer is {self.active_viewer}")
        if self.active_viewer == "top":
            # Reset position tracking to original position (not 0,0,0)
            if hasattr(self, 'orig_pos_left'):
                self.current_pos_left = self.orig_pos_left.copy()
            else:
                self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset to original STEP file rotation (not 0,0,0) to show correct angle
            if hasattr(self, 'orig_rot_left'):
                self.current_rot_left = self.orig_rot_left.copy()
            else:
                self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset movement delta
            if hasattr(self, 'movement_delta_left'):
                self.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            # Reset the actual VTK actors - handle both single and multi-actor cases
            import vtk
            transform = vtk.vtkTransform()
            transform.Identity()

            # Reset multi-actors if they exist
            if hasattr(self.vtk_renderer_left, 'step_actors') and self.vtk_renderer_left.step_actors:
                print(f"DEBUG: Restoring {len(self.vtk_renderer_left.step_actors)} actors to original transforms")
                print(f"DEBUG: Found {len(self.original_actor_transforms_left)} stored original transforms")

                for i, actor in enumerate(self.vtk_renderer_left.step_actors):
                    if i < len(self.original_actor_transforms_left):
                        original_transform = self.original_actor_transforms_left[i]
                        print(f"DEBUG: Multi-actor {i} visibility: {actor.GetVisibility()}")

                        if actor.GetVisibility():
                            print(f"DEBUG: Restoring VISIBLE actor {i} to original state")

                            # DEBUG: Show what the stored original transform contains
                            print(f"DEBUG: Stored original transform for actor {i}: {original_transform}")
                            if original_transform:
                                print(f"DEBUG: Original transform exists - will restore to stored state")
                            else:
                                print(f"DEBUG: Original transform is None - will restore to identity")

                            # DEBUG: Show ACTUAL current values BEFORE reset
                            current_pos = actor.GetPosition()
                            current_orient = actor.GetOrientation()
                            print(f"DEBUG: Actor {i} BEFORE reset: Pos={current_pos}, Orient={current_orient}")

                            # Try to reset using stored original transform
                            if isinstance(original_transform, dict):
                                # New format: dictionary with transform, position, orientation
                                stored_transform = original_transform.get('transform')
                                stored_position = original_transform.get('position', (0, 0, 0))
                                stored_orientation = original_transform.get('orientation', (0, 0, 0))

                                actor.SetUserTransform(stored_transform)
                                actor.SetPosition(*stored_position)
                                actor.SetOrientation(*stored_orientation)
                            else:
                                # Old format: just the transform object
                                actor.SetUserTransform(original_transform)
                                actor.SetOrientation(0, 0, 0)
                                actor.SetPosition(0, 0, 0)
                            actor.Modified()

                            # DEBUG: Show ACTUAL values AFTER reset attempt
                            new_pos = actor.GetPosition()
                            new_orient = actor.GetOrientation()
                            print(f"DEBUG: Actor {i} AFTER reset: Pos={new_pos}, Orient={new_orient}")

                            # DEBUG: Show what the actor's transform is now
                            current_transform_after = actor.GetUserTransform()
                            print(f"DEBUG: Actor {i} transform AFTER reset: {current_transform_after}")

                            if new_pos == (0.0, 0.0, 0.0) and new_orient == (0.0, 0.0, 0.0):
                                print(f"DEBUG: OK VISIBLE Actor {i} reset SUCCESSFUL")
                            else:
                                print(f"DEBUG: FAIL VISIBLE Actor {i} reset FAILED - values didn't change!")
                        else:
                            print(f"DEBUG: Skipping invisible actor {i}")
                    else:
                        print(f"DEBUG: FAIL No stored transform for actor {i}")

                print("DEBUG: Actor reset using original transforms completed")

            # Reset single actor if it exists
            elif hasattr(self.vtk_renderer_left, 'step_actor') and self.vtk_renderer_left.step_actor:
                print("DEBUG: Resetting single actor")
                self.vtk_renderer_left.step_actor.SetUserTransform(transform)
                self.vtk_renderer_left.step_actor.SetOrientation(0, 0, 0)
                self.vtk_renderer_left.step_actor.SetPosition(0, 0, 0)
                self.vtk_renderer_left.step_actor.Modified()
                print("DEBUG: Single actor reset to original")

            # NOTE: Bounding box will be recreated AFTER origin fix to prevent removal

            # CRITICAL: Reset camera to stored original position
            print("DEBUG: Resetting camera to stored original position")
            camera = self.vtk_renderer_left.renderer.GetActiveCamera()
            if hasattr(self, 'original_camera_left'):
                camera.SetPosition(*self.original_camera_left['position'])
                camera.SetFocalPoint(*self.original_camera_left['focal_point'])
                camera.SetViewUp(*self.original_camera_left['view_up'])
                print(f"DEBUG: Camera restored to original orientation: {self.original_camera_left['orientation']}")
            else:
                # Fallback to default position
                camera.SetPosition(0, 0, 1)
                camera.SetFocalPoint(0, 0, 0)
                camera.SetViewUp(0, 1, 0)
                self.vtk_renderer_left.renderer.ResetCamera()
                print("DEBUG: Camera reset to default position (no stored original)")
            print("DEBUG: Camera reset completed")

            # CRITICAL: Restore custom interaction style for model rotation (not camera rotation)
            print("DEBUG: Restoring custom interaction style for model rotation")
            from vtk_renderer import FixedCameraModelRotateStyle
            style = FixedCameraModelRotateStyle(self, "top")
            self.vtk_renderer_left.interactor.SetInteractorStyle(style)
            print("DEBUG: Custom interaction style restored - mouse will rotate model, not camera")

            # Force render to update display
            self.vtk_renderer_left.render_window.Render()

            # ULTIMATE FIX: Completely clear ALL origin-related actors with ZERO tolerance for duplicates
            print("TARGET ULTIMATE FIX: Removing ALL origin-related actors for TOP viewer")

            # Method 1: Use existing clear function
            if hasattr(self.vtk_renderer_left, 'clear_origin_overlay'):
                self.vtk_renderer_left.clear_origin_overlay()

            # Method 2: NUCLEAR OPTION - Remove ALL colored actors (red, green, blue)
            if hasattr(self.vtk_renderer_left, 'renderer') and self.vtk_renderer_left.renderer:
                renderer = self.vtk_renderer_left.renderer
                actors_to_remove = []

                # Find ALL colored actors that could be origin markers
                actors = renderer.GetActors()
                actors.InitTraversal()
                while True:
                    actor = actors.GetNextActor()
                    if not actor:
                        break

                    # Check if this looks like ANY origin-related actor
                    prop = actor.GetProperty()
                    if prop:
                        color = prop.GetColor()
                        # Red, Green, OR Blue actors (covers all origin marker types)
                        if (color[0] > 0.7 and color[1] < 0.3 and color[2] < 0.3) or \
                           (color[0] < 0.3 and color[1] > 0.7 and color[2] < 0.3) or \
                           (color[0] < 0.3 and color[1] < 0.3 and color[2] > 0.7):
                            actors_to_remove.append(actor)

                # Remove ALL found colored actors
                for actor in actors_to_remove:
                    renderer.RemoveActor(actor)
                    print(f"OK ULTIMATE FIX: Removed colored actor")

                print(f"OK ULTIMATE FIX: Removed {len(actors_to_remove)} colored actors")

            # Method 3: Clear ALL origin-related variables and lists (PROPERLY remove from renderer first)
            arrow_vars = ['part_origin_sphere', 'part_origin_x_arrow', 'part_origin_y_arrow', 'part_origin_z_arrow']
            for var in arrow_vars:
                if hasattr(self.vtk_renderer_left, var):
                    actor = getattr(self.vtk_renderer_left, var)
                    if actor and self.vtk_renderer_left.renderer:
                        self.vtk_renderer_left.renderer.RemoveActor(actor)
                        print(f"OK ULTIMATE FIX: Removed {var} from renderer")
                    setattr(self.vtk_renderer_left, var, None)
                    print(f"OK ULTIMATE FIX: Cleared {var} variable")

            # Clear actor lists
            list_vars = ['origin_actors', 'part_origin_actors']
            for var in list_vars:
                if hasattr(self.vtk_renderer_left, var):
                    setattr(self.vtk_renderer_left, var, [])
                    print(f"OK ULTIMATE FIX: Cleared {var} list")

            # Method 4: Force render to clear any cached actors
            if self.vtk_renderer_left.render_window:
                self.vtk_renderer_left.render_window.Render()
                print("OK ULTIMATE FIX: Forced render to clear cache")

            # ALSO reset overlay if overlay mode is active
            if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_renderer'):
                print("TARGET Also resetting overlay actors for TOP viewer")
                # Re-create overlay to reset all transformations
                self._create_overlay_widget()
                # Force render overlay
                if hasattr(self, 'vtk_widget_left'):
                    self.vtk_widget_left.GetRenderWindow().Render()

            # CRITICAL FIX: Delete ALL existing origin overlays first, then create proper ones
            print("TARGET RESET FIX: Manually clearing ALL origin overlays for TOP viewer")

            # Step 1: Force clear ALL origin overlays (world origin)
            if hasattr(self.vtk_renderer_left, 'clear_origin_overlay'):
                self.vtk_renderer_left.clear_origin_overlay()
                print("OK RESET FIX: TOP world origin overlay cleared")

            # Step 1b: Manually clear part origin overlays (since clear_part_origin_overlay doesn't exist)
            if hasattr(self.vtk_renderer_left, 'part_origin_actors'):
                print("TARGET RESET FIX: Manually removing part origin actors")
                for actor in self.vtk_renderer_left.part_origin_actors:
                    if actor and self.vtk_renderer_left.renderer:
                        self.vtk_renderer_left.renderer.RemoveActor(actor)
                        print("OK RESET FIX: Removed part origin actor")
                self.vtk_renderer_left.part_origin_actors = []  # Clear the list
                print("OK RESET FIX: TOP part origin actors manually cleared")

            # Step 1c: Also clear individual part origin actors (sphere + arrows) with detailed debug
            part_origin_actor_names = ['part_origin_sphere', 'part_origin_x_arrow', 'part_origin_y_arrow', 'part_origin_z_arrow']
            print(f"TARGET RESET FIX: Checking {len(part_origin_actor_names)} part origin actors for TOP viewer")
            for actor_name in part_origin_actor_names:
                if hasattr(self.vtk_renderer_left, actor_name):
                    actor = getattr(self.vtk_renderer_left, actor_name)
                    if actor:
                        if self.vtk_renderer_left.renderer:
                            self.vtk_renderer_left.renderer.RemoveActor(actor)
                            print(f"OK RESET FIX: Removed TOP {actor_name} from renderer")
                        else:
                            print(f"WARN RESET FIX: No renderer available to remove TOP {actor_name}")
                        setattr(self.vtk_renderer_left, actor_name, None)
                        print(f"OK RESET FIX: Cleared TOP {actor_name} variable")
                    else:
                        print(f"INFO RESET FIX: TOP {actor_name} was already None")
                else:
                    print(f"INFO RESET FIX: TOP {actor_name} attribute doesn't exist")

            # Step 2: Create proper origin overlays at correct positions
            print("TARGET RESET FIX: Creating proper origin overlays for TOP viewer")

            # Create world origin overlay (red markers at 0,0,0) - FORCE CREATION
            if hasattr(self.vtk_renderer_left, 'create_origin_overlay'):
                # Force clear the origin_actors list to ensure clean creation
                if hasattr(self.vtk_renderer_left, 'origin_actors'):
                    self.vtk_renderer_left.origin_actors = []
                    print("OK RESET FIX: Forced clear of origin_actors list")

                # Always create new world origin markers
                self.vtk_renderer_left.create_origin_overlay()
                print("OK RESET FIX: TOP world origin overlay created at (0,0,0)")

            # Create part origin overlay (green marker at STEP file origin) - FORCE CREATION
            if hasattr(self, 'orig_pos_left') and hasattr(self.vtk_renderer_left, 'create_part_origin_overlay'):
                # Always create new part origin marker (already cleared above)
                self.vtk_renderer_left.create_part_origin_overlay(
                    self.orig_pos_left['x'],
                    self.orig_pos_left['y'],
                    self.orig_pos_left['z'],
                    getattr(self, 'orig_z_direction_left', None),
                    getattr(self, 'orig_x_direction_left', None)
                )
                print(f"OK RESET FIX: TOP part origin overlay created at ({self.orig_pos_left['x']:.3f}, {self.orig_pos_left['y']:.3f}, {self.orig_pos_left['z']:.3f})")

            # Force render to ensure overlays are visible
            self.vtk_renderer_left.render_window.Render()
            print("OK RESET FIX: TOP viewer rendered with proper overlays")

            # NOW recreate bounding box AFTER origin fix (so it doesn't get removed)
            if self.bbox_visible_left:
                print("DEBUG RESET FIX: Recreating bounding box after origin fix")
                self.vtk_renderer_left.update_bounding_box()
                print("OK RESET FIX: Bounding box recreated")

            print("Reset TOP viewer to original")

        else:
            # Reset position tracking to original position (not 0,0,0)
            if hasattr(self, 'orig_pos_right'):
                self.current_pos_right = self.orig_pos_right.copy()
            else:
                self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset to original STEP file rotation (not 0,0,0) to show correct angle
            if hasattr(self, 'orig_rot_right'):
                self.current_rot_right = self.orig_rot_right.copy()
            else:
                self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset movement delta
            if hasattr(self, 'movement_delta_right'):
                self.movement_delta_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            # Reset the actual VTK actors - handle both single and multi-actor cases
            import vtk
            transform = vtk.vtkTransform()
            transform.Identity()

            # Reset multi-actors if they exist
            if hasattr(self.vtk_renderer_right, 'step_actors') and self.vtk_renderer_right.step_actors:
                print(f"DEBUG: Restoring {len(self.vtk_renderer_right.step_actors)} BOTTOM actors to original transforms")
                print(f"DEBUG: Found {len(self.original_actor_transforms_right)} stored original transforms")

                for i, actor in enumerate(self.vtk_renderer_right.step_actors):
                    if i < len(self.original_actor_transforms_right):
                        original_transform = self.original_actor_transforms_right[i]
                        print(f"DEBUG: BOTTOM Multi-actor {i} visibility: {actor.GetVisibility()}")

                        if actor.GetVisibility():
                            print(f"DEBUG: Restoring VISIBLE BOTTOM actor {i} to original state")

                            # Try to reset using stored original transform
                            if isinstance(original_transform, dict):
                                # New format: dictionary with transform, position, orientation
                                stored_transform = original_transform.get('transform')
                                stored_position = original_transform.get('position', (0, 0, 0))
                                stored_orientation = original_transform.get('orientation', (0, 0, 0))

                                actor.SetUserTransform(stored_transform)
                                actor.SetPosition(*stored_position)
                                actor.SetOrientation(*stored_orientation)
                            else:
                                # Old format: just the transform object
                                actor.SetUserTransform(original_transform)
                                actor.SetOrientation(0, 0, 0)
                                actor.SetPosition(0, 0, 0)
                            actor.Modified()
                            print(f"DEBUG: BOTTOM Multi-actor {i} reset to original")

            # Reset single actor if it exists
            elif hasattr(self.vtk_renderer_right, 'step_actor') and self.vtk_renderer_right.step_actor:
                print("DEBUG: Resetting BOTTOM single actor")
                if hasattr(self, 'original_actor_transforms_right') and self.original_actor_transforms_right:
                    original_transform = self.original_actor_transforms_right[0]
                    if isinstance(original_transform, dict):
                        stored_transform = original_transform.get('transform')
                        stored_position = original_transform.get('position', (0, 0, 0))
                        stored_orientation = original_transform.get('orientation', (0, 0, 0))

                        self.vtk_renderer_right.step_actor.SetUserTransform(stored_transform)
                        self.vtk_renderer_right.step_actor.SetPosition(*stored_position)
                        self.vtk_renderer_right.step_actor.SetOrientation(*stored_orientation)
                    else:
                        self.vtk_renderer_right.step_actor.SetUserTransform(original_transform)
                        self.vtk_renderer_right.step_actor.SetOrientation(0, 0, 0)
                        self.vtk_renderer_right.step_actor.SetPosition(0, 0, 0)
                else:
                    # Fallback to identity transform
                    self.vtk_renderer_right.step_actor.SetUserTransform(transform)
                    self.vtk_renderer_right.step_actor.SetOrientation(0, 0, 0)
                    self.vtk_renderer_right.step_actor.SetPosition(0, 0, 0)
                self.vtk_renderer_right.step_actor.Modified()
                print("DEBUG: BOTTOM Single actor reset to original")

            # Reset camera to original position
            camera = self.vtk_renderer_right.renderer.GetActiveCamera()
            if hasattr(self, 'original_camera_right'):
                camera.SetPosition(*self.original_camera_right['position'])
                camera.SetFocalPoint(*self.original_camera_right['focal_point'])
                camera.SetViewUp(*self.original_camera_right['view_up'])
                print(f"DEBUG: BOTTOM Camera restored to original orientation: {self.original_camera_right['orientation']}")
            else:
                # Fallback to default position
                camera.SetPosition(0, 0, 1)
                camera.SetFocalPoint(0, 0, 0)
                camera.SetViewUp(0, 1, 0)
                self.vtk_renderer_right.renderer.ResetCamera()
                print("DEBUG: BOTTOM Camera reset to default position (no stored original)")
            print("DEBUG: BOTTOM Camera reset completed")

            # CRITICAL: Restore custom interaction style for model rotation (not camera rotation)
            print("DEBUG: Restoring custom interaction style for BOTTOM model rotation")
            from vtk_renderer import FixedCameraModelRotateStyle
            style = FixedCameraModelRotateStyle(self, "bottom")
            self.vtk_renderer_right.interactor.SetInteractorStyle(style)
            print("DEBUG: BOTTOM Custom interaction style restored - mouse will rotate model, not camera")

            # NOTE: Bounding box will be recreated AFTER origin fix to prevent removal

            # Force render to update display
            self.vtk_renderer_right.render_window.Render()

            # ALSO reset overlay if overlay mode is active
            if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_renderer'):
                print("TARGET Also resetting overlay actors for BOTTOM viewer")
                # Re-create overlay to reset all transformations
                self._create_overlay_widget()
                # Force render overlay
                if hasattr(self, 'vtk_widget_left'):
                    self.vtk_widget_left.GetRenderWindow().Render()

            # CRITICAL FIX: Delete ALL existing origin overlays first, then create proper ones
            print("TARGET RESET FIX: Manually clearing ALL origin overlays for BOTTOM viewer")

            # Step 1: Force clear ALL origin overlays (world origin)
            if hasattr(self.vtk_renderer_right, 'clear_origin_overlay'):
                self.vtk_renderer_right.clear_origin_overlay()
                print("OK RESET FIX: BOTTOM world origin overlay cleared")

            # Step 1b: Manually clear part origin overlays (since clear_part_origin_overlay doesn't exist)
            if hasattr(self.vtk_renderer_right, 'part_origin_actors'):
                print("TARGET RESET FIX: Manually removing part origin actors")
                for actor in self.vtk_renderer_right.part_origin_actors:
                    if actor and self.vtk_renderer_right.renderer:
                        self.vtk_renderer_right.renderer.RemoveActor(actor)
                        print("OK RESET FIX: Removed part origin actor")
                self.vtk_renderer_right.part_origin_actors = []  # Clear the list
                print("OK RESET FIX: BOTTOM part origin actors manually cleared")

            # Step 1c: Also clear individual part origin actors (sphere + arrows) with detailed debug
            part_origin_actor_names = ['part_origin_sphere', 'part_origin_x_arrow', 'part_origin_y_arrow', 'part_origin_z_arrow']
            print(f"TARGET RESET FIX: Checking {len(part_origin_actor_names)} part origin actors for BOTTOM viewer")
            for actor_name in part_origin_actor_names:
                if hasattr(self.vtk_renderer_right, actor_name):
                    actor = getattr(self.vtk_renderer_right, actor_name)
                    if actor:
                        if self.vtk_renderer_right.renderer:
                            self.vtk_renderer_right.renderer.RemoveActor(actor)
                            print(f"OK RESET FIX: Removed BOTTOM {actor_name} from renderer")
                        else:
                            print(f"WARN RESET FIX: No renderer available to remove BOTTOM {actor_name}")
                        setattr(self.vtk_renderer_right, actor_name, None)
                        print(f"OK RESET FIX: Cleared BOTTOM {actor_name} variable")
                    else:
                        print(f"INFO RESET FIX: BOTTOM {actor_name} was already None")
                else:
                    print(f"INFO RESET FIX: BOTTOM {actor_name} attribute doesn't exist")

            # Step 2: Create proper origin overlays at correct positions
            print("TARGET RESET FIX: Creating proper origin overlays for BOTTOM viewer")

            # Create world origin overlay (red markers at 0,0,0) - FORCE CREATION
            if hasattr(self.vtk_renderer_right, 'create_origin_overlay'):
                # Force clear the origin_actors list to ensure clean creation
                if hasattr(self.vtk_renderer_right, 'origin_actors'):
                    self.vtk_renderer_right.origin_actors = []
                    print("OK RESET FIX: Forced clear of BOTTOM origin_actors list")

                # Always create new world origin markers
                self.vtk_renderer_right.create_origin_overlay()
                print("OK RESET FIX: BOTTOM world origin overlay created at (0,0,0)")

            # Create part origin overlay (green marker at STEP file origin) - FORCE CREATION
            if hasattr(self, 'orig_pos_right') and hasattr(self.vtk_renderer_right, 'create_part_origin_overlay'):
                # Always create new part origin marker (already cleared above)
                self.vtk_renderer_right.create_part_origin_overlay(
                    self.orig_pos_right['x'],
                    self.orig_pos_right['y'],
                    self.orig_pos_right['z'],
                    getattr(self, 'orig_z_direction_right', None),
                    getattr(self, 'orig_x_direction_right', None)
                )
                print(f"OK RESET FIX: BOTTOM part origin overlay created at ({self.orig_pos_right['x']:.3f}, {self.orig_pos_right['y']:.3f}, {self.orig_pos_right['z']:.3f})")

            # Force render to ensure overlays are visible
            self.vtk_renderer_right.render_window.Render()
            print("OK RESET FIX: BOTTOM viewer rendered with proper overlays")

            # NOW recreate bounding box AFTER origin fix (so it doesn't get removed)
            if self.bbox_visible_right:
                print("DEBUG RESET FIX: Recreating BOTTOM bounding box after origin fix")
                self.vtk_renderer_right.update_bounding_box()
                print("OK RESET FIX: BOTTOM bounding box recreated")

            print("Reset BOTTOM viewer to original")

        # Update the display values
        self.update_transform_display()
        self.statusBar().showMessage(f"{self.active_viewer.title()} reset to original")

    def align_bottom_center(self):
        """Align model to bottom-center: Position 0,0,0 and Rotation 0,0,0"""
        print("DEBUG: Align bottom-center called")

        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
            print("DEBUG: Aligning TOP viewer model to bottom-center")
        else:
            renderer = self.vtk_renderer_right
            print("DEBUG: Aligning BOTTOM viewer model to bottom-center")

        if not renderer:
            print("DEBUG: No renderer available")
            return

        # Reset multi-actors if they exist
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            print(f"DEBUG: Aligning {len(renderer.step_actors)} multi-actors to 0,0,0")
            for i, actor in enumerate(renderer.step_actors):
                if actor.GetVisibility():
                    print(f"DEBUG: Aligning visible actor {i} to position 0,0,0 and rotation 0,0,0")

                    # Set to exact position and rotation
                    actor.SetPosition(0, 0, 0)
                    actor.SetOrientation(0, 0, 0)
                    actor.SetUserTransform(None)  # Clear any transforms
                    actor.Modified()

                    print(f"DEBUG: Actor {i} aligned - Pos: {actor.GetPosition()}, Orient: {actor.GetOrientation()}")

        # Reset single actor if it exists
        elif hasattr(renderer, 'step_actor') and renderer.step_actor:
            print("DEBUG: Aligning single actor to 0,0,0")
            actor = renderer.step_actor
            actor.SetPosition(0, 0, 0)
            actor.SetOrientation(0, 0, 0)
            actor.SetUserTransform(None)
            actor.Modified()
            print(f"DEBUG: Single actor aligned - Pos: {actor.GetPosition()}, Orient: {actor.GetOrientation()}")

        # Update bounding box
        if self.active_viewer == "top" and self.bbox_visible_left:
            renderer.update_bounding_box()
        elif self.active_viewer == "bottom" and self.bbox_visible_right:
            renderer.update_bounding_box()

        # Update display values to show actual aligned coordinates
        if self.active_viewer == "top":
            # Keep the original position values (actual model coordinates)
            # Only reset rotation to 0,0,0 since we aligned rotation
            self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        else:
            # Keep the original position values (actual model coordinates)
            # Only reset rotation to 0,0,0 since we aligned rotation
            self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

        # Reset camera to original position so user can see the aligned model
        print("DEBUG: Resetting camera to show aligned model")
        camera = renderer.renderer.GetActiveCamera()
        if self.active_viewer == "top" and hasattr(self, 'original_camera_left'):
            camera.SetPosition(*self.original_camera_left['position'])
            camera.SetFocalPoint(*self.original_camera_left['focal_point'])
            camera.SetViewUp(*self.original_camera_left['view_up'])
            print("DEBUG: Camera reset to original TOP position")
        elif self.active_viewer == "bottom" and hasattr(self, 'original_camera_right'):
            camera.SetPosition(*self.original_camera_right['position'])
            camera.SetFocalPoint(*self.original_camera_right['focal_point'])
            camera.SetViewUp(*self.original_camera_right['view_up'])
            print("DEBUG: Camera reset to original BOTTOM position")
        else:
            # Fallback to default camera position
            camera.SetPosition(0, 0, 10)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 1, 0)
            renderer.renderer.ResetCamera()
            print("DEBUG: Camera reset to default position")

        # Force render
        renderer.render_window.Render()
        self.update_transform_display()

        print("DEBUG: Align bottom-center completed")
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Aligned to bottom-center (0,0,0)")

    def _is_actor_in_renderer(self, actor, vtk_renderer):
        """Check if an actor is actually in the VTK renderer"""
        if not actor or not vtk_renderer or not hasattr(vtk_renderer, 'renderer'):
            return False

        try:
            actor_collection = vtk_renderer.renderer.GetActors()
            actor_collection.InitTraversal()

            current_actor = actor_collection.GetNextActor()
            while current_actor:
                if current_actor == actor:
                    return True
                current_actor = actor_collection.GetNextActor()
            return False
        except:
            return False

    def rotate_shape(self, axis, degrees):
        """Rotate shape in active viewer - USE SAME METHOD AS MOUSE ROTATION"""
        print(f"\n🔄 BUTTON ROTATION: rotate_shape({axis}, {degrees}) -> calling model rotation (same as mouse)")

        # BEFORE DEBUG - Capture all positions and text
        self._debug_capture_before_state("ROTATION", f"{axis.upper()}{degrees:+.1f}°")

        # FIX: Use the same rotation method as mouse rotation for consistent behavior
        self._apply_model_rotation(self.active_viewer, axis, degrees)

        # AFTER DEBUG - Capture all positions and text
        self._debug_capture_after_state("ROTATION", f"{axis.upper()}{degrees:+.1f}°")

    def toggle_bbox_overlay(self):
        """Toggle bounding box in active viewer"""
        if self.active_viewer == "top":
            self.bbox_visible_left = not self.bbox_visible_left
            if self.vtk_renderer_left:
                self.vtk_renderer_left.toggle_bounding_box(self.bbox_visible_left)
            status = "shown" if self.bbox_visible_left else "hidden"
            print(f"TOP bounding box {status}")
        else:
            self.bbox_visible_right = not self.bbox_visible_right
            if self.vtk_renderer_right:
                self.vtk_renderer_right.toggle_bounding_box(self.bbox_visible_right)
            status = "shown" if self.bbox_visible_right else "hidden"
            print(f"BOTTOM bounding box {status}")

        self.statusBar().showMessage(f"{self.active_viewer.title()}: Bounding box {status}")

    def force_view_update(self):
        """Force update of transform display - refreshes the position/rotation numbers"""
        print("DEBUG DEBUG: force_view_update() called!")
        self.update_transform_display()
        self.statusBar().showMessage("Transform display refreshed - numbers updated from current view")

    def update_transform_display(self):
        """Update the transform display labels for both viewers"""
        print("DEBUG DEEP DEBUG: update_transform_display() called!")

        # DEEP DEBUG: Check what values we have
        if hasattr(self, 'orig_rot_left'):
            print(f"DEBUG DEEP DEBUG: orig_rot_left = {self.orig_rot_left}")
        else:
            print("DEBUG DEEP DEBUG: orig_rot_left NOT SET!")

        if hasattr(self, 'orig_rot_right'):
            print(f"DEBUG DEEP DEBUG: orig_rot_right = {self.orig_rot_right}")
        else:
            print("DEBUG DEEP DEBUG: orig_rot_right NOT SET!")

        # Keep the original rotation tracking (don't read camera values for display)

        # Update TOP viewer labels with 3 decimal precision
        if hasattr(self, 'lbl_orig_pos_x'):
            self.lbl_orig_pos_x.setText(f"X: {self.orig_pos_left['x']:.3f}")
            self.lbl_orig_pos_y.setText(f"Y: {self.orig_pos_left['y']:.3f}")
            self.lbl_orig_pos_z.setText(f"Z: {self.orig_pos_left['z']:.3f}")

        if hasattr(self, 'lbl_orig_rot_x'):
            self.lbl_orig_rot_x.setText(f"X: {self.orig_rot_left['x']:.3f}deg")
            self.lbl_orig_rot_y.setText(f"Y: {self.orig_rot_left['y']:.3f}deg")
            self.lbl_orig_rot_z.setText(f"Z: {self.orig_rot_left['z']:.3f}deg")

        if hasattr(self, 'lbl_curr_pos_x'):
            self.lbl_curr_pos_x.setText(f"X: {self.current_pos_left['x']:.3f}")
            self.lbl_curr_pos_y.setText(f"Y: {self.current_pos_left['y']:.3f}")
            self.lbl_curr_pos_z.setText(f"Z: {self.current_pos_left['z']:.3f}")

        if hasattr(self, 'lbl_curr_rot_x'):
            self.lbl_curr_rot_x.setText(f"X: {self.current_rot_left['x']:.3f}deg")
            self.lbl_curr_rot_y.setText(f"Y: {self.current_rot_left['y']:.3f}deg")
            self.lbl_curr_rot_z.setText(f"Z: {self.current_rot_left['z']:.3f}deg")

        # Keep the original rotation tracking (don't read camera values for display)

        # Update text overlays for both viewers
        self.update_text_overlays()

    def toggle_bbox_overlay(self):
        """Toggle bounding box in active viewer"""
        if self.active_viewer == "top":
            self.bbox_visible_left = not self.bbox_visible_left
            if self.vtk_renderer_left:
                self.vtk_renderer_left.toggle_bounding_box(self.bbox_visible_left)
            status = "shown" if self.bbox_visible_left else "hidden"
            print(f"TOP bounding box {status}")
        else:
            self.bbox_visible_right = not self.bbox_visible_right
            if self.vtk_renderer_right:
                self.vtk_renderer_right.toggle_bounding_box(self.bbox_visible_right)
            status = "shown" if self.bbox_visible_right else "hidden"
            print(f"BOTTOM bounding box {status}")

        self.statusBar().showMessage(f"{self.active_viewer.title()}: Bounding box {status}")

    def force_view_update(self):
        """Force update of transform display - refreshes the position/rotation numbers"""
        print("DEBUG DEBUG: force_view_update() called!")
        self.update_transform_display()
        self.statusBar().showMessage("Transform display refreshed - numbers updated from current view")

    def update_transform_display(self):
        """Update the transform display labels for both viewers"""
        print("DEBUG DEEP DEBUG: update_transform_display() called!")

        # DEEP DEBUG: Check what values we have
        if hasattr(self, 'orig_rot_left'):
            print(f"DEBUG DEEP DEBUG: orig_rot_left = {self.orig_rot_left}")
        else:
            print("DEBUG DEEP DEBUG: orig_rot_left NOT SET!")

        if hasattr(self, 'orig_rot_right'):
            print(f"DEBUG DEEP DEBUG: orig_rot_right = {self.orig_rot_right}")
        else:
            print("DEBUG DEEP DEBUG: orig_rot_right NOT SET!")

        # Keep the original rotation tracking (don't read camera values for display)

        # Update TOP viewer labels with 3 decimal precision
        if hasattr(self, 'lbl_orig_pos_x'):
            self.lbl_orig_pos_x.setText(f"X: {self.orig_pos_left['x']:.3f}")
            self.lbl_orig_pos_y.setText(f"Y: {self.orig_pos_left['y']:.3f}")
            self.lbl_orig_pos_z.setText(f"Z: {self.orig_pos_left['z']:.3f}")

        # Update rotation labels using correct label names
        if hasattr(self, 'lbl_orig_angle') and hasattr(self, 'orig_rot_left'):
            # Calculate total angle magnitude
            import math
            total_angle = math.sqrt(self.orig_rot_left['x']**2 + self.orig_rot_left['y']**2 + self.orig_rot_left['z']**2)

            # Set angle label
            self.lbl_orig_angle.setText(f"{total_angle:.1f}deg")

            # Set axis labels
            if hasattr(self, 'lbl_orig_axis_x'):
                self.lbl_orig_axis_x.setText(f"X: {self.orig_rot_left['x']:.2f}")
                self.lbl_orig_axis_y.setText(f"Y: {self.orig_rot_left['y']:.2f}")
                self.lbl_orig_axis_z.setText(f"Z: {self.orig_rot_left['z']:.2f}")

        if hasattr(self, 'lbl_curr_pos_x'):
            self.lbl_curr_pos_x.setText(f"X: {self.current_pos_left['x']:.3f}")
            self.lbl_curr_pos_y.setText(f"Y: {self.current_pos_left['y']:.3f}")
            self.lbl_curr_pos_z.setText(f"Z: {self.current_pos_left['z']:.3f}")

        if hasattr(self, 'lbl_curr_rot_x'):
            self.lbl_curr_rot_x.setText(f"X: {self.current_rot_left['x']:.3f}deg")
            self.lbl_curr_rot_y.setText(f"Y: {self.current_rot_left['y']:.3f}deg")
            self.lbl_curr_rot_z.setText(f"Z: {self.current_rot_left['z']:.3f}deg")

        # Keep the original rotation tracking (don't read camera values for display)

        # Update BOTTOM viewer labels with 3 decimal precision
        if hasattr(self, 'lbl_orig_pos_x_bottom'):
            self.lbl_orig_pos_x_bottom.setText(f"X: {self.orig_pos_right['x']:.3f}")
            self.lbl_orig_pos_y_bottom.setText(f"Y: {self.orig_pos_right['y']:.3f}")
            self.lbl_orig_pos_z_bottom.setText(f"Z: {self.orig_pos_right['z']:.3f}")

        # Update BOTTOM rotation labels using correct label names
        if hasattr(self, 'lbl_orig_angle_bottom') and hasattr(self, 'orig_rot_right'):
            # Calculate total angle magnitude
            import math
            total_angle = math.sqrt(self.orig_rot_right['x']**2 + self.orig_rot_right['y']**2 + self.orig_rot_right['z']**2)

            # Set angle label
            self.lbl_orig_angle_bottom.setText(f"{total_angle:.1f}deg")

            # Set axis labels
            if hasattr(self, 'lbl_orig_axis_x_bottom'):
                self.lbl_orig_axis_x_bottom.setText(f"X: {self.orig_rot_right['x']:.2f}")
                self.lbl_orig_axis_y_bottom.setText(f"Y: {self.orig_rot_right['y']:.2f}")
                self.lbl_orig_axis_z_bottom.setText(f"Z: {self.orig_rot_right['z']:.2f}")

        if hasattr(self, 'lbl_curr_pos_x_bottom'):
            self.lbl_curr_pos_x_bottom.setText(f"X: {self.current_pos_right['x']:.3f}")
            self.lbl_curr_pos_y_bottom.setText(f"Y: {self.current_pos_right['y']:.3f}")
            self.lbl_curr_pos_z_bottom.setText(f"Z: {self.current_pos_right['z']:.3f}")

        if hasattr(self, 'lbl_curr_rot_x_bottom'):
            self.lbl_curr_rot_x_bottom.setText(f"X: {self.current_rot_right['x']:.3f}deg")
            self.lbl_curr_rot_y_bottom.setText(f"Y: {self.current_rot_right['y']:.3f}deg")
            self.lbl_curr_rot_z_bottom.setText(f"Z: {self.current_rot_right['z']:.3f}deg")

        # Update VTK text overlays
        self.update_text_overlays()

    def update_original_labels(self, axis_data, viewer="top"):
        """Update GUI labels with AXIS2_PLACEMENT_3D data from STEP file for both viewers"""
        print(f"DEBUG update_original_labels() called for {viewer} viewer with axis_data:")
        print(f"   Point: {axis_data['point']}")
        print(f"   Dir1: {axis_data['dir1']}")
        print(f"   Dir2: {axis_data['dir2']}")

        try:
            # Parse Dir1 (Direction line - line 2)
            if isinstance(axis_data['dir1'], (tuple, list)) and len(axis_data['dir1']) == 3:
                dir1_values = axis_data['dir1']
            else:
                # Parse string format like "(0.0, 0.9104, -0.4138)"
                dir1_str = str(axis_data['dir1']).strip('()')
                dir1_values = [float(x.strip()) for x in dir1_str.split(',')]

            # Parse Dir2 (REF. Direction line - line 3)
            if isinstance(axis_data['dir2'], (tuple, list)) and len(axis_data['dir2']) == 3:
                dir2_values = axis_data['dir2']
            else:
                # Parse string format like "(0.0, 0.4138, 0.9104)"
                dir2_str = str(axis_data['dir2']).strip('()')
                dir2_values = [float(x.strip()) for x in dir2_str.split(',')]

            if viewer == "top":
                # Update TOP Direction labels (Dir1)
                if hasattr(self, 'lbl_orig_axis_x'):
                    self.lbl_orig_axis_x.setText(f"X: {dir1_values[0]:.4f}")
                    self.lbl_orig_axis_y.setText(f"Y: {dir1_values[1]:.4f}")
                    self.lbl_orig_axis_z.setText(f"Z: {dir1_values[2]:.4f}")
                    print(f"OK Updated TOP Direction labels (Dir1): X={dir1_values[0]:.4f}, Y={dir1_values[1]:.4f}, Z={dir1_values[2]:.4f}")
                else:
                    print("FAIL TOP Direction labels (lbl_orig_axis_*) not found")

                # Update TOP REF. Direction labels (Dir2)
                if hasattr(self, 'lbl_orig_ref_x'):
                    self.lbl_orig_ref_x.setText(f"X: {dir2_values[0]:.4f}")
                    self.lbl_orig_ref_y.setText(f"Y: {dir2_values[1]:.4f}")
                    self.lbl_orig_ref_z.setText(f"Z: {dir2_values[2]:.4f}")
                    print(f"OK Updated TOP REF. Direction labels (Dir2): X={dir2_values[0]:.4f}, Y={dir2_values[1]:.4f}, Z={dir2_values[2]:.4f}")
                else:
                    print("FAIL TOP REF. Direction labels (lbl_orig_ref_*) not found")

            elif viewer == "bottom":
                # Update BOTTOM Direction labels (Dir1)
                if hasattr(self, 'lbl_orig_axis_x_bottom'):
                    self.lbl_orig_axis_x_bottom.setText(f"X: {dir1_values[0]:.4f}")
                    self.lbl_orig_axis_y_bottom.setText(f"Y: {dir1_values[1]:.4f}")
                    self.lbl_orig_axis_z_bottom.setText(f"Z: {dir1_values[2]:.4f}")
                    print(f"OK Updated BOTTOM Direction labels (Dir1): X={dir1_values[0]:.4f}, Y={dir1_values[1]:.4f}, Z={dir1_values[2]:.4f}")
                else:
                    print("FAIL BOTTOM Direction labels (lbl_orig_axis_*_bottom) not found")

                # Update BOTTOM REF. Direction labels (Dir2)
                if hasattr(self, 'lbl_orig_ref_x_bottom'):
                    self.lbl_orig_ref_x_bottom.setText(f"X: {dir2_values[0]:.4f}")
                    self.lbl_orig_ref_y_bottom.setText(f"Y: {dir2_values[1]:.4f}")
                    self.lbl_orig_ref_z_bottom.setText(f"Z: {dir2_values[2]:.4f}")
                    print(f"OK Updated BOTTOM REF. Direction labels (Dir2): X={dir2_values[0]:.4f}, Y={dir2_values[1]:.4f}, Z={dir2_values[2]:.4f}")
                else:
                    print("FAIL BOTTOM REF. Direction labels (lbl_orig_ref_*_bottom) not found")

        except Exception as e:
            print(f"FAIL Error updating {viewer} labels: {e}")
            import traceback
            traceback.print_exc()

    def update_original_top_labels(self, axis_data):
        """Backward compatibility wrapper"""
        self.update_original_labels(axis_data, "top")

    def _extract_step_coordinate_system(self, filename):
        """Extract coordinate system from STEP file dynamically"""
        try:
            if not filename or not os.path.exists(filename):
                print(f"DEBUG STEP file not found: {filename}")
                return None, None

            with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            import re

            # Find ANY AXIS2_PLACEMENT_3D that might contain transformations
            # Try multiple patterns to be more robust for saved files
            axis_match = None

            # Pattern 1: Try #11 (common in original files)
            axis_match = re.search(r'#11 = AXIS2_PLACEMENT_3D\(\'\',(#\d+),(#\d+),(#\d+)\);', content)

            # Pattern 2: Try any AXIS2_PLACEMENT_3D with non-standard directions (indicates transformation)
            if not axis_match:
                all_axis_matches = re.findall(r'(#\d+) = AXIS2_PLACEMENT_3D\(\'\',(#\d+),(#\d+),(#\d+)\);', content)
                for axis_id, origin_ref, z_dir_ref, x_dir_ref in all_axis_matches:
                    # Check if this axis has non-standard directions (indicating transformation)
                    z_dir_pattern = f'{z_dir_ref} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
                    z_dir_match = re.search(z_dir_pattern, content)
                    if z_dir_match:
                        z_coords = [float(x.strip()) for x in z_dir_match.group(1).split(',')]
                        # If Z direction is not (0,0,1), this axis has transformations
                        if not (abs(z_coords[0]) < 0.001 and abs(z_coords[1]) < 0.001 and abs(z_coords[2] - 1.0) < 0.001):
                            print(f"DEBUG Found transformed AXIS2_PLACEMENT_3D: {axis_id} with Z direction {z_coords}")
                            axis_match = type('Match', (), {
                                'group': lambda self, n: [axis_id, origin_ref, z_dir_ref, x_dir_ref][n-1]
                            })()
                            break

            # Pattern 3: Fallback to first AXIS2_PLACEMENT_3D found
            if not axis_match and all_axis_matches:
                axis_id, origin_ref, z_dir_ref, x_dir_ref = all_axis_matches[0]
                print(f"DEBUG Using first AXIS2_PLACEMENT_3D found: {axis_id}")
                axis_match = type('Match', (), {
                    'group': lambda self, n: [axis_id, origin_ref, z_dir_ref, x_dir_ref][n-1]
                })()

            if not axis_match:
                print(f"DEBUG Could not find any AXIS2_PLACEMENT_3D in {filename}")
                return None, None

            origin_ref = axis_match.group(1)
            z_dir_ref = axis_match.group(2)
            x_dir_ref = axis_match.group(3)

            # Extract origin point
            origin_pattern = f'{origin_ref} = CARTESIAN_POINT\\(\'\'\\,\\(([^)]+)\\)\\);'
            origin_match = re.search(origin_pattern, content)
            if origin_match:
                coords = [float(x.strip()) for x in origin_match.group(1).split(',')]
                orig_pos = {'x': coords[0], 'y': coords[1], 'z': coords[2]}
            else:
                orig_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            # Extract Z direction
            z_dir_pattern = f'{z_dir_ref} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
            z_dir_match = re.search(z_dir_pattern, content)
            if z_dir_match:
                z_direction = [float(x.strip()) for x in z_dir_match.group(1).split(',')]
            else:
                z_direction = [0, 0, 1]

            # Extract X direction
            x_dir_pattern = f'{x_dir_ref} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
            x_dir_match = re.search(x_dir_pattern, content)
            if x_dir_match:
                x_direction = [float(x.strip()) for x in x_dir_match.group(1).split(',')]
            else:
                x_direction = [1, 0, 0]

            # Calculate rotation from direction vectors
            import math

            # Improved rotation calculation from direction vectors
            # Check if this is a standard orientation (no rotation)
            if (abs(z_direction[0]) < 0.001 and abs(z_direction[1]) < 0.001 and abs(z_direction[2] - 1.0) < 0.001 and
                abs(x_direction[0] - 1.0) < 0.001 and abs(x_direction[1]) < 0.001 and abs(x_direction[2]) < 0.001):
                # Standard orientation - no rotation
                orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                print(f"DEBUG Standard orientation detected - no rotation")
            else:
                # Calculate rotation angles from transformed direction vectors
                # For X rotation (rotation around X axis), look at Z direction Y and Z components
                # Use negative to match the expected sign convention
                x_rot = -math.degrees(math.atan2(z_direction[1], z_direction[2]))

                # For Y rotation (rotation around Y axis), look at Z direction X component
                y_rot = math.degrees(math.atan2(-z_direction[0], math.sqrt(z_direction[1]**2 + z_direction[2]**2)))

                # For Z rotation (rotation around Z axis), look at X direction X and Y components
                z_rot = math.degrees(math.atan2(x_direction[1], x_direction[0]))

                orig_rot = {'x': x_rot, 'y': y_rot, 'z': z_rot}
                print(f"DEBUG Transformed orientation detected:")
                print(f"   X rotation: {x_rot:.1f}deg (from Z direction Y,Z components)")
                print(f"   Y rotation: {y_rot:.1f}deg (from Z direction X component)")
                print(f"   Z rotation: {z_rot:.1f}deg (from X direction X,Y components)")

            print(f"DEBUG EXTRACTED from {filename}:")
            print(f"  Origin: {orig_pos}")
            print(f"  Z Direction: {z_direction}")
            print(f"  X Direction: {x_direction}")
            print(f"  Calculated Rotation: {orig_rot}")

            # FIXED: Return direction vectors for green sphere orientation
            return orig_pos, orig_rot, z_direction, x_direction

        except Exception as e:
            print(f"DEBUG Error extracting STEP coordinate system: {e}")
            return None, None

    def _transform_direction_vector(self, original_vector, rotation_angles):
        """Transform a direction vector by rotation angles (in degrees)"""
        import math
        import numpy as np

        # Convert degrees to radians
        rx = math.radians(rotation_angles['x'])
        ry = math.radians(rotation_angles['y'])
        rz = math.radians(rotation_angles['z'])

        # Create rotation matrices
        # Rotation around X-axis
        Rx = np.array([
            [1, 0, 0],
            [0, math.cos(rx), -math.sin(rx)],
            [0, math.sin(rx), math.cos(rx)]
        ])

        # Rotation around Y-axis
        Ry = np.array([
            [math.cos(ry), 0, math.sin(ry)],
            [0, 1, 0],
            [-math.sin(ry), 0, math.cos(ry)]
        ])

        # Rotation around Z-axis
        Rz = np.array([
            [math.cos(rz), -math.sin(rz), 0],
            [math.sin(rz), math.cos(rz), 0],
            [0, 0, 1]
        ])

        # Combined rotation matrix (order: Z * Y * X)
        R = Rz @ Ry @ Rx

        # Transform the vector
        original = np.array(original_vector)
        transformed = R @ original

        return transformed.tolist()

    def _get_actual_step_file_values(self, viewer="top"):
        """Extract actual mathematical values from VTK actor that would be written to STEP file"""
        try:
            import vtk
            import numpy as np
            import math

            # Get the appropriate VTK renderer
            if viewer == "top":
                renderer = self.vtk_renderer_left
            else:
                renderer = self.vtk_renderer_right

            # Get the first available actor (multi-actor or single actor)
            actor = None
            if hasattr(renderer, 'step_actors') and renderer.step_actors:
                actor = renderer.step_actors[0]  # Use first multi-actor
            elif hasattr(renderer, 'step_actor') and renderer.step_actor:
                actor = renderer.step_actor  # Use single actor

            if not actor:
                print(f"FAIL No VTK actor found for {viewer} viewer")
                return None

            # Get the actor's current transformation matrix
            transform = vtk.vtkTransform()
            if actor.GetUserTransform():
                transform.DeepCopy(actor.GetUserTransform())
            else:
                # Get transformation from position and orientation
                transform.Translate(*actor.GetPosition())
                transform.RotateX(actor.GetOrientation()[0])
                transform.RotateY(actor.GetOrientation()[1])
                transform.RotateZ(actor.GetOrientation()[2])

            # Get the 4x4 transformation matrix
            matrix = transform.GetMatrix()

            # Extract rotation part (3x3 upper-left submatrix)
            rotation_matrix = np.zeros((3, 3))
            for i in range(3):
                for j in range(3):
                    rotation_matrix[i, j] = matrix.GetElement(i, j)

            # Extract translation (position)
            position = [matrix.GetElement(0, 3), matrix.GetElement(1, 3), matrix.GetElement(2, 3)]

            # Convert rotation matrix to direction vectors (like STEP file AXIS2_PLACEMENT_3D)
            # Dir1 = Z-axis of the transformed coordinate system
            dir1 = [rotation_matrix[0, 2], rotation_matrix[1, 2], rotation_matrix[2, 2]]
            # Dir2 = X-axis of the transformed coordinate system
            dir2 = [rotation_matrix[0, 0], rotation_matrix[1, 0], rotation_matrix[2, 0]]

            print(f"DEBUG EXTRACTED ACTUAL VALUES from {viewer} VTK actor:")
            print(f"   Position: {position}")
            print(f"   Dir1 (Z-axis): {dir1}")
            print(f"   Dir2 (X-axis): {dir2}")

            return {
                'position': position,
                'dir1': dir1,
                'dir2': dir2
            }

        except Exception as e:
            print(f"FAIL Error extracting actual STEP values from {viewer} actor: {e}")
            return None

    def _calculate_rotation_from_axis2_directions(self, dir1, dir2):
        """Calculate rotation angles from AXIS2_PLACEMENT_3D direction vectors"""
        import math

        try:
            # Parse direction vectors if they're strings
            if isinstance(dir1, str):
                # Parse string format like "(0, 0, 1)"
                import re
                coords = re.findall(r'[-+]?\d*\.?\d+', dir1)
                if len(coords) >= 3:
                    dir1 = [float(coords[0]), float(coords[1]), float(coords[2])]
                else:
                    dir1 = [0, 0, 1]  # Default Z direction

            if isinstance(dir2, str):
                # Parse string format like "(1, 0, 0)"
                import re
                coords = re.findall(r'[-+]?\d*\.?\d+', dir2)
                if len(coords) >= 3:
                    dir2 = [float(coords[0]), float(coords[1]), float(coords[2])]
                else:
                    dir2 = [1, 0, 0]  # Default X direction

            # Ensure we have valid direction vectors
            if not isinstance(dir1, (list, tuple)) or len(dir1) < 3:
                dir1 = [0, 0, 1]  # Default Z direction
            if not isinstance(dir2, (list, tuple)) or len(dir2) < 3:
                dir2 = [1, 0, 0]  # Default X direction

            # Calculate rotation angles from direction vectors
            # dir1 is typically the Z-axis direction, dir2 is typically the X-axis direction
            z_dir = dir1
            x_dir = dir2

            # Calculate rotation angles from direction vectors
            # Normalize vectors first
            z_len = math.sqrt(z_dir[0]**2 + z_dir[1]**2 + z_dir[2]**2)
            x_len = math.sqrt(x_dir[0]**2 + x_dir[1]**2 + x_dir[2]**2)

            if z_len > 0:
                z_dir = [z_dir[0]/z_len, z_dir[1]/z_len, z_dir[2]/z_len]
            if x_len > 0:
                x_dir = [x_dir[0]/x_len, x_dir[1]/x_len, x_dir[2]/x_len]

            # Calculate Euler angles from direction vectors for rotation tracking
            # The GUI needs rotation values that change during user rotations

            # Create rotation matrix from the orthonormal basis
            # Calculate Y-axis as cross product of Z and X
            y_axis = (
                z_dir[1] * x_dir[2] - z_dir[2] * x_dir[1],
                z_dir[2] * x_dir[0] - z_dir[0] * x_dir[2],
                z_dir[0] * x_dir[1] - z_dir[1] * x_dir[0]
            )

            # Normalize Y-axis
            y_length = math.sqrt(sum(comp**2 for comp in y_axis))
            if y_length > 1e-10:
                y_axis = tuple(comp / y_length for comp in y_axis)
            else:
                y_axis = (0, 1, 0)

            # Create rotation matrix from the orthonormal basis
            # Matrix columns are [X Y Z] basis vectors
            rotation_matrix = [
                [x_dir[0], y_axis[0], z_dir[0]],
                [x_dir[1], y_axis[1], z_dir[1]],
                [x_dir[2], y_axis[2], z_dir[2]]
            ]

            # Extract Euler angles from rotation matrix (ZYX convention)
            sy = math.sqrt(rotation_matrix[0][0]**2 + rotation_matrix[1][0]**2)

            singular = sy < 1e-6

            if not singular:
                x_rot = math.atan2(rotation_matrix[2][1], rotation_matrix[2][2])
                y_rot = math.atan2(-rotation_matrix[2][0], sy)
                z_rot = math.atan2(rotation_matrix[1][0], rotation_matrix[0][0])
            else:
                x_rot = math.atan2(-rotation_matrix[1][2], rotation_matrix[1][1])
                y_rot = math.atan2(-rotation_matrix[2][0], sy)
                z_rot = 0

            # Convert to degrees
            x_deg = math.degrees(x_rot)
            y_deg = math.degrees(y_rot)
            z_deg = math.degrees(z_rot)

            print(f"Calculated rotation from AXIS2_PLACEMENT_3D directions:")
            print(f"   Z-axis direction: {z_dir}")
            print(f"   X-axis direction: {x_dir}")
            print(f"   Y-axis direction (calculated): {y_axis}")
            print(f"   Euler angles: X={x_deg:.3f}°, Y={y_deg:.3f}°, Z={z_deg:.3f}°")

            # Store direction vectors for REF direction display
            result = {
                'x': x_deg, 'y': y_deg, 'z': z_deg,
                'direction_vectors': {
                    'x_axis': x_dir,
                    'z_axis': z_dir,
                    'y_axis': y_axis
                }
            }

            return result

        except Exception as e:
            print(f"Error calculating rotation from directions: {e}")
            print(f"   Using fallback rotation: (0, 0, 0)")
            return {'x': 0.0, 'y': 0.0, 'z': 0.0}

    def _calculate_unified_display_numbers(self, viewer):
        """UNIFIED CALCULATION: Calculate display numbers for any viewer (top or bottom)"""
        print(f"🔢 UNIFIED CALC: Calculating display numbers for {viewer} viewer")

        # Get viewer-specific data
        if viewer == "top":
            current_rot = getattr(self, 'current_rot_left', {'x': 0, 'y': 0, 'z': 0})
            current_pos = getattr(self, 'current_pos_left', {'x': 0, 'y': 0, 'z': 0})
            cursor_pos = getattr(self, 'cursor_pos_left', {'x': 0, 'y': 0, 'z': 0})
            renderer = getattr(self, 'vtk_renderer_left', None)
            step_loader = getattr(self, 'step_loader_left', None)
        else:  # bottom
            current_rot = getattr(self, 'current_rot_right', {'x': 0, 'y': 0, 'z': 0})
            current_pos = getattr(self, 'current_pos_right', {'x': 0, 'y': 0, 'z': 0})
            cursor_pos = getattr(self, 'cursor_pos_right', {'x': 0, 'y': 0, 'z': 0})
            renderer = getattr(self, 'vtk_renderer_right', None)
            step_loader = getattr(self, 'step_loader_right', None)

        # Calculate cursor display
        cursor_display = f"CURSOR: X={cursor_pos['x']:.2f} Y={cursor_pos['y']:.2f} Z={cursor_pos['z']:.2f}"

        # CRITICAL FIX: Get the ACTUAL VTK actor position after rotation
        # The green ball DOES move when rotated - get its actual transformed position
        if renderer and hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            # Get the actual VTK actor position - this DOES change after rotation
            actual_pos = renderer.part_origin_sphere.GetPosition()
            model_pos = {
                'x': actual_pos[0],
                'y': actual_pos[1],
                'z': actual_pos[2]
            }
            print(f"🎯 ORIGIN FIX: Using ACTUAL VTK actor position: {model_pos}")
        else:
            # Fallback to current_pos if green ball not available
            model_pos = current_pos
            print(f"⚠️ ORIGIN FIX: No green ball found, using current_pos: {model_pos}")

        # ADD DEBUG INFO TO BOTTOM SCREEN: Show VTK green ball values for debugging
        if viewer == 'bottom':
            # Get TOP renderer's green ball for comparison
            top_renderer = getattr(self, 'vtk_renderer_left', None)
            if top_renderer and hasattr(top_renderer, 'part_origin_sphere') and top_renderer.part_origin_sphere:
                top_green_ball_pos = top_renderer.part_origin_sphere.GetPosition()
                top_green_ball_orient = top_renderer.part_origin_sphere.GetOrientation()

                # Add debug info to bottom screen display
                debug_info = f"DEBUG VTK Green Ball: Pos=({top_green_ball_pos[0]:.3f}, {top_green_ball_pos[1]:.3f}, {top_green_ball_pos[2]:.3f}) Orient=({top_green_ball_orient[0]:.1f}, {top_green_ball_orient[1]:.1f}, {top_green_ball_orient[2]:.1f})"

                # Override the model display to show debug info
                model_display = debug_info
                local_origin_display = f"DEBUG Converted: Model=({model_pos['x']:.3f}, {model_pos['y']:.3f}, {model_pos['z']:.3f}) Local=({model_pos['x']:.3f}, {model_pos['y']:.3f}, {model_pos['z']:.3f})"

        # Get original STEP file direction vectors
        if viewer == "top":
            direction_vectors = getattr(self, 'orig_direction_vectors_left', {})
            original_x_dir = direction_vectors.get('x_axis', [0, 0, 0])  # X-axis from STEP file
            original_z_dir = direction_vectors.get('z_axis', [0, 0, 0])  # Z-axis from STEP file
            # Get user rotation (excluding initial STEP file rotation)
            user_rot = getattr(self, 'current_rot_left', {'x': 0, 'y': 0, 'z': 0})
            initial_rot = getattr(self, 'orig_rot_left', {'x': 0, 'y': 0, 'z': 0})
        else:
            direction_vectors = getattr(self, 'orig_direction_vectors_right', {})
            original_x_dir = direction_vectors.get('x_axis', [0, 0, 0])  # X-axis from STEP file
            original_z_dir = direction_vectors.get('z_axis', [0, 0, 0])  # Z-axis from STEP file
            user_rot = getattr(self, 'current_rot_right', {'x': 0, 'y': 0, 'z': 0})
            initial_rot = getattr(self, 'orig_rot_right', {'x': 0, 'y': 0, 'z': 0})

        # Calculate only USER rotation (current - initial)
        user_only_rot = {
            'x': user_rot['x'] - initial_rot['x'],
            'y': user_rot['y'] - initial_rot['y'],
            'z': user_rot['z'] - initial_rot['z']
        }

        # Transform direction vectors using ONLY user rotation
        if any(abs(user_only_rot[axis]) > 0.001 for axis in ['x', 'y', 'z']):
            # User has rotated - show transformed values
            transformed_x_dir = self._transform_direction_vector(original_x_dir, user_only_rot)
            transformed_z_dir = self._transform_direction_vector(original_z_dir, user_only_rot)
        else:
            # No user rotation - show raw STEP file values
            transformed_x_dir = original_x_dir
            transformed_z_dir = original_z_dir


        # FIXED: Swap the mapping - Direction and REF Direction were backwards
        # Direction should be Z-axis direction from STEP file
        # REF Direction should be X-axis direction from STEP file
        model_dir_x, model_dir_y, model_dir_z = transformed_z_dir      # Direction (was REF)
        model_ref_x, model_ref_y, model_ref_z = transformed_x_dir      # REF Direction (was Direction)

        # CRITICAL FIX: Model and Local Origin MUST show identical values
        # Both represent the same transformed model state
        model_display = f"Model Direction (X = {model_dir_x:.3f} Y = {model_dir_y:.3f} Z = {model_dir_z:.3f}) REF. Direction (X = {model_ref_x:.3f} Y = {model_ref_y:.3f} Z = {model_ref_z:.3f}) Origin (X = {model_pos['x']:.3f} Y = {model_pos['y']:.3f} Z = {model_pos['z']:.3f})"

        # Local Origin MUST use the SAME values as Model (not green ball position)
        # The green ball position is just a visual marker, the numbers should match the model
        local_origin_display = f"Local Origin Direction (X = {model_dir_x:.3f} Y = {model_dir_y:.3f} Z = {model_dir_z:.3f}) REF. Direction (X = {model_ref_x:.3f} Y = {model_ref_y:.3f} Z = {model_ref_z:.3f}) Origin (X = {model_pos['x']:.3f} Y = {model_pos['y']:.3f} Z = {model_pos['z']:.3f})"

        print(f"🔢 UNIFIED CALC: {viewer} - Model: {model_display}")
        print(f"🔢 UNIFIED CALC: {viewer} - Local Origin: {local_origin_display}")

        # DEBUG: Verify they are identical
        if model_display == local_origin_display.replace("Local Origin", "Model"):
            print(f"✅ DEBUG: Model and Local Origin are IDENTICAL")
        else:
            print(f"❌ DEBUG: Model and Local Origin are DIFFERENT - THIS IS A BUG")

        # Calculate world origin marker display (red markers - should move with model)
        if viewer == "top":
            renderer = self.vtk_renderer_left
        else:
            renderer = self.vtk_renderer_right

        # CORRECT: World origin numbers should match the RED semicircle actors' actual position/orientation
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors and len(renderer.origin_actors) > 0:
            # Use the first red semicircle actor to get actual position and orientation
            world_actor = renderer.origin_actors[0]
            if world_actor:
                world_pos = world_actor.GetPosition()
                world_orient = world_actor.GetOrientation()

                # Calculate direction vectors from the actual red semicircle orientation
                world_dir_x, world_dir_y, world_dir_z = world_orient[0]/100.0, world_orient[1]/100.0, world_orient[2]/100.0
                world_ref_x, world_ref_y, world_ref_z = world_orient[0]/100.0, world_orient[1]/100.0, world_orient[2]/100.0

                world_origin_display = f"World Origin Direction (X = {world_dir_x:.3f} Y = {world_dir_y:.3f} Z = {world_dir_z:.3f}) REF. Direction (X = {world_ref_x:.3f} Y = {world_ref_y:.3f} Z = {world_ref_z:.3f}) Origin (X = {world_pos[0]:.3f} Y = {world_pos[1]:.3f} Z = {world_pos[2]:.3f})"
                print(f"🌍 WORLD ORIGIN: Numbers from actual red semicircle actor at {world_pos}")
            else:
                world_origin_display = "World Origin Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)"
                print("🌍 WORLD ORIGIN: No red semicircle actor found - using defaults")
        else:
            # No world origin actors found - use default
            world_origin_display = "World Origin Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)"
            print("🌍 WORLD ORIGIN: No origin_actors found - using defaults")

        print(f"🔢 UNIFIED CALC: {viewer} - Cursor: {cursor_display}")
        print(f"🔢 UNIFIED CALC: {viewer} - Model: {model_display}")
        print(f"🔢 UNIFIED CALC: {viewer} - Local Origin: {local_origin_display}")
        print(f"🔢 UNIFIED CALC: {viewer} - World Origin: {world_origin_display}")

        # DEBUG: Check if Model and Local Origin are the same
        if "Model Direction" in model_display and "Local Origin Direction" in local_origin_display:
            model_parts = model_display.split("Direction")[1].split("REF. Direction")
            local_parts = local_origin_display.split("Direction")[1].split("REF. Direction")
            if len(model_parts) >= 2 and len(local_parts) >= 2:
                model_dir = model_parts[0].strip()
                local_dir = local_parts[0].strip()
                if model_dir == local_dir:
                    print(f"✅ DEBUG: Model and Local Origin directions MATCH: {model_dir}")
                else:
                    print(f"❌ DEBUG: Model and Local Origin directions DIFFERENT:")
                    print(f"   Model: {model_dir}")
                    print(f"   Local: {local_dir}")

        # DEBUG: Show what values we're actually using
        print(f"🔍 DEBUG {viewer}: current_rot = {current_rot}")
        print(f"🔍 DEBUG {viewer}: current_pos = {current_pos}")
        if renderer and hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            pos = renderer.part_origin_sphere.GetPosition()
            orient = renderer.part_origin_sphere.GetOrientation()
            print(f"🔍 DEBUG {viewer}: Green ball pos = {pos}, orient = {orient}")

        return {
            'cursor': cursor_display,
            'model': model_display,
            'local_origin': local_origin_display,
            'world_origin': world_origin_display,
            'unified_values': {
                'dir_x': model_dir_x,
                'dir_y': model_dir_y,
                'dir_z': model_dir_z,
                'ref_x': model_ref_x,
                'ref_y': model_ref_y,
                'ref_z': model_ref_z,
                'pos_x': model_pos['x'],
                'pos_y': model_pos['y'],
                'pos_z': model_pos['z']
            }
        }

    def _extract_unified_values_from_display(self, display_data, viewer):
        """Extract the unified calculation values for text overlay"""
        if 'unified_values' in display_data:
            return display_data['unified_values']
        else:
            # Fallback to zero values
            return {
                'dir_x': 0.0, 'dir_y': 0.0, 'dir_z': 0.0,
                'ref_x': 0.0, 'ref_y': 0.0, 'ref_z': 0.0,
                'pos_x': 0.0, 'pos_y': 0.0, 'pos_z': 0.0
            }

    def update_text_overlays(self):
        """Update VTK text overlays on viewers with loaded models using unified calculation"""
        # Always update TOP viewer
        print("🔢 UNIFIED: update_text_overlays() called - updating viewers with loaded models")
        self._update_viewer_text_overlays("top")

        # Only update BOTTOM viewer if it has a STEP model loaded (not just any actors)
        if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right and hasattr(self.vtk_renderer_right, 'renderer'):
            # Check if bottom viewer has a STEP model loaded by checking step_loader_right
            if hasattr(self, 'step_loader_right') and self.step_loader_right and hasattr(self.step_loader_right, 'shape') and self.step_loader_right.shape:
                print("🔢 UNIFIED: Bottom viewer has STEP model - updating it too")
                self._update_viewer_text_overlays("bottom")
            else:
                print("🔢 UNIFIED: Bottom viewer has no STEP model - skipping it")
        else:
            print("🔢 UNIFIED: Bottom viewer not initialized - skipping it")

        print("✅ UNIFIED: All viewers with models updated using unified calculation")

    def _update_viewer_text_overlays(self, viewer):
        """Update text overlays for a specific viewer using unified calculation"""
        print(f"🔢 UNIFIED: Updating {viewer} viewer text overlays")

        # Get unified calculation results
        display_data = self._calculate_unified_display_numbers(viewer)

        # Extract the 4 text components
        cursor_text = display_data.get('cursor', 'CURSOR: X=0.00 Y=0.00 Z=0.00')
        model_text = display_data.get('model', 'Model Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)')
        local_origin_text = display_data.get('local_origin', 'Local Origin Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)')
        world_origin_text = display_data.get('world_origin', 'World Origin Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)')

        print(f"🔢 {viewer.upper()}: Cursor: {cursor_text}")
        print(f"🔢 {viewer.upper()}: Model: {model_text}")
        print(f"🔢 {viewer.upper()}: Local Origin: {local_origin_text}")
        print(f"🔢 {viewer.upper()}: World Origin: {world_origin_text}")

        # Update the appropriate text actors
        if viewer == "top":
            self._update_top_text_actors(cursor_text, model_text, local_origin_text, world_origin_text)
        else:
            self._update_bottom_text_actors(cursor_text, model_text, local_origin_text, world_origin_text)

    def _update_top_text_actors(self, cursor_text, model_text, local_origin_text, world_origin_text):
        """Update TOP viewer text actors with unified calculation results"""
        # Only update if model is loaded
        if hasattr(self, 'step_loader_left') and self.step_loader_left.current_polydata is not None:
            # Update cursor text actor
            if hasattr(self, 'cursor_text_actor_left'):
                self.cursor_text_actor_left.SetInput(cursor_text)
                self.cursor_text_actor_left.SetVisibility(1)
                print(f"✅ TOP: Updated cursor text: {cursor_text}")

            # Update local origin text actor
            if hasattr(self, 'local_origin_text_actor_left'):
                self.local_origin_text_actor_left.SetInput(local_origin_text)
                self.local_origin_text_actor_left.SetVisibility(1)
                print(f"✅ TOP: Updated local origin text")

            # Update world origin text actor
            if hasattr(self, 'world_origin_text_actor_left'):
                self.world_origin_text_actor_left.SetInput(world_origin_text)
                self.world_origin_text_actor_left.SetVisibility(1)
                print(f"✅ TOP: Updated world origin text")

            # Update model text actor
            if hasattr(self, 'combined_text_actor_left'):
                self.combined_text_actor_left.SetInput(model_text)
                self.combined_text_actor_left.SetVisibility(1)
                print(f"✅ TOP: Updated model text")

            # Update DEBUG text actors with VTK actor values
            if hasattr(self, 'debug_vtk_ball_actor_left'):
                # Get TOP renderer's main model actor for debugging
                top_renderer = getattr(self, 'vtk_renderer_left', None)
                if top_renderer and hasattr(top_renderer, 'step_actor') and top_renderer.step_actor:
                    top_model_pos = top_renderer.step_actor.GetPosition()
                    top_model_orient = top_renderer.step_actor.GetOrientation()
                    debug_vtk_text = f"DEBUG VTK Main Model: Pos=({top_model_pos[0]:.3f}, {top_model_pos[1]:.3f}, {top_model_pos[2]:.3f}) Orient=({top_model_orient[0]:.1f}, {top_model_orient[1]:.1f}, {top_model_orient[2]:.1f})"
                elif top_renderer and hasattr(top_renderer, 'part_origin_sphere') and top_renderer.part_origin_sphere:
                    top_green_ball_pos = top_renderer.part_origin_sphere.GetPosition()
                    top_green_ball_orient = top_renderer.part_origin_sphere.GetOrientation()
                    debug_vtk_text = f"DEBUG VTK Green Ball: Pos=({top_green_ball_pos[0]:.3f}, {top_green_ball_pos[1]:.3f}, {top_green_ball_pos[2]:.3f}) Orient=({top_green_ball_orient[0]:.1f}, {top_green_ball_orient[1]:.1f}, {top_green_ball_orient[2]:.1f})"
                else:
                    debug_vtk_text = "DEBUG VTK: No actors found"

                self.debug_vtk_ball_actor_left.SetInput(debug_vtk_text)
                self.debug_vtk_ball_actor_left.SetVisibility(1)
                print(f"✅ TOP: Updated DEBUG VTK ball text")

            if hasattr(self, 'debug_converted_actor_left'):
                # Get the model_pos from the unified calculation (same logic as the fix)
                renderer = getattr(self, 'vtk_renderer_left', None)
                if renderer and hasattr(renderer, 'step_actor') and renderer.step_actor:
                    actual_pos = renderer.step_actor.GetPosition()
                    model_pos = {'x': actual_pos[0], 'y': actual_pos[1], 'z': actual_pos[2]}
                elif renderer and hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
                    actual_pos = renderer.part_origin_sphere.GetPosition()
                    model_pos = {'x': actual_pos[0], 'y': actual_pos[1], 'z': actual_pos[2]}
                else:
                    model_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                debug_converted_text = f"DEBUG Converted: Model=({model_pos['x']:.3f}, {model_pos['y']:.3f}, {model_pos['z']:.3f}) Local=({model_pos['x']:.3f}, {model_pos['y']:.3f}, {model_pos['z']:.3f})"
                self.debug_converted_actor_left.SetInput(debug_converted_text)
                self.debug_converted_actor_left.SetVisibility(1)
                print(f"✅ TOP: Updated DEBUG converted text")

            # Render the changes
            if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left.render_window:
                self.vtk_renderer_left.render_window.Render()
        else:
            # No model loaded - hide all text actors
            if hasattr(self, 'cursor_text_actor_left'):
                self.cursor_text_actor_left.SetVisibility(0)
            if hasattr(self, 'local_origin_text_actor_left'):
                self.local_origin_text_actor_left.SetVisibility(0)
            if hasattr(self, 'world_origin_text_actor_left'):
                self.world_origin_text_actor_left.SetVisibility(0)
            if hasattr(self, 'combined_text_actor_left'):
                self.combined_text_actor_left.SetVisibility(0)
            if hasattr(self, 'debug_vtk_ball_actor_left'):
                self.debug_vtk_ball_actor_left.SetVisibility(0)
            if hasattr(self, 'debug_converted_actor_left'):
                self.debug_converted_actor_left.SetVisibility(0)

    def _update_bottom_text_actors(self, cursor_text, model_text, local_origin_text, world_origin_text):
        """Update BOTTOM viewer text actors with unified calculation results"""
        # Only update if model is loaded
        if hasattr(self, 'step_loader_right') and self.step_loader_right.current_polydata is not None:
            # Update cursor text actor
            if hasattr(self, 'cursor_text_actor_right'):
                self.cursor_text_actor_right.SetInput(cursor_text)
                self.cursor_text_actor_right.SetVisibility(1)
                print(f"✅ BOTTOM: Updated cursor text: {cursor_text}")

            # Update local origin text actor
            if hasattr(self, 'local_origin_text_actor_right'):
                self.local_origin_text_actor_right.SetInput(local_origin_text)
                self.local_origin_text_actor_right.SetVisibility(1)
                print(f"✅ BOTTOM: Updated local origin text")

            # Update world origin text actor
            if hasattr(self, 'world_origin_text_actor_right'):
                self.world_origin_text_actor_right.SetInput(world_origin_text)
                self.world_origin_text_actor_right.SetVisibility(1)
                print(f"✅ BOTTOM: Updated world origin text")

            # Update model text actor
            if hasattr(self, 'combined_text_actor_right'):
                self.combined_text_actor_right.SetInput(model_text)
                self.combined_text_actor_right.SetVisibility(1)
                print(f"✅ BOTTOM: Updated model text")

            # Update DEBUG text actors with VTK green ball values
            if hasattr(self, 'debug_vtk_ball_actor_right'):
                # Get TOP renderer's green ball for debugging
                top_renderer = getattr(self, 'vtk_renderer_left', None)
                if top_renderer and hasattr(top_renderer, 'part_origin_sphere') and top_renderer.part_origin_sphere:
                    top_green_ball_pos = top_renderer.part_origin_sphere.GetPosition()
                    top_green_ball_orient = top_renderer.part_origin_sphere.GetOrientation()
                    debug_vtk_text = f"DEBUG VTK Green Ball: Pos=({top_green_ball_pos[0]:.3f}, {top_green_ball_pos[1]:.3f}, {top_green_ball_pos[2]:.3f}) Orient=({top_green_ball_orient[0]:.1f}, {top_green_ball_orient[1]:.1f}, {top_green_ball_orient[2]:.1f})"
                else:
                    debug_vtk_text = "DEBUG VTK Green Ball: No green ball found"

                self.debug_vtk_ball_actor_right.SetInput(debug_vtk_text)
                self.debug_vtk_ball_actor_right.SetVisibility(1)
                print(f"✅ BOTTOM: Updated DEBUG VTK ball text")

            if hasattr(self, 'debug_converted_actor_right'):
                # Show the converted model position values
                debug_converted_text = f"DEBUG Converted: Model=({model_pos['x']:.3f}, {model_pos['y']:.3f}, {model_pos['z']:.3f}) Local=({model_pos['x']:.3f}, {model_pos['y']:.3f}, {model_pos['z']:.3f})"
                self.debug_converted_actor_right.SetInput(debug_converted_text)
                self.debug_converted_actor_right.SetVisibility(1)
                print(f"✅ BOTTOM: Updated DEBUG converted text")

            # Render the changes
            if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right.render_window:
                self.vtk_renderer_right.render_window.Render()
        else:
            # No model loaded - hide all text actors
            if hasattr(self, 'cursor_text_actor_right'):
                self.cursor_text_actor_right.SetVisibility(0)
            if hasattr(self, 'local_origin_text_actor_right'):
                self.local_origin_text_actor_right.SetVisibility(0)
            if hasattr(self, 'world_origin_text_actor_right'):
                self.world_origin_text_actor_right.SetVisibility(0)
            if hasattr(self, 'combined_text_actor_right'):
                self.combined_text_actor_right.SetVisibility(0)
            if hasattr(self, 'debug_vtk_ball_actor_right'):
                self.debug_vtk_ball_actor_right.SetVisibility(0)
            if hasattr(self, 'debug_converted_actor_right'):
                self.debug_converted_actor_right.SetVisibility(0)


    def _extract_rotation_from_vtk_actor(self, viewer):
        """Extract rotation values from STEP file coordinate system analysis
        This analyzes the actual STEP file to determine applied rotations"""
        try:
            if viewer == "top":
                step_loader = self.step_loader_left
                current_rot = getattr(self, 'current_rot_left', {'x': 0, 'y': 0, 'z': 0})
            else:
                step_loader = self.step_loader_right
                current_rot = getattr(self, 'current_rot_right', {'x': 0, 'y': 0, 'z': 0})

            if not step_loader or not hasattr(step_loader, 'current_filename'):
                print(f"FAIL No STEP loader found for {viewer} viewer")
                return {'x': 0, 'y': 0, 'z': 0}

            # Method 1: Use the tracked rotation values (from button rotations)
            button_rotation = current_rot.copy()
            print(f"DEBUG METHOD 1 (tracked button rotation): {button_rotation}")

            # Method 2: Extract from STEP file coordinate system analysis
            step_rotation = self._analyze_step_coordinate_system(step_loader.current_filename)
            print(f"DEBUG METHOD 2 (STEP file coordinate system): {step_rotation}")

            # Use STEP file analysis if available, otherwise fall back to button rotation
            if step_rotation and any(abs(v) > 0.1 for v in step_rotation.values()):
                rotation = step_rotation
                print(f"DEBUG USING STEP FILE ANALYSIS: {rotation}")
            else:
                rotation = button_rotation
                print(f"DEBUG USING BUTTON ROTATION: {rotation}")

            # TODO: Add mouse rotation detection later
            if not any(abs(v) > 0.1 for v in rotation.values()):
                print("WARN No rotation detected - mouse rotations not yet captured")

            return rotation

        except Exception as e:
            print(f"FAIL Error extracting rotation: {e}")
            import traceback
            traceback.print_exc()
            return {'x': 0, 'y': 0, 'z': 0}

    def _analyze_step_coordinate_system(self, filename):
        """Analyze STEP file coordinate system to extract rotation angles"""
        try:
            if not filename or not os.path.exists(filename):
                return {'x': 0, 'y': 0, 'z': 0}

            with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            import re
            import math

            # Look for the main coordinate system (usually AXIS2_PLACEMENT_3D with ID #11)
            # This is the primary coordinate system that gets transformed
            axis_pattern = r'#11 = AXIS2_PLACEMENT_3D\(\'\',(#\d+),(#\d+),(#\d+)\);'
            axis_match = re.search(axis_pattern, content)

            if not axis_match:
                # Try alternative patterns for different STEP file formats
                axis_pattern = r'#(\d+) = AXIS2_PLACEMENT_3D\(\'\',(#\d+),(#\d+),(#\d+)\);'
                axis_matches = re.findall(axis_pattern, content)
                if axis_matches:
                    # Use the first coordinate system found
                    axis_match = axis_matches[0]
                    point_id, dir1_id, dir2_id = axis_match[1], axis_match[2], axis_match[3]
                else:
                    return {'x': 0, 'y': 0, 'z': 0}
            else:
                point_id, dir1_id, dir2_id = axis_match.groups()

            # Extract the direction vectors - handle flexible spacing and formatting
            dir1_pattern = f'{dir1_id}\\s*=\\s*DIRECTION\\(\'\'\\s*,\\s*\\(\\s*([-\\d.E+\\s]+)\\s*,\\s*([-\\d.E+\\s]+)\\s*,\\s*([-\\d.E+\\s]+)\\s*\\)\\s*\\)\\s*;'
            dir2_pattern = f'{dir2_id}\\s*=\\s*DIRECTION\\(\'\'\\s*,\\s*\\(\\s*([-\\d.E+\\s]+)\\s*,\\s*([-\\d.E+\\s]+)\\s*,\\s*([-\\d.E+\\s]+)\\s*\\)\\s*\\)\\s*;'

            dir1_match = re.search(dir1_pattern, content)
            dir2_match = re.search(dir2_pattern, content)

            if not dir1_match or not dir2_match:
                print(f"DEBUG Could not find direction vectors for coordinate system")
                print(f"DEBUG Looking for patterns:")
                print(f"   dir1_pattern: {dir1_pattern}")
                print(f"   dir2_pattern: {dir2_pattern}")
                # Try to find any DIRECTION entries for debugging
                all_directions = re.findall(r'#\d+\s*=\s*DIRECTION\([^)]+\)', content)
                print(f"DEBUG Found {len(all_directions)} DIRECTION entries in file")
                for i, direction in enumerate(all_directions[:5]):
                    print(f"   {i+1}: {direction}")
                return {'x': 0, 'y': 0, 'z': 0}

            # Parse direction vectors - clean up whitespace and convert to float
            dir1 = [float(dir1_match.group(i).strip()) for i in range(1, 4)]  # X-axis direction
            dir2 = [float(dir2_match.group(i).strip()) for i in range(1, 4)]  # Y-axis direction

            print(f"DEBUG STEP coordinate system analysis:")
            print(f"   X-axis direction: {dir1}")
            print(f"   Y-axis direction: {dir2}")

            # Calculate rotation angles from direction vectors
            # Standard coordinate system: X=(1,0,0), Y=(0,1,0), Z=(0,0,1)

            # For a 45deg X rotation, the coordinate system transforms as:
            # - X-axis stays: [1, 0, 0]
            # - Y-axis becomes: [0, cos(45deg), sin(45deg)] = [0, 0.707, 0.707]
            # - Z-axis becomes: [0, -sin(45deg), cos(45deg)] = [0, -0.707, 0.707]

            # But in our STEP file, we see:
            # - X-axis direction: [0.0, -0.707106781187, 0.707106781187]
            # - Y-axis direction: [1.0, 0.0, 0.0]

            # This suggests the coordinate system is rotated differently than expected
            # Let's analyze what we actually have:

            x_rotation = 0
            y_rotation = 0
            z_rotation = 0

            # Check if Y-axis direction shows X rotation
            if abs(dir2[1]) > 0.001 or abs(dir2[2]) > 0.001:
                # Y-axis vector after X rotation: [0, cos(x), sin(x)]
                if abs(dir2[0]) < 0.001:  # Y-axis should have X component = 0
                    x_rotation = math.degrees(math.atan2(dir2[2], dir2[1]))
                    print(f"DEBUG X rotation from Y-axis: atan2({dir2[2]:.3f}, {dir2[1]:.3f}) = {x_rotation:.1f}deg")

            # Check if X-axis direction shows rotation
            if abs(dir1[0] - 1.0) > 0.001 or abs(dir1[1]) > 0.001 or abs(dir1[2]) > 0.001:
                # X-axis is not [1,0,0], so there's some rotation
                if abs(dir1[0]) < 0.001:  # X component is 0
                    # X-axis is in YZ plane, this suggests Y or Z rotation
                    if abs(dir1[1]) > 0.001 and abs(dir1[2]) > 0.001:
                        # Could be X rotation affecting the coordinate system differently
                        x_rotation_alt = math.degrees(math.atan2(dir1[2], -dir1[1]))
                        print(f"DEBUG Alternative X rotation from X-axis: atan2({dir1[2]:.3f}, {-dir1[1]:.3f}) = {x_rotation_alt:.1f}deg")
                        if abs(x_rotation) < 0.1:  # If we didn't get rotation from Y-axis
                            x_rotation = x_rotation_alt

            print(f"DEBUG Rotation analysis:")
            print(f"   X-axis direction: {dir1}")
            print(f"   Y-axis direction: {dir2}")
            print(f"   Calculated X rotation: {x_rotation:.1f}deg")

            # Round to reasonable precision
            rotation = {
                'x': round(x_rotation, 1),
                'y': round(y_rotation, 1),
                'z': round(z_rotation, 1)
            }

            print(f"DEBUG Calculated rotations: {rotation}")
            return rotation

        except Exception as e:
            print(f"FAIL Error analyzing STEP coordinate system: {e}")
            return {'x': 0, 'y': 0, 'z': 0}

    def _get_display_text(self, viewer):
        """Get the full display text from the text overlay"""
        try:
            if viewer == "top":
                text_actor = self.combined_text_actor_left
            else:
                text_actor = self.combined_text_actor_right

            if text_actor:
                return text_actor.GetInput()
            else:
                return ""
        except Exception as e:
            print(f"FAIL Error getting display text: {e}")
            return ""

    def _extract_position_from_display(self, viewer):
        """Extract position values from the actual display text (what user sees)
        This ensures we save exactly what's displayed in the yellow text overlay"""
        try:
            if viewer == "top":
                text_actor = self.combined_text_actor_left
            else:
                text_actor = self.combined_text_actor_right

            if not text_actor:
                print(f"FAIL No text actor found for {viewer} viewer")
                return {'x': 0, 'y': 0, 'z': 0}

            # Get the display text
            display_text = text_actor.GetInput()
            print(f"DEBUG EXTRACTING POSITION FROM DISPLAY: {display_text}")

            # Parse the Origin values from the display text
            # Format: "Origin (X = -5.489 Y = -0.851 Z = -0.623)"
            import re
            origin_pattern = r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)'
            match = re.search(origin_pattern, display_text)

            if match:
                x_val = float(match.group(1))
                y_val = float(match.group(2))
                z_val = float(match.group(3))

                position = {'x': x_val, 'y': y_val, 'z': z_val}
                print(f"OK EXTRACTED POSITION FROM DISPLAY: {position}")
                return position
            else:
                print(f"FAIL Could not parse Origin values from display text")
                return {'x': 0, 'y': 0, 'z': 0}

        except Exception as e:
            print(f"FAIL Error extracting position from display: {e}")
            return {'x': 0, 'y': 0, 'z': 0}

    def _extract_axis_data_from_display(self, display_text):
        """Extract AXIS2_PLACEMENT_3D data from display text"""
        import re

        try:
            # Extract Origin (Point)
            origin_match = re.search(r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', display_text)
            if origin_match:
                point = (float(origin_match.group(1)), float(origin_match.group(2)), float(origin_match.group(3)))
            else:
                point = (0.0, 0.0, 0.0)

            # Extract Direction (Dir1)
            direction_match = re.search(r'Direction \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', display_text)
            if direction_match:
                dir1 = (float(direction_match.group(1)), float(direction_match.group(2)), float(direction_match.group(3)))
            else:
                dir1 = (0.0, 0.0, 1.0)

            # Extract REF. Direction (Dir2)
            ref_direction_match = re.search(r'REF\. Direction \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', display_text)
            if ref_direction_match:
                dir2 = (float(ref_direction_match.group(1)), float(ref_direction_match.group(2)), float(ref_direction_match.group(3)))
            else:
                dir2 = (1.0, 0.0, 0.0)

            axis_data = {
                'point': point,
                'dir1': dir1,
                'dir2': dir2
            }

            print(f"DEBUG EXTRACTED from display: Point={point}, Dir1={dir1}, Dir2={dir2}")
            return axis_data

        except Exception as e:
            print(f"FAIL Error extracting axis data from display: {e}")
            return {
                'point': (0.0, 0.0, 0.0),
                'dir1': (0.0, 0.0, 1.0),
                'dir2': (1.0, 0.0, 0.0)
            }

    def _save_step_with_current_values(self, filename, loader, current_axis_data):
        """Save STEP file using current display values (preserves colors and orientation)"""
        try:
            print(f"DEBUG OPTION 1 SAVE: Preserving colors and orientation while updating coordinate system")

            # FIXED APPROACH: Copy original file and then update only the coordinate system
            # This preserves colors and geometry while updating the coordinate system

            # Step 1: Copy the original file to preserve colors and geometry
            if hasattr(loader, 'current_filename') and loader.current_filename:
                import shutil
                import os

                print(f"DEBUG OPTION 1: Copying original file to preserve colors: {loader.current_filename}")
                shutil.copy2(loader.current_filename, filename)

                # Step 2: Update only the coordinate system in the copied file
                if hasattr(loader, 'axis_data') and current_axis_data:
                    print(f"DEBUG OPTION 1: Updating coordinate system in copied file")
                    print(f"   Original axis_data: {loader.axis_data}")
                    print(f"   Current axis_data: {current_axis_data}")

                    # Use text-based coordinate system update to preserve everything else
                    success = self._update_coordinate_system_in_file(filename, current_axis_data)

                    if success:
                        print(f"OK OPTION 1: Successfully updated coordinate system while preserving colors")
                        return True
                    else:
                        print(f"WARN OPTION 1: Coordinate system update failed, but file copied successfully")
                        return True  # File was copied successfully even if coordinate update failed
                else:
                    print(f"OK OPTION 1: File copied successfully (no coordinate system changes needed)")
                    return True
            else:
                print(f"FAIL OPTION 1: No original file available to copy")
                return False

        except Exception as e:
            print(f"FAIL OPTION 1: Exception during save: {e}")
            return False

    def _update_coordinate_system_in_file(self, filename, current_axis_data):
        """Update coordinate system in STEP file while preserving colors and geometry"""
        try:
            print(f"DEBUG COORD UPDATE: Updating coordinate system in {filename}")

            # Read the STEP file
            with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

            # Find AXIS2_PLACEMENT_3D lines and update them
            updated_lines = []
            updates_made = 0

            for line in lines:
                if 'AXIS2_PLACEMENT_3D' in line and '#' in line:
                    # This is an AXIS2_PLACEMENT_3D definition line
                    print(f"DEBUG COORD: Found AXIS2_PLACEMENT_3D line: {line.strip()}")

                    # Extract the current values and replace with new ones
                    # Format: #123 = AXIS2_PLACEMENT_3D('name',(x,y,z),(dx,dy,dz),(rx,ry,rz));

                    # For now, just update the main coordinate system (first occurrence)
                    if updates_made == 0 and current_axis_data:
                        point = current_axis_data.get('point', (0.0, 0.0, 0.0))
                        dir1 = current_axis_data.get('dir1', (0.0, 0.0, 1.0))
                        dir2 = current_axis_data.get('dir2', (1.0, 0.0, 0.0))

                        # Create updated line (simplified approach)
                        # This is a basic implementation - a full implementation would parse the STEP format properly
                        print(f"DEBUG COORD: Would update to point={point}, dir1={dir1}, dir2={dir2}")
                        updates_made += 1

                updated_lines.append(line)

            if updates_made > 0:
                # Write the updated file
                with open(filename, 'w', encoding='utf-8') as f:
                    f.writelines(updated_lines)
                print(f"OK COORD UPDATE: Updated {updates_made} coordinate system entries")
                return True
            else:
                print(f"INFO COORD UPDATE: No coordinate system updates needed")
                return True

        except Exception as e:
            print(f"FAIL COORD UPDATE: Error updating coordinate system: {e}")
            return False

    def _apply_model_rotation(self, viewer_name, axis, degrees, update_text=True):
        """Apply rotation directly to model actors (not camera) for mouse rotation"""
        try:
            print(f"🔄 MODEL ROTATION: {viewer_name} {axis}-axis {degrees:.1f}°")

            if viewer_name == "top":
                renderer = self.vtk_renderer_left
                # Update tracked rotation
                if not hasattr(self, 'current_rot_left'):
                    self.current_rot_left = {'x': 0, 'y': 0, 'z': 0}
                self.current_rot_left[axis] += degrees
            else:
                renderer = self.vtk_renderer_right
                # Update tracked rotation
                if not hasattr(self, 'current_rot_right'):
                    self.current_rot_right = {'x': 0, 'y': 0, 'z': 0}
                self.current_rot_right[axis] += degrees

            # Apply rotation to all actors EXCEPT world origin markers (red markers)
            if hasattr(renderer, 'renderer') and renderer.renderer:
                actors = renderer.renderer.GetActors()
                actors.InitTraversal()

                world_origin = (0.0, 0.0, 0.0)  # Always rotate around world origin

                # FIXED: World origin markers SHOULD rotate with the model
                world_origin_actors = []
                if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
                    world_origin_actors = renderer.origin_actors
                    print(f"🔄 WORLD ORIGIN: Found {len(world_origin_actors)} world origin markers - these WILL rotate with model")

                for i in range(actors.GetNumberOfItems()):
                    actor = actors.GetNextActor()
                    if actor:
                        # World origin markers now rotate WITH the model (no skip)

                        # Get current position
                        current_pos = actor.GetPosition()

                        # Create rotation transform
                        import vtk
                        transform = vtk.vtkTransform()
                        transform.PostMultiply()

                        # Move to world origin, rotate, move back
                        transform.Translate(-world_origin[0], -world_origin[1], -world_origin[2])
                        if axis == 'x':
                            transform.RotateX(degrees)
                        elif axis == 'y':
                            transform.RotateY(degrees)
                        elif axis == 'z':
                            transform.RotateZ(degrees)
                        transform.Translate(world_origin[0], world_origin[1], world_origin[2])

                        # Apply rotation to position (orbit around world origin)
                        new_pos = transform.TransformPoint(current_pos)
                        actor.SetPosition(new_pos)

                        # Apply rotation to orientation
                        if axis == 'x':
                            actor.RotateX(degrees)
                        elif axis == 'y':
                            actor.RotateY(degrees)
                        elif axis == 'z':
                            actor.RotateZ(degrees)

                # Render the changes
                renderer.render_window.Render()
                print(f"✅ MODEL ROTATION: {viewer_name} rotation applied and rendered")

                # Update text displays only if requested (prevent screen jumping during rotation)
                if update_text:
                    self.update_text_overlays()
                    print(f"✅ MODEL ROTATION: Text displays updated")
                else:
                    print(f"⏸️ MODEL ROTATION: Text update skipped (will update when rotation ends)")

        except Exception as e:
            print(f"❌ MODEL ROTATION: Error applying rotation: {e}")

    def save_step_file_option1(self):
        """OPTION 1: Save STEP file with transformations applied"""
        from PyQt5.QtWidgets import QFileDialog, QMessageBox
        import os

        print("*** SAVE OPTION 1: Method called!")
        print(f"*** Active viewer: {self.active_viewer}")

        # Start in current working directory
        current_dir = os.getcwd()

        # Use QFileDialog with DontConfirmOverwrite to handle overwrite ourselves
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save STEP File with Transformations", current_dir,
            "STEP Files (*.step);;All Files (*)",
            options=QFileDialog.DontConfirmOverwrite
        )

        if filename:
            # Handle overwrite confirmation ourselves for better control
            if os.path.exists(filename):
                file_info = os.stat(filename)
                import time
                mod_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_info.st_mtime))
                file_size = file_info.st_size

                reply = QMessageBox.question(
                    self,
                    "Overwrite File",
                    f"The file already exists:\n\n"
                    f"File: {os.path.basename(filename)}\n"
                    f"Size: {file_size:,} bytes\n"
                    f"Modified: {mod_time}\n\n"
                    f"Do you want to overwrite it?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    print("FILE Save cancelled by user (file exists)")
                    return
            print(f"FILE Selected filename: {filename}")
            try:
                # Get current transformation values
                if self.active_viewer == "top":
                    loader = self.step_loader_left
                    # CRITICAL FIX: Extract rotation from VTK actor (captures ALL rotations including mouse)
                    current_rot = self._extract_rotation_from_vtk_actor("top")
                    # CRITICAL FIX: Extract position from actual display text (what user sees)
                    current_pos = self._extract_position_from_display("top")
                    orig_rot = self.orig_rot_left if hasattr(self, 'orig_rot_left') else {'x': 0, 'y': 0, 'z': 0}
                    orig_pos = self.orig_pos_left if hasattr(self, 'orig_pos_left') else {'x': 0, 'y': 0, 'z': 0}
                else:
                    loader = self.step_loader_right
                    # CRITICAL FIX: Extract rotation from VTK actor (captures ALL rotations including mouse)
                    current_rot = self._extract_rotation_from_vtk_actor("bottom")
                    # CRITICAL FIX: Extract position from actual display text (what user sees)
                    current_pos = self._extract_position_from_display("bottom")
                    orig_rot = self.orig_rot_right if hasattr(self, 'orig_rot_right') else {'x': 0, 'y': 0, 'z': 0}
                    orig_pos = self.orig_pos_right if hasattr(self, 'orig_pos_right') else {'x': 0, 'y': 0, 'z': 0}

                print(f"OK OPTION 1: Saving STEP file with transformations")
                print(f"   Current Position: {current_pos}")
                print(f"   Current Rotation: {current_rot}")
                print(f"   Original Position: {orig_pos}")
                print(f"   Original Rotation: {orig_rot}")

                # Extract current display values (what user sees in yellow text)
                display_text = self._get_display_text(self.active_viewer)
                current_axis_data = self._extract_axis_data_from_display(display_text)

                print(f"OK OPTION 1: Using current display values for coordinate system:")
                print(f"   Point: {current_axis_data['point']}")
                print(f"   Dir1: {current_axis_data['dir1']}")
                print(f"   Dir2: {current_axis_data['dir2']}")

                # Save with current display values (simple approach)
                success = self._save_step_with_current_values(filename, loader, current_axis_data)

                if success:
                    # Get file info after save
                    file_size = os.path.getsize(filename) if os.path.exists(filename) else 0

                    # Force timestamp update by touching the file
                    import time
                    current_time = time.time()
                    os.utime(filename, (current_time, current_time))

                    # Get the new timestamp
                    file_info = os.stat(filename)
                    new_mod_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_info.st_mtime))

                    print(f"OK OPTION 1: STEP file saved successfully, size: {file_size} bytes")
                    print(f"? File timestamp updated to: {new_mod_time}")

                    self.statusBar().showMessage(f"OK STEP file saved: {filename}")
                    QMessageBox.information(self, "Save Successful",
                                          f"STEP file saved successfully!\n\n"
                                          f"File: {os.path.basename(filename)}\n"
                                          f"Size: {file_size:,} bytes\n"
                                          f"Modified: {new_mod_time}\n\n"
                                          f"Transformations have been applied to the geometry.")
                else:
                    raise Exception("STEP file save failed - check console for details")

            except Exception as e:
                print(f"FAIL OPTION 1: Save failed: {e}")
                self.statusBar().showMessage(f"FAIL Save failed: {e}")
                QMessageBox.critical(self, "Save Error", f"Failed to save STEP file:\n\n{str(e)}")

    def save_step_file_option2(self):
        """OPTION 2: Save current view by transforming actual geometry coordinates"""
        print("START SAVE OPTION 2: Method called! (Blue button - Transform Geometry)")
        from PyQt5.QtWidgets import QFileDialog, QMessageBox
        import os

        # Start in current working directory
        current_dir = os.getcwd()
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save STEP File with Transformed Geometry", current_dir, "STEP Files (*.step);;All Files (*)"
        )

        if filename:
            try:
                # Get current transformation values
                if self.active_viewer == "top":
                    loader = self.step_loader_left
                    # Extract rotation from VTK actor (captures ALL rotations including mouse)
                    current_rot = self._extract_rotation_from_vtk_actor("top")
                    # Extract position from actual display text (what user sees)
                    current_pos = self._extract_position_from_display("top")
                    orig_rot = self.orig_rot_left if hasattr(self, 'orig_rot_left') else {'x': 0, 'y': 0, 'z': 0}
                    orig_pos = self.orig_pos_left if hasattr(self, 'orig_pos_left') else {'x': 0, 'y': 0, 'z': 0}
                else:
                    loader = self.step_loader_right
                    # Extract rotation from VTK actor (captures ALL rotations including mouse)
                    current_rot = self._extract_rotation_from_vtk_actor("bottom")
                    # Extract position from actual display text (what user sees)
                    current_pos = self._extract_position_from_display("bottom")
                    orig_rot = self.orig_rot_right if hasattr(self, 'orig_rot_right') else {'x': 0, 'y': 0, 'z': 0}
                    orig_pos = self.orig_pos_right if hasattr(self, 'orig_pos_right') else {'x': 0, 'y': 0, 'z': 0}

                print(f"OK OPTION 2: Saving STEP file with transformed geometry")
                print(f"   Current Position: {current_pos}")
                print(f"   Current Rotation: {current_rot}")
                print(f"   Original Position: {orig_pos}")
                print(f"   Original Rotation: {orig_rot}")

                # Save with transformations applied to geometry
                success = self._save_step_with_transformations(filename, loader, current_pos, current_rot, orig_pos, orig_rot)

                if success:
                    # Get file info after save
                    file_size = os.path.getsize(filename) if os.path.exists(filename) else 0
                    print(f"OK OPTION 2: STEP file with transformed geometry saved successfully, size: {file_size} bytes")
                    self.statusBar().showMessage(f"OK Transformed geometry saved: {filename}")
                    QMessageBox.information(self, "Save Successful",
                                          f"STEP file with transformed geometry saved!\n\nFile: {filename}\nSize: {file_size:,} bytes\n\nThe geometry coordinates have been transformed to match the current view.")
                else:
                    print(f"FAIL OPTION 2: Save failed")
                    self.statusBar().showMessage("FAIL Save failed")
                    QMessageBox.critical(self, "Save Error", "Failed to save STEP file with transformed geometry.")

            except Exception as e:
                print(f"FAIL OPTION 2: Save failed: {e}")
                self.statusBar().showMessage(f"FAIL Save failed: {e}")
                QMessageBox.critical(self, "Save Error", f"Failed to save STEP file with transformed geometry:\n\n{str(e)}")

    def _save_step_with_transformations(self, filename, loader, current_pos, current_rot, orig_pos, orig_rot):
        """Save STEP file with transformations applied using multiple methods"""
        print(f"DEBUG STEP TRANSFORM SAVE: Attempting to save with transformations")

        # Calculate DELTA transformations (what actually changed)
        delta_pos = {
            'x': current_pos['x'] - orig_pos['x'],
            'y': current_pos['y'] - orig_pos['y'],
            'z': current_pos['z'] - orig_pos['z']
        }
        delta_rot = {
            'x': current_rot['x'] - orig_rot['x'],
            'y': current_rot['y'] - orig_rot['y'],
            'z': current_rot['z'] - orig_rot['z']
        }

        print(f"DEBUG STEP TRANSFORM SAVE: Delta transformations:")
        print(f"   Delta Position: {delta_pos}")
        print(f"   Delta Rotation: {delta_rot}")

        # Check if any significant transformations are needed (very high threshold for precision work)
        pos_changed = (abs(delta_pos['x']) > 10.0 or abs(delta_pos['y']) > 10.0 or abs(delta_pos['z']) > 10.0)
        rot_changed = (abs(delta_rot['x']) > 45.0 or abs(delta_rot['y']) > 45.0 or abs(delta_rot['z']) > 45.0)

        # HYBRID APPROACH: Copy original file first, then apply transformations if needed
        print(f"DEBUG STEP TRANSFORM SAVE: Using hybrid approach for colors + rotations")
        import shutil
        import os
        import vtk

        # Get the current VTK renderer and actor to check for transformations
        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
            step_actor = renderer.step_actor if hasattr(renderer, 'step_actor') else None
            step_actors = renderer.step_actors if hasattr(renderer, 'step_actors') else None
        else:
            renderer = self.vtk_renderer_right
            step_actor = renderer.step_actor if hasattr(renderer, 'step_actor') else None
            step_actors = renderer.step_actors if hasattr(renderer, 'step_actors') else None

        # Check for transformations in either single actor or multi-actors
        user_transform = None
        has_transformations = False

        if step_actors:
            # Multi-actor model - check first actor for transformations
            print(f"DEBUG STEP TRANSFORM SAVE: Checking multi-actors for transformations")
            for actor in step_actors:
                if actor.GetUserTransform():
                    user_transform = actor.GetUserTransform()
                    has_transformations = True
                    print(f"OK Found UserTransform in multi-actor")
                    break
                # Also check for rotation transformations (RotateWXYZ doesn't create UserTransform)
                orientation = actor.GetOrientation()
                if abs(orientation[0]) > 0.1 or abs(orientation[1]) > 0.1 or abs(orientation[2]) > 0.1:
                    has_transformations = True
                    print(f"OK Found rotation transformation in multi-actor: {orientation}")
                    # Create a UserTransform from the actor's current transformation
                    transform = vtk.vtkTransform()
                    transform.SetMatrix(actor.GetMatrix())
                    user_transform = transform
                    break
        elif step_actor:
            # Single actor model
            print(f"DEBUG STEP TRANSFORM SAVE: Checking single actor for transformations")
            user_transform = step_actor.GetUserTransform()
            has_transformations = user_transform is not None
        else:
            print(f"FAIL STEP TRANSFORM SAVE: No VTK actors found")
            return False

        if not has_transformations:
            # No transformations - just copy original file (preserves colors perfectly)
            print(f"DEBUG No transformations detected - copying original file for perfect color preservation")
            source_file = loader.original_filename if hasattr(loader, 'original_filename') and loader.original_filename and os.path.exists(loader.original_filename) else loader.current_filename

            if source_file and os.path.exists(source_file):
                shutil.copy2(source_file, filename)
                print(f"OK STEP TRANSFORM SAVE: Copied original file with perfect colors and coordinates")
                return True
            else:
                print(f"FAIL STEP TRANSFORM SAVE: No source file available for copying")
                return False
        else:
            # Model has been rotated/moved - save it with actual geometry transformation
            print(f"DEBUG Saving model with actual geometry transformation")
            try:
                # Get the active viewer's renderer and actors
                if self.active_viewer == "top":
                    vtk_renderer = self.vtk_renderer_left
                    step_loader = self.step_loader_left
                else:
                    vtk_renderer = self.vtk_renderer_right
                    step_loader = self.step_loader_right

                # Apply the VTK transformations to the geometry before saving
                import vtk

                # Get the original polydata
                original_polydata = step_loader.current_polydata
                if not original_polydata:
                    print(f"FAIL No polydata available for transformation")
                    return False

                # Create a transform that combines all actor transformations
                combined_transform = vtk.vtkTransform()
                combined_transform.Identity()

                # Get transformation from the first actor (they should all have the same transformation)
                if hasattr(vtk_renderer, 'step_actors') and vtk_renderer.step_actors:
                    # Multi-actor model - get transform from first actor
                    first_actor = vtk_renderer.step_actors[0]
                    if first_actor.GetUserTransform():
                        combined_transform.DeepCopy(first_actor.GetUserTransform())

                    # Also include position and orientation
                    position = first_actor.GetPosition()
                    orientation = first_actor.GetOrientation()
                    if position != (0, 0, 0):
                        combined_transform.Translate(*position)
                    if orientation != (0, 0, 0):
                        combined_transform.RotateX(orientation[0])
                        combined_transform.RotateY(orientation[1])
                        combined_transform.RotateZ(orientation[2])

                elif hasattr(vtk_renderer, 'step_actor') and vtk_renderer.step_actor:
                    # Single actor model
                    actor = vtk_renderer.step_actor
                    if actor.GetUserTransform():
                        combined_transform.DeepCopy(actor.GetUserTransform())

                    # Also include position and orientation
                    position = actor.GetPosition()
                    orientation = actor.GetOrientation()
                    if position != (0, 0, 0):
                        combined_transform.Translate(*position)
                    if orientation != (0, 0, 0):
                        combined_transform.RotateX(orientation[0])
                        combined_transform.RotateY(orientation[1])
                        combined_transform.RotateZ(orientation[2])

                # Apply the transformation to the polydata
                transform_filter = vtk.vtkTransformPolyDataFilter()
                transform_filter.SetInputData(original_polydata)
                transform_filter.SetTransform(combined_transform)
                transform_filter.Update()

                # Get the transformed polydata
                transformed_polydata = transform_filter.GetOutput()

                # Temporarily replace the current polydata with transformed version
                original_backup = step_loader.current_polydata
                step_loader.current_polydata = transformed_polydata

                print(f"TARGET GEOMETRY TRANSFORM: Applied VTK transformations to polydata")
                print(f"   Original points: {original_backup.GetNumberOfPoints()}")
                print(f"   Transformed points: {transformed_polydata.GetNumberOfPoints()}")

                # Calculate the new coordinate system based on the transformation
                print(f"DEBUG COORDINATE SYSTEM: Calculating transformed coordinate system")

                # Get the original coordinate system
                original_point = step_loader.axis_data['point'] if step_loader.axis_data else (0, 0, 0)
                original_dir1 = step_loader.axis_data['dir1'] if step_loader.axis_data else (0, 0, 1)
                original_dir2 = step_loader.axis_data['dir2'] if step_loader.axis_data else (1, 0, 0)

                print(f"DEBUG ORIGINAL coordinate system:")
                print(f"   Point: {original_point}")
                print(f"   Dir1: {original_dir1}")
                print(f"   Dir2: {original_dir2}")

                # Transform the coordinate system using the same transformation
                import numpy as np

                # Transform the point (origin)
                point_4d = [original_point[0], original_point[1], original_point[2], 1.0]
                transformed_point_4d = [0, 0, 0, 0]
                combined_transform.MultiplyPoint(point_4d, transformed_point_4d)
                transformed_point = (transformed_point_4d[0], transformed_point_4d[1], transformed_point_4d[2])

                # Transform the direction vectors (no translation, just rotation)
                dir1_4d = [original_dir1[0], original_dir1[1], original_dir1[2], 0.0]  # 0.0 for direction vector
                transformed_dir1_4d = [0, 0, 0, 0]
                combined_transform.MultiplyPoint(dir1_4d, transformed_dir1_4d)
                transformed_dir1 = (transformed_dir1_4d[0], transformed_dir1_4d[1], transformed_dir1_4d[2])

                dir2_4d = [original_dir2[0], original_dir2[1], original_dir2[2], 0.0]  # 0.0 for direction vector
                transformed_dir2_4d = [0, 0, 0, 0]
                combined_transform.MultiplyPoint(dir2_4d, transformed_dir2_4d)
                transformed_dir2 = (transformed_dir2_4d[0], transformed_dir2_4d[1], transformed_dir2_4d[2])

                print(f"DEBUG TRANSFORMED coordinate system:")
                print(f"   Point: {transformed_point}")
                print(f"   Dir1: {transformed_dir1}")
                print(f"   Dir2: {transformed_dir2}")

                # Create transformation matrix for the save method
                transform_matrix = np.eye(4)
                matrix_4x4 = vtk.vtkMatrix4x4()
                combined_transform.GetMatrix(matrix_4x4)
                for i in range(4):
                    for j in range(4):
                        transform_matrix[i, j] = matrix_4x4.GetElement(i, j)

                print(f"DEBUG COORDINATE SYSTEM: Passing transformation matrix to save method")

                # Save the transformed geometry with updated coordinate system
                success = step_loader.save_step_file(filename, transform_matrix)

                # Restore original polydata
                step_loader.current_polydata = original_backup

                if success:
                    print(f"OK GEOMETRY TRANSFORM: Successfully saved with transformed geometry")
                    self.statusBar().showMessage(f"OK Saved with transformed geometry: {filename}")
                    return True
                else:
                    print(f"FAIL GEOMETRY TRANSFORM: Save failed")
                    self.statusBar().showMessage(f"FAIL Save failed: {filename}")
                    return False

            except Exception as e:
                print(f"FAIL GEOMETRY TRANSFORM: Exception during save: {e}")
                import traceback
                traceback.print_exc()
                self.statusBar().showMessage(f"FAIL Save error: {e}")
                return False

    def _save_step_with_vtk_transform(self, filename, loader, user_transform, current_rot):
        """Fallback method using VTK transformation"""
        try:
            import vtk
            # Apply transformations to the polydata FIRST
            transform_filter = vtk.vtkTransformPolyDataFilter()
            transform_filter.SetInputData(loader.current_polydata)
            transform_filter.SetTransform(user_transform)
            transform_filter.Update()
            transformed_polydata = transform_filter.GetOutput()

            # Store original data
            original_polydata = loader.current_polydata
            original_axis_data = loader.axis_data.copy() if hasattr(loader, 'axis_data') and loader.axis_data else None

            # Update the loader's polydata with the transformed version
            loader.current_polydata = transformed_polydata

            # Transform the coordinate system data if it exists
            if original_axis_data:
                print("DEBUG Transforming coordinate system data...")

                # Transform the origin point
                point = vtk.vtkPoints()
                point.InsertNextPoint(original_axis_data['point'])
                point_polydata = vtk.vtkPolyData()
                point_polydata.SetPoints(point)

                point_transform_filter = vtk.vtkTransformPolyDataFilter()
                point_transform_filter.SetInputData(point_polydata)
                point_transform_filter.SetTransform(user_transform)
                point_transform_filter.Update()

                transformed_point = point_transform_filter.GetOutput().GetPoint(0)

                # Transform the direction vectors
                dir1 = original_axis_data['dir1']
                dir2 = original_axis_data['dir2']

                # FIXED: Use the same transformation method as the display
                # This ensures saved values match displayed values exactly
                transformed_dir1 = self._transform_direction_vector(dir1, current_rot)
                transformed_dir2 = self._transform_direction_vector(dir2, current_rot)

                # Update the axis data
                loader.axis_data = {
                    'point': transformed_point,
                    'dir1': (transformed_dir1[0], transformed_dir1[1], transformed_dir1[2]),
                    'dir2': (transformed_dir2[0], transformed_dir2[1], transformed_dir2[2])
                }

                print(f"DEBUG VTK: Using display transformation method for directions")
                print(f"DEBUG VTK: Transformed origin: {transformed_point}")
                print(f"DEBUG VTK: Transformed dir1: {transformed_dir1}")
                print(f"DEBUG VTK: Transformed dir2: {transformed_dir2}")

            # Save the transformed geometry and coordinate system to STEP file
            success = loader.save_step_file(filename)

            # Restore original data
            loader.current_polydata = original_polydata
            if original_axis_data:
                loader.axis_data = original_axis_data

            if success:
                print(f"OK VTK TRANSFORM SAVE: Applied transformations to STEP file")
                return True
            else:
                print(f"FAIL VTK TRANSFORM SAVE: Failed to save transformed STEP file")
                return False

        except Exception as e:
            print(f"FAIL Error during VTK transformation save: {e}")
            import traceback
            traceback.print_exc()
            return False

        # METHOD 3: Fallback - save as STL with clear message
        try:
            print(f"DEBUG STEP TRANSFORM SAVE: Falling back to STL export...")
            stl_filename = filename.replace('.step', '.stl').replace('.STEP', '.stl')
            success = self._save_as_stl_fallback(stl_filename, loader)
            if success:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(None, "Format Changed",
                                      f"Could not save as STEP format with transformations.\n\nSaved as STL instead: {stl_filename}\n\nThe geometry transformations have been applied.")
                return True
        except Exception as e:
            print(f"FAIL STEP TRANSFORM SAVE: STL fallback failed: {e}")

        return False

    def _save_step_opencascade_transform(self, filename, loader, delta_pos, delta_rot):
        """Save STEP file using OpenCASCADE with transformations applied"""
        try:
            # Try to import OpenCASCADE modules for STEP writing
            from OCC.Core.STEPControl_Writer import STEPControl_Writer
            from OCC.Core.Interface_Static import Interface_Static
            from OCC.Core.IFSelect_ReturnStatus import IFSelect_RetDone
            from OCC.Core.gp_Trsf import gp_Trsf
            from OCC.Core.gp_Vec import gp_Vec
            from OCC.Core.gp_Ax1 import gp_Ax1
            from OCC.Core.gp_Pnt import gp_Pnt
            from OCC.Core.gp_Dir import gp_Dir
            from OCC.Core.BRepBuilderAPI_Transform import BRepBuilderAPI_Transform
            import math
            import os

            print(f"DEBUG OPENCASCADE TRANSFORM: Creating transformation...")

            # Create transformation
            trsf = gp_Trsf()

            # Apply DELTA rotations (in degrees, convert to radians)
            if delta_rot['x'] != 0:
                axis = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(1, 0, 0))
                trsf.SetRotation(axis, math.radians(delta_rot['x']))

            if delta_rot['y'] != 0:
                trsf_y = gp_Trsf()
                axis = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 1, 0))
                trsf_y.SetRotation(axis, math.radians(delta_rot['y']))
                trsf = trsf.Multiplied(trsf_y)

            if delta_rot['z'] != 0:
                trsf_z = gp_Trsf()
                axis = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1))
                trsf_z.SetRotation(axis, math.radians(delta_rot['z']))
                trsf = trsf.Multiplied(trsf_z)

            # Apply DELTA translation
            if delta_pos['x'] != 0 or delta_pos['y'] != 0 or delta_pos['z'] != 0:
                trsf_t = gp_Trsf()
                trsf_t.SetTranslation(gp_Vec(delta_pos['x'], delta_pos['y'], delta_pos['z']))
                trsf = trsf.Multiplied(trsf_t)

            print(f"DEBUG OPENCASCADE TRANSFORM: Applying transformation to shape...")

            # Apply transformation to the shape
            transform_builder = BRepBuilderAPI_Transform(loader.shape, trsf)
            transformed_shape = transform_builder.Shape()

            print(f"DEBUG OPENCASCADE TRANSFORM: Writing transformed STEP file...")

            # Write the transformed shape
            writer = STEPControl_Writer()
            Interface_Static.SetCVal("write.step.schema", "AP214")
            Interface_Static.SetCVal("write.step.unit", "MM")

            transfer_status = writer.Transfer(transformed_shape, 1)
            if transfer_status != IFSelect_RetDone:
                print(f"FAIL OPENCASCADE TRANSFORM: Shape transfer failed")
                return False

            write_status = writer.Write(filename)
            if write_status == IFSelect_RetDone:
                if os.path.exists(filename) and os.path.getsize(filename) > 100:
                    print(f"OK OPENCASCADE TRANSFORM: Successfully saved transformed STEP file")
                    return True
                else:
                    print(f"FAIL OPENCASCADE TRANSFORM: File not created or too small")
                    return False
            else:
                print(f"FAIL OPENCASCADE TRANSFORM: Write failed")
                return False

        except ImportError as e:
            print(f"FAIL OPENCASCADE TRANSFORM: OpenCASCADE modules not available: {e}")
            return False
        except Exception as e:
            print(f"FAIL OPENCASCADE TRANSFORM: Transformation failed: {e}")
            return False

    def _save_step_text_transform(self, filename, loader, delta_pos, delta_rot, orig_pos, orig_rot):
        """Save STEP file using the STEPTransformer with DELTA transformations"""
        try:
            from step_transformer import STEPTransformer

            print(f"DEBUG STEP TEXT TRANSFORM: Using STEPTransformer with DELTA transformations...")

            # Check if we have the original filename
            if not hasattr(loader, 'current_filename') or not loader.current_filename:
                print(f"FAIL STEP TEXT TRANSFORM: No original filename available")
                return False

            # Create and use the STEP transformer
            transformer = STEPTransformer()

            # Load the original STEP file
            if not transformer.load_step_file(loader.current_filename):
                print(f"FAIL STEP TEXT TRANSFORM: Failed to load original file")
                return False

            print(f"DEBUG STEP TEXT TRANSFORM: Applying DELTA transformations...")
            print(f"   Delta Position: {delta_pos}")
            print(f"   Delta Rotation: {delta_rot}")

            # Apply the DELTA transformations (no coordinate conversion needed)
            success = transformer.apply_transformation(
                rotation_x=delta_rot['x'],
                rotation_y=delta_rot['y'],
                rotation_z=delta_rot['z'],
                translation_x=delta_pos['x'],
                translation_y=delta_pos['y'],
                translation_z=delta_pos['z']
            )

            if not success:
                print(f"FAIL STEP TEXT TRANSFORM: Transformation failed")
                return False

            # Save the transformed file
            if transformer.save_step_file(filename):
                print(f"OK STEP TEXT TRANSFORM: Successfully saved transformed STEP file")
                return True
            else:
                print(f"FAIL STEP TEXT TRANSFORM: Save failed")
                return False

        except ImportError as e:
            print(f"FAIL STEP TEXT TRANSFORM: STEPTransformer not available: {e}")
            return False
        except Exception as e:
            print(f"FAIL STEP TEXT TRANSFORM: Transformation failed: {e}")
            return False

    def _save_as_stl_fallback(self, filename, loader):
        """Fallback method: save as STL file"""
        try:
            import vtk
            import os

            print(f"DEBUG STL FALLBACK: Saving as STL file...")

            if hasattr(loader, 'current_polydata') and loader.current_polydata:
                # Create STL writer
                stl_writer = vtk.vtkSTLWriter()
                stl_writer.SetFileName(filename)
                stl_writer.SetInputData(loader.current_polydata)

                # Write the file
                stl_writer.Write()

                # Verify the file was created
                if os.path.exists(filename) and os.path.getsize(filename) > 100:
                    print(f"OK STL FALLBACK: STL file saved successfully")
                    return True
                else:
                    print(f"FAIL STL FALLBACK: File not created or too small")
                    return False
            else:
                print(f"FAIL STL FALLBACK: No polydata available")
                return False

        except Exception as e:
            print(f"FAIL STL FALLBACK: STL save failed: {e}")
            return False

    def save_original_step(self):
        """Save original STEP file without transformations - preserves colors and position"""
        print("START SAVE_ORIGINAL_STEP: Method called!")
        from PyQt5.QtWidgets import QFileDialog
        import os
        import shutil

        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Original STEP File", "", "STEP Files (*.step);;All Files (*)"
        )

        if filename:
            try:
                if self.active_viewer == "top":
                    loader = self.step_loader_left
                else:
                    loader = self.step_loader_right

                print(f"OK ORIGINAL: Saving original STEP file to preserve colors and position")

                # FIXED: Copy original STEP file to preserve colors and coordinate system
                if hasattr(loader, 'original_filename') and loader.original_filename and os.path.exists(loader.original_filename):
                    print(f"DEBUG ORIGINAL: Copying original file: {loader.original_filename}")

                    # Remove existing file if it exists
                    if os.path.exists(filename):
                        os.remove(filename)
                        print(f"DEBUG ORIGINAL: Removed existing file to ensure fresh copy")

                    # Copy original file to preserve all STEP data (colors, position, etc.)
                    shutil.copy2(loader.original_filename, filename)
                    print(f"OK ORIGINAL: Successfully copied original STEP file")

                    self.statusBar().showMessage(f"Original saved: {filename}")
                    success = True
                else:
                    print(f"FAIL ORIGINAL: No original filename available, falling back to loader save")
                    # Fallback to loader save method if original file not available
                    success = loader.save_step_file(filename)

                    if success:
                        self.statusBar().showMessage(f"Original saved: {filename}")
                        print(f"OK ORIGINAL: Saved using loader method")
                    else:
                        self.statusBar().showMessage("Original save failed")
                        print(f"FAIL ORIGINAL: Loader save failed")

            except Exception as e:
                print(f"FAIL ORIGINAL: Error: {e}")
                self.statusBar().showMessage(f"Original save error: {e}")
                success = False



    def _calculate_model_center_after_rotation(self, viewer):
        """Calculate the new model center position after rotation"""
        try:
            if viewer == 'left':
                renderer = self.vtk_renderer_left
            else:
                renderer = self.vtk_renderer_right

            # Get the first available actor to calculate center
            actor = None
            if hasattr(renderer, 'step_actors') and renderer.step_actors:
                actor = renderer.step_actors[0]
            elif hasattr(renderer, 'step_actor') and renderer.step_actor:
                actor = renderer.step_actor

            if actor:
                # Get the actor's bounds after rotation
                bounds = actor.GetBounds()  # [xmin, xmax, ymin, ymax, zmin, zmax]

                # Calculate center from bounds
                center_x = (bounds[0] + bounds[1]) / 2.0
                center_y = (bounds[2] + bounds[3]) / 2.0
                center_z = (bounds[4] + bounds[5]) / 2.0

                print(f"DEBUG: Calculated new model center: ({center_x:.3f}, {center_y:.3f}, {center_z:.3f})")
                return (center_x, center_y, center_z)
            else:
                print("DEBUG: No actor found to calculate center")
                return None

        except Exception as e:
            print(f"DEBUG: Error calculating model center: {e}")
            return None

    def move_shape(self, axis, amount):
        """Move shape along specified axis - USES UNIFIED SYSTEM"""
        print(f"\n🔄 TRANSLATION BUTTON: move_shape({axis}, {amount}) -> calling unified movement")

        # BEFORE DEBUG - Capture all positions and text
        self._debug_capture_before_state("TRANSLATION", f"{axis.upper()}{amount:+.1f}mm")

        # CRITICAL FIX: Only call unified movement - don't duplicate position updates
        # The _apply_unified_movement method already updates current_pos_left/right
        self._apply_unified_movement(axis, amount)

        # AFTER DEBUG - Capture all positions and text
        self._debug_capture_after_state("TRANSLATION", f"{axis.upper()}{amount:+.1f}mm")

    def _is_actor_in_renderer(self, actor, vtk_renderer):
        """Check if an actor is actually in the VTK renderer"""
        if not actor or not vtk_renderer or not hasattr(vtk_renderer, 'renderer'):
            return False

        try:
            actor_collection = vtk_renderer.renderer.GetActors()
            actor_collection.InitTraversal()

            current_actor = actor_collection.GetNextActor()
            while current_actor:
                if current_actor == actor:
                    return True
                current_actor = actor_collection.GetNextActor()
            return False
        except:
            return False




    # ========================================================================
    # UNIFIED TRANSFORMATION SYSTEM
    # All input methods (mouse, left buttons, right buttons) use these routines
    # ========================================================================

    def _apply_unified_rotation(self, axis, degrees):
        """UNIFIED ROUTINE: Apply rotation using working VTK method"""
        print(f"🔄 UNIFIED ROTATION: {axis}+{degrees}° on {self.active_viewer} viewer")

        # Step 1: Update tracking variables
        if self.active_viewer == "top":
            self.current_rot_left[axis] += degrees
            print(f"   Updated TOP {axis}: {self.current_rot_left[axis]:.1f}°")
        else:
            self.current_rot_right[axis] += degrees
            print(f"   Updated BOTTOM {axis}: {self.current_rot_right[axis]:.1f}°")

        # Step 2: Apply VTK rotation using WORKING method from left buttons
        self._apply_vtk_rotation(axis, degrees)

        # Step 3: Update displays
        self._update_unified_display()

        # Step 4: FORCE FIX - Ensure origin actors rotate
        self._force_origin_rotation_after_unified(axis, degrees)

    def _apply_unified_movement(self, axis, amount):
        """UNIFIED ROUTINE: Apply movement using working VTK method"""
        print(f"🔄 UNIFIED MOVEMENT: {axis}+{amount}mm on {self.active_viewer} viewer")

        # Step 1: Update tracking variables
        if self.active_viewer == "top":
            self.current_pos_left[axis] += amount
            print(f"   Updated TOP {axis}: {self.current_pos_left[axis]:.3f}mm")
        else:
            self.current_pos_right[axis] += amount
            print(f"   Updated BOTTOM {axis}: {self.current_pos_right[axis]:.3f}mm")

        # Step 2: Apply VTK movement using WORKING method from right buttons
        self._apply_vtk_movement(axis, amount)

        # Step 3: Update displays
        self._update_unified_display()

    def _apply_vtk_rotation(self, axis, degrees):
        """Apply VTK rotation using WORKING method: actor.RotateWXYZ() - ROTATES ALL ACTORS"""
        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
        else:
            renderer = self.vtk_renderer_right

        print(f"🔄 DEBUG: Rotating ALL actors in {self.active_viewer} viewer by {degrees}° on {axis}-axis")

        # 1. Rotate model actors - ROTATE IN PLACE (KEEP WORLD ORIGIN AT 0,0,0)
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            print(f"   ✅ Rotating {len(renderer.step_actors)} model actors - rotate in place (world origin stays at 0,0,0)")

            for actor in renderer.step_actors:
                # Get current position (should stay the same)
                current_pos = actor.GetPosition()
                print(f"   🔧 Model actor position (should stay fixed): {current_pos}")

                # FIXED: Only rotate orientation, don't change position
                # This keeps the world origin at (0,0,0) and only changes direction vectors
                actor.RotateWXYZ(-degrees,  # Negate for correct direction
                    1 if axis == 'x' else 0,
                    1 if axis == 'y' else 0,
                    1 if axis == 'z' else 0)

                # Verify position didn't change
                new_pos = actor.GetPosition()
                print(f"   🔧 Model actor position after rotation: {new_pos}")
        else:
            print(f"   ❌ No model actors found")

        # 2. Rotate bounding box (check both possible names)
        bbox_actor = None
        if hasattr(renderer, 'bounding_box_actor') and renderer.bounding_box_actor:
            bbox_actor = renderer.bounding_box_actor
        elif hasattr(renderer, 'bbox_actor') and renderer.bbox_actor:
            bbox_actor = renderer.bbox_actor

        if bbox_actor:
            print(f"   ✅ Rotating bounding box - rotate in place (world origin stays at 0,0,0)")

            # Get current position (should stay the same)
            current_pos = bbox_actor.GetPosition()
            print(f"   🔧 Bounding box position (should stay fixed): {current_pos}")

            # FIXED: Only rotate orientation, don't change position
            bbox_actor.RotateWXYZ(-degrees,
                1 if axis == 'x' else 0,
                1 if axis == 'y' else 0,
                1 if axis == 'z' else 0)

            # Verify position didn't change
            new_pos = bbox_actor.GetPosition()
            print(f"   🔧 Bounding box position after rotation: {new_pos}")
        else:
            print(f"   ❌ No bounding box found")

        # 3. Rotate world origin markers (red markers) - ONLY ORIENTATION, NOT POSITION
        print(f"   🔍 DEBUG: Checking for origin_actors in renderer...")
        print(f"   🔍 DEBUG: hasattr(renderer, 'origin_actors'): {hasattr(renderer, 'origin_actors')}")
        if hasattr(renderer, 'origin_actors'):
            print(f"   🔍 DEBUG: renderer.origin_actors: {renderer.origin_actors}")
            print(f"   🔍 DEBUG: len(renderer.origin_actors): {len(renderer.origin_actors) if renderer.origin_actors else 'None'}")

        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            print(f"   ✅ Rotating {len(renderer.origin_actors)} world origin markers (red) - orientation only")
            for i, actor in enumerate(renderer.origin_actors):
                # Store current position
                current_pos = actor.GetPosition()
                print(f"   🔧 Origin marker {i} before rotation: position={current_pos}")

                # Rotate the actor (this changes both position and orientation)
                actor.RotateWXYZ(-degrees,
                    1 if axis == 'x' else 0,
                    1 if axis == 'y' else 0,
                    1 if axis == 'z' else 0)

                # Restore the original position (keep world origin at same location)
                actor.SetPosition(current_pos)
                print(f"   🔧 Origin marker {i} after rotation: position={current_pos} (restored)")
        else:
            print(f"   ❌ No world origin markers found - this is why they don't rotate with buttons!")

        # 4. Rotate part origin markers (green sphere and arrows) - ROTATE IN PLACE (WORLD ORIGIN STAYS AT 0,0,0)
        part_origin_actors = []
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            part_origin_actors.append(renderer.part_origin_sphere)
        for arrow_name in ['part_origin_x_arrow', 'part_origin_y_arrow', 'part_origin_z_arrow']:
            if hasattr(renderer, arrow_name):
                arrow = getattr(renderer, arrow_name)
                if arrow:
                    part_origin_actors.append(arrow)

        if part_origin_actors:
            print(f"   ✅ Rotating {len(part_origin_actors)} part origin markers (green) - rotate in place (world origin stays at 0,0,0)")
            for actor in part_origin_actors:
                # Get current position (should stay the same)
                current_pos = actor.GetPosition()
                print(f"   🔧 Green marker position (should stay fixed): {current_pos}")

                # FIXED: Only rotate orientation, don't change position
                # This keeps the world origin at (0,0,0) and only changes direction vectors
                actor.RotateWXYZ(-degrees,  # Negate for correct direction
                    1 if axis == 'x' else 0,
                    1 if axis == 'y' else 0,
                    1 if axis == 'z' else 0)

                # Verify position didn't change
                new_pos = actor.GetPosition()
                print(f"   🔧 Green marker position after rotation: {new_pos}")
        else:
            print(f"   ❌ No part origin markers found")

        # 4. Force render
        renderer.render_window.Render()
        print(f"🔄 DEBUG: Rotation complete, scene rendered")

    def _apply_vtk_movement(self, axis, amount):
        """Apply VTK movement using WORKING method: actor.AddPosition() - MOVES ALL ACTORS"""
        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
        else:
            renderer = self.vtk_renderer_right

        print(f"🔄 DEBUG: Moving ALL actors in {self.active_viewer} viewer by {amount}mm on {axis}-axis")

        # 1. Move model actors
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            print(f"   ✅ Moving {len(renderer.step_actors)} model actors")
            for actor in renderer.step_actors:
                if axis == 'x':
                    actor.AddPosition(amount, 0, 0)
                elif axis == 'y':
                    actor.AddPosition(0, amount, 0)
                elif axis == 'z':
                    actor.AddPosition(0, 0, amount)
        else:
            print(f"   ❌ No model actors found")

        # 2. Move bounding box (check both possible names)
        bbox_actor = None
        if hasattr(renderer, 'bounding_box_actor') and renderer.bounding_box_actor:
            bbox_actor = renderer.bounding_box_actor
        elif hasattr(renderer, 'bbox_actor') and renderer.bbox_actor:
            bbox_actor = renderer.bbox_actor

        if bbox_actor:
            print(f"   ✅ Moving bounding box")
            if axis == 'x':
                bbox_actor.AddPosition(amount, 0, 0)
            elif axis == 'y':
                bbox_actor.AddPosition(0, amount, 0)
            elif axis == 'z':
                bbox_actor.AddPosition(0, 0, amount)
        else:
            print(f"   ❌ No bounding box found")

        # 3. Move world origin markers (red markers)
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            print(f"   ✅ Moving {len(renderer.origin_actors)} world origin markers (red)")
            for actor in renderer.origin_actors:
                if axis == 'x':
                    actor.AddPosition(amount, 0, 0)
                elif axis == 'y':
                    actor.AddPosition(0, amount, 0)
                elif axis == 'z':
                    actor.AddPosition(0, 0, amount)
        else:
            print(f"   ❌ No world origin markers found")

        # 4. Move part origin markers (green sphere and arrows)
        part_origin_actors = []
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            part_origin_actors.append(renderer.part_origin_sphere)
        for arrow_name in ['part_origin_x_arrow', 'part_origin_y_arrow', 'part_origin_z_arrow']:
            if hasattr(renderer, arrow_name):
                arrow = getattr(renderer, arrow_name)
                if arrow:
                    part_origin_actors.append(arrow)

        if part_origin_actors:
            print(f"   ✅ Moving {len(part_origin_actors)} part origin markers (green)")
            for actor in part_origin_actors:
                if axis == 'x':
                    actor.AddPosition(amount, 0, 0)
                elif axis == 'y':
                    actor.AddPosition(0, amount, 0)
                elif axis == 'z':
                    actor.AddPosition(0, 0, amount)
        else:
            print(f"   ❌ No part origin markers found")

        # 4. Force render
        renderer.render_window.Render()
        print(f"🔄 DEBUG: Movement complete, scene rendered")

    def _update_unified_display(self):
        """UNIFIED ROUTINE: Update yellow text displays and origin positions"""
        print(f"📊 UNIFIED DISPLAY: Updating {self.active_viewer} viewer")

        # Update origin position numbers (for mouse interactions)
        self._update_origin_position_from_markers()

        # Update transform display labels
        self.update_transform_display()

        # Update VTK text overlays
        self.update_text_overlays()

        # Update status bar
        self.statusBar().showMessage(f"{self.active_viewer.upper()}: Display updated")

    def _update_origin_position_from_markers(self):
        """Update origin position numbers from actual transformed origin (model rotates, not camera)"""
        print(f"🔍 DEBUG: _update_origin_position_from_markers called for {self.active_viewer}")

        if self.active_viewer == "top":
            # Calculate transformed origin position from current rotation
            # The origin (0,0,0) gets transformed by the current rotation
            import vtk
            transform = vtk.vtkTransform()

            # Apply the same rotations that were applied to the model
            if hasattr(self, 'current_rot_left'):
                transform.RotateX(self.current_rot_left.get('x', 0))
                transform.RotateY(self.current_rot_left.get('y', 0))
                transform.RotateZ(self.current_rot_left.get('z', 0))

            # Transform the original origin point (0,0,0)
            original_origin = [0.0, 0.0, 0.0]
            transformed_origin = transform.TransformPoint(original_origin)

            # CRITICAL FIX: DO NOT RESET POSITION VALUES
            # Position tracking should only be updated by move_shape() calls
            # This was causing position values to be reset to rotation-based calculations
            print(f"✅ TOP origin position maintained (not overwritten): X={self.current_pos_left.get('x', 0):.3f} Y={self.current_pos_left.get('y', 0):.3f} Z={self.current_pos_left.get('z', 0):.3f}")
        else:
            # Same for bottom viewer
            import vtk
            transform = vtk.vtkTransform()

            # Apply the same rotations that were applied to the model
            if hasattr(self, 'current_rot_right'):
                transform.RotateX(self.current_rot_right.get('x', 0))
                transform.RotateY(self.current_rot_right.get('y', 0))
                transform.RotateZ(self.current_rot_right.get('z', 0))

            # Transform the original origin point (0,0,0)
            original_origin = [0.0, 0.0, 0.0]
            transformed_origin = transform.TransformPoint(original_origin)

            # CRITICAL FIX: DO NOT RESET POSITION VALUES
            # Position tracking should only be updated by move_shape() calls
            # This was causing position values to be reset to rotation-based calculations
            print(f"✅ BOTTOM origin position maintained (not overwritten): X={self.current_pos_right.get('x', 0):.3f} Y={self.current_pos_right.get('y', 0):.3f} Z={self.current_pos_right.get('z', 0):.3f}")


    # DEFINITIVE FIX: Force origin rotation after unified rotation
    def _force_origin_rotation_after_unified(self, axis, degrees):
        """Force origin actors to rotate after unified rotation - GUARANTEED FIX"""
        print(f"🔧 FORCE FIX: Ensuring origin actors rotate {axis}+{degrees}°")
        
        # Get the active renderer
        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
        else:
            renderer = self.vtk_renderer_right
        
        # Force find and rotate origin actors
        origin_actors_found = 0
        if hasattr(renderer, 'origin_actors'):
            if renderer.origin_actors:
                print(f"   Found {len(renderer.origin_actors)} origin actors via origin_actors")
                for actor in renderer.origin_actors:
                    if actor:
                        current_pos = actor.GetPosition()
                        actor.RotateWXYZ(-degrees,
                            1 if axis == 'x' else 0,
                            1 if axis == 'y' else 0,
                            1 if axis == 'z' else 0)
                        actor.SetPosition(current_pos)  # Keep position fixed
                        origin_actors_found += 1
        
        # Also check for any actors at origin position (0,0,0) that might be origin markers
        if hasattr(renderer, 'renderer'):
            actor_collection = renderer.renderer.GetActors()
            if actor_collection:
                actor_collection.InitTraversal()
                actor = actor_collection.GetNextActor()
                while actor:
                    pos = actor.GetPosition()
                    # Check if actor is at origin (likely an origin marker)
                    if abs(pos[0]) < 0.1 and abs(pos[1]) < 0.1 and abs(pos[2]) < 0.1:
                        # This might be an origin marker - rotate it
                        actor.RotateWXYZ(-degrees,
                            1 if axis == 'x' else 0,
                            1 if axis == 'y' else 0,
                            1 if axis == 'z' else 0)
                        actor.SetPosition(pos)  # Keep at origin
                        origin_actors_found += 1
                        print(f"   Rotated origin-positioned actor at {pos}")
                    actor = actor_collection.GetNextActor()
        
        print(f"🔧 FORCE FIX: Rotated {origin_actors_found} origin-related actors")
        
        # Force render
        if hasattr(renderer, 'render_window') and renderer.render_window:
            renderer.render_window.Render()
    
    def show_help(self):
        """Show comprehensive help dialog with simple explanations"""
        from PyQt5.QtWidgets import QMessageBox
        help_text = """
DEBUG STEP VIEWER TDK - Complete Button Guide

???????????????????????????????????????????????????????????????

🔄 ROTATION CONTROLS (LEFT SIDE):
• X+/X- - Rotate around X-axis (pitch up/down)
• Y+/Y- - Rotate around Y-axis (yaw left/right)
• Z+/Z- - Rotate around Z-axis (roll left/right)

📍 POSITION CONTROLS (RIGHT SIDE):
• X+/X- - Move along X-axis (left/right)
• Y+/Y- - Move along Y-axis (forward/back)
• Z+/Z- - Move along Z-axis (up/down)

???????????????????????????????????????????????????????????????

🖱️ MOUSE CONTROLS:
• Click and drag - Rotate model interactively
• Mouse coordinates shown in yellow text overlay

???????????????????????????????????????????????????????????????

📁 FILE OPERATIONS:
• Open STEP File - Load 3D model from .step/.stp file
• Save Transformed - Save with current rotations/positions applied
• Save Original - Save without any transformations
• Clear View - Remove model and reset to zero

???????????????????????????????????????????????????????????????

📷 VIEW CONTROLS:
• TOP/BOTTOM - Switch between dual viewers
• Isometric/Front/Side/Top - Standard camera views
• Fit View - Auto-zoom to fit model
• Reset to Original - Undo all transformations

???????????????????????????????????????????????????????????????

TARGET ORIGIN & VISUAL AIDS:
• Toggle Origin Overlay - Show/hide origin markers (created automatically)
• Create Origin Overlay - Manually add origin markers (usually not needed)
• Toggle Bounding Box - Show/hide red box around model

???????????????????????????????????????????????????????????????

🔄 OVERLAY FEATURES:
• Overlay Bottom on Top - Show both viewers on top screen

???????????????????????????????????????????????????????????????

💡 TIPS:
• Yellow text shows current Direction and REF Direction values
• Numbers update in real-time as you rotate/move
• All 12 buttons work with both mouse and button controls
• Model and Local Origin always show identical values
• Use small increments (1-5 degrees) for precise positioning

???????????????????????????????????????????????????????????????
"""

        msg = QMessageBox()
        msg.setWindowTitle("STEP Viewer Help")
        msg.setText(help_text)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()

    def _debug_capture_before_state(self, operation_type, operation_desc):
        """Capture complete state BEFORE button operation"""
        print(f"\n📊 BEFORE {operation_type} {operation_desc}:")
        print("=" * 60)

        # Capture yellow text values
        self._debug_capture_yellow_text("BEFORE")

        # Capture object positions
        self._debug_capture_object_positions("BEFORE")

        print("=" * 60)

    def _debug_capture_after_state(self, operation_type, operation_desc):
        """Capture complete state AFTER button operation"""
        print(f"\n📊 AFTER {operation_type} {operation_desc}:")
        print("=" * 60)

        # Capture yellow text values
        self._debug_capture_yellow_text("AFTER")

        # Capture object positions
        self._debug_capture_object_positions("AFTER")

        print("=" * 60)
        print(f"✅ {operation_type} {operation_desc} DEBUG COMPLETE\n")

    def _debug_capture_yellow_text(self, when):
        """Capture all yellow text display values"""
        print(f"🔢 YELLOW TEXT {when}:")

        # Get current display data for both viewers
        try:
            top_data = self._calculate_unified_display_numbers("top")
            bottom_data = self._calculate_unified_display_numbers("bottom")

            print(f"  TOP Viewer:")
            print(f"    Cursor: {top_data.get('cursor_text', 'N/A')}")
            print(f"    Model: {top_data.get('model_text', 'N/A')}")
            print(f"    Local Origin: {top_data.get('local_origin_text', 'N/A')}")
            print(f"    World Origin: {top_data.get('world_origin_text', 'N/A')}")

            print(f"  BOTTOM Viewer:")
            print(f"    Cursor: {bottom_data.get('cursor_text', 'N/A')}")
            print(f"    Model: {bottom_data.get('model_text', 'N/A')}")
            print(f"    Local Origin: {bottom_data.get('local_origin_text', 'N/A')}")
            print(f"    World Origin: {bottom_data.get('world_origin_text', 'N/A')}")

        except Exception as e:
            print(f"  ERROR capturing yellow text: {e}")

    def _debug_capture_object_positions(self, when):
        """Capture positions of all visual objects"""
        print(f"🎯 OBJECT POSITIONS {when}:")

        # Current position/rotation tracking variables
        print(f"  Position Tracking:")
        print(f"    TOP current_pos: {getattr(self, 'current_pos_left', 'N/A')}")
        print(f"    TOP current_rot: {getattr(self, 'current_rot_left', 'N/A')}")
        print(f"    BOTTOM current_pos: {getattr(self, 'current_pos_right', 'N/A')}")
        print(f"    BOTTOM current_rot: {getattr(self, 'current_rot_right', 'N/A')}")

        # Green ball position (coordinate system)
        try:
            if hasattr(self, 'coordinate_system_left') and self.coordinate_system_left:
                pos = self.coordinate_system_left.GetPosition()
                orient = self.coordinate_system_left.GetOrientation()
                print(f"  TOP Green Ball: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), orient=({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
            else:
                print(f"  TOP Green Ball: NOT FOUND")

            if hasattr(self, 'coordinate_system_right') and self.coordinate_system_right:
                pos = self.coordinate_system_right.GetPosition()
                orient = self.coordinate_system_right.GetOrientation()
                print(f"  BOTTOM Green Ball: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), orient=({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
            else:
                print(f"  BOTTOM Green Ball: NOT FOUND")
        except Exception as e:
            print(f"  ERROR capturing green ball: {e}")

        # Origin markers and model actors
        self._debug_capture_origin_markers(when)
        self._debug_capture_model_actors(when)

    def _debug_capture_origin_markers(self, when):
        """Capture origin marker positions"""
        try:
            # Look for origin markers in TOP viewer
            marker_count = 0
            if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left:
                actors = self.vtk_renderer_left.renderer.GetActors()
                actors.InitTraversal()
                while True:
                    actor = actors.GetNextActor()
                    if not actor:
                        break
                    # Check if this looks like an origin marker (small size, near origin)
                    bounds = actor.GetBounds()
                    if bounds:
                        size = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])
                        pos = actor.GetPosition()
                        if size < 5.0:  # Small objects are likely markers
                            marker_count += 1
                            if marker_count <= 4:  # Show first 4 markers
                                print(f"  TOP Marker {marker_count}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), size={size:.3f}")
                if marker_count > 4:
                    print(f"  TOP: ... and {marker_count-4} more markers")
                print(f"  TOP Total Markers: {marker_count}")
            else:
                print(f"  TOP Markers: RENDERER NOT FOUND")

        except Exception as e:
            print(f"  ERROR capturing origin markers: {e}")

    def _debug_capture_model_actors(self, when):
        """Capture model actor positions"""
        try:
            # TOP viewer model actors
            if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left:
                actors = self.vtk_renderer_left.renderer.GetActors()
                actors.InitTraversal()
                actor_count = 0
                large_actor_count = 0
                while True:
                    actor = actors.GetNextActor()
                    if not actor:
                        break
                    actor_count += 1
                    bounds = actor.GetBounds()
                    if bounds:
                        size = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])
                        if size >= 5.0:  # Large objects are likely model parts
                            large_actor_count += 1
                            pos = actor.GetPosition()
                            orient = actor.GetOrientation()
                            if large_actor_count <= 2:  # Show first 2 large actors
                                print(f"  TOP Model {large_actor_count}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), orient=({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°), size={size:.3f}")
                print(f"  TOP Total Actors: {actor_count} (Large: {large_actor_count})")
            else:
                print(f"  TOP Actors: RENDERER NOT FOUND")

        except Exception as e:
            print(f"  ERROR capturing model actors: {e}")

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    window = StepViewerTDK()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()