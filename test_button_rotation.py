#!/usr/bin/env python3
"""
Test Button Rotation Fixes
==========================
This script tests if the button rotation fixes work:
1. Yellow numbers change with button rotation
2. Origin markers move with Y and Z button rotation
"""

import sys
import os
import time

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_button_rotation():
    """Test button rotation directly without GUI"""
    print("🧪 TESTING BUTTON ROTATION FIXES")
    print("=" * 50)
    
    # Import after path setup
    try:
        from step_viewer import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
    except Exception as e:
        print(f"❌ Failed to import: {e}")
        return False
    
    # Create minimal Qt app
    app = QApplication([])
    
    # Create step viewer (no GUI)
    viewer = StepViewerTDK()
    viewer.hide()
    
    # Set active viewer
    viewer.active_viewer = "top"
    print("✅ Step viewer created")
    
    # Test 1: Check if rotate_shape calls _apply_model_rotation
    print("\n🔄 TEST 1: Button rotation method")
    print("-" * 30)
    
    # Check what method rotate_shape calls
    import inspect
    try:
        source = inspect.getsource(viewer.rotate_shape)
        if "_apply_model_rotation" in source:
            print("✅ SUCCESS: rotate_shape calls _apply_model_rotation (same as mouse)")
        elif "_apply_unified_rotation" in source:
            print("❌ FAILED: rotate_shape still calls _apply_unified_rotation (different from mouse)")
        else:
            print("⚠️  UNKNOWN: rotate_shape calls unknown method")
    except Exception as e:
        print(f"❌ Error checking rotate_shape: {e}")
    
    # Test 2: Check if _apply_model_rotation updates text displays
    print("\n📝 TEST 2: Text display updates")
    print("-" * 30)
    
    try:
        source = inspect.getsource(viewer._apply_model_rotation)
        if "update_text_overlays" in source:
            print("✅ SUCCESS: _apply_model_rotation calls update_text_overlays (yellow numbers will change)")
        else:
            print("❌ FAILED: _apply_model_rotation does NOT call update_text_overlays (yellow numbers won't change)")
    except Exception as e:
        print(f"❌ Error checking _apply_model_rotation: {e}")
    
    # Test 3: Test actual rotation call
    print("\n🔄 TEST 3: Actual rotation test")
    print("-" * 30)
    
    try:
        # Create mock renderer to avoid errors
        class MockRenderer:
            def __init__(self):
                self.renderer = None
                self.render_window = MockRenderWindow()
        
        class MockRenderWindow:
            def Render(self):
                print("   Mock render called")
        
        # Set up mock renderer
        viewer.vtk_renderer_left = MockRenderer()
        
        # Test button rotation
        print("🔄 Calling rotate_shape('y', 15.0)...")
        viewer.rotate_shape('y', 15.0)
        
        # Check if tracking was updated
        if hasattr(viewer, 'current_rot_left'):
            print(f"✅ SUCCESS: Rotation tracking updated: {viewer.current_rot_left}")
        else:
            print("❌ FAILED: No rotation tracking found")
            
    except Exception as e:
        print(f"❌ Error testing rotation: {e}")
        import traceback
        traceback.print_exc()
    
    app.quit()
    
    print("\n" + "=" * 50)
    print("🎯 SUMMARY:")
    print("If all tests pass, button rotation should now:")
    print("  • Change yellow numbers (world origin)")
    print("  • Move origin markers correctly")
    print("  • Work identically to mouse rotation")

if __name__ == "__main__":
    test_button_rotation()
