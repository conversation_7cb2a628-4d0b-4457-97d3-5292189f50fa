#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

from step_loader import ST<PERSON><PERSON>oa<PERSON>

def test_gui_loading():
    """Test what happens when we try to load a file like the GUI does"""
    
    # Find a STEP file to test with
    step_files = []
    for file in os.listdir('.'):
        if file.endswith('.step') or file.endswith('.stp'):
            step_files.append(file)
    
    if not step_files:
        print("❌ No STEP files found in current directory")
        return
    
    test_file = step_files[0]
    print(f"🔍 Testing GUI loading with file: {test_file}")
    
    try:
        # Test the exact same way the GUI loads files
        loader = STEPLoader()
        print(f"✅ STEPLoader created successfully")
        
        # Try to load the file
        print(f"📂 Attempting to load: {test_file}")
        success = loader.load_step_file(test_file)
        
        if success:
            print(f"✅ File loaded successfully")

            # Check what attributes the loader actually has
            attrs = [attr for attr in dir(loader) if not attr.startswith('_')]
            print(f"📊 Loader attributes: {attrs}")

            # Check for filename-related attributes
            if hasattr(loader, 'step_filename'):
                print(f"📊 Loader step_filename: {loader.step_filename}")
            if hasattr(loader, 'current_filename'):
                print(f"📊 Loader current_filename: {loader.current_filename}")

            print(f"📊 Has actors: {len(loader.actors) if hasattr(loader, 'actors') else 'No actors attribute'}")

            if hasattr(loader, 'actors') and loader.actors:
                print(f"📊 Number of actors: {len(loader.actors)}")
                for i, actor in enumerate(loader.actors[:3]):  # Show first 3
                    print(f"   Actor {i}: {type(actor)}")
            else:
                print("❌ No actors created - this is the problem!")
                
        else:
            print(f"❌ File loading failed")
            
    except Exception as e:
        print(f"❌ Exception during loading: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gui_loading()
