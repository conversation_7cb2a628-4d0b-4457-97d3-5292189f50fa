#!/usr/bin/env python3
"""
Demo: Load -> Rotate -> Save -> Load workflow to show it works
"""

import sys
import os
import time
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== DEMO: ROTATION SAVE WORKFLOW ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()
print("✅ Created viewer")

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"\n1. LOADING: {test_file}")
    
    # Load file in top viewer
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ File loaded in TOP viewer")
        
        # Show original rotation
        viewer.update_text_overlays()
        original_rot = viewer.current_rot_left.copy()
        print(f"   Original rotation: X={original_rot['x']:.1f}° Y={original_rot['y']:.1f}° Z={original_rot['z']:.1f}°")
        
        print(f"\n2. ROTATING: Applying complex rotations (30° X, 45° Y, 60° Z)...")

        # Apply rotations in all 3 directions to get complex direction vectors
        viewer._apply_model_rotation("top", "x", 30.0)
        viewer._apply_model_rotation("top", "y", 45.0)
        viewer._apply_model_rotation("top", "z", 60.0)
        viewer.update_text_overlays()
        
        # Show rotated values
        rotated_rot = viewer.current_rot_left.copy()
        print(f"✅ Rotated: X={rotated_rot['x']:.1f}° Y={rotated_rot['y']:.1f}° Z={rotated_rot['z']:.1f}°")
        
        # Get bounds to show geometry changed
        top_renderer = viewer.vtk_renderer_left
        if hasattr(top_renderer, 'step_actors') and top_renderer.step_actors:
            rotated_bounds = top_renderer.step_actors[0].GetBounds()
            print(f"   Rotated bounds: {[round(b, 2) for b in rotated_bounds]}")
        
        print(f"\n3. SAVING: Saving rotated model...")
        
        # Save the rotated file
        demo_save_file = "demo_rotated.step"
        if os.path.exists(demo_save_file):
            os.remove(demo_save_file)
        
        # Use the save method
        loader = viewer.step_loader_left
        success = viewer._save_step_with_current_rotation(demo_save_file, loader, rotated_rot)
        
        if success and os.path.exists(demo_save_file):
            file_size = os.path.getsize(demo_save_file)
            print(f"✅ Saved: {demo_save_file} ({file_size} bytes)")
            
            print(f"\n4. LOADING SAVED FILE: Loading in bottom viewer...")
            
            # Load the saved file in bottom viewer
            viewer.active_viewer = "bottom"
            load_success = viewer.load_step_file_direct(demo_save_file)
            
            if load_success:
                print("✅ Saved file loaded in BOTTOM viewer")
                
                # Update text to show the values
                viewer.update_text_overlays()
                
                # Get the rotation values from bottom viewer
                saved_rot = viewer.current_rot_right.copy()
                print(f"   Saved file rotation: X={saved_rot['x']:.1f}° Y={saved_rot['y']:.1f}° Z={saved_rot['z']:.1f}°")
                
                # Get bounds to show geometry
                bottom_renderer = viewer.vtk_renderer_right
                if hasattr(bottom_renderer, 'step_actors') and bottom_renderer.step_actors:
                    saved_bounds = bottom_renderer.step_actors[0].GetBounds()
                    print(f"   Saved file bounds: {[round(b, 2) for b in saved_bounds]}")
                
                print(f"\n=== RESULTS ===")
                
                # Compare rotations
                rotation_preserved = (abs(rotated_rot['x'] - saved_rot['x']) < 1.0 and
                                    abs(rotated_rot['y'] - saved_rot['y']) < 1.0 and
                                    abs(rotated_rot['z'] - saved_rot['z']) < 1.0)
                
                if rotation_preserved:
                    print("🎉 SUCCESS: Rotation values preserved!")
                    print(f"   Applied: X={rotated_rot['x']:.1f}° Y={rotated_rot['y']:.1f}° Z={rotated_rot['z']:.1f}°")
                    print(f"   Saved:   X={saved_rot['x']:.1f}° Y={saved_rot['y']:.1f}° Z={saved_rot['z']:.1f}°")
                else:
                    print("❌ FAILURE: Rotation values not preserved")
                    print(f"   Applied: X={rotated_rot['x']:.1f}° Y={rotated_rot['y']:.1f}° Z={rotated_rot['z']:.1f}°")
                    print(f"   Saved:   X={saved_rot['x']:.1f}° Y={saved_rot['y']:.1f}° Z={saved_rot['z']:.1f}°")
                
                # Compare geometry
                if 'rotated_bounds' in locals() and 'saved_bounds' in locals():
                    geometry_preserved = all(abs(a - b) < 0.1 for a, b in zip(rotated_bounds, saved_bounds))
                    
                    if geometry_preserved:
                        print("🎉 SUCCESS: Geometry preserved!")
                    else:
                        print("❌ FAILURE: Geometry not preserved")
                        print(f"   Rotated: {[round(b, 2) for b in rotated_bounds]}")
                        print(f"   Saved:   {[round(b, 2) for b in saved_bounds]}")
                
                if rotation_preserved:
                    print(f"\n✅ ROTATION SAVE SYSTEM IS WORKING!")
                    print(f"   - Load STEP file ✅")
                    print(f"   - Apply rotation ✅") 
                    print(f"   - Save rotated file ✅")
                    print(f"   - Load saved file ✅")
                    print(f"   - Rotation preserved ✅")
                else:
                    print(f"\n❌ ROTATION SAVE SYSTEM FAILED")
            else:
                print("❌ Failed to load saved file")
            
            # Clean up
            os.remove(demo_save_file)
        else:
            print("❌ Failed to save file")
    else:
        print("❌ Failed to load original file")
else:
    print("❌ No STEP files found")

app.quit()
print("\n=== DEMO FINISHED ===")
