#!/usr/bin/env python3
"""
Debug mouse initialization - step by step
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

def debug_mouse_init():
    """Debug the mouse initialization process"""
    
    print("🔍 DEBUG MOUSE INITIALIZATION")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # Import the main viewer
    print("📦 Importing step_viewer...")
    from step_viewer import StepViewerTDK
    
    print("🏗️ Creating StepViewerTDK...")
    viewer = StepViewerTDK()
    
    print("👁️ Showing viewer...")
    viewer.show()
    
    # Check VTK renderer initialization
    print("\n🔍 CHECKING VTK RENDERER INITIALIZATION:")
    
    # Check LEFT renderer
    if hasattr(viewer, 'vtk_renderer_left'):
        print(f"✅ vtk_renderer_left exists: {viewer.vtk_renderer_left}")
        
        if hasattr(viewer.vtk_renderer_left, 'interactor'):
            interactor = viewer.vtk_renderer_left.interactor
            print(f"✅ LEFT interactor exists: {interactor}")
            
            if interactor:
                print(f"   Interactor enabled: {interactor.GetEnabled()}")
                print(f"   Interactor initialized: {interactor.GetInitialized()}")
                
                style = interactor.GetInteractorStyle()
                print(f"   Interaction style: {style}")
                print(f"   Style type: {type(style)}")
            else:
                print("❌ LEFT interactor is None")
        else:
            print("❌ LEFT renderer has no interactor attribute")
    else:
        print("❌ No vtk_renderer_left")
    
    # Check RIGHT renderer
    if hasattr(viewer, 'vtk_renderer_right'):
        print(f"✅ vtk_renderer_right exists: {viewer.vtk_renderer_right}")
        
        if hasattr(viewer.vtk_renderer_right, 'interactor'):
            interactor = viewer.vtk_renderer_right.interactor
            print(f"✅ RIGHT interactor exists: {interactor}")
            
            if interactor:
                print(f"   Interactor enabled: {interactor.GetEnabled()}")
                print(f"   Interactor initialized: {interactor.GetInitialized()}")
                
                style = interactor.GetInteractorStyle()
                print(f"   Interaction style: {style}")
                print(f"   Style type: {type(style)}")
            else:
                print("❌ RIGHT interactor is None")
        else:
            print("❌ RIGHT renderer has no interactor attribute")
    else:
        print("❌ No vtk_renderer_right")
    
    print("\n" + "=" * 50)
    print("🖱️ NOW TEST MOUSE INTERACTION:")
    print("1. Try clicking and dragging in the TOP viewer")
    print("2. Check if you see any debug messages")
    print("3. Press Ctrl+C to exit when done")
    print("=" * 50)
    
    # Run the application
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n👋 Debug completed")

if __name__ == "__main__":
    debug_mouse_init()
