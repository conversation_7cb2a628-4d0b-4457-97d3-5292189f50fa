#!/usr/bin/env python3
"""
Test All Rotation Issues
========================
This script will test:
1. Yellow numbers (world origin) changing with rotate buttons
2. Y and Z buttons moving origin markers correctly
3. Compare mouse vs button rotation behavior
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_rotation_issues():
    """Test all rotation issues comprehensively"""
    print("🧪 TESTING ALL ROTATION ISSUES")
    print("=" * 60)
    
    # Import after path setup
    try:
        from step_viewer import StepViewerTDK
    except Exception as e:
        print(f"❌ Failed to import step_viewer: {e}")
        return False
    
    # Create Qt application
    app = QApplication([])
    
    # Create step viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Set active viewer
    viewer.active_viewer = "top"
    print("✅ Step viewer created and shown")
    
    # Wait for initialization
    time.sleep(2)
    
    # Find a STEP file to test with
    step_files = [f for f in os.listdir('.') if f.endswith(('.step', '.stp', '.STEP', '.STP'))]
    if step_files:
        step_file = step_files[0]
        print(f"📁 Found STEP file: {step_file}")
        
        # Try to load the file
        try:
            # Check if we can access the step loader
            if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
                if hasattr(viewer.vtk_renderer_left, 'step_loader'):
                    loader = viewer.vtk_renderer_left.step_loader
                    success, message = loader.load_step_file(step_file)
                    if success:
                        print(f"✅ File loaded: {message}")
                        
                        # Create origin overlay
                        viewer.vtk_renderer_left.create_origin_overlay()
                        print("✅ Origin overlay created")
                        
                        # Wait for everything to initialize
                        time.sleep(1)
                        
                        # Test the issues
                        test_yellow_numbers(viewer)
                        test_origin_markers(viewer)
                        
                    else:
                        print(f"❌ File load failed: {message}")
                else:
                    print("❌ No step loader found")
            else:
                print("❌ No renderer found")
        except Exception as e:
            print(f"❌ Error loading file: {e}")
    else:
        print("⚠️  No STEP files found - testing without file")
        test_yellow_numbers(viewer)
        test_origin_markers(viewer)
    
    # Close after test
    QTimer.singleShot(5000, app.quit)  # Close after 5 seconds
    app.exec_()

def test_yellow_numbers(viewer):
    """Test if yellow numbers (world origin) change with rotate buttons"""
    print("\n🟡 TESTING YELLOW NUMBERS (World Origin)")
    print("-" * 50)
    
    try:
        # Get initial world origin text
        initial_text = get_world_origin_text(viewer)
        print(f"📊 Initial world origin: {initial_text}")
        
        # Apply button rotation
        print("🔄 Applying X+15° rotation via button...")
        viewer.rotate_shape('x', 15.0)
        time.sleep(0.5)
        
        # Get new world origin text
        new_text = get_world_origin_text(viewer)
        print(f"📊 After X+15°: {new_text}")
        
        # Check if numbers changed
        if initial_text != new_text:
            print("✅ SUCCESS: Yellow numbers changed with button rotation")
        else:
            print("❌ FAILED: Yellow numbers did NOT change with button rotation")
            
        # Test Y rotation
        print("🔄 Applying Y+15° rotation via button...")
        viewer.rotate_shape('y', 15.0)
        time.sleep(0.5)
        
        y_text = get_world_origin_text(viewer)
        print(f"📊 After Y+15°: {y_text}")
        
        # Test Z rotation
        print("🔄 Applying Z+15° rotation via button...")
        viewer.rotate_shape('z', 15.0)
        time.sleep(0.5)
        
        z_text = get_world_origin_text(viewer)
        print(f"📊 After Z+15°: {z_text}")
        
    except Exception as e:
        print(f"❌ Error testing yellow numbers: {e}")
        import traceback
        traceback.print_exc()

def test_origin_markers(viewer):
    """Test if origin markers move correctly with Y and Z buttons"""
    print("\n🔴 TESTING ORIGIN MARKERS")
    print("-" * 50)
    
    try:
        # Get initial origin marker positions
        initial_positions = get_origin_marker_positions(viewer)
        print(f"📊 Initial origin marker positions: {len(initial_positions)} markers")
        for i, pos in enumerate(initial_positions):
            print(f"   Marker {i}: {pos}")
        
        # Test Y button
        print("🔄 Testing Y+15° button...")
        viewer.rotate_shape('y', 15.0)
        time.sleep(0.5)
        
        y_positions = get_origin_marker_positions(viewer)
        print(f"📊 After Y+15°: {len(y_positions)} markers")
        for i, pos in enumerate(y_positions):
            print(f"   Marker {i}: {pos}")
        
        # Check if Y rotation moved markers
        if len(initial_positions) > 0 and len(y_positions) > 0:
            if initial_positions != y_positions:
                print("✅ SUCCESS: Y button moved origin markers")
            else:
                print("❌ FAILED: Y button did NOT move origin markers")
        
        # Test Z button
        print("🔄 Testing Z+15° button...")
        viewer.rotate_shape('z', 15.0)
        time.sleep(0.5)
        
        z_positions = get_origin_marker_positions(viewer)
        print(f"📊 After Z+15°: {len(z_positions)} markers")
        for i, pos in enumerate(z_positions):
            print(f"   Marker {i}: {pos}")
        
        # Check if Z rotation moved markers
        if len(y_positions) > 0 and len(z_positions) > 0:
            if y_positions != z_positions:
                print("✅ SUCCESS: Z button moved origin markers")
            else:
                print("❌ FAILED: Z button did NOT move origin markers")
        
    except Exception as e:
        print(f"❌ Error testing origin markers: {e}")
        import traceback
        traceback.print_exc()

def get_world_origin_text(viewer):
    """Get the current world origin text (yellow numbers)"""
    try:
        if hasattr(viewer, 'world_origin_text_actor_left'):
            return viewer.world_origin_text_actor_left.GetInput()
        return "No world origin text found"
    except:
        return "Error getting world origin text"

def get_origin_marker_positions(viewer):
    """Get current positions of all origin markers"""
    positions = []
    try:
        if hasattr(viewer, 'vtk_renderer_left'):
            renderer = viewer.vtk_renderer_left
            if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
                for actor in renderer.origin_actors:
                    if actor:
                        positions.append(actor.GetPosition())
    except Exception as e:
        print(f"Error getting origin positions: {e}")
    return positions

if __name__ == "__main__":
    test_rotation_issues()
