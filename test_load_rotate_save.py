#!/usr/bin/env python3
"""
Test script to load a file, rotate it, and save it
This will help identify what's going wrong with the save functionality
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

def test_load_rotate_save():
    print("=== LOAD -> ROTATE -> SAVE TEST ===")
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    try:
        # Create viewer
        viewer = StepViewerTDK()
        print("✅ Created viewer")
        
        # Find a STEP file to test with
        step_files = [f for f in os.listdir('.') if f.endswith('.step') or f.endswith('.STEP')]
        if not step_files:
            print("❌ No STEP files found in current directory")
            return False
            
        test_file = step_files[0]
        print(f"📁 Using test file: {test_file}")
        
        # Step 1: Load the file
        print(f"\n1. LOADING: {test_file}")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(test_file)
        
        if not success:
            print("❌ Failed to load file")
            return False
            
        print("✅ File loaded successfully")
        
        # Check if we have the necessary components
        loader = viewer.step_loader_left
        renderer = viewer.vtk_renderer_left
        
        print(f"   Loader has shape: {hasattr(loader, 'shape') and loader.shape is not None}")
        print(f"   Renderer has actors: {hasattr(renderer, 'step_actors') and len(renderer.step_actors) > 0}")
        
        # Step 2: Apply rotation
        print(f"\n2. ROTATING: Applying 90° X rotation")
        viewer._apply_model_rotation('top', 'x', 90.0)
        
        # Get current rotation state
        current_rot = viewer.current_rot_left.copy()
        print(f"   Current rotation state: {current_rot}")

        # Check original rotation state
        if hasattr(viewer, 'orig_rot_left'):
            orig_rot = viewer.orig_rot_left
            print(f"   Original rotation state: {orig_rot}")
        else:
            print("   No original rotation state found")

        # Update text overlays to get direction vectors
        viewer.update_text_overlays()
        
        # Get unified results to see the transformation
        unified_results = viewer._calculate_unified_display_numbers("top")
        model_data = unified_results['model']
        print(f"   Model data after rotation: {model_data[:100]}...")  # First 100 chars
        
        # Step 3: Save the rotated file
        output_file = "test_rotated_output.step"
        print(f"\n3. SAVING: Saving to {output_file}")
        
        # Remove existing file if it exists
        if os.path.exists(output_file):
            os.remove(output_file)
            print(f"   Removed existing {output_file}")
        
        # Try to save using the main save method
        save_success = viewer._save_step_with_current_rotation(output_file, loader, current_rot)
        
        if save_success:
            print("✅ Save method returned success")
            
            # Check if file was actually created
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"✅ File created successfully: {output_file} ({file_size} bytes)")
                
                # Step 4: Verify by loading the saved file
                print(f"\n4. VERIFICATION: Loading saved file to verify rotation")
                viewer.active_viewer = "bottom"
                verify_success = viewer.load_step_file_direct(output_file)

                if verify_success:
                    print("✅ Saved file loads successfully")

                    # Give the bottom viewer a moment to process
                    import time
                    time.sleep(0.5)

                    # Check both renderers for actors
                    print(f"   Top renderer has actors: {hasattr(viewer.vtk_renderer_left, 'step_actors') and len(viewer.vtk_renderer_left.step_actors) > 0}")
                    print(f"   Bottom renderer has actors: {hasattr(viewer.vtk_renderer_right, 'step_actors') and len(viewer.vtk_renderer_right.step_actors) > 0}")

                    # Compare original and rotated file bounds
                    orig_bounds = None
                    if hasattr(viewer.vtk_renderer_left, 'step_actors') and viewer.vtk_renderer_left.step_actors:
                        orig_bounds = viewer.vtk_renderer_left.step_actors[0].GetBounds()
                        print(f"   Original bounds: {[round(b, 2) for b in orig_bounds]}")

                    rotated_bounds = None
                    if hasattr(viewer.vtk_renderer_right, 'step_actors') and viewer.vtk_renderer_right.step_actors:
                        rotated_bounds = viewer.vtk_renderer_right.step_actors[0].GetBounds()
                        print(f"   Rotated bounds: {[round(b, 2) for b in rotated_bounds]}")

                        # Check if bounds are different (indicating rotation was saved)
                        if orig_bounds and rotated_bounds and orig_bounds != rotated_bounds:
                            print("✅ Bounds are different - rotation appears to be saved!")
                            return True
                        elif orig_bounds and rotated_bounds:
                            print("❌ Bounds are identical - rotation may not be saved")
                            # Let's also check the STEP file content to see if it was modified
                            print("   Checking STEP file content for rotation...")
                            return _verify_step_file_rotation(output_file, test_file)
                        else:
                            print("❌ Could not get bounds for comparison")
                            return False
                    else:
                        print("❌ No actors in bottom viewer - but file was created successfully")
                        # The save worked, let's check the file content
                        return _verify_step_file_rotation(output_file, test_file)
                else:
                    print("❌ Failed to load saved file for verification")
                    return False
            else:
                print("❌ File was not created despite success return")
                return False
        else:
            print("❌ Save method returned failure")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

def _verify_step_file_rotation(output_file, original_file):
    """Verify that the STEP file was actually rotated by comparing content"""
    try:
        print(f"   Comparing STEP file content: {output_file} vs {original_file}")

        # Read both files
        with open(original_file, 'r', encoding='utf-8', errors='ignore') as f:
            original_content = f.read()

        with open(output_file, 'r', encoding='utf-8', errors='ignore') as f:
            rotated_content = f.read()

        # Check if files are different
        if original_content != rotated_content:
            print("✅ STEP file content is different - rotation was applied!")

            # Look for DIRECTION changes (rotation should change direction vectors)
            import re
            orig_directions = re.findall(r'DIRECTION\([^)]+\)', original_content)
            rot_directions = re.findall(r'DIRECTION\([^)]+\)', rotated_content)

            if orig_directions != rot_directions:
                print("✅ Direction vectors changed - rotation confirmed!")
                return True
            else:
                print("⚠️  File content changed but direction vectors are the same")
                return True  # Still consider it a success since content changed
        else:
            print("❌ STEP file content is identical - no rotation applied")
            return False

    except Exception as e:
        print(f"❌ Error verifying STEP file: {e}")
        return False

if __name__ == "__main__":
    success = test_load_rotate_save()
    if success:
        print("\n🎉 TEST PASSED: Load -> Rotate -> Save workflow works!")
    else:
        print("\n💥 TEST FAILED: Load -> Rotate -> Save workflow has issues")
    
    sys.exit(0 if success else 1)
