#!/usr/bin/env python3
"""
Complete debug test of the entire workflow
"""

import sys
import os
sys.path.append('.')

def test_step_1_imports():
    print("=== STEP 1: Testing imports ===")
    try:
        from step_viewer import StepViewerTDK
        from step_loader import ST<PERSON><PERSON>oader
        from PyQt5.QtWidgets import QApplication
        import vtk
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_step_2_file_exists():
    print("\n=== STEP 2: Testing STEP file exists ===")
    step_files = [f for f in os.listdir('.') if f.endswith('.STEP') or f.endswith('.step')]
    if step_files:
        test_file = step_files[0]
        print(f"✅ Found STEP file: {test_file}")
        return test_file
    else:
        print("❌ No STEP files found")
        return None

def test_step_3_loader_basic():
    print("\n=== STEP 3: Testing basic loader ===")
    try:
        from step_loader import STEPLoader
        loader = STEPLoader()
        print("✅ STEPLoader created")
        return loader
    except Exception as e:
        print(f"❌ STEPLoader creation failed: {e}")
        return None

def test_step_4_load_file(loader, filename):
    print(f"\n=== STEP 4: Testing file loading: {filename} ===")
    try:
        success, message = loader.load_step_file(filename)
        print(f"Load result: {success}")
        print(f"Load message: {message}")
        
        if success:
            print(f"✅ File loaded successfully")
            print(f"   Has polydata: {loader.current_polydata is not None}")
            if loader.current_polydata:
                print(f"   Polydata cells: {loader.current_polydata.GetNumberOfCells()}")
                print(f"   Polydata points: {loader.current_polydata.GetNumberOfPoints()}")
            return True
        else:
            print(f"❌ File loading failed: {message}")
            return False
    except Exception as e:
        print(f"❌ File loading exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_step_5_gui_creation():
    print("\n=== STEP 5: Testing GUI creation ===")
    try:
        from PyQt5.QtWidgets import QApplication
        from step_viewer import StepViewerTDK
        
        app = QApplication(sys.argv)
        viewer = StepViewerTDK()
        print("✅ GUI created successfully")
        
        # Check if VTK widgets exist
        if hasattr(viewer, 'vtk_widget_left') and viewer.vtk_widget_left:
            print("✅ Top VTK widget exists")
        else:
            print("❌ Top VTK widget missing")
            
        if hasattr(viewer, 'vtk_widget_right') and viewer.vtk_widget_right:
            print("✅ Bottom VTK widget exists")
        else:
            print("❌ Bottom VTK widget missing")
            
        return viewer, app
    except Exception as e:
        print(f"❌ GUI creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_step_6_gui_load(viewer, filename):
    print(f"\n=== STEP 6: Testing GUI file loading: {filename} ===")
    try:
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(filename)
        
        if success:
            print("✅ GUI file loading successful")
            
            # Check if geometry was added to renderer
            if hasattr(viewer, 'vtk_widget_left') and viewer.vtk_widget_left:
                renderer = viewer.vtk_widget_left.GetRenderWindow().GetRenderers().GetFirstRenderer()
                if renderer:
                    actors = renderer.GetActors()
                    actors.InitTraversal()
                    actor_count = 0
                    while actors.GetNextActor():
                        actor_count += 1
                    print(f"✅ Top viewer has {actor_count} actors")
                    return actor_count > 0
                else:
                    print("❌ No renderer found")
                    return False
            else:
                print("❌ No VTK widget found")
                return False
        else:
            print("❌ GUI file loading failed")
            return False
    except Exception as e:
        print(f"❌ GUI loading exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_step_7_rotation(viewer):
    print("\n=== STEP 7: Testing rotation ===")
    try:
        # Apply a 90 degree rotation around X axis
        viewer.current_rot_left = [90, 0, 0]
        viewer.orig_rot_left = [0, 0, 0]
        
        print(f"✅ Rotation set: {viewer.current_rot_left}")
        return True
    except Exception as e:
        print(f"❌ Rotation failed: {e}")
        return False

def test_step_8_save(viewer, filename):
    print(f"\n=== STEP 8: Testing save functionality ===")
    try:
        output_file = "debug_test_output.step"
        
        # Get the loader
        loader = viewer.step_loader_left
        if not loader:
            print("❌ No loader found")
            return False
            
        # Test the save method
        success = viewer._save_step_with_current_rotation(output_file, loader, viewer.current_rot_left)
        
        if success:
            print(f"✅ Save successful: {output_file}")
            
            # Check if file was created
            if os.path.exists(output_file):
                print(f"✅ Output file exists")
                file_size = os.path.getsize(output_file)
                print(f"   File size: {file_size} bytes")
                return True
            else:
                print("❌ Output file not created")
                return False
        else:
            print("❌ Save failed")
            return False
    except Exception as e:
        print(f"❌ Save exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔧 COMPLETE WORKFLOW DEBUG TEST")
    print("=" * 50)
    
    # Step 1: Test imports
    if not test_step_1_imports():
        return False
    
    # Step 2: Find STEP file
    test_file = test_step_2_file_exists()
    if not test_file:
        return False
    
    # Step 3: Test basic loader
    loader = test_step_3_loader_basic()
    if not loader:
        return False
    
    # Step 4: Test file loading
    if not test_step_4_load_file(loader, test_file):
        return False
    
    # Step 5: Test GUI creation
    viewer, app = test_step_5_gui_creation()
    if not viewer or not app:
        return False
    
    # Step 6: Test GUI loading
    if not test_step_6_gui_load(viewer, test_file):
        return False
    
    # Step 7: Test rotation
    if not test_step_7_rotation(viewer):
        return False
    
    # Step 8: Test save
    if not test_step_8_save(viewer, test_file):
        return False
    
    print("\n" + "=" * 50)
    print("🎉 ALL TESTS PASSED - WORKFLOW IS WORKING!")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n" + "=" * 50)
        print("❌ TESTS FAILED - WORKFLOW IS BROKEN")
        sys.exit(1)
