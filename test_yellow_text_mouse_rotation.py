#!/usr/bin/env python3
"""
Test script to verify that yellow text numbers change after mouse rotation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import time

def test_yellow_text_mouse_rotation():
    """Test that yellow text numbers change after mouse rotation"""
    print("🧪 TESTING: Yellow text numbers during mouse rotation")
    
    # Create application
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Load a STEP file
    test_file = "test.step"
    if not os.path.exists(test_file):
        print(f"❌ Test file {test_file} not found")
        return False
    
    print(f"📁 Loading STEP file: {test_file}")
    viewer.load_step_file_direct(test_file)
    
    # Wait for load to complete
    app.processEvents()
    time.sleep(1)
    
    # Get BEFORE yellow text numbers
    print("\n🔍 BEFORE MOUSE ROTATION:")
    before_display = viewer._calculate_unified_display_numbers("top")
    print(f"📄 Model Origin: {before_display['model']}")
    print(f"📄 Local Origin: {before_display['local_origin']}")
    
    # Extract just the Origin numbers for comparison
    import re
    model_origin_match = re.search(r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', before_display['model'])
    local_origin_match = re.search(r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', before_display['local_origin'])
    
    if model_origin_match and local_origin_match:
        before_model_origin = (float(model_origin_match.group(1)), float(model_origin_match.group(2)), float(model_origin_match.group(3)))
        before_local_origin = (float(local_origin_match.group(1)), float(local_origin_match.group(2)), float(local_origin_match.group(3)))
        print(f"🔢 BEFORE Model Origin: ({before_model_origin[0]:.3f}, {before_model_origin[1]:.3f}, {before_model_origin[2]:.3f})")
        print(f"🔢 BEFORE Local Origin: ({before_local_origin[0]:.3f}, {before_local_origin[1]:.3f}, {before_local_origin[2]:.3f})")
    else:
        print("❌ Could not parse BEFORE origin numbers")
        return False
    
    # Simulate mouse rotation
    print("\n🖱️ SIMULATING MOUSE ROTATION: X+15°")
    viewer._apply_model_rotation("top", "x", 15.0)
    
    # Wait for updates
    app.processEvents()
    time.sleep(0.5)
    
    # Get AFTER yellow text numbers
    print("\n🔍 AFTER MOUSE ROTATION:")
    after_display = viewer._calculate_unified_display_numbers("top")
    print(f"📄 Model Origin: {after_display['model']}")
    print(f"📄 Local Origin: {after_display['local_origin']}")
    
    # Extract AFTER Origin numbers
    model_origin_match = re.search(r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', after_display['model'])
    local_origin_match = re.search(r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', after_display['local_origin'])
    
    if model_origin_match and local_origin_match:
        after_model_origin = (float(model_origin_match.group(1)), float(model_origin_match.group(2)), float(model_origin_match.group(3)))
        after_local_origin = (float(local_origin_match.group(1)), float(local_origin_match.group(2)), float(local_origin_match.group(3)))
        print(f"🔢 AFTER Model Origin: ({after_model_origin[0]:.3f}, {after_model_origin[1]:.3f}, {after_model_origin[2]:.3f})")
        print(f"🔢 AFTER Local Origin: ({after_local_origin[0]:.3f}, {after_local_origin[1]:.3f}, {after_local_origin[2]:.3f})")
    else:
        print("❌ Could not parse AFTER origin numbers")
        return False
    
    # Compare BEFORE vs AFTER
    print("\n📊 COMPARISON:")
    model_changed = (abs(before_model_origin[0] - after_model_origin[0]) > 0.001 or
                    abs(before_model_origin[1] - after_model_origin[1]) > 0.001 or
                    abs(before_model_origin[2] - after_model_origin[2]) > 0.001)
    
    local_changed = (abs(before_local_origin[0] - after_local_origin[0]) > 0.001 or
                    abs(before_local_origin[1] - after_local_origin[1]) > 0.001 or
                    abs(before_local_origin[2] - after_local_origin[2]) > 0.001)
    
    if model_changed:
        print(f"✅ Model Origin CHANGED: ({before_model_origin[0]:.3f}, {before_model_origin[1]:.3f}, {before_model_origin[2]:.3f}) → ({after_model_origin[0]:.3f}, {after_model_origin[1]:.3f}, {after_model_origin[2]:.3f})")
    else:
        print(f"❌ Model Origin DID NOT CHANGE: ({before_model_origin[0]:.3f}, {before_model_origin[1]:.3f}, {before_model_origin[2]:.3f})")
    
    if local_changed:
        print(f"✅ Local Origin CHANGED: ({before_local_origin[0]:.3f}, {before_local_origin[1]:.3f}, {before_local_origin[2]:.3f}) → ({after_local_origin[0]:.3f}, {after_local_origin[1]:.3f}, {after_local_origin[2]:.3f})")
    else:
        print(f"❌ Local Origin DID NOT CHANGE: ({before_local_origin[0]:.3f}, {before_local_origin[1]:.3f}, {before_local_origin[2]:.3f})")
    
    # Final verdict
    if model_changed and local_changed:
        print("\n🎉 SUCCESS: Yellow text numbers changed after mouse rotation!")
        return True
    else:
        print("\n❌ FAILURE: Yellow text numbers did not change after mouse rotation!")
        return False

if __name__ == "__main__":
    success = test_yellow_text_mouse_rotation()
    sys.exit(0 if success else 1)
