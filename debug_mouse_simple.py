#!/usr/bin/env python3
"""
Simple VTK mouse test - based on official VTK MouseEvents example
"""

import sys
import vtk
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer

try:
    from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKR<PERSON>WindowInteractor
except ImportError:
    from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class SimpleMouseTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Simple VTK Mouse Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create VTK widget
        self.vtk_widget = QVTKRenderWindowInteractor(central_widget)
        layout.addWidget(self.vtk_widget)
        
        # Create VTK pipeline
        self.setup_vtk()
        
        # Show the widget
        self.show()
        
    def setup_vtk(self):
        """Setup VTK rendering pipeline"""
        # Create renderer
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)
        
        # Create a simple cube
        cube_source = vtk.vtkCubeSource()
        cube_mapper = vtk.vtkPolyDataMapper()
        cube_mapper.SetInputConnection(cube_source.GetOutputPort())
        
        self.cube_actor = vtk.vtkActor()
        self.cube_actor.SetMapper(cube_mapper)
        self.cube_actor.GetProperty().SetColor(1.0, 0.0, 0.0)  # Red
        
        self.renderer.AddActor(self.cube_actor)
        
        # Setup render window
        render_window = self.vtk_widget.GetRenderWindow()
        render_window.AddRenderer(self.renderer)
        
        # Setup interactor
        interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
        
        # Create custom interaction style - EXACTLY like VTK MouseEvents example
        class TestMouseStyle(vtk.vtkInteractorStyle):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                self.dragging = False
                self.last_pos = None
                
            def OnLeftButtonDown(self):
                print("🖱️🖱️🖱️ LEFT BUTTON DOWN DETECTED!")
                self.dragging = True
                self.last_pos = self.GetInteractor().GetEventPosition()
                print(f"   Position: {self.last_pos}")
                
            def OnLeftButtonUp(self):
                print("🖱️🖱️🖱️ LEFT BUTTON UP DETECTED!")
                self.dragging = False
                self.last_pos = None
                
            def OnMouseMove(self):
                if self.dragging and self.last_pos:
                    current_pos = self.GetInteractor().GetEventPosition()
                    dx = current_pos[0] - self.last_pos[0]
                    dy = current_pos[1] - self.last_pos[1]
                    print(f"🖱️ DRAG: dx={dx}, dy={dy}")
                    
                    # Rotate the cube
                    if abs(dx) > 2:
                        self.parent.cube_actor.RotateWXYZ(dx * 0.5, 0, 1, 0)
                        print(f"🔄 ROTATED Y: {dx * 0.5}°")
                    if abs(dy) > 2:
                        self.parent.cube_actor.RotateWXYZ(dy * 0.5, 1, 0, 0)
                        print(f"🔄 ROTATED X: {dy * 0.5}°")
                    
                    self.GetInteractor().GetRenderWindow().Render()
                    self.last_pos = current_pos
        
        # Set the interaction style
        style = TestMouseStyle(self)
        interactor.SetInteractorStyle(style)
        
        # Initialize
        interactor.Initialize()
        
        print("✅ SIMPLE TEST READY - drag the red cube with LEFT mouse")

def main():
    app = QApplication(sys.argv)
    
    test = SimpleMouseTest()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
