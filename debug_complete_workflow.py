#!/usr/bin/env python3
"""
Complete workflow debug - test every step until it works
"""

import sys
import os
import time
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

print("=== COMPLETE WORKFLOW DEBUG ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()
viewer.show()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Testing with: {test_file}")
    
    def run_complete_test():
        try:
            print("\n=== STEP 1: LOAD ORIGINAL FILE ===")
            viewer.active_viewer = "top"
            success = viewer.load_step_file_direct(test_file)
            
            if not success:
                print("❌ FAILED: Could not load original file")
                app.quit()
                return
                
            print("✅ Original file loaded")
            
            # Get original bounds
            renderer = viewer.vtk_renderer_left
            if not (hasattr(renderer, 'step_actors') and renderer.step_actors):
                print("❌ FAILED: No actors found after loading")
                app.quit()
                return
                
            original_actor = renderer.step_actors[0]
            original_bounds = original_actor.GetBounds()
            print(f"Original bounds: {original_bounds}")
            
            print("\n=== STEP 2: APPLY ROTATION ===")
            # Apply 90 degree X rotation
            print("Applying 90° X rotation...")
            viewer._apply_model_rotation("top", "x", 90.0)
            
            # Get rotated bounds
            rotated_bounds = original_actor.GetBounds()
            print(f"Rotated bounds: {rotated_bounds}")
            
            # Check if bounds actually changed
            bounds_changed = any(abs(a - b) > 0.01 for a, b in zip(original_bounds, rotated_bounds))
            print(f"Visual rotation working: {bounds_changed}")
            
            if not bounds_changed:
                print("❌ FAILED: Visual rotation not working - bounds didn't change")
                app.quit()
                return
                
            print("✅ Visual rotation working")
            
            print("\n=== STEP 3: GET ROTATION VALUES ===")
            current_rot = viewer._extract_rotation_from_vtk_actor("top")
            orig_rot = viewer.orig_rot_left if hasattr(viewer, 'orig_rot_left') else {'x': 0, 'y': 0, 'z': 0}
            
            print(f"Current rotation: {current_rot}")
            print(f"Original rotation: {orig_rot}")
            
            delta_x = current_rot['x'] - orig_rot['x']
            delta_y = current_rot['y'] - orig_rot['y']
            delta_z = current_rot['z'] - orig_rot['z']
            
            print(f"Delta rotation: X={delta_x:.1f}° Y={delta_y:.1f}° Z={delta_z:.1f}°")
            
            has_rotation = any(abs(delta) > 0.1 for delta in [delta_x, delta_y, delta_z])
            print(f"Has rotation to save: {has_rotation}")
            
            if not has_rotation:
                print("❌ FAILED: No rotation detected for save")
                app.quit()
                return
                
            print("✅ Rotation detected for save")
            
            print("\n=== STEP 4: TEST SAVE METHOD ===")
            test_save_file = "debug_complete_workflow.step"
            
            if os.path.exists(test_save_file):
                os.remove(test_save_file)
                
            loader = viewer.step_loader_left
            print("Calling save method...")
            
            # Call the actual GUI save method that should be used
            success = viewer._save_step_simple(test_save_file, loader, current_rot, orig_rot)
            
            print(f"Save method returned: {success}")
            
            if not success:
                print("❌ FAILED: Save method returned False")
                app.quit()
                return
                
            if not os.path.exists(test_save_file):
                print("❌ FAILED: Save file was not created")
                app.quit()
                return
                
            file_size = os.path.getsize(test_save_file)
            print(f"✅ Save file created: {file_size} bytes")
            
            print("\n=== STEP 5: LOAD SAVED FILE ===")
            viewer.active_viewer = "bottom"
            load_success = viewer.load_step_file_direct(test_save_file)
            
            if not load_success:
                print("❌ FAILED: Could not load saved file")
                os.remove(test_save_file)
                app.quit()
                return
                
            print("✅ Saved file loaded")
            
            print("\n=== STEP 6: COMPARE RESULTS ===")
            bottom_renderer = viewer.vtk_renderer_right
            if not (hasattr(bottom_renderer, 'step_actors') and bottom_renderer.step_actors):
                print("❌ FAILED: No actors in saved file")
                os.remove(test_save_file)
                app.quit()
                return
                
            saved_actor = bottom_renderer.step_actors[0]
            saved_bounds = saved_actor.GetBounds()
            
            print(f"Original bounds:  {original_bounds}")
            print(f"Rotated bounds:   {rotated_bounds}")
            print(f"Saved bounds:     {saved_bounds}")
            
            # The saved bounds should match the rotated bounds (not the original)
            saved_matches_rotated = all(abs(a - b) < 0.1 for a, b in zip(rotated_bounds, saved_bounds))
            saved_matches_original = all(abs(a - b) < 0.1 for a, b in zip(original_bounds, saved_bounds))
            
            print(f"Saved matches rotated: {saved_matches_rotated}")
            print(f"Saved matches original: {saved_matches_original}")
            
            print("\n=== FINAL RESULT ===")
            if saved_matches_rotated and not saved_matches_original:
                print("🎉 SUCCESS: Rotation save system is working!")
                print("✅ Saved file contains the rotated geometry")
                print("✅ Rotation is preserved in the saved file")
            elif saved_matches_original and not saved_matches_rotated:
                print("❌ FAILURE: Saved file contains original geometry (rotation not applied)")
                print("❌ The transformation is not being applied to the OpenCASCADE shape")
            else:
                print("❌ FAILURE: Unexpected bounds comparison result")
                print("❌ Something is wrong with the geometry comparison")
            
            # Clean up
            os.remove(test_save_file)
            
        except Exception as e:
            print(f"❌ ERROR: Exception during test: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n=== DEBUG COMPLETE ===")
        app.quit()
    
    # Run test after GUI initializes
    QTimer.singleShot(1000, run_complete_test)
    app.exec_()
else:
    print("❌ No STEP files found")
    app.quit()
