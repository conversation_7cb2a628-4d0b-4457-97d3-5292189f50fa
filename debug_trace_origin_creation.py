#!/usr/bin/env python3
"""
Debug test to trace all origin marker creation during movement.
This test will monkey-patch the origin creation methods to trace when they're called.
"""

import sys
import os
import glob
from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from step_viewer import Step<PERSON>iewer<PERSON>D<PERSON>

def debug_trace_origin_creation():
    """Trace all origin marker creation during movement"""
    print("🔍 ORIGIN CREATION TRACE DEBUG TEST")
    print("=" * 50)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    
    # Find and load a STEP file
    step_files = glob.glob("*.step") + glob.glob("*.stp") + glob.glob("*.STEP") + glob.glob("*.STP")
    
    if step_files:
        step_file = step_files[0]
        print(f"📁 Loading STEP file: {step_file}")
        
        # Set active viewer to top
        viewer.active_viewer = "top"
        
        # Load the STEP file
        success = viewer.load_step_file_direct(step_file)
        QTest.qWait(3000)  # Wait for loading
        
        if success:
            print("✅ STEP file loaded successfully")
            
            # Create origin overlays
            viewer.create_origin_overlay()
            QTest.qWait(1000)
            
            print("✅ Origin overlays created")
            
            # MONKEY PATCH: Trace all origin creation methods
            original_create_origin_overlay = viewer.vtk_renderer_left.create_origin_overlay
            original_create_part_origin_overlay = viewer.vtk_renderer_left.create_part_origin_overlay
            original_step_viewer_create_origin_overlay = viewer.create_origin_overlay
            
            def traced_vtk_create_origin_overlay():
                print("🚨 TRACE: vtk_renderer.create_origin_overlay() CALLED!")
                import traceback
                traceback.print_stack()
                return original_create_origin_overlay()
            
            def traced_vtk_create_part_origin_overlay(*args, **kwargs):
                print("🚨 TRACE: vtk_renderer.create_part_origin_overlay() CALLED!")
                import traceback
                traceback.print_stack()
                return original_create_part_origin_overlay(*args, **kwargs)
            
            def traced_step_viewer_create_origin_overlay():
                print("🚨 TRACE: step_viewer.create_origin_overlay() CALLED!")
                import traceback
                traceback.print_stack()
                return original_step_viewer_create_origin_overlay()
            
            # Apply monkey patches
            viewer.vtk_renderer_left.create_origin_overlay = traced_vtk_create_origin_overlay
            viewer.vtk_renderer_left.create_part_origin_overlay = traced_vtk_create_part_origin_overlay
            viewer.create_origin_overlay = traced_step_viewer_create_origin_overlay
            
            print("🔧 MONKEY PATCHES APPLIED - All origin creation will be traced")
            
            # Count actors BEFORE movement
            renderer = viewer.vtk_renderer_left
            if renderer and renderer.renderer:
                all_actors = renderer.renderer.GetActors()
                all_actors.InitTraversal()
                
                before_count = 0
                while True:
                    actor = all_actors.GetNextActor()
                    if not actor:
                        break
                    before_count += 1
                
                print(f"📊 BEFORE: {before_count} total actors")
                
                # Perform X+ movement and watch for origin creation traces
                print("\n🔄 PERFORMING X+ MOVEMENT WITH ORIGIN CREATION TRACING...")
                print("-" * 60)
                
                try:
                    # Set active viewer to top
                    viewer.active_viewer = "top"
                    
                    # Perform X+ move - this should trigger traces if origin markers are created
                    viewer.move_shape("x", 2.0)
                    QTest.qWait(3000)  # Wait for move to complete
                    
                    print("✅ X+ move completed")
                    
                except Exception as e:
                    print(f"❌ X+ move FAILED: {e}")
                    import traceback
                    traceback.print_exc()
                
                # Count actors AFTER movement
                all_actors = renderer.renderer.GetActors()
                all_actors.InitTraversal()
                
                after_count = 0
                while True:
                    actor = all_actors.GetNextActor()
                    if not actor:
                        break
                    after_count += 1
                
                print(f"\n📊 AFTER: {after_count} total actors")
                
                # Analysis
                print(f"\n🔍 ANALYSIS:")
                print(f"   Before: {before_count} actors")
                print(f"   After:  {after_count} actors")
                print(f"   Change: {after_count - before_count} actors")
                
                if after_count > before_count:
                    print("🚨 DUPLICATE ACTORS DETECTED!")
                    print("🔧 Check the trace output above to see what created them")
                else:
                    print("✅ No duplicate actors - origin creation is working correctly")
                
        else:
            print("❌ STEP file loading failed")
    else:
        print("❌ No STEP files found")
    
    # Close application properly
    print("\n🔚 CLOSING APPLICATION...")
    try:
        if hasattr(viewer, 'close'):
            viewer.close()
        if hasattr(app, 'quit'):
            app.quit()
        if hasattr(app, 'exit'):
            app.exit(0)
    except Exception as e:
        print(f"⚠️  Error during cleanup: {e}")
    
    print("✅ Origin creation trace test completed")

if __name__ == "__main__":
    debug_trace_origin_creation()
    sys.exit(0)
