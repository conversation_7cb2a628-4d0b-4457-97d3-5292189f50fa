#!/usr/bin/env python3
"""
COMPREHENSIVE ROTATION BUTTON DEBUG PROGRAM
Tests ALL 6 rotation buttons with exact before/after data for EVERY actor
No guessing - shows exactly what moves and what doesn't
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def comprehensive_rotation_test():
    """Test ALL 6 rotation buttons with complete actor tracking"""
    print("🧪 COMPREHENSIVE ROTATION BUTTON DEBUG")
    print("=" * 60)
    
    try:
        # Import the main application
        from step_viewer import StepViewerTDK

        # Create Qt application
        app = QApplication(sys.argv)

        # Create main window
        viewer = StepViewerTDK()
        viewer.show()
        
        # Wait for initialization, then start tests
        QTimer.singleShot(3000, lambda: run_comprehensive_tests(viewer, app))
        
        # Start event loop
        app.exec_()
        
    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()

def run_comprehensive_tests(viewer, app):
    """Run comprehensive rotation button tests"""
    try:
        print("\n🔄 STARTING COMPREHENSIVE ROTATION TESTS...")
        print("=" * 60)
        
        # Load a STEP file first
        step_files = [f for f in os.listdir('.') if f.endswith('.step') or f.endswith('.STEP')]
        if not step_files:
            print("❌ No STEP files found in current directory")
            app.quit()
            return
            
        # Load first STEP file found
        step_file = step_files[0]
        print(f"📁 Loading STEP file: {step_file}")
        
        # Set active viewer to TOP
        viewer.active_viewer = "top"
        print(f"✅ Set active viewer to: {viewer.active_viewer}")
        
        # Load the file
        if hasattr(viewer, 'load_step_file_direct'):
            success = viewer.load_step_file_direct(step_file)
            if not success:
                print("❌ Failed to load STEP file")
                app.quit()
                return
        else:
            print("❌ load_step_file_direct method not found")
            app.quit()
            return
            
        # Wait for file to load, then test buttons
        QTimer.singleShot(2000, lambda: test_all_rotation_buttons(viewer, app))
        
    except Exception as e:
        print(f"❌ TEST SETUP FAILED: {e}")
        import traceback
        traceback.print_exc()
        app.quit()

def test_all_rotation_buttons(viewer, app):
    """Test all 6 rotation buttons systematically"""
    try:
        print("\n🎯 TESTING ALL 6 ROTATION BUTTONS")
        print("=" * 60)
        
        # Get renderer for TOP viewer
        if not hasattr(viewer, 'vtk_renderer_left') or not viewer.vtk_renderer_left:
            print("❌ TOP renderer not found")
            app.quit()
            return
            
        renderer = viewer.vtk_renderer_left
        
        # Test each rotation button
        rotation_tests = [
            ('X+', 'x', 15.0),
            ('X-', 'x', -15.0), 
            ('Y+', 'y', 15.0),
            ('Y-', 'y', -15.0),
            ('Z+', 'z', 15.0),
            ('Z-', 'z', -15.0)
        ]
        
        for button_name, axis, degrees in rotation_tests:
            print(f"\n🧪 TESTING {button_name} ROTATION BUTTON")
            print("-" * 40)
            
            # Capture BEFORE state
            before_state = capture_all_actor_states(renderer, f"BEFORE {button_name}")
            
            # Execute rotation
            print(f"🔄 Executing rotation: {axis} axis, {degrees}°")
            viewer.rotate_shape(axis, degrees)
            
            # Small delay to ensure rotation completes
            QTimer.singleShot(100, lambda: None)
            
            # Capture AFTER state  
            after_state = capture_all_actor_states(renderer, f"AFTER {button_name}")
            
            # Compare states
            compare_actor_states(before_state, after_state, button_name)
            
            print(f"✅ {button_name} rotation test completed")
            
        print("\n🎯 ALL ROTATION TESTS COMPLETED")
        print("=" * 60)
        
        # Exit after tests
        QTimer.singleShot(2000, app.quit)
        
    except Exception as e:
        print(f"❌ ROTATION TESTS FAILED: {e}")
        import traceback
        traceback.print_exc()
        app.quit()

def capture_all_actor_states(renderer, label):
    """Capture complete state of all actors in renderer"""
    print(f"\n📊 {label} - CAPTURING ALL ACTOR STATES:")
    
    states = {}
    actor_count = 0
    
    try:
        if hasattr(renderer, 'renderer') and renderer.renderer:
            actors = renderer.renderer.GetActors()
            actors.InitTraversal()
            
            for i in range(actors.GetNumberOfItems()):
                actor = actors.GetNextActor()
                if actor:
                    actor_count += 1
                    
                    # Get position and orientation
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    
                    # Get color to identify actor type
                    prop = actor.GetProperty()
                    color = prop.GetColor() if prop else (0.0, 0.0, 0.0)
                    
                    # Classify actor by color
                    actor_type = classify_actor_by_color(color)
                    
                    # Store state
                    states[f"Actor_{i}_{actor_type}"] = {
                        'position': pos,
                        'orientation': orient,
                        'color': color
                    }
                    
                    print(f"   Actor_{i}_{actor_type}: Pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), Orient=({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
                    
        print(f"📊 Total actors captured: {actor_count}")
        
    except Exception as e:
        print(f"❌ Error capturing actor states: {e}")
        
    return states

def classify_actor_by_color(color):
    """Classify actor type by color"""
    r, g, b = color
    
    if r > 0.8 and g < 0.2 and b < 0.2:
        return "RED_WORLD_ORIGIN"
    elif r < 0.2 and g > 0.8 and b < 0.2:
        return "GREEN_PART_ORIGIN"  
    elif r > 0.8 and g > 0.8 and b < 0.2:
        return "YELLOW_ARROW"
    elif r < 0.2 and g < 0.2 and b > 0.8:
        return "BLUE_UNKNOWN"
    elif r > 0.5 and g > 0.5 and b > 0.5:
        return "WHITE_OR_GRAY"
    else:
        return "MODEL_OR_BBOX"

def compare_actor_states(before, after, button_name):
    """Compare before and after states to show what changed"""
    print(f"\n📈 {button_name} ROTATION ANALYSIS:")
    print("-" * 40)
    
    moved_actors = []
    stationary_actors = []
    
    for actor_id in before:
        if actor_id in after:
            before_pos = before[actor_id]['position']
            after_pos = after[actor_id]['position']
            
            # Calculate movement
            dx = after_pos[0] - before_pos[0]
            dy = after_pos[1] - before_pos[1] 
            dz = after_pos[2] - before_pos[2]
            movement = (dx**2 + dy**2 + dz**2)**0.5
            
            # Calculate orientation change
            before_orient = before[actor_id]['orientation']
            after_orient = after[actor_id]['orientation']
            orient_change = max(
                abs(after_orient[0] - before_orient[0]),
                abs(after_orient[1] - before_orient[1]),
                abs(after_orient[2] - before_orient[2])
            )
            
            if movement > 0.001 or orient_change > 0.1:
                moved_actors.append({
                    'id': actor_id,
                    'movement': movement,
                    'orient_change': orient_change,
                    'pos_change': (dx, dy, dz),
                    'orient_before': before_orient,
                    'orient_after': after_orient
                })
            else:
                stationary_actors.append(actor_id)
    
    # Report results
    print(f"✅ MOVED ACTORS ({len(moved_actors)}):")
    for actor in moved_actors:
        print(f"   {actor['id']}: Movement={actor['movement']:.3f}, Orient_Change={actor['orient_change']:.1f}°")
        print(f"      Position change: ({actor['pos_change'][0]:.3f}, {actor['pos_change'][1]:.3f}, {actor['pos_change'][2]:.3f})")
        print(f"      Orientation: {actor['orient_before']} → {actor['orient_after']}")
        
    print(f"❌ STATIONARY ACTORS ({len(stationary_actors)}):")
    for actor_id in stationary_actors:
        print(f"   {actor_id}: NO MOVEMENT")
        
    # Summary
    total_actors = len(before)
    print(f"\n📊 SUMMARY FOR {button_name}:")
    print(f"   Total actors: {total_actors}")
    print(f"   Moved correctly: {len(moved_actors)}")
    print(f"   Did not move: {len(stationary_actors)}")
    
    if len(stationary_actors) > 0:
        print(f"   ❌ PROBLEM: {len(stationary_actors)} actors did not move during rotation!")
    else:
        print(f"   ✅ SUCCESS: All actors moved during rotation!")

if __name__ == "__main__":
    comprehensive_rotation_test()
