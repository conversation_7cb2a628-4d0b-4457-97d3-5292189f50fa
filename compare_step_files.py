#!/usr/bin/env python3
"""
Compare STEP files to see the actual differences
"""

import sys
import os
import re

def compare_step_files(original_file, rotated_file):
    print(f"=== COMPARING STEP FILES ===")
    print(f"Original: {original_file}")
    print(f"Rotated:  {rotated_file}")
    
    if not os.path.exists(original_file):
        print(f"❌ Original file not found: {original_file}")
        return False
        
    if not os.path.exists(rotated_file):
        print(f"❌ Rotated file not found: {rotated_file}")
        return False
    
    # Read both files
    with open(original_file, 'r', encoding='utf-8', errors='ignore') as f:
        original_content = f.read()
    
    with open(rotated_file, 'r', encoding='utf-8', errors='ignore') as f:
        rotated_content = f.read()
    
    print(f"Original file size: {len(original_content)} characters")
    print(f"Rotated file size:  {len(rotated_content)} characters")
    
    # Check if files are identical
    if original_content == rotated_content:
        print("❌ Files are identical")
        return False
    else:
        print("✅ Files are different")
    
    # Extract DIRECTION vectors from both files
    print("\n=== DIRECTION VECTORS ===")
    
    # Find all DIRECTION entries
    direction_pattern = r'#(\d+) = DIRECTION\([^,]*,\(([^)]+)\)\);'
    
    orig_directions = re.findall(direction_pattern, original_content)
    rot_directions = re.findall(direction_pattern, rotated_content)
    
    print(f"Original file has {len(orig_directions)} DIRECTION vectors:")
    for i, (id_num, coords) in enumerate(orig_directions[:10]):  # Show first 10
        print(f"  #{id_num}: ({coords})")
    
    print(f"\nRotated file has {len(rot_directions)} DIRECTION vectors:")
    for i, (id_num, coords) in enumerate(rot_directions[:10]):  # Show first 10
        print(f"  #{id_num}: ({coords})")
    
    # Compare specific direction vectors
    print("\n=== DIRECTION VECTOR COMPARISON ===")
    changes_found = 0
    for i, ((orig_id, orig_coords), (rot_id, rot_coords)) in enumerate(zip(orig_directions, rot_directions)):
        if orig_coords != rot_coords:
            print(f"CHANGED #{orig_id}: {orig_coords} -> {rot_coords}")
            changes_found += 1
        elif i < 5:  # Show first 5 unchanged for reference
            print(f"SAME    #{orig_id}: {orig_coords}")
    
    if changes_found > 0:
        print(f"✅ Found {changes_found} changed direction vectors")
        return True
    else:
        print("❌ No direction vector changes found")
        return False

if __name__ == "__main__":
    # Use the files from our test
    original = "SOIC16P127_1270X940X610L89X51.STEP"
    rotated = "debug_save_output.step"
    
    success = compare_step_files(original, rotated)
    
    if success:
        print("\n🎉 STEP FILE ROTATION CONFIRMED!")
    else:
        print("\n💥 NO ROTATION DETECTED IN STEP FILE!")
    
    sys.exit(0 if success else 1)
