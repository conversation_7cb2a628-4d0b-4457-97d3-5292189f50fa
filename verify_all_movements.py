#!/usr/bin/env python3
"""
COMPREHENSIVE VERIFICATION: Capture exact positions before and after each operation
This MUST verify that all items move correctly and are in the right locations.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import <PERSON><PERSON><PERSON>werTDK
from PyQt5.QtWidgets import QApplication
import time

def verify_all_movements():
    """Verify all movements with before/after position capture"""
    
    print("🔍 COMPREHENSIVE MOVEMENT VERIFICATION")
    print("="*100)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for UI to initialize
    app.processEvents()
    time.sleep(1)
    
    def capture_complete_state(label):
        """Capture EXACT positions of ALL items"""
        print(f"\n{'='*100}")
        print(f"📍 {label}")
        print(f"{'='*100}")
        
        state = {
            'tracking_vars': {},
            'all_actors': [],
            'green_ball': None,
            'model_actors': [],
            'world_origin_actors': [],
            'display_numbers': {}
        }
        
        # 1. TRACKING VARIABLES
        print(f"📊 TRACKING VARIABLES:")
        if hasattr(viewer, 'current_pos_left'):
            state['tracking_vars']['current_pos_left'] = viewer.current_pos_left.copy()
            print(f"   current_pos_left: {viewer.current_pos_left}")
        
        if hasattr(viewer, 'current_rot_left'):
            state['tracking_vars']['current_rot_left'] = viewer.current_rot_left.copy()
            print(f"   current_rot_left: {viewer.current_rot_left}")
        
        if hasattr(viewer, 'orig_pos_left'):
            state['tracking_vars']['orig_pos_left'] = viewer.orig_pos_left.copy()
            print(f"   orig_pos_left: {viewer.orig_pos_left}")
        
        # 2. ALL ACTORS - EXACT POSITIONS
        print(f"\n📦 ALL ACTORS - EXACT POSITIONS:")
        if hasattr(viewer, 'vtk_renderer_left') and hasattr(viewer.vtk_renderer_left, 'renderer'):
            renderer = viewer.vtk_renderer_left.renderer
            actors = renderer.GetActors()
            actors.InitTraversal()
            
            for i in range(actors.GetNumberOfItems()):
                actor = actors.GetNextActor()
                if actor:
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    bounds = actor.GetBounds()
                    
                    # Get color
                    try:
                        prop = actor.GetProperty()
                        color = prop.GetColor()
                        opacity = prop.GetOpacity()
                    except:
                        color = (0, 0, 0)
                        opacity = 1.0
                    
                    # Calculate size
                    size_x = bounds[1] - bounds[0] if bounds[1] > bounds[0] else 0
                    size_y = bounds[3] - bounds[2] if bounds[3] > bounds[2] else 0
                    size_z = bounds[5] - bounds[4] if bounds[5] > bounds[4] else 0
                    max_size = max(size_x, size_y, size_z)
                    
                    actor_info = {
                        'id': i,
                        'position': pos,
                        'orientation': orient,
                        'bounds': bounds,
                        'color': color,
                        'opacity': opacity,
                        'size': max_size
                    }
                    
                    # Classify actor
                    if max_size > 5.0:
                        actor_info['type'] = 'MODEL'
                        state['model_actors'].append(actor_info)
                        print(f"   MODEL {i}: pos=({pos[0]:8.3f}, {pos[1]:8.3f}, {pos[2]:8.3f}) orient=({orient[0]:6.1f}°, {orient[1]:6.1f}°, {orient[2]:6.1f}°) size={max_size:.3f}")
                    elif abs(pos[0]) < 0.1 and abs(pos[1]) < 0.1 and abs(pos[2]) < 0.1:
                        actor_info['type'] = 'WORLD_ORIGIN'
                        state['world_origin_actors'].append(actor_info)
                        print(f"   WORLD {i}: pos=({pos[0]:8.3f}, {pos[1]:8.3f}, {pos[2]:8.3f}) orient=({orient[0]:6.1f}°, {orient[1]:6.1f}°, {orient[2]:6.1f}°)")
                    elif color[1] > 0.8 and color[0] < 0.3 and color[2] < 0.3:  # Green
                        actor_info['type'] = 'GREEN_BALL'
                        state['green_ball'] = actor_info
                        print(f"   GREEN {i}: pos=({pos[0]:8.3f}, {pos[1]:8.3f}, {pos[2]:8.3f}) orient=({orient[0]:6.1f}°, {orient[1]:6.1f}°, {orient[2]:6.1f}°) size={max_size:.3f}")
                    else:
                        actor_info['type'] = 'OTHER'
                        print(f"   OTHER {i}: pos=({pos[0]:8.3f}, {pos[1]:8.3f}, {pos[2]:8.3f}) color=({color[0]:.3f}, {color[1]:.3f}, {color[2]:.3f}) size={max_size:.3f}")
                    
                    state['all_actors'].append(actor_info)
        
        # 3. CHECK GREEN BALL EXISTENCE
        print(f"\n🟢 GREEN BALL CHECK:")
        if hasattr(viewer, 'vtk_renderer_left') and hasattr(viewer.vtk_renderer_left, 'part_origin_sphere'):
            if viewer.vtk_renderer_left.part_origin_sphere:
                green_ball = viewer.vtk_renderer_left.part_origin_sphere
                pos = green_ball.GetPosition()
                orient = green_ball.GetOrientation()
                print(f"   ✅ part_origin_sphere EXISTS: pos=({pos[0]:8.3f}, {pos[1]:8.3f}, {pos[2]:8.3f}) orient=({orient[0]:6.1f}°, {orient[1]:6.1f}°, {orient[2]:6.1f}°)")
                
                # Check if it's in the renderer
                renderer_actors = viewer.vtk_renderer_left.renderer.GetActors()
                renderer_actors.InitTraversal()
                found_in_renderer = False
                
                for i in range(renderer_actors.GetNumberOfItems()):
                    actor = renderer_actors.GetNextActor()
                    if actor == green_ball:
                        found_in_renderer = True
                        print(f"   ✅ Green ball IS in renderer (actor {i})")
                        break
                
                if not found_in_renderer:
                    print(f"   ❌ Green ball is NOT in renderer!")
            else:
                print(f"   ❌ part_origin_sphere is None")
        else:
            print(f"   ❌ part_origin_sphere doesn't exist")
        
        # 4. CALCULATE MODEL CENTER
        if state['model_actors']:
            min_x = min(actor['bounds'][0] for actor in state['model_actors'])
            max_x = max(actor['bounds'][1] for actor in state['model_actors'])
            min_y = min(actor['bounds'][2] for actor in state['model_actors'])
            max_y = max(actor['bounds'][3] for actor in state['model_actors'])
            min_z = min(actor['bounds'][4] for actor in state['model_actors'])
            max_z = max(actor['bounds'][5] for actor in state['model_actors'])
            
            model_center = ((min_x + max_x) / 2, (min_y + max_y) / 2, (min_z + max_z) / 2)
            state['model_center'] = model_center
            print(f"\n🎯 CALCULATED MODEL CENTER: ({model_center[0]:8.3f}, {model_center[1]:8.3f}, {model_center[2]:8.3f})")
        
        return state
    
    def verify_movement(before_state, after_state, operation):
        """Verify that movement happened correctly"""
        print(f"\n🔍 MOVEMENT VERIFICATION: {operation}")
        print(f"{'='*100}")
        
        issues = []
        
        # 1. VERIFY MODEL ACTORS MOVED/ROTATED
        print(f"📦 MODEL ACTOR VERIFICATION:")
        if len(before_state['model_actors']) != len(after_state['model_actors']):
            issues.append(f"Model actor count changed: {len(before_state['model_actors'])} → {len(after_state['model_actors'])}")
        
        for i, (before_actor, after_actor) in enumerate(zip(before_state['model_actors'], after_state['model_actors'])):
            pos_change = (
                after_actor['position'][0] - before_actor['position'][0],
                after_actor['position'][1] - before_actor['position'][1],
                after_actor['position'][2] - before_actor['position'][2]
            )
            pos_distance = (pos_change[0]**2 + pos_change[1]**2 + pos_change[2]**2)**0.5
            
            orient_change = (
                after_actor['orientation'][0] - before_actor['orientation'][0],
                after_actor['orientation'][1] - before_actor['orientation'][1],
                after_actor['orientation'][2] - before_actor['orientation'][2]
            )
            
            print(f"   Model {i}: pos_change=({pos_change[0]:8.3f}, {pos_change[1]:8.3f}, {pos_change[2]:8.3f}) orient_change=({orient_change[0]:6.1f}°, {orient_change[1]:6.1f}°, {orient_change[2]:6.1f}°)")
            
            if "rotation" in operation.lower():
                if abs(orient_change[0]) < 0.1 and abs(orient_change[1]) < 0.1 and abs(orient_change[2]) < 0.1:
                    issues.append(f"Model actor {i} did not rotate during {operation}")
            
        # 2. VERIFY GREEN BALL FOLLOWS MODEL
        print(f"\n🟢 GREEN BALL VERIFICATION:")
        if before_state['green_ball'] and after_state['green_ball']:
            before_green_pos = before_state['green_ball']['position']
            after_green_pos = after_state['green_ball']['position']
            
            green_movement = (
                after_green_pos[0] - before_green_pos[0],
                after_green_pos[1] - before_green_pos[1],
                after_green_pos[2] - before_green_pos[2]
            )
            green_distance = (green_movement[0]**2 + green_movement[1]**2 + green_movement[2]**2)**0.5
            
            print(f"   Green ball moved: ({green_movement[0]:8.3f}, {green_movement[1]:8.3f}, {green_movement[2]:8.3f}) distance={green_distance:.6f}")
            
            # Check if green ball is at model center
            if 'model_center' in after_state:
                model_center = after_state['model_center']
                green_vs_model = (
                    after_green_pos[0] - model_center[0],
                    after_green_pos[1] - model_center[1],
                    after_green_pos[2] - model_center[2]
                )
                center_distance = (green_vs_model[0]**2 + green_vs_model[1]**2 + green_vs_model[2]**2)**0.5
                
                print(f"   Green ball vs model center: distance={center_distance:.6f}")
                
                if center_distance > 0.1:
                    issues.append(f"Green ball is {center_distance:.6f} units away from model center")
                else:
                    print(f"   ✅ Green ball is correctly positioned at model center")
        
        elif before_state['green_ball'] and not after_state['green_ball']:
            issues.append("Green ball disappeared during operation")
        elif not before_state['green_ball'] and after_state['green_ball']:
            print(f"   ✅ Green ball appeared during operation")
        else:
            issues.append("Green ball was missing both before and after operation")
        
        # 3. VERIFY WORLD ORIGIN ARROWS
        print(f"\n🌍 WORLD ORIGIN VERIFICATION:")
        if len(before_state['world_origin_actors']) != len(after_state['world_origin_actors']):
            issues.append(f"World origin actor count changed: {len(before_state['world_origin_actors'])} → {len(after_state['world_origin_actors'])}")
        
        for i, (before_world, after_world) in enumerate(zip(before_state['world_origin_actors'], after_state['world_origin_actors'])):
            pos_change = (
                after_world['position'][0] - before_world['position'][0],
                after_world['position'][1] - before_world['position'][1],
                after_world['position'][2] - before_world['position'][2]
            )
            pos_distance = (pos_change[0]**2 + pos_change[1]**2 + pos_change[2]**2)**0.5
            
            orient_change = (
                after_world['orientation'][0] - before_world['orientation'][0],
                after_world['orientation'][1] - before_world['orientation'][1],
                after_world['orientation'][2] - before_world['orientation'][2]
            )
            
            print(f"   World {i}: pos_change=({pos_change[0]:8.3f}, {pos_change[1]:8.3f}, {pos_change[2]:8.3f}) orient_change=({orient_change[0]:6.1f}°, {orient_change[1]:6.1f}°, {orient_change[2]:6.1f}°)")
            
            if pos_distance > 0.001:
                issues.append(f"World origin actor {i} moved {pos_distance:.6f} units (should stay at 0,0,0)")
            
            if "rotation" in operation.lower():
                if abs(orient_change[0]) < 0.1 and abs(orient_change[1]) < 0.1 and abs(orient_change[2]) < 0.1:
                    issues.append(f"World origin actor {i} did not rotate during {operation}")
        
        # 4. VERIFY TRACKING VARIABLES
        print(f"\n📊 TRACKING VARIABLE VERIFICATION:")
        if 'current_pos_left' in before_state['tracking_vars'] and 'current_pos_left' in after_state['tracking_vars']:
            before_tracked = before_state['tracking_vars']['current_pos_left']
            after_tracked = after_state['tracking_vars']['current_pos_left']
            
            tracked_change = (
                after_tracked['x'] - before_tracked['x'],
                after_tracked['y'] - before_tracked['y'],
                after_tracked['z'] - before_tracked['z']
            )
            tracked_distance = (tracked_change[0]**2 + tracked_change[1]**2 + tracked_change[2]**2)**0.5
            
            print(f"   Tracked position changed: ({tracked_change[0]:8.3f}, {tracked_change[1]:8.3f}, {tracked_change[2]:8.3f}) distance={tracked_distance:.6f}")
            
            # Check if tracked position matches model center
            if 'model_center' in after_state:
                model_center = after_state['model_center']
                tracked_vs_model = (
                    after_tracked['x'] - model_center[0],
                    after_tracked['y'] - model_center[1],
                    after_tracked['z'] - model_center[2]
                )
                tracking_distance = (tracked_vs_model[0]**2 + tracked_vs_model[1]**2 + tracked_vs_model[2]**2)**0.5
                
                print(f"   Tracked vs model center: distance={tracking_distance:.6f}")
                
                if tracking_distance > 0.1:
                    issues.append(f"Tracked position is {tracking_distance:.6f} units away from actual model center")
                else:
                    print(f"   ✅ Tracked position matches actual model center")
        
        # 5. SUMMARY
        print(f"\n📋 VERIFICATION SUMMARY:")
        if issues:
            print(f"   ❌ {len(issues)} ISSUES FOUND:")
            for issue in issues:
                print(f"      • {issue}")
        else:
            print(f"   ✅ ALL VERIFICATIONS PASSED")
        
        return len(issues) == 0
    
    print(f"🔍 Please load a STEP file, then press Enter...")
    input("Press Enter when STEP file is loaded...")
    
    # Capture initial state
    initial_state = capture_complete_state("INITIAL STATE AFTER LOADING")
    
    input("\nPress Enter to test X+ rotation...")
    
    # Test X+ rotation
    print(f"\n🔄 Testing X+ 15° rotation...")
    before_x = capture_complete_state("BEFORE X+ ROTATION")
    
    viewer.rotate_shape('x', 15.0)
    app.processEvents()
    time.sleep(1)
    
    after_x = capture_complete_state("AFTER X+ ROTATION")
    x_success = verify_movement(before_x, after_x, "X+ 15° rotation")
    
    input("\nPress Enter to test Y+ rotation...")
    
    # Test Y+ rotation
    print(f"\n🔄 Testing Y+ 15° rotation...")
    before_y = capture_complete_state("BEFORE Y+ ROTATION")
    
    viewer.rotate_shape('y', 15.0)
    app.processEvents()
    time.sleep(1)
    
    after_y = capture_complete_state("AFTER Y+ ROTATION")
    y_success = verify_movement(before_y, after_y, "Y+ 15° rotation")
    
    print(f"\n🔍 FINAL VERIFICATION RESULTS:")
    print(f"{'='*100}")
    print(f"X+ rotation: {'✅ PASSED' if x_success else '❌ FAILED'}")
    print(f"Y+ rotation: {'✅ PASSED' if y_success else '❌ FAILED'}")
    print(f"Overall: {'✅ ALL TESTS PASSED' if x_success and y_success else '❌ SOME TESTS FAILED'}")
    
    print(f"\nPress Ctrl+C to exit")
    
    try:
        app.exec_()
    except KeyboardInterrupt:
        print("Exiting...")

if __name__ == "__main__":
    verify_all_movements()
