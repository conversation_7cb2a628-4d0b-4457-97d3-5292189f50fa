#!/usr/bin/env python3
"""
Test mouse events by writing to a file
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

def test_mouse_with_file():
    """Test mouse events by writing debug info to a file"""
    
    # Clear the debug file
    with open("mouse_debug.txt", "w") as f:
        f.write("🔍 MOUSE DEBUG TEST STARTED\n")
        f.write("=" * 50 + "\n")
    
    app = QApplication(sys.argv)
    
    # Import and patch the mouse event methods to write to file
    from step_viewer import StepViewerTDK
    
    # Create viewer
    viewer = StepViewerTDK()
    
    # Patch the mouse methods to write to file
    original_setup = viewer.setup_vtk_observers
    
    def patched_setup():
        with open("mouse_debug.txt", "a") as f:
            f.write("📦 setup_vtk_observers called\n")
        
        result = original_setup()
        
        # Patch the interaction style methods
        if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
            if hasattr(viewer.vtk_renderer_left, 'interactor') and viewer.vtk_renderer_left.interactor:
                style = viewer.vtk_renderer_left.interactor.GetInteractorStyle()
                if style:
                    with open("mouse_debug.txt", "a") as f:
                        f.write(f"✅ Found interaction style: {type(style)}\n")
                    
                    # Patch the mouse methods
                    original_left_down = style.OnLeftButtonDown
                    original_left_up = style.OnLeftButtonUp
                    original_mouse_move = style.OnMouseMove
                    
                    def patched_left_down():
                        with open("mouse_debug.txt", "a") as f:
                            f.write("🖱️ LEFT BUTTON DOWN detected!\n")
                        return original_left_down()
                    
                    def patched_left_up():
                        with open("mouse_debug.txt", "a") as f:
                            f.write("🖱️ LEFT BUTTON UP detected!\n")
                        return original_left_up()
                    
                    def patched_mouse_move():
                        with open("mouse_debug.txt", "a") as f:
                            f.write("🖱️ MOUSE MOVE detected!\n")
                        return original_mouse_move()
                    
                    style.OnLeftButtonDown = patched_left_down
                    style.OnLeftButtonUp = patched_left_up
                    style.OnMouseMove = patched_mouse_move
                    
                    with open("mouse_debug.txt", "a") as f:
                        f.write("✅ Mouse methods patched for file logging\n")
        
        return result
    
    viewer.setup_vtk_observers = patched_setup
    
    viewer.show()
    
    with open("mouse_debug.txt", "a") as f:
        f.write("✅ Viewer shown - try clicking and dragging\n")
        f.write("Check this file for mouse event logs\n")
        f.write("=" * 50 + "\n")
    
    # Run the application
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        with open("mouse_debug.txt", "a") as f:
            f.write("👋 Test completed\n")

if __name__ == "__main__":
    test_mouse_with_file()
