#!/usr/bin/env python3
"""
Test the Origin Rotation Fix
============================
This script automatically tests if the origin rotation fix works.
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from step_viewer import StepViewerTDK
except ImportError as e:
    print(f"Import error: {e}")
    sys.exit(1)

def test_origin_rotation_fix():
    """Test if the origin rotation fix works"""
    print("🧪 TESTING ORIGIN ROTATION FIX")
    print("=" * 50)
    
    # Create Qt application
    app = QApplication([])
    
    # Create step viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    # Set active viewer
    viewer.active_viewer = "top"
    
    # Find a STEP file to test with
    step_files = [f for f in os.listdir('.') if f.endswith(('.step', '.stp', '.STEP', '.STP'))]
    if not step_files:
        print("❌ No STEP files found for testing")
        app.quit()
        return
    
    step_file = step_files[0]
    print(f"📁 Testing with: {step_file}")
    
    # Load the file
    try:
        if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
            if hasattr(viewer.vtk_renderer_left, 'step_loader'):
                loader = viewer.vtk_renderer_left.step_loader
                success, message = loader.load_step_file(step_file)
                if success:
                    print(f"✅ File loaded: {message}")
                    
                    # Create origin overlay
                    viewer.vtk_renderer_left.create_origin_overlay()
                    print("✅ Origin overlay created")
                    
                    # Wait a moment for everything to initialize
                    time.sleep(1)
                    
                    # Test button rotation
                    print("\n🔘 TESTING BUTTON ROTATION")
                    print("-" * 30)
                    
                    # Get initial origin orientations
                    initial_orientations = get_origin_orientations(viewer)
                    print(f"📊 Initial orientations: {len(initial_orientations)} actors")
                    
                    # Apply button rotation
                    print("🔄 Applying Y+15° rotation via button...")
                    viewer.rotate_shape('y', 15.0)
                    
                    # Wait for rotation to complete
                    time.sleep(0.5)
                    
                    # Get new orientations
                    new_orientations = get_origin_orientations(viewer)
                    print(f"📊 After rotation: {len(new_orientations)} actors")
                    
                    # Check if orientations changed
                    if len(initial_orientations) > 0 and len(new_orientations) > 0:
                        orientations_changed = initial_orientations != new_orientations
                        print(f"🔍 Orientations changed: {orientations_changed}")
                        
                        if orientations_changed:
                            print("✅ SUCCESS: Button rotation rotates origin markers!")
                            print("🎯 FIX VERIFIED: Origin markers now rotate with buttons")
                        else:
                            print("❌ FAILED: Button rotation still doesn't rotate origin markers")
                            print("🔧 Fix may need adjustment")
                    else:
                        print("⚠️  No origin actors found to test")
                    
                else:
                    print(f"❌ File load failed: {message}")
            else:
                print("❌ No step loader found")
        else:
            print("❌ No renderer found")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Close after test
    QTimer.singleShot(2000, app.quit)  # Close after 2 seconds
    app.exec_()

def get_origin_orientations(viewer):
    """Get current orientations of all origin actors"""
    orientations = []
    try:
        if hasattr(viewer, 'vtk_renderer_left'):
            renderer = viewer.vtk_renderer_left
            if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
                for actor in renderer.origin_actors:
                    if actor:
                        orientations.append(actor.GetOrientation())
                        print(f"   Actor orientation: {actor.GetOrientation()}")
    except Exception as e:
        print(f"Error getting orientations: {e}")
    return orientations

if __name__ == "__main__":
    test_origin_rotation_fix()
