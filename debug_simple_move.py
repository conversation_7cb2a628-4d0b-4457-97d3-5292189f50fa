#!/usr/bin/env python3
"""
Simple debug test to isolate the duplicate arrow creation issue.
This test focuses only on the VTK movement operations without GUI complexity.
"""

import sys
import os
import glob
from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from step_viewer import <PERSON><PERSON>iewer<PERSON>D<PERSON>

def debug_simple_move():
    """Simple test to isolate duplicate arrow creation during movement"""
    print("🔍 SIMPLE MOVE DEBUG TEST")
    print("=" * 50)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create viewer
    viewer = StepViewerTDK()
    
    # Find and load a STEP file
    step_files = glob.glob("*.step") + glob.glob("*.stp") + glob.glob("*.STEP") + glob.glob("*.STP")
    
    if step_files:
        step_file = step_files[0]
        print(f"📁 Loading STEP file: {step_file}")
        
        # Set active viewer to top
        viewer.active_viewer = "top"
        
        # Load the STEP file
        success = viewer.load_step_file_direct(step_file)
        QTest.qWait(3000)  # Wait for loading
        
        if success:
            print("✅ STEP file loaded successfully")
            
            # Create origin overlays
            viewer.create_origin_overlay()
            QTest.qWait(1000)
            
            print("✅ Origin overlays created")
            
            # Count actors BEFORE movement
            renderer = viewer.vtk_renderer_left
            if renderer and renderer.renderer:
                all_actors = renderer.renderer.GetActors()
                all_actors.InitTraversal()
                
                before_count = 0
                before_positions = []
                
                while True:
                    actor = all_actors.GetNextActor()
                    if not actor:
                        break
                    before_count += 1
                    pos = actor.GetPosition()
                    before_positions.append(pos)
                
                print(f"📊 BEFORE: {before_count} total actors")
                for i, pos in enumerate(before_positions):
                    print(f"   Actor {i+1}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
                
                # Perform ONLY the VTK movement operation (no text updates, no display updates)
                print("\n🔄 PERFORMING SIMPLE VTK MOVEMENT...")
                print("-" * 30)
                
                # Get the renderer
                vtk_renderer = viewer.vtk_renderer_left
                
                # Move ONLY the origin actors using AddPosition
                if hasattr(vtk_renderer, 'origin_actors') and vtk_renderer.origin_actors:
                    print(f"🎯 Moving {len(vtk_renderer.origin_actors)} origin actors by X+2.0mm")
                    for i, actor in enumerate(vtk_renderer.origin_actors):
                        old_pos = actor.GetPosition()
                        print(f"   Actor {i+1} BEFORE: pos=({old_pos[0]:.3f}, {old_pos[1]:.3f}, {old_pos[2]:.3f})")
                        
                        # This is the exact operation that might be causing duplicates
                        actor.AddPosition(2.0, 0, 0)
                        
                        new_pos = actor.GetPosition()
                        print(f"   Actor {i+1} AFTER:  pos=({new_pos[0]:.3f}, {new_pos[1]:.3f}, {new_pos[2]:.3f})")
                else:
                    print("❌ No origin actors found")
                
                # Render the scene
                if vtk_renderer.render_window:
                    vtk_renderer.render_window.Render()
                
                QTest.qWait(1000)  # Wait for render
                
                # Count actors AFTER movement
                all_actors = renderer.renderer.GetActors()
                all_actors.InitTraversal()
                
                after_count = 0
                after_positions = []
                
                while True:
                    actor = all_actors.GetNextActor()
                    if not actor:
                        break
                    after_count += 1
                    pos = actor.GetPosition()
                    after_positions.append(pos)
                
                print(f"\n📊 AFTER: {after_count} total actors")
                for i, pos in enumerate(after_positions):
                    print(f"   Actor {i+1}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
                
                # Analysis
                print(f"\n🔍 ANALYSIS:")
                print(f"   Before: {before_count} actors")
                print(f"   After:  {after_count} actors")
                print(f"   Change: {after_count - before_count} actors")
                
                if after_count > before_count:
                    print("🚨 DUPLICATE ACTORS DETECTED!")
                    print("🔧 The issue is in the VTK AddPosition() operation itself")
                else:
                    print("✅ No duplicate actors - VTK movement is working correctly")
                
        else:
            print("❌ STEP file loading failed")
    else:
        print("❌ No STEP files found")
    
    # Close application properly
    print("\n🔚 CLOSING APPLICATION...")
    try:
        if hasattr(viewer, 'close'):
            viewer.close()
        if hasattr(app, 'quit'):
            app.quit()
        if hasattr(app, 'exit'):
            app.exit(0)
    except Exception as e:
        print(f"⚠️  Error during cleanup: {e}")
    
    print("✅ Simple debug test completed")

if __name__ == "__main__":
    debug_simple_move()
    sys.exit(0)
