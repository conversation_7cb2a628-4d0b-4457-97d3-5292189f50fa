#!/usr/bin/env python3
"""
Find VTK rotation centers for model and green ball
Focus ONLY on VTK rotation center properties
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

print("🎯 FINDING VTK ROTATION CENTERS")

# Import the viewer
from step_viewer import StepViewerTDK

def examine_vtk_rotation_centers():
    """Examine VTK rotation center properties for model and green ball"""
    print("\n" + "="*80)
    print("🎯 EXAMINING VTK ROTATION CENTERS")
    print("="*80)
    
    app = QApplication(sys.argv)
    
    # Create viewer and load STEP file
    viewer = StepViewerTDK()
    viewer.show()
    app.processEvents()
    time.sleep(2)
    
    # Load STEP file
    step_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if os.path.exists(step_file):
        viewer.active_viewer = "top"
        viewer.load_step_file_direct(step_file)
        app.processEvents()
        time.sleep(3)
        
        renderer = viewer.vtk_renderer_left
        
        print("\n🔍 VTK ROTATION CENTER ANALYSIS:")
        
        # Examine model actors
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            print(f"\n📦 MODEL ACTORS ({len(renderer.step_actors)}):")
            for i, actor in enumerate(renderer.step_actors):
                print(f"\n   Actor {i} VTK Rotation Properties:")
                print(f"      Position: {actor.GetPosition()}")
                print(f"      Origin: {actor.GetOrigin()}")  # THIS IS THE ROTATION CENTER
                print(f"      Orientation: {actor.GetOrientation()}")
                print(f"      Scale: {actor.GetScale()}")
                
                # Check transformation matrices
                user_matrix = actor.GetUserMatrix()
                if user_matrix:
                    print(f"      UserMatrix: EXISTS")
                    for row in range(4):
                        matrix_row = [user_matrix.GetElement(row, col) for col in range(4)]
                        print(f"         Row {row}: {matrix_row}")
                else:
                    print(f"      UserMatrix: None")
                
                # Check user transform
                user_transform = actor.GetUserTransform()
                if user_transform:
                    print(f"      UserTransform: EXISTS")
                else:
                    print(f"      UserTransform: None")
        
        # Examine green ball
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            actor = renderer.part_origin_sphere
            print(f"\n🟢 GREEN BALL VTK Rotation Properties:")
            print(f"      Position: {actor.GetPosition()}")
            print(f"      Origin: {actor.GetOrigin()}")  # THIS IS THE ROTATION CENTER
            print(f"      Orientation: {actor.GetOrientation()}")
            print(f"      Scale: {actor.GetScale()}")
            
            # Check transformation matrices
            user_matrix = actor.GetUserMatrix()
            if user_matrix:
                print(f"      UserMatrix: EXISTS")
                for row in range(4):
                    matrix_row = [user_matrix.GetElement(row, col) for col in range(4)]
                    print(f"         Row {row}: {matrix_row}")
            else:
                print(f"      UserMatrix: None")
            
            # Check user transform
            user_transform = actor.GetUserTransform()
            if user_transform:
                print(f"      UserTransform: EXISTS")
            else:
                print(f"      UserTransform: None")
        
        print("\n🎯 KEY FINDING:")
        print("   The VTK rotation center is controlled by actor.GetOrigin()")
        print("   If model and green ball have different Origins, they rotate around different centers")
        
        # Test setting same origin
        print("\n🔧 TESTING: Setting green ball origin to match model origin...")
        if hasattr(renderer, 'step_actors') and renderer.step_actors and hasattr(renderer, 'part_origin_sphere'):
            model_origin = renderer.step_actors[0].GetOrigin()
            green_ball_origin = renderer.part_origin_sphere.GetOrigin()
            
            print(f"   Model origin: {model_origin}")
            print(f"   Green ball origin BEFORE: {green_ball_origin}")
            
            # Set green ball origin to match model
            renderer.part_origin_sphere.SetOrigin(model_origin[0], model_origin[1], model_origin[2])
            
            green_ball_origin_after = renderer.part_origin_sphere.GetOrigin()
            print(f"   Green ball origin AFTER: {green_ball_origin_after}")
            
            print("   ✅ Green ball origin now matches model origin")
    
    # Exit after 5 seconds
    QTimer.singleShot(5000, app.quit)
    app.exec_()

if __name__ == "__main__":
    examine_vtk_rotation_centers()
