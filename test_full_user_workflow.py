#!/usr/bin/env python3
"""
Complete automated test that simulates the exact user workflow
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== COMPLETE USER WORKFLOW TEST ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"🔧 Testing with: {test_file}")
    
    print("\n=== STEP 1: LOAD STEP FILE ===")
    # Simulate clicking "Open STEP File" button
    viewer.active_viewer = "top"  # Make sure we're in top viewer
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ STEP file loaded in TOP viewer")
        print(f"   Initial position: {viewer.current_pos_left}")
        print(f"   Initial rotation: {viewer.current_rot_left}")
        
        # Check if yellow text is showing correct values
        if hasattr(viewer, 'combined_text_actor_left'):
            initial_text = viewer.combined_text_actor_left.GetInput()
            print(f"   Initial yellow text: {initial_text}")
        
        print("\n=== STEP 2: APPLY ROTATIONS ===")
        # Simulate clicking rotation buttons (like user would do)
        try:
            print("🔄 Applying X+15° rotation (simulating button click)")
            viewer._apply_model_rotation("top", "x", 15.0)

            print("🔄 Applying Y+10° rotation (simulating button click)")
            viewer._apply_model_rotation("top", "y", 10.0)
            print("✅ Rotations applied successfully")
        except Exception as e:
            print(f"❌ Exception during rotation: {e}")
            import traceback
            traceback.print_exc()
        
        print(f"   After rotations - position: {viewer.current_pos_left}")
        print(f"   After rotations - rotation: {viewer.current_rot_left}")
        
        # Check if yellow text updated
        if hasattr(viewer, 'combined_text_actor_left'):
            rotated_text = viewer.combined_text_actor_left.GetInput()
            print(f"   Updated yellow text: {rotated_text}")
            
            if initial_text != rotated_text:
                print("✅ Yellow text UPDATED correctly after rotation")
            else:
                print("❌ Yellow text DID NOT UPDATE after rotation")
        
        print("\n=== STEP 3: SAVE WITH GREEN BUTTON (OPTION 1) ===")
        # Simulate clicking the green "Save with Transformations" button
        test_save_file = "test_user_workflow_save.step"
        
        # Remove existing file if it exists
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
            
        print(f"🔧 Saving to: {test_save_file}")

        # Get the values that would be used for saving
        print("🔧 About to get save values...")
        try:
            current_pos = viewer._extract_position_from_display("top")
            current_rot = viewer._extract_rotation_from_vtk_actor("top")
            orig_pos = viewer.orig_pos_left
            orig_rot = viewer.orig_rot_left
            
            print(f"   Save values:")
            print(f"     current_pos: {current_pos}")
            print(f"     current_rot: {current_rot}")
            print(f"     orig_pos: {orig_pos}")
            print(f"     orig_rot: {orig_rot}")
            
            # Call the actual save method (simulating green button click)
            loader = viewer.step_loader_left
            print(f"🔧 About to call _save_step_with_transformations")
            print(f"   Method exists: {hasattr(viewer, '_save_step_with_transformations')}")

            try:
                save_success = viewer._save_step_with_transformations(
                    test_save_file, loader, current_pos, current_rot, orig_pos, orig_rot
                )
                print(f"🔧 Method returned: {save_success}")
            except Exception as e:
                print(f"❌ Exception in save method: {e}")
                import traceback
                traceback.print_exc()
                save_success = False
            
            if save_success and os.path.exists(test_save_file):
                print("✅ Save file created successfully")
                file_size = os.path.getsize(test_save_file)
                print(f"   Saved file size: {file_size:,} bytes")
            else:
                print("❌ Save failed or file not created")
                print("=== TEST FAILED ===")
                app.quit()
                exit(1)
                
        except Exception as e:
            print(f"❌ Save process failed: {e}")
            import traceback
            traceback.print_exc()
            print("=== TEST FAILED ===")
            app.quit()
            exit(1)
        
        print("\n=== STEP 4: LOAD SAVED FILE IN BOTTOM VIEWER ===")
        # Simulate switching to bottom viewer and loading the saved file
        viewer.active_viewer = "bottom"
        print(f"🔧 Switching to BOTTOM viewer")
        print(f"🔧 Loading saved file: {test_save_file}")
        
        load_success = viewer.load_step_file_direct(test_save_file)
        
        if load_success:
            print("✅ Saved file loaded in BOTTOM viewer")
            print(f"   Loaded position: {viewer.current_pos_right}")
            print(f"   Loaded rotation: {viewer.current_rot_right}")
            
            # Check if yellow text shows the saved values
            if hasattr(viewer, 'combined_text_actor_right'):
                loaded_text = viewer.combined_text_actor_right.GetInput()
                print(f"   Loaded yellow text: {loaded_text}")
        else:
            print("❌ Failed to load saved file")
            print("=== TEST FAILED ===")
            app.quit()
            exit(1)
        
        print("\n=== STEP 5: COMPARE RESULTS ===")
        print("🔍 Comparing what was saved vs what was loaded:")
        
        # Compare positions
        pos_match = (
            abs(current_pos['x'] - viewer.current_pos_right['x']) < 0.01 and
            abs(current_pos['y'] - viewer.current_pos_right['y']) < 0.01 and
            abs(current_pos['z'] - viewer.current_pos_right['z']) < 0.01
        )
        
        # Compare rotations (allowing for small floating point differences)
        rot_match = (
            abs(current_rot['x'] - viewer.current_rot_right['x']) < 1.0 and
            abs(current_rot['y'] - viewer.current_rot_right['y']) < 1.0 and
            abs(current_rot['z'] - viewer.current_rot_right['z']) < 1.0
        )
        
        print(f"   Position preserved: {'✅ YES' if pos_match else '❌ NO'}")
        print(f"   Rotation preserved: {'✅ YES' if rot_match else '❌ NO'}")
        
        if pos_match and rot_match:
            print("\n🎉 SUCCESS: Save and load workflow works correctly!")
            print("   The green button properly preserves transformations.")
        else:
            print("\n❌ FAILURE: Save and load workflow has issues!")
            print("   Expected position:", current_pos)
            print("   Actual position:  ", viewer.current_pos_right)
            print("   Expected rotation:", current_rot)
            print("   Actual rotation:  ", viewer.current_rot_right)
            
            if not pos_match:
                print("   🔧 ISSUE: Position not preserved correctly")
            if not rot_match:
                print("   🔧 ISSUE: Rotation not preserved correctly")
        
        # Clean up test file
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
            print(f"🔧 Cleaned up test file: {test_save_file}")
            
    else:
        print("❌ Failed to load initial STEP file")
        print("=== TEST FAILED ===")
        
else:
    print("❌ No STEP files found for testing")
    print("=== TEST FAILED ===")

app.quit()
print("=== COMPLETE USER WORKFLOW TEST FINISHED ===")
