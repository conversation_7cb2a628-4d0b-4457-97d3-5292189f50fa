#!/usr/bin/env python3

"""
Test button rotation fixes by directly calling rotate_shape method.
This test will verify that both local and world origin displays update correctly
when using button rotations (15 degree increments).
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import Step<PERSON>iewerTDK as StepViewer
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import time

def test_button_rotation_fixes():
    """Test button rotation fixes for origin displays"""
    
    print("=" * 80)
    print("TESTING BUTTON ROTATION FIXES FOR ORIGIN DISPLAYS")
    print("=" * 80)
    
    app = QApplication(sys.argv)
    
    try:
        # Create the main viewer
        viewer = StepViewer()
        viewer.show()
        
        # Wait for GUI to initialize
        app.processEvents()
        time.sleep(1)
        
        print("\n1. Loading SOIC-16 footprint...")
        
        # Load the SOIC-16 file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        # Set to TOP viewer and load file
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        print("✅ STEP file loaded successfully")
        
        # Wait for loading to complete
        app.processEvents()
        time.sleep(2)
        
        print("\n2. Testing initial origin displays...")
        
        # Trigger text overlay update to see initial values
        viewer.update_text_overlays()
        app.processEvents()
        
        print("\n3. Testing button rotation (15 degrees X)...")
        
        # Directly call rotate_shape method (this is what the buttons call)
        print("   Calling rotate_shape('x', 15)...")
        viewer.rotate_shape('x', 15)
        app.processEvents()
        time.sleep(1)
        
        print("   ✅ X rotation applied - check debug output for origin updates")
            
        print("\n4. Testing another button rotation (15 degrees Y)...")
        
        # Apply Y rotation
        print("   Calling rotate_shape('y', 15)...")
        viewer.rotate_shape('y', 15)
        app.processEvents()
        time.sleep(1)
        
        print("   ✅ Y rotation applied - check debug output for origin updates")
            
        print("\n5. Testing Z rotation (15 degrees Z)...")
        
        # Apply Z rotation
        print("   Calling rotate_shape('z', 15)...")
        viewer.rotate_shape('z', 15)
        app.processEvents()
        time.sleep(1)
        
        print("   ✅ Z rotation applied - check debug output for origin updates")
        
        print("\n6. Test completed - check debug output above")
        print("   Look for 'DEBUG LOCAL ORIGIN:' and 'DEBUG WORLD ORIGIN:' messages")
        print("   Both local and world origins should update after each button rotation")
        print("   Position values should change to reflect the rotated green sphere position")
        
        # Keep GUI open for visual verification
        print("\n7. GUI is open for visual verification...")
        print("   Try more button rotations and mouse rotation to verify both work correctly")
        print("   Press Ctrl+C to exit")
        
        # Run the GUI event loop
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_button_rotation_fixes()
