#!/usr/bin/env python3
"""
VISUAL TEST: Origin Location Display Fix

This test demonstrates that the origin location values in the yellow text display
now correctly update to match the visual position of the origin markers when
rotation buttons are clicked.

BEFORE THE FIX: Origin values stayed static even when markers moved visually
AFTER THE FIX: Origin values update to match the transformed marker positions

The test will:
1. Load a STEP file
2. Show initial origin values
3. Apply rotations using buttons
4. Show that origin values update to match visual positions
5. Demonstrate that both TOP and BOTTOM viewers work correctly
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main program
from step_viewer import StepViewerTDK

def main():
    print("🎯 VISUAL TEST: Origin Location Display Fix")
    print("=" * 60)
    print("This test demonstrates the fix for origin location display")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    # Create viewer
    print("1. Creating 3D viewer...")
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load test model
    test_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if os.path.exists(test_file):
        print(f"2. Loading test model: {test_file}")
        success = viewer.load_step_file_direct(test_file)
        if success:
            print("✅ Test model loaded successfully")
        else:
            print("⚠️ Failed to load test file")
            return
    else:
        print(f"⚠️ Test file not found: {test_file}")
        return
    
    # Test sequence
    test_step = [0]  # Use list to allow modification in nested functions
    
    def run_test_sequence():
        """Run the automated test sequence"""
        step = test_step[0]
        
        if step == 0:
            print("\n3. INITIAL STATE:")
            print(f"   TOP Origin values: X={viewer.current_pos_left['x']:.3f}, Y={viewer.current_pos_left['y']:.3f}, Z={viewer.current_pos_left['z']:.3f}")
            print("   👀 Look at the yellow text in the TOP viewer - note the Origin values")
            test_step[0] = 1
            QTimer.singleShot(3000, run_test_sequence)
            
        elif step == 1:
            print("\n4. APPLYING X+ ROTATION (30 degrees)...")
            print("   Watch the yellow text Origin values - they should change!")
            viewer.rotate_shape('x', 30)
            test_step[0] = 2
            QTimer.singleShot(2000, run_test_sequence)
            
        elif step == 2:
            print("\n5. AFTER X+ ROTATION:")
            print(f"   TOP Origin values: X={viewer.current_pos_left['x']:.3f}, Y={viewer.current_pos_left['y']:.3f}, Z={viewer.current_pos_left['z']:.3f}")
            print("   ✅ Origin values should have changed to match visual position!")
            print("   👀 Compare with the yellow text display - they should match")
            test_step[0] = 3
            QTimer.singleShot(3000, run_test_sequence)
            
        elif step == 3:
            print("\n6. APPLYING Y+ ROTATION (45 degrees)...")
            print("   Watch for another change in Origin values!")
            viewer.rotate_shape('y', 45)
            test_step[0] = 4
            QTimer.singleShot(2000, run_test_sequence)
            
        elif step == 4:
            print("\n7. AFTER Y+ ROTATION:")
            print(f"   TOP Origin values: X={viewer.current_pos_left['x']:.3f}, Y={viewer.current_pos_left['y']:.3f}, Z={viewer.current_pos_left['z']:.3f}")
            print("   ✅ Origin values should have changed again!")
            test_step[0] = 5
            QTimer.singleShot(3000, run_test_sequence)
            
        elif step == 5:
            print("\n8. TESTING BOTTOM VIEWER...")
            print("   Switching to bottom viewer and applying rotation...")
            viewer.active_viewer = "bottom"
            viewer.rotate_shape('z', 60)
            test_step[0] = 6
            QTimer.singleShot(2000, run_test_sequence)
            
        elif step == 6:
            print("\n9. FINAL RESULTS:")
            print(f"   TOP Origin values: X={viewer.current_pos_left['x']:.3f}, Y={viewer.current_pos_left['y']:.3f}, Z={viewer.current_pos_left['z']:.3f}")
            if hasattr(viewer, 'current_pos_right'):
                print(f"   BOTTOM Origin values: X={viewer.current_pos_right['x']:.3f}, Y={viewer.current_pos_right['y']:.3f}, Z={viewer.current_pos_right['z']:.3f}")
            
            print("\n" + "=" * 60)
            print("🎉 TEST COMPLETE!")
            print("=" * 60)
            print("WHAT YOU SHOULD SEE:")
            print("✅ Origin values in yellow text change when rotation buttons are clicked")
            print("✅ Origin values match the visual position of the green origin markers")
            print("✅ Both TOP and BOTTOM viewers update their origin displays correctly")
            print("✅ The fix ensures displayed values match what gets saved to STEP files")
            print("\nBEFORE THE FIX:")
            print("❌ Origin values stayed static at original STEP file values")
            print("❌ Visual markers moved but numbers didn't update")
            print("\nAFTER THE FIX:")
            print("✅ Origin values update to match transformed positions")
            print("✅ What you see is what gets saved!")
            
    # Start test sequence after viewer is fully loaded
    QTimer.singleShot(2000, run_test_sequence)
    
    print("\n👀 WATCH THE GUI:")
    print("   - Yellow text displays in both viewers")
    print("   - Origin values should change when rotations are applied")
    print("   - Green origin markers should move with the model")
    print("   - Numbers should match visual positions")
    
    # Run the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
