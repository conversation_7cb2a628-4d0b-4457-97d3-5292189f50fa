#!/usr/bin/env python3
"""
AUTOMATED Debug Origin Marker Rotation Issue
============================================
This script automatically tests and fixes why origin markers rotate with mouse but not with buttons.

Problem:
- Mouse rotation: Origin markers rotate with model ✅
- Button rotation: Origin markers stay fixed ❌

This script will:
1. Auto-load a STEP file
2. Test mouse rotation
3. Test button rotation
4. Compare results
5. Automatically fix the issue
"""

import sys
import os
import glob

# Add current directory to path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from step_viewer import StepViewerTDK
    from vtk_renderer import VTKRenderer
    from PyQt5.QtWidgets import QApplication
    import vtk
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure step_viewer.py and vtk_renderer.py are in the same directory")
    sys.exit(1)

def auto_find_step_file():
    """Automatically find a STEP file to test with"""
    # Look for STEP files in current directory
    step_files = glob.glob("*.step") + glob.glob("*.stp") + glob.glob("*.STEP") + glob.glob("*.STP")
    if step_files:
        return step_files[0]

    # Look in common directories
    test_dirs = [".", "test", "examples", "samples"]
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            step_files = glob.glob(os.path.join(test_dir, "*.step")) + glob.glob(os.path.join(test_dir, "*.stp"))
            if step_files:
                return step_files[0]

    return None

def run_automated_debug():
    """Run the complete automated debug sequence"""
    print("🚀 AUTOMATED ORIGIN ROTATION DEBUG")
    print("="*60)

    # Step 1: Find a STEP file
    step_file = auto_find_step_file()
    if not step_file:
        print("❌ No STEP file found for testing")
        print("   Creating a test STEP file...")
        # Create a minimal test STEP file
        step_file = "debug_test.step"
        create_test_step_file(step_file)

    print(f"📁 Using STEP file: {step_file}")

    # Step 2: Create step viewer (no GUI)
    app = QApplication([])  # Minimal app for Qt
    step_viewer = StepViewerTDK()
    step_viewer.hide()  # Don't show GUI

    # Step 3: Load the file
    print("📂 Loading STEP file...")
    step_viewer.active_viewer = "top"

    # Simulate file loading
    if hasattr(step_viewer, 'vtk_renderer_left') and step_viewer.vtk_renderer_left:
        if hasattr(step_viewer.vtk_renderer_left, 'step_loader'):
            loader = step_viewer.vtk_renderer_left.step_loader
            success, message = loader.load_step_file(step_file)
            if success:
                print(f"✅ File loaded: {message}")
                # Create origin overlay
                step_viewer.vtk_renderer_left.create_origin_overlay()
                print("✅ Origin overlay created")
            else:
                print(f"❌ File load failed: {message}")
                return
        else:
            print("❌ No step loader found")
            return
    else:
        print("❌ No renderer found")
        return

    # Step 4: Test mouse rotation
    print("\n🖱️ TESTING MOUSE ROTATION")
    print("-" * 40)

    # Get initial origin actor orientations
    initial_orientations = get_origin_orientations(step_viewer)
    print(f"📊 Initial origin orientations: {initial_orientations}")

    # Test mouse rotation
    if hasattr(step_viewer, '_apply_model_rotation'):
        print("📍 Calling _apply_model_rotation (mouse method)")
        step_viewer._apply_model_rotation("top", 'y', 15.0)
        mouse_orientations = get_origin_orientations(step_viewer)
        print(f"📊 After mouse rotation: {mouse_orientations}")
        mouse_rotated = mouse_orientations != initial_orientations
        print(f"🔍 Mouse rotation changed origins: {mouse_rotated}")
    else:
        print("❌ Mouse rotation method not found")
        mouse_rotated = False

    # Step 5: Reset and test button rotation
    print("\n🔘 TESTING BUTTON ROTATION")
    print("-" * 40)

    # Reset to initial state
    if hasattr(step_viewer, 'reset_to_original'):
        step_viewer.reset_to_original()
        print("🔄 Reset to original state")

    reset_orientations = get_origin_orientations(step_viewer)
    print(f"📊 After reset: {reset_orientations}")

    # Test button rotation
    if hasattr(step_viewer, 'rotate_shape'):
        print("📍 Calling rotate_shape (button method)")
        step_viewer.rotate_shape('y', 15.0)
        button_orientations = get_origin_orientations(step_viewer)
        print(f"📊 After button rotation: {button_orientations}")
        button_rotated = button_orientations != reset_orientations
        print(f"🔍 Button rotation changed origins: {button_rotated}")
    else:
        print("❌ Button rotation method not found")
        button_rotated = False

    # Step 6: Analyze results and fix if needed
    print("\n🔧 ANALYSIS AND FIX")
    print("=" * 40)

    if mouse_rotated and button_rotated:
        print("✅ BOTH methods work - no fix needed")
    elif mouse_rotated and not button_rotated:
        print("🎯 PROBLEM IDENTIFIED: Button rotation doesn't rotate origins")
        print("🔧 APPLYING FIX...")
        apply_origin_rotation_fix(step_viewer)

        # Test the fix
        print("🧪 TESTING FIX...")
        step_viewer.reset_to_original()
        pre_fix_orientations = get_origin_orientations(step_viewer)
        step_viewer.rotate_shape('y', 15.0)
        post_fix_orientations = get_origin_orientations(step_viewer)
        fix_worked = post_fix_orientations != pre_fix_orientations
        print(f"✅ Fix successful: {fix_worked}")

    elif not mouse_rotated and button_rotated:
        print("🤔 UNEXPECTED: Mouse doesn't work but buttons do")
    else:
        print("❌ NEITHER method works - deeper issue")

    print("\n🏁 AUTOMATED DEBUG COMPLETE")
    app.quit()

def get_origin_orientations(step_viewer):
    """Get current orientations of all origin actors"""
    orientations = []
    if hasattr(step_viewer, 'vtk_renderer_left'):
        renderer = step_viewer.vtk_renderer_left
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            for actor in renderer.origin_actors:
                if actor:
                    orientations.append(actor.GetOrientation())
    return orientations

def apply_origin_rotation_fix(step_viewer):
    """Apply the fix for origin rotation in button mode"""
    print("🔧 Applying origin rotation fix...")

    # The fix is to ensure the unified rotation system properly rotates origin actors
    # This involves checking the _apply_vtk_rotation method

    # Import the step_viewer module to patch it
    import step_viewer as sv

    # Store the original method
    original_apply_vtk_rotation = step_viewer._apply_vtk_rotation

    def fixed_apply_vtk_rotation(self, axis, degrees):
        """Fixed version that ensures origin actors are rotated"""
        print(f"🔧 FIXED ROTATION: {axis}+{degrees}° on {self.active_viewer} viewer")

        # Call original method
        result = original_apply_vtk_rotation(axis, degrees)

        # ADDITIONAL FIX: Ensure origin actors are rotated
        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
        else:
            renderer = self.vtk_renderer_right

        # Force rotate origin actors if they weren't rotated
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            print(f"🔧 FORCE ROTATING {len(renderer.origin_actors)} origin actors")
            for actor in renderer.origin_actors:
                if actor:
                    current_pos = actor.GetPosition()
                    actor.RotateWXYZ(-degrees,
                        1 if axis == 'x' else 0,
                        1 if axis == 'y' else 0,
                        1 if axis == 'z' else 0)
                    actor.SetPosition(current_pos)  # Keep position fixed

        return result

    # Patch the method
    step_viewer._apply_vtk_rotation = fixed_apply_vtk_rotation.__get__(step_viewer, type(step_viewer))
    print("✅ Origin rotation fix applied")

def create_test_step_file(filename):
    """Create a minimal test STEP file"""
    step_content = """ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Test file'),'2;1');
FILE_NAME('test.step','2025-01-01T00:00:00',('Test'),('Test'),'Test','Test','');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN'));
ENDSEC;
DATA;
#1 = CARTESIAN_POINT('Origin',(0.,0.,0.));
#2 = DIRECTION('Z',(0.,0.,1.));
#3 = DIRECTION('X',(1.,0.,0.));
#4 = AXIS2_PLACEMENT_3D('',#1,#2,#3);
ENDSEC;
END-ISO-10303-21;"""

    with open(filename, 'w') as f:
        f.write(step_content)
    print(f"✅ Created test STEP file: {filename}")

def main():
    """Run the automated debug and fix"""
    try:
        run_automated_debug()
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
