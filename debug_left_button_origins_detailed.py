#!/usr/bin/env python3
"""
Debug script to see exactly what happens to origin actors during left button rotation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK
import time

class OriginDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def run_debug(self):
        print("🔧 Starting detailed origin debugging for left button rotation...")
        
        # Create viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for viewer to initialize
        QTimer.singleShot(2000, self.load_test_file)
        
        # Start the app
        self.app.exec_()
    
    def load_test_file(self):
        """Load a test STEP file"""
        print("📁 Loading test STEP file...")
        
        # Try to load a STEP file
        test_files = [
            "e:/python/viewer/save/test.step",
            "e:/python/viewer/save/sample.step", 
            "test.step",
            "sample.step"
        ]
        
        loaded = False
        for test_file in test_files:
            if os.path.exists(test_file):
                try:
                    print(f"   Loading: {test_file}")
                    # Use the correct method name
                    success = self.viewer.load_step_file_direct(test_file)
                    if success:
                        loaded = True
                        print(f"   ✓ Successfully loaded: {test_file}")
                        break
                    else:
                        print(f"   ✗ Failed to load: {test_file}")
                except Exception as e:
                    print(f"   Failed to load {test_file}: {e}")

        if not loaded:
            print("❌ No test STEP file found - creating a simple test model")
            # Continue anyway to test the rotation mechanism

        # Wait a bit then start debugging
        QTimer.singleShot(3000, self.debug_origin_positions)
    
    def debug_origin_positions(self):
        """Debug origin positions before and after rotation"""
        print("\n" + "="*60)
        print("🔍 DEBUGGING ORIGIN POSITIONS DURING LEFT BUTTON ROTATION")
        print("="*60)
        
        # Check if origins exist
        self.check_origin_actors()
        
        # Record initial positions
        print("\n📍 INITIAL ORIGIN POSITIONS:")
        initial_positions = self.record_origin_positions()
        
        # Simulate left button rotation
        print("\n🔄 SIMULATING LEFT BUTTON X+ ROTATION (15 degrees)...")
        self.simulate_left_button_rotation('x', 15)
        
        # Wait a moment for the rotation to complete
        QTimer.singleShot(1000, lambda: self.check_after_rotation(initial_positions))
    
    def check_origin_actors(self):
        """Check what origin actors exist"""
        print("\n🔍 CHECKING ORIGIN ACTORS:")
        
        # Check left viewer
        if hasattr(self.viewer, 'vtk_renderer_left'):
            renderer = self.viewer.vtk_renderer_left
            
            # Check world origin actors (red)
            if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
                print(f"   ✅ LEFT viewer has {len(renderer.origin_actors)} world origin actors (red)")
                for i, actor in enumerate(renderer.origin_actors):
                    if actor:
                        pos = actor.GetPosition()
                        print(f"      Actor[{i}]: Position = {pos}")
            else:
                print("   ❌ LEFT viewer has NO world origin actors")
            
            # Check part origin actors (green)
            part_actors = []
            if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
                part_actors.append(('sphere', renderer.part_origin_sphere))
            if hasattr(renderer, 'part_origin_x_arrow') and renderer.part_origin_x_arrow:
                part_actors.append(('x_arrow', renderer.part_origin_x_arrow))
            if hasattr(renderer, 'part_origin_y_arrow') and renderer.part_origin_y_arrow:
                part_actors.append(('y_arrow', renderer.part_origin_y_arrow))
            if hasattr(renderer, 'part_origin_z_arrow') and renderer.part_origin_z_arrow:
                part_actors.append(('z_arrow', renderer.part_origin_z_arrow))
            
            if part_actors:
                print(f"   ✅ LEFT viewer has {len(part_actors)} part origin actors (green)")
                for name, actor in part_actors:
                    pos = actor.GetPosition()
                    print(f"      {name}: Position = {pos}")
            else:
                print("   ❌ LEFT viewer has NO part origin actors")
        else:
            print("   ❌ No vtk_renderer_left found")
    
    def record_origin_positions(self):
        """Record current positions of all origin actors"""
        positions = {}
        
        if hasattr(self.viewer, 'vtk_renderer_left'):
            renderer = self.viewer.vtk_renderer_left
            
            # Record world origin positions
            if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
                positions['world_origins'] = []
                for i, actor in enumerate(renderer.origin_actors):
                    if actor:
                        pos = actor.GetPosition()
                        positions['world_origins'].append(pos)
                        print(f"   World Origin[{i}]: {pos}")
            
            # Record part origin positions
            part_actors = [
                ('sphere', getattr(renderer, 'part_origin_sphere', None)),
                ('x_arrow', getattr(renderer, 'part_origin_x_arrow', None)),
                ('y_arrow', getattr(renderer, 'part_origin_y_arrow', None)),
                ('z_arrow', getattr(renderer, 'part_origin_z_arrow', None))
            ]
            
            positions['part_origins'] = {}
            for name, actor in part_actors:
                if actor:
                    pos = actor.GetPosition()
                    positions['part_origins'][name] = pos
                    print(f"   Part Origin {name}: {pos}")
        
        return positions
    
    def simulate_left_button_rotation(self, axis, degrees):
        """Simulate pressing a left button rotation"""
        print(f"   Calling rotate_shape('{axis}', {degrees})")
        
        # Set active viewer to top (left)
        self.viewer.active_viewer = "top"
        
        # Call the rotation method directly
        self.viewer.rotate_shape(axis, degrees)
        
        print("   Rotation command completed")
    
    def check_after_rotation(self, initial_positions):
        """Check positions after rotation and compare"""
        print("\n📍 ORIGIN POSITIONS AFTER ROTATION:")
        final_positions = self.record_origin_positions()
        
        print("\n📊 COMPARISON:")
        
        # Compare world origins
        if 'world_origins' in initial_positions and 'world_origins' in final_positions:
            initial_world = initial_positions['world_origins']
            final_world = final_positions['world_origins']
            
            print("   World Origins (red):")
            for i in range(min(len(initial_world), len(final_world))):
                init_pos = initial_world[i]
                final_pos = final_world[i]
                moved = any(abs(init_pos[j] - final_pos[j]) > 0.001 for j in range(3))
                status = "✅ MOVED" if moved else "❌ NO CHANGE"
                print(f"      Actor[{i}]: {init_pos} → {final_pos} {status}")
        
        # Compare part origins
        if 'part_origins' in initial_positions and 'part_origins' in final_positions:
            initial_part = initial_positions['part_origins']
            final_part = final_positions['part_origins']
            
            print("   Part Origins (green):")
            for name in initial_part:
                if name in final_part:
                    init_pos = initial_part[name]
                    final_pos = final_part[name]
                    moved = any(abs(init_pos[j] - final_pos[j]) > 0.001 for j in range(3))
                    status = "✅ MOVED" if moved else "❌ NO CHANGE"
                    print(f"      {name}: {init_pos} → {final_pos} {status}")
        
        print("\n" + "="*60)
        print("🏁 DEBUGGING COMPLETE")
        print("="*60)
        
        # Close after a moment
        QTimer.singleShot(3000, self.app.quit)

if __name__ == "__main__":
    debugger = OriginDebugger()
    debugger.run_debug()
