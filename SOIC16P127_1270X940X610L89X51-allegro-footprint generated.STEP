ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('STEP AP214'),'1');
FILE_NAME('SOIC16P127_1270X940X610L89X51-allegro-footprint generated.STEP','2025-09-22T19:58:17',(' '),(' '),'Spatial InterOp 3D',' ',' ');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1=PRODUCT_DEFINITION_CONTEXT('',#133,'design');
#2=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#133);
#3=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#134,#135);
#4=SHAPE_DEFINITION_REPRESENTATION(#136,#137);
#5=PRODUCT_DEFINITION_CONTEXT('',#138,'design');
#6=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#138);
#7=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#139,#140);
#8=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#141,#142);
#9=SHAPE_DEFINITION_REPRESENTATION(#143,#144);
#10=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#144,#145);
#11=PRODUCT_DEFINITION_CONTEXT('',#146,'design');
#12=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#146);
#13=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#147,#148);
#14=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#149,#150);
#15=SHAPE_DEFINITION_REPRESENTATION(#151,#152);
#16=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#152,#153);
#17=PRODUCT_DEFINITION_CONTEXT('',#154,'design');
#18=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#154);
#19=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#155,#156);
#20=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#157,#158);
#21=SHAPE_DEFINITION_REPRESENTATION(#159,#160);
#22=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#160,#161);
#23=PRODUCT_DEFINITION_CONTEXT('',#162,'design');
#24=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#162);
#25=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#163,#164);
#26=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#165,#166);
#27=SHAPE_DEFINITION_REPRESENTATION(#167,#168);
#28=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#168,#169);
#29=PRODUCT_DEFINITION_CONTEXT('',#170,'design');
#30=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#170);
#31=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#171,#172);
#32=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#173,#174);
#33=SHAPE_DEFINITION_REPRESENTATION(#175,#176);
#34=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#176,#177);
#35=PRODUCT_DEFINITION_CONTEXT('',#178,'design');
#36=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#178);
#37=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#179,#180);
#38=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#181,#182);
#39=SHAPE_DEFINITION_REPRESENTATION(#183,#184);
#40=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#184,#185);
#41=PRODUCT_DEFINITION_CONTEXT('',#186,'design');
#42=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#186);
#43=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#187,#188);
#44=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#189,#190);
#45=SHAPE_DEFINITION_REPRESENTATION(#191,#192);
#46=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#192,#193);
#47=PRODUCT_DEFINITION_CONTEXT('',#194,'design');
#48=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#194);
#49=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#195,#196);
#50=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#197,#198);
#51=SHAPE_DEFINITION_REPRESENTATION(#199,#200);
#52=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#200,#201);
#53=PRODUCT_DEFINITION_CONTEXT('',#202,'design');
#54=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#202);
#55=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#203,#204);
#56=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#205,#206);
#57=SHAPE_DEFINITION_REPRESENTATION(#207,#208);
#58=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#208,#209);
#59=PRODUCT_DEFINITION_CONTEXT('',#210,'design');
#60=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#210);
#61=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#211,#212);
#62=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#213,#214);
#63=SHAPE_DEFINITION_REPRESENTATION(#215,#216);
#64=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#216,#217);
#65=PRODUCT_DEFINITION_CONTEXT('',#218,'design');
#66=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#218);
#67=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#219,#220);
#68=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#221,#222);
#69=SHAPE_DEFINITION_REPRESENTATION(#223,#224);
#70=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#224,#225);
#71=PRODUCT_DEFINITION_CONTEXT('',#226,'design');
#72=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#226);
#73=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#227,#228);
#74=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#229,#230);
#75=SHAPE_DEFINITION_REPRESENTATION(#231,#232);
#76=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#232,#233);
#77=PRODUCT_DEFINITION_CONTEXT('',#234,'design');
#78=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#234);
#79=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#235,#236);
#80=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#237,#238);
#81=SHAPE_DEFINITION_REPRESENTATION(#239,#240);
#82=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#240,#241);
#83=PRODUCT_DEFINITION_CONTEXT('',#242,'design');
#84=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#242);
#85=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#243,#244);
#86=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#245,#246);
#87=SHAPE_DEFINITION_REPRESENTATION(#247,#248);
#88=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#248,#249);
#89=PRODUCT_DEFINITION_CONTEXT('',#250,'design');
#90=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#250);
#91=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#251,#252);
#92=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#253,#254);
#93=SHAPE_DEFINITION_REPRESENTATION(#255,#256);
#94=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#256,#257);
#95=PRODUCT_DEFINITION_CONTEXT('',#258,'design');
#96=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#258);
#97=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#259,#260);
#98=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#261,#262);
#99=SHAPE_DEFINITION_REPRESENTATION(#263,#264);
#100=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#264,#265);
#101=PRODUCT_DEFINITION_CONTEXT('',#266,'design');
#102=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#266);
#103=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#267,#268);
#104=SHAPE_DEFINITION_REPRESENTATION(#269,#270);
#105=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#271,#272);
#106=PRODUCT_DEFINITION_CONTEXT('',#273,'design');
#107=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#273);
#108=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#274,#275);
#109=SHAPE_DEFINITION_REPRESENTATION(#276,#277);
#110=PRODUCT_DEFINITION_CONTEXT('',#278,'design');
#111=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#278);
#112=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#279,#280);
#113=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#281,#282);
#114=SHAPE_DEFINITION_REPRESENTATION(#283,#284);
#115=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#284,#285);
#116=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#286,#287);
#117=PRODUCT_DEFINITION_CONTEXT('',#288,'design');
#118=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#288);
#119=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#289,#290);
#120=SHAPE_DEFINITION_REPRESENTATION(#291,#292);
#121=PRODUCT_DEFINITION_CONTEXT('',#293,'design');
#122=APPLICATION_PROTOCOL_DEFINITION('INTERNATIONAL STANDARD','automotive_design',1994,#293);
#123=PRODUCT_CATEGORY_RELATIONSHIP('NONE','NONE',#294,#295);
#124=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#296,#297);
#125=SHAPE_DEFINITION_REPRESENTATION(#298,#299);
#126=SHAPE_REPRESENTATION_RELATIONSHIP('NONE','NONE',#299,#300);
#127=CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#301,#302);
#128=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION(' ',(#303,#304,#305,#306,#307,#308,#309,#310,#311,#312,#313,#314,#315,#316,#317,#318,#319,#320,#321,#322,#323,#324,#325,#326,#327,#328,#329,#330,#331,#332,#333,#334,#335,#336,#337,#338,#339,#340,#341,#342,#343,#344,#345,#346,#347,#348,#349,#350,#351,#352,#353,#354,#355,#356,#357,#358,#359,#360,#361,#362,#363,#364,#365,#366,#367,#368,#369,#370,#371,#372,#373,#374,#375,#376,#377,#378,#379,#380,#381,#382,#383,#384,#385,#386,#387,#388,#389,#390,#391,#392,#393,#394,#395,#396,#397,#398,#399,#400,#401,#402,#403,#404,#405,#406,#407,#408,#409,#410,#411,#412,#413,#414,#415,#416,#417,#418,#419,#420,#421,#422,#423,#424,#425,#426,#427,#428,#429,#430,#431,#432,#433,#434,#435,#436,#437,#438,#439,#440,#441,#442,#443,#444,#445,#446,#447,#448,#449,#450,#451,#452,#453,#454,#455,#456,#457,#458,#459,#460,#461,#462,#463,#464,#465,#466,#467,#468,#469,#470,#471,#472,#473,#474,#475),#129);
#129= (GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#478))GLOBAL_UNIT_ASSIGNED_CONTEXT((#480,#481,#482))REPRESENTATION_CONTEXT('NONE','WORKSPACE'));
#133=APPLICATION_CONTEXT(' ');
#134=PRODUCT_CATEGORY('part','NONE');
#135=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#484));
#136=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#485);
#137=SHAPE_REPRESENTATION('Pins',(#486,#487,#488,#489,#490,#491,#492,#493,#494,#495,#496,#497,#498,#499,#500,#501,#502),#503);
#138=APPLICATION_CONTEXT(' ');
#139=PRODUCT_CATEGORY('part','NONE');
#140=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#504));
#141=(REPRESENTATION_RELATIONSHIP('','',#144,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#507)SHAPE_REPRESENTATION_RELATIONSHIP());
#142=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#509);
#143=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#510);
#144=SHAPE_REPRESENTATION('PIN',(#486),#503);
#145=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#511),#129);
#146=APPLICATION_CONTEXT(' ');
#147=PRODUCT_CATEGORY('part','NONE');
#148=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#512));
#149=(REPRESENTATION_RELATIONSHIP('','',#152,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#515)SHAPE_REPRESENTATION_RELATIONSHIP());
#150=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#517);
#151=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#518);
#152=SHAPE_REPRESENTATION('PIN',(#486),#503);
#153=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#519),#129);
#154=APPLICATION_CONTEXT(' ');
#155=PRODUCT_CATEGORY('part','NONE');
#156=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#520));
#157=(REPRESENTATION_RELATIONSHIP('','',#160,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#523)SHAPE_REPRESENTATION_RELATIONSHIP());
#158=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#525);
#159=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#526);
#160=SHAPE_REPRESENTATION('PIN',(#486),#503);
#161=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#527),#129);
#162=APPLICATION_CONTEXT(' ');
#163=PRODUCT_CATEGORY('part','NONE');
#164=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#528));
#165=(REPRESENTATION_RELATIONSHIP('','',#168,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#531)SHAPE_REPRESENTATION_RELATIONSHIP());
#166=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#533);
#167=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#534);
#168=SHAPE_REPRESENTATION('PIN',(#486),#503);
#169=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#535),#129);
#170=APPLICATION_CONTEXT(' ');
#171=PRODUCT_CATEGORY('part','NONE');
#172=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#536));
#173=(REPRESENTATION_RELATIONSHIP('','',#176,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#539)SHAPE_REPRESENTATION_RELATIONSHIP());
#174=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#541);
#175=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#542);
#176=SHAPE_REPRESENTATION('PIN',(#486),#503);
#177=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#543),#129);
#178=APPLICATION_CONTEXT(' ');
#179=PRODUCT_CATEGORY('part','NONE');
#180=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#544));
#181=(REPRESENTATION_RELATIONSHIP('','',#184,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#547)SHAPE_REPRESENTATION_RELATIONSHIP());
#182=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#549);
#183=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#550);
#184=SHAPE_REPRESENTATION('PIN',(#486),#503);
#185=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#551),#129);
#186=APPLICATION_CONTEXT(' ');
#187=PRODUCT_CATEGORY('part','NONE');
#188=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#552));
#189=(REPRESENTATION_RELATIONSHIP('','',#192,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#555)SHAPE_REPRESENTATION_RELATIONSHIP());
#190=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#557);
#191=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#558);
#192=SHAPE_REPRESENTATION('PIN',(#486),#503);
#193=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#559),#129);
#194=APPLICATION_CONTEXT(' ');
#195=PRODUCT_CATEGORY('part','NONE');
#196=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#560));
#197=(REPRESENTATION_RELATIONSHIP('','',#200,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#563)SHAPE_REPRESENTATION_RELATIONSHIP());
#198=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#565);
#199=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#566);
#200=SHAPE_REPRESENTATION('PIN',(#486),#503);
#201=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#567),#129);
#202=APPLICATION_CONTEXT(' ');
#203=PRODUCT_CATEGORY('part','NONE');
#204=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#568));
#205=(REPRESENTATION_RELATIONSHIP('','',#208,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#571)SHAPE_REPRESENTATION_RELATIONSHIP());
#206=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#573);
#207=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#574);
#208=SHAPE_REPRESENTATION('PIN',(#486),#503);
#209=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#575),#129);
#210=APPLICATION_CONTEXT(' ');
#211=PRODUCT_CATEGORY('part','NONE');
#212=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#576));
#213=(REPRESENTATION_RELATIONSHIP('','',#216,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#579)SHAPE_REPRESENTATION_RELATIONSHIP());
#214=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#581);
#215=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#582);
#216=SHAPE_REPRESENTATION('PIN',(#486),#503);
#217=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#583),#129);
#218=APPLICATION_CONTEXT(' ');
#219=PRODUCT_CATEGORY('part','NONE');
#220=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#584));
#221=(REPRESENTATION_RELATIONSHIP('','',#224,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#587)SHAPE_REPRESENTATION_RELATIONSHIP());
#222=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#589);
#223=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#590);
#224=SHAPE_REPRESENTATION('PIN',(#486),#503);
#225=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#591),#129);
#226=APPLICATION_CONTEXT(' ');
#227=PRODUCT_CATEGORY('part','NONE');
#228=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#592));
#229=(REPRESENTATION_RELATIONSHIP('','',#232,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#595)SHAPE_REPRESENTATION_RELATIONSHIP());
#230=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#597);
#231=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#598);
#232=SHAPE_REPRESENTATION('PIN',(#486),#503);
#233=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#599),#129);
#234=APPLICATION_CONTEXT(' ');
#235=PRODUCT_CATEGORY('part','NONE');
#236=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#600));
#237=(REPRESENTATION_RELATIONSHIP('','',#240,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#603)SHAPE_REPRESENTATION_RELATIONSHIP());
#238=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#605);
#239=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#606);
#240=SHAPE_REPRESENTATION('PIN',(#486),#503);
#241=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#607),#129);
#242=APPLICATION_CONTEXT(' ');
#243=PRODUCT_CATEGORY('part','NONE');
#244=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#608));
#245=(REPRESENTATION_RELATIONSHIP('','',#248,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#611)SHAPE_REPRESENTATION_RELATIONSHIP());
#246=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#613);
#247=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#614);
#248=SHAPE_REPRESENTATION('PIN',(#486),#503);
#249=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#615),#129);
#250=APPLICATION_CONTEXT(' ');
#251=PRODUCT_CATEGORY('part','NONE');
#252=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#616));
#253=(REPRESENTATION_RELATIONSHIP('','',#256,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#619)SHAPE_REPRESENTATION_RELATIONSHIP());
#254=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#621);
#255=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#622);
#256=SHAPE_REPRESENTATION('PIN',(#486),#503);
#257=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#623),#129);
#258=APPLICATION_CONTEXT(' ');
#259=PRODUCT_CATEGORY('part','NONE');
#260=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#624));
#261=(REPRESENTATION_RELATIONSHIP('','',#264,#137)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#627)SHAPE_REPRESENTATION_RELATIONSHIP());
#262=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#629);
#263=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#630);
#264=SHAPE_REPRESENTATION('PIN',(#486),#503);
#265=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#631),#129);
#266=APPLICATION_CONTEXT(' ');
#267=PRODUCT_CATEGORY('part','NONE');
#268=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#632));
#269=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#633);
#270=SHAPE_REPRESENTATION('soic16p254_1992x978x470l87x51',(#634,#635,#636,#637),#638);
#271=(REPRESENTATION_RELATIONSHIP('','',#137,#270)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#641)SHAPE_REPRESENTATION_RELATIONSHIP());
#272=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#643);
#273=APPLICATION_CONTEXT(' ');
#274=PRODUCT_CATEGORY('part','NONE');
#275=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#644));
#276=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#645);
#277=SHAPE_REPRESENTATION('Shapes',(#646,#647),#648);
#278=APPLICATION_CONTEXT(' ');
#279=PRODUCT_CATEGORY('part','NONE');
#280=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#649));
#281=(REPRESENTATION_RELATIONSHIP('','',#284,#277)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#652)SHAPE_REPRESENTATION_RELATIONSHIP());
#282=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#654);
#283=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#655);
#284=SHAPE_REPRESENTATION('SILKSCREEN_TOP',(#646),#648);
#285=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#656,#657,#658,#659,#660,#661,#662,#663),#129);
#286=(REPRESENTATION_RELATIONSHIP('','',#277,#270)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#666)SHAPE_REPRESENTATION_RELATIONSHIP());
#287=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#668);
#288=APPLICATION_CONTEXT(' ');
#289=PRODUCT_CATEGORY('part','NONE');
#290=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#669));
#291=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#670);
#292=SHAPE_REPRESENTATION('Symbols',(#671,#672),#673);
#293=APPLICATION_CONTEXT(' ');
#294=PRODUCT_CATEGORY('part','NONE');
#295=PRODUCT_RELATED_PRODUCT_CATEGORY('detail',' ',(#674));
#296=(REPRESENTATION_RELATIONSHIP('','',#299,#292)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#677)SHAPE_REPRESENTATION_RELATIONSHIP());
#297=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#679);
#298=PRODUCT_DEFINITION_SHAPE('NONE','NONE',#680);
#299=SHAPE_REPRESENTATION('',(#671),#673);
#300=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#681),#129);
#301=(REPRESENTATION_RELATIONSHIP('','',#292,#270)REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#684)SHAPE_REPRESENTATION_RELATIONSHIP());
#302=PRODUCT_DEFINITION_SHAPE('NAUO-PROD-DEF','NAUO-PROD-DEF',#686);
#303=STYLED_ITEM('',(#687),#688);
#304=STYLED_ITEM('',(#689),#690);
#305=STYLED_ITEM('',(#691),#692);
#306=STYLED_ITEM('',(#693),#694);
#307=STYLED_ITEM('',(#695),#696);
#308=STYLED_ITEM('',(#697),#698);
#309=STYLED_ITEM('',(#699),#511);
#310=STYLED_ITEM('',(#700),#701);
#311=STYLED_ITEM('',(#702),#703);
#312=STYLED_ITEM('',(#704),#705);
#313=STYLED_ITEM('',(#706),#707);
#314=STYLED_ITEM('',(#708),#709);
#315=STYLED_ITEM('',(#710),#711);
#316=STYLED_ITEM('',(#712),#519);
#317=STYLED_ITEM('',(#713),#714);
#318=STYLED_ITEM('',(#715),#716);
#319=STYLED_ITEM('',(#717),#718);
#320=STYLED_ITEM('',(#719),#720);
#321=STYLED_ITEM('',(#721),#722);
#322=STYLED_ITEM('',(#723),#724);
#323=STYLED_ITEM('',(#725),#527);
#324=STYLED_ITEM('',(#726),#727);
#325=STYLED_ITEM('',(#728),#729);
#326=STYLED_ITEM('',(#730),#731);
#327=STYLED_ITEM('',(#732),#733);
#328=STYLED_ITEM('',(#734),#735);
#329=STYLED_ITEM('',(#736),#737);
#330=STYLED_ITEM('',(#738),#535);
#331=STYLED_ITEM('',(#739),#740);
#332=STYLED_ITEM('',(#741),#742);
#333=STYLED_ITEM('',(#743),#744);
#334=STYLED_ITEM('',(#745),#746);
#335=STYLED_ITEM('',(#747),#748);
#336=STYLED_ITEM('',(#749),#750);
#337=STYLED_ITEM('',(#751),#543);
#338=STYLED_ITEM('',(#752),#753);
#339=STYLED_ITEM('',(#754),#755);
#340=STYLED_ITEM('',(#756),#757);
#341=STYLED_ITEM('',(#758),#759);
#342=STYLED_ITEM('',(#760),#761);
#343=STYLED_ITEM('',(#762),#763);
#344=STYLED_ITEM('',(#764),#551);
#345=STYLED_ITEM('',(#765),#766);
#346=STYLED_ITEM('',(#767),#768);
#347=STYLED_ITEM('',(#769),#770);
#348=STYLED_ITEM('',(#771),#772);
#349=STYLED_ITEM('',(#773),#774);
#350=STYLED_ITEM('',(#775),#776);
#351=STYLED_ITEM('',(#777),#559);
#352=STYLED_ITEM('',(#778),#779);
#353=STYLED_ITEM('',(#780),#781);
#354=STYLED_ITEM('',(#782),#783);
#355=STYLED_ITEM('',(#784),#785);
#356=STYLED_ITEM('',(#786),#787);
#357=STYLED_ITEM('',(#788),#789);
#358=STYLED_ITEM('',(#790),#567);
#359=STYLED_ITEM('',(#791),#792);
#360=STYLED_ITEM('',(#793),#794);
#361=STYLED_ITEM('',(#795),#796);
#362=STYLED_ITEM('',(#797),#798);
#363=STYLED_ITEM('',(#799),#800);
#364=STYLED_ITEM('',(#801),#802);
#365=STYLED_ITEM('',(#803),#575);
#366=STYLED_ITEM('',(#804),#805);
#367=STYLED_ITEM('',(#806),#807);
#368=STYLED_ITEM('',(#808),#809);
#369=STYLED_ITEM('',(#810),#811);
#370=STYLED_ITEM('',(#812),#813);
#371=STYLED_ITEM('',(#814),#815);
#372=STYLED_ITEM('',(#816),#583);
#373=STYLED_ITEM('',(#817),#818);
#374=STYLED_ITEM('',(#819),#820);
#375=STYLED_ITEM('',(#821),#822);
#376=STYLED_ITEM('',(#823),#824);
#377=STYLED_ITEM('',(#825),#826);
#378=STYLED_ITEM('',(#827),#828);
#379=STYLED_ITEM('',(#829),#591);
#380=STYLED_ITEM('',(#830),#831);
#381=STYLED_ITEM('',(#832),#833);
#382=STYLED_ITEM('',(#834),#835);
#383=STYLED_ITEM('',(#836),#837);
#384=STYLED_ITEM('',(#838),#839);
#385=STYLED_ITEM('',(#840),#841);
#386=STYLED_ITEM('',(#842),#599);
#387=STYLED_ITEM('',(#843),#844);
#388=STYLED_ITEM('',(#845),#846);
#389=STYLED_ITEM('',(#847),#848);
#390=STYLED_ITEM('',(#849),#850);
#391=STYLED_ITEM('',(#851),#852);
#392=STYLED_ITEM('',(#853),#854);
#393=STYLED_ITEM('',(#855),#607);
#394=STYLED_ITEM('',(#856),#857);
#395=STYLED_ITEM('',(#858),#859);
#396=STYLED_ITEM('',(#860),#861);
#397=STYLED_ITEM('',(#862),#863);
#398=STYLED_ITEM('',(#864),#865);
#399=STYLED_ITEM('',(#866),#867);
#400=STYLED_ITEM('',(#868),#615);
#401=STYLED_ITEM('',(#869),#870);
#402=STYLED_ITEM('',(#871),#872);
#403=STYLED_ITEM('',(#873),#874);
#404=STYLED_ITEM('',(#875),#876);
#405=STYLED_ITEM('',(#877),#878);
#406=STYLED_ITEM('',(#879),#880);
#407=STYLED_ITEM('',(#881),#623);
#408=STYLED_ITEM('',(#882),#883);
#409=STYLED_ITEM('',(#884),#885);
#410=STYLED_ITEM('',(#886),#887);
#411=STYLED_ITEM('',(#888),#889);
#412=STYLED_ITEM('',(#890),#891);
#413=STYLED_ITEM('',(#892),#893);
#414=STYLED_ITEM('',(#894),#631);
#415=STYLED_ITEM('',(#895),#896);
#416=STYLED_ITEM('',(#897),#898);
#417=STYLED_ITEM('',(#899),#900);
#418=STYLED_ITEM('',(#901),#902);
#419=STYLED_ITEM('',(#903),#656);
#420=STYLED_ITEM('',(#904),#905);
#421=STYLED_ITEM('',(#906),#907);
#422=STYLED_ITEM('',(#908),#909);
#423=STYLED_ITEM('',(#910),#911);
#424=STYLED_ITEM('',(#912),#913);
#425=STYLED_ITEM('',(#914),#915);
#426=STYLED_ITEM('',(#916),#657);
#427=STYLED_ITEM('',(#917),#918);
#428=STYLED_ITEM('',(#919),#920);
#429=STYLED_ITEM('',(#921),#922);
#430=STYLED_ITEM('',(#923),#924);
#431=STYLED_ITEM('',(#925),#926);
#432=STYLED_ITEM('',(#927),#928);
#433=STYLED_ITEM('',(#929),#658);
#434=STYLED_ITEM('',(#930),#931);
#435=STYLED_ITEM('',(#932),#933);
#436=STYLED_ITEM('',(#934),#935);
#437=STYLED_ITEM('',(#936),#937);
#438=STYLED_ITEM('',(#938),#939);
#439=STYLED_ITEM('',(#940),#941);
#440=STYLED_ITEM('',(#942),#659);
#441=STYLED_ITEM('',(#943),#944);
#442=STYLED_ITEM('',(#945),#946);
#443=STYLED_ITEM('',(#947),#948);
#444=STYLED_ITEM('',(#949),#950);
#445=STYLED_ITEM('',(#951),#952);
#446=STYLED_ITEM('',(#953),#954);
#447=STYLED_ITEM('',(#955),#660);
#448=STYLED_ITEM('',(#956),#957);
#449=STYLED_ITEM('',(#958),#959);
#450=STYLED_ITEM('',(#960),#961);
#451=STYLED_ITEM('',(#962),#963);
#452=STYLED_ITEM('',(#964),#965);
#453=STYLED_ITEM('',(#966),#967);
#454=STYLED_ITEM('',(#968),#661);
#455=STYLED_ITEM('',(#969),#970);
#456=STYLED_ITEM('',(#971),#972);
#457=STYLED_ITEM('',(#973),#974);
#458=STYLED_ITEM('',(#975),#976);
#459=STYLED_ITEM('',(#977),#978);
#460=STYLED_ITEM('',(#979),#980);
#461=STYLED_ITEM('',(#981),#662);
#462=STYLED_ITEM('',(#982),#983);
#463=STYLED_ITEM('',(#984),#985);
#464=STYLED_ITEM('',(#986),#987);
#465=STYLED_ITEM('',(#988),#989);
#466=STYLED_ITEM('',(#990),#991);
#467=STYLED_ITEM('',(#992),#993);
#468=STYLED_ITEM('',(#994),#663);
#469=STYLED_ITEM('',(#995),#996);
#470=STYLED_ITEM('',(#997),#998);
#471=STYLED_ITEM('',(#999),#1000);
#472=STYLED_ITEM('',(#1001),#1002);
#473=STYLED_ITEM('',(#1003),#1004);
#474=STYLED_ITEM('',(#1005),#1006);
#475=STYLED_ITEM('',(#1007),#681);
#478=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.0E-006),#480,'','');
#480= (CONVERSION_BASED_UNIT('MILLIMETRE',#1010)LENGTH_UNIT()NAMED_UNIT(#1013));
#481= (NAMED_UNIT(#1015)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#482= (NAMED_UNIT(#1015)SOLID_ANGLE_UNIT()SI_UNIT($,.STERADIAN.));
#484=PRODUCT('Pins','Pins','PART--DESC',(#1021));
#485=PRODUCT_DEFINITION('Pins','Pins',#1022,#1);
#486=AXIS2_PLACEMENT_3D('',#1023,#1024,#1025);
#487=AXIS2_PLACEMENT_3D('',#1026,#1027,#1028);
#488=AXIS2_PLACEMENT_3D('',#1029,#1030,#1031);
#489=AXIS2_PLACEMENT_3D('',#1032,#1033,#1034);
#490=AXIS2_PLACEMENT_3D('',#1035,#1036,#1037);
#491=AXIS2_PLACEMENT_3D('',#1038,#1039,#1040);
#492=AXIS2_PLACEMENT_3D('',#1041,#1042,#1043);
#493=AXIS2_PLACEMENT_3D('',#1044,#1045,#1046);
#494=AXIS2_PLACEMENT_3D('',#1047,#1048,#1049);
#495=AXIS2_PLACEMENT_3D('',#1050,#1051,#1052);
#496=AXIS2_PLACEMENT_3D('',#1053,#1054,#1055);
#497=AXIS2_PLACEMENT_3D('',#1056,#1057,#1058);
#498=AXIS2_PLACEMENT_3D('',#1059,#1060,#1061);
#499=AXIS2_PLACEMENT_3D('',#1062,#1063,#1064);
#500=AXIS2_PLACEMENT_3D('',#1065,#1066,#1067);
#501=AXIS2_PLACEMENT_3D('',#1068,#1069,#1070);
#502=AXIS2_PLACEMENT_3D('',#1071,#1072,#1073);
#503= (GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1076))GLOBAL_UNIT_ASSIGNED_CONTEXT((#1078,#1079,#1080))REPRESENTATION_CONTEXT('NONE','WORKSPACE'));
#504=PRODUCT('PIN','PIN','PART-PIN-DESC',(#1082));
#507=ITEM_DEFINED_TRANSFORMATION('','',#486,#487);
#509=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#510,$);
#510=PRODUCT_DEFINITION('PIN','PIN',#1083,#5);
#511=MANIFOLD_SOLID_BREP('',#1084);
#512=PRODUCT('PIN','PIN','PART--DESC',(#1085));
#515=ITEM_DEFINED_TRANSFORMATION('','',#486,#488);
#517=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#518,$);
#518=PRODUCT_DEFINITION('PIN','PIN',#1086,#11);
#519=MANIFOLD_SOLID_BREP('',#1087);
#520=PRODUCT('PIN','PIN','PART--DESC',(#1088));
#523=ITEM_DEFINED_TRANSFORMATION('','',#486,#489);
#525=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#526,$);
#526=PRODUCT_DEFINITION('PIN','PIN',#1089,#17);
#527=MANIFOLD_SOLID_BREP('',#1090);
#528=PRODUCT('PIN','PIN','PART--DESC',(#1091));
#531=ITEM_DEFINED_TRANSFORMATION('','',#486,#490);
#533=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#534,$);
#534=PRODUCT_DEFINITION('PIN','PIN',#1092,#23);
#535=MANIFOLD_SOLID_BREP('',#1093);
#536=PRODUCT('PIN','PIN','PART--DESC',(#1094));
#539=ITEM_DEFINED_TRANSFORMATION('','',#486,#491);
#541=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#542,$);
#542=PRODUCT_DEFINITION('PIN','PIN',#1095,#29);
#543=MANIFOLD_SOLID_BREP('',#1096);
#544=PRODUCT('PIN','PIN','PART--DESC',(#1097));
#547=ITEM_DEFINED_TRANSFORMATION('','',#486,#492);
#549=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#550,$);
#550=PRODUCT_DEFINITION('PIN','PIN',#1098,#35);
#551=MANIFOLD_SOLID_BREP('',#1099);
#552=PRODUCT('PIN','PIN','PART--DESC',(#1100));
#555=ITEM_DEFINED_TRANSFORMATION('','',#486,#493);
#557=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#558,$);
#558=PRODUCT_DEFINITION('PIN','PIN',#1101,#41);
#559=MANIFOLD_SOLID_BREP('',#1102);
#560=PRODUCT('PIN','PIN','PART--DESC',(#1103));
#563=ITEM_DEFINED_TRANSFORMATION('','',#486,#494);
#565=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#566,$);
#566=PRODUCT_DEFINITION('PIN','PIN',#1104,#47);
#567=MANIFOLD_SOLID_BREP('',#1105);
#568=PRODUCT('PIN','PIN','PART--DESC',(#1106));
#571=ITEM_DEFINED_TRANSFORMATION('','',#486,#495);
#573=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#574,$);
#574=PRODUCT_DEFINITION('PIN','PIN',#1107,#53);
#575=MANIFOLD_SOLID_BREP('',#1108);
#576=PRODUCT('PIN','PIN','PART--DESC',(#1109));
#579=ITEM_DEFINED_TRANSFORMATION('','',#486,#496);
#581=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#582,$);
#582=PRODUCT_DEFINITION('PIN','PIN',#1110,#59);
#583=MANIFOLD_SOLID_BREP('',#1111);
#584=PRODUCT('PIN','PIN','PART--DESC',(#1112));
#587=ITEM_DEFINED_TRANSFORMATION('','',#486,#497);
#589=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#590,$);
#590=PRODUCT_DEFINITION('PIN','PIN',#1113,#65);
#591=MANIFOLD_SOLID_BREP('',#1114);
#592=PRODUCT('PIN','PIN','PART--DESC',(#1115));
#595=ITEM_DEFINED_TRANSFORMATION('','',#486,#498);
#597=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#598,$);
#598=PRODUCT_DEFINITION('PIN','PIN',#1116,#71);
#599=MANIFOLD_SOLID_BREP('',#1117);
#600=PRODUCT('PIN','PIN','PART--DESC',(#1118));
#603=ITEM_DEFINED_TRANSFORMATION('','',#486,#499);
#605=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#606,$);
#606=PRODUCT_DEFINITION('PIN','PIN',#1119,#77);
#607=MANIFOLD_SOLID_BREP('',#1120);
#608=PRODUCT('PIN','PIN','PART--DESC',(#1121));
#611=ITEM_DEFINED_TRANSFORMATION('','',#486,#500);
#613=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#614,$);
#614=PRODUCT_DEFINITION('PIN','PIN',#1122,#83);
#615=MANIFOLD_SOLID_BREP('',#1123);
#616=PRODUCT('PIN','PIN','PART--DESC',(#1124));
#619=ITEM_DEFINED_TRANSFORMATION('','',#486,#501);
#621=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#622,$);
#622=PRODUCT_DEFINITION('PIN','PIN',#1125,#89);
#623=MANIFOLD_SOLID_BREP('',#1126);
#624=PRODUCT('PIN','PIN','PART--DESC',(#1127));
#627=ITEM_DEFINED_TRANSFORMATION('','',#486,#502);
#629=NEXT_ASSEMBLY_USAGE_OCCURRENCE('PIN','PIN','PIN',#485,#630,$);
#630=PRODUCT_DEFINITION('PIN','PIN',#1128,#95);
#631=MANIFOLD_SOLID_BREP('',#1129);
#632=PRODUCT('soic16p254_1992x978x470l87x51','soic16p254_1992x978x470l87x51','PART--DESC',(#1130));
#633=PRODUCT_DEFINITION('soic16p254_1992x978x470l87x51','soic16p254_1992x978x470l87x51',#1131,#101);
#634=AXIS2_PLACEMENT_3D('',#1132,#1133,#1134);
#635=AXIS2_PLACEMENT_3D('',#1135,#1136,#1137);
#636=AXIS2_PLACEMENT_3D('',#1138,#1139,#1140);
#637=AXIS2_PLACEMENT_3D('',#1141,#1142,#1143);
#638= (GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1146))GLOBAL_UNIT_ASSIGNED_CONTEXT((#1148,#1149,#1150))REPRESENTATION_CONTEXT('NONE','WORKSPACE'));
#641=ITEM_DEFINED_TRANSFORMATION('','',#486,#635);
#643=NEXT_ASSEMBLY_USAGE_OCCURRENCE('','','',#633,#485,$);
#644=PRODUCT('Shapes','Shapes','PART--DESC',(#1152));
#645=PRODUCT_DEFINITION('Shapes','Shapes',#1153,#106);
#646=AXIS2_PLACEMENT_3D('',#1154,#1155,#1156);
#647=AXIS2_PLACEMENT_3D('',#1157,#1158,#1159);
#648= (GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1162))GLOBAL_UNIT_ASSIGNED_CONTEXT((#1164,#1165,#1166))REPRESENTATION_CONTEXT('NONE','WORKSPACE'));
#649=PRODUCT('SILKSCREEN_TOP','SILKSCREEN_TOP','PART-SILKSCREEN_TOP-DESC',(#1168));
#652=ITEM_DEFINED_TRANSFORMATION('','',#646,#647);
#654=NEXT_ASSEMBLY_USAGE_OCCURRENCE('','','',#645,#655,$);
#655=PRODUCT_DEFINITION('SILKSCREEN_TOP','SILKSCREEN_TOP',#1169,#110);
#656=MANIFOLD_SOLID_BREP('',#1170);
#657=MANIFOLD_SOLID_BREP('',#1171);
#658=MANIFOLD_SOLID_BREP('',#1172);
#659=MANIFOLD_SOLID_BREP('',#1173);
#660=MANIFOLD_SOLID_BREP('',#1174);
#661=MANIFOLD_SOLID_BREP('',#1175);
#662=MANIFOLD_SOLID_BREP('',#1176);
#663=MANIFOLD_SOLID_BREP('',#1177);
#666=ITEM_DEFINED_TRANSFORMATION('','',#646,#636);
#668=NEXT_ASSEMBLY_USAGE_OCCURRENCE('','','',#633,#645,$);
#669=PRODUCT('Symbols','Symbols','PART--DESC',(#1178));
#670=PRODUCT_DEFINITION('Symbols','Symbols',#1179,#117);
#671=AXIS2_PLACEMENT_3D('',#1180,#1181,#1182);
#672=AXIS2_PLACEMENT_3D('',#1183,#1184,#1185);
#673= (GEOMETRIC_REPRESENTATION_CONTEXT(3)GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1188))GLOBAL_UNIT_ASSIGNED_CONTEXT((#1190,#1191,#1192))REPRESENTATION_CONTEXT('NONE','WORKSPACE'));
#674=PRODUCT('ASSEM1','ASSEM1','PART-ASSEM1-DESC',(#1194));
#677=ITEM_DEFINED_TRANSFORMATION('','',#671,#672);
#679=NEXT_ASSEMBLY_USAGE_OCCURRENCE('soic16p254_1992x978x470l87x51','soic16p254_1992x978x470l87x51','soic16p254_1992x978x470l87x51',#670,#680,$);
#680=PRODUCT_DEFINITION('','NONE',#1195,#121);
#681=MANIFOLD_SOLID_BREP('',#1196);
#684=ITEM_DEFINED_TRANSFORMATION('','',#671,#637);
#686=NEXT_ASSEMBLY_USAGE_OCCURRENCE('','','',#633,#670,$);
#687=PRESENTATION_STYLE_ASSIGNMENT((#1197));
#688=ADVANCED_FACE('',(#1198),#1199,.T.);
#689=PRESENTATION_STYLE_ASSIGNMENT((#1200));
#690=ADVANCED_FACE('',(#1201),#1202,.F.);
#691=PRESENTATION_STYLE_ASSIGNMENT((#1203));
#692=ADVANCED_FACE('',(#1204),#1205,.F.);
#693=PRESENTATION_STYLE_ASSIGNMENT((#1206));
#694=ADVANCED_FACE('',(#1207),#1208,.F.);
#695=PRESENTATION_STYLE_ASSIGNMENT((#1209));
#696=ADVANCED_FACE('',(#1210),#1211,.F.);
#697=PRESENTATION_STYLE_ASSIGNMENT((#1212));
#698=ADVANCED_FACE('',(#1213),#1214,.F.);
#699=PRESENTATION_STYLE_ASSIGNMENT((#1215));
#700=PRESENTATION_STYLE_ASSIGNMENT((#1216));
#701=ADVANCED_FACE('',(#1217),#1218,.T.);
#702=PRESENTATION_STYLE_ASSIGNMENT((#1219));
#703=ADVANCED_FACE('',(#1220),#1221,.F.);
#704=PRESENTATION_STYLE_ASSIGNMENT((#1222));
#705=ADVANCED_FACE('',(#1223),#1224,.F.);
#706=PRESENTATION_STYLE_ASSIGNMENT((#1225));
#707=ADVANCED_FACE('',(#1226),#1227,.F.);
#708=PRESENTATION_STYLE_ASSIGNMENT((#1228));
#709=ADVANCED_FACE('',(#1229),#1230,.F.);
#710=PRESENTATION_STYLE_ASSIGNMENT((#1231));
#711=ADVANCED_FACE('',(#1232),#1233,.F.);
#712=PRESENTATION_STYLE_ASSIGNMENT((#1234));
#713=PRESENTATION_STYLE_ASSIGNMENT((#1235));
#714=ADVANCED_FACE('',(#1236),#1237,.T.);
#715=PRESENTATION_STYLE_ASSIGNMENT((#1238));
#716=ADVANCED_FACE('',(#1239),#1240,.F.);
#717=PRESENTATION_STYLE_ASSIGNMENT((#1241));
#718=ADVANCED_FACE('',(#1242),#1243,.F.);
#719=PRESENTATION_STYLE_ASSIGNMENT((#1244));
#720=ADVANCED_FACE('',(#1245),#1246,.F.);
#721=PRESENTATION_STYLE_ASSIGNMENT((#1247));
#722=ADVANCED_FACE('',(#1248),#1249,.F.);
#723=PRESENTATION_STYLE_ASSIGNMENT((#1250));
#724=ADVANCED_FACE('',(#1251),#1252,.F.);
#725=PRESENTATION_STYLE_ASSIGNMENT((#1253));
#726=PRESENTATION_STYLE_ASSIGNMENT((#1254));
#727=ADVANCED_FACE('',(#1255),#1256,.T.);
#728=PRESENTATION_STYLE_ASSIGNMENT((#1257));
#729=ADVANCED_FACE('',(#1258),#1259,.F.);
#730=PRESENTATION_STYLE_ASSIGNMENT((#1260));
#731=ADVANCED_FACE('',(#1261),#1262,.F.);
#732=PRESENTATION_STYLE_ASSIGNMENT((#1263));
#733=ADVANCED_FACE('',(#1264),#1265,.F.);
#734=PRESENTATION_STYLE_ASSIGNMENT((#1266));
#735=ADVANCED_FACE('',(#1267),#1268,.F.);
#736=PRESENTATION_STYLE_ASSIGNMENT((#1269));
#737=ADVANCED_FACE('',(#1270),#1271,.F.);
#738=PRESENTATION_STYLE_ASSIGNMENT((#1272));
#739=PRESENTATION_STYLE_ASSIGNMENT((#1273));
#740=ADVANCED_FACE('',(#1274),#1275,.T.);
#741=PRESENTATION_STYLE_ASSIGNMENT((#1276));
#742=ADVANCED_FACE('',(#1277),#1278,.F.);
#743=PRESENTATION_STYLE_ASSIGNMENT((#1279));
#744=ADVANCED_FACE('',(#1280),#1281,.F.);
#745=PRESENTATION_STYLE_ASSIGNMENT((#1282));
#746=ADVANCED_FACE('',(#1283),#1284,.F.);
#747=PRESENTATION_STYLE_ASSIGNMENT((#1285));
#748=ADVANCED_FACE('',(#1286),#1287,.F.);
#749=PRESENTATION_STYLE_ASSIGNMENT((#1288));
#750=ADVANCED_FACE('',(#1289),#1290,.F.);
#751=PRESENTATION_STYLE_ASSIGNMENT((#1291));
#752=PRESENTATION_STYLE_ASSIGNMENT((#1292));
#753=ADVANCED_FACE('',(#1293),#1294,.T.);
#754=PRESENTATION_STYLE_ASSIGNMENT((#1295));
#755=ADVANCED_FACE('',(#1296),#1297,.F.);
#756=PRESENTATION_STYLE_ASSIGNMENT((#1298));
#757=ADVANCED_FACE('',(#1299),#1300,.F.);
#758=PRESENTATION_STYLE_ASSIGNMENT((#1301));
#759=ADVANCED_FACE('',(#1302),#1303,.F.);
#760=PRESENTATION_STYLE_ASSIGNMENT((#1304));
#761=ADVANCED_FACE('',(#1305),#1306,.F.);
#762=PRESENTATION_STYLE_ASSIGNMENT((#1307));
#763=ADVANCED_FACE('',(#1308),#1309,.F.);
#764=PRESENTATION_STYLE_ASSIGNMENT((#1310));
#765=PRESENTATION_STYLE_ASSIGNMENT((#1311));
#766=ADVANCED_FACE('',(#1312),#1313,.T.);
#767=PRESENTATION_STYLE_ASSIGNMENT((#1314));
#768=ADVANCED_FACE('',(#1315),#1316,.F.);
#769=PRESENTATION_STYLE_ASSIGNMENT((#1317));
#770=ADVANCED_FACE('',(#1318),#1319,.F.);
#771=PRESENTATION_STYLE_ASSIGNMENT((#1320));
#772=ADVANCED_FACE('',(#1321),#1322,.F.);
#773=PRESENTATION_STYLE_ASSIGNMENT((#1323));
#774=ADVANCED_FACE('',(#1324),#1325,.F.);
#775=PRESENTATION_STYLE_ASSIGNMENT((#1326));
#776=ADVANCED_FACE('',(#1327),#1328,.F.);
#777=PRESENTATION_STYLE_ASSIGNMENT((#1329));
#778=PRESENTATION_STYLE_ASSIGNMENT((#1330));
#779=ADVANCED_FACE('',(#1331),#1332,.T.);
#780=PRESENTATION_STYLE_ASSIGNMENT((#1333));
#781=ADVANCED_FACE('',(#1334),#1335,.F.);
#782=PRESENTATION_STYLE_ASSIGNMENT((#1336));
#783=ADVANCED_FACE('',(#1337),#1338,.F.);
#784=PRESENTATION_STYLE_ASSIGNMENT((#1339));
#785=ADVANCED_FACE('',(#1340),#1341,.F.);
#786=PRESENTATION_STYLE_ASSIGNMENT((#1342));
#787=ADVANCED_FACE('',(#1343),#1344,.F.);
#788=PRESENTATION_STYLE_ASSIGNMENT((#1345));
#789=ADVANCED_FACE('',(#1346),#1347,.F.);
#790=PRESENTATION_STYLE_ASSIGNMENT((#1348));
#791=PRESENTATION_STYLE_ASSIGNMENT((#1349));
#792=ADVANCED_FACE('',(#1350),#1351,.T.);
#793=PRESENTATION_STYLE_ASSIGNMENT((#1352));
#794=ADVANCED_FACE('',(#1353),#1354,.F.);
#795=PRESENTATION_STYLE_ASSIGNMENT((#1355));
#796=ADVANCED_FACE('',(#1356),#1357,.F.);
#797=PRESENTATION_STYLE_ASSIGNMENT((#1358));
#798=ADVANCED_FACE('',(#1359),#1360,.F.);
#799=PRESENTATION_STYLE_ASSIGNMENT((#1361));
#800=ADVANCED_FACE('',(#1362),#1363,.F.);
#801=PRESENTATION_STYLE_ASSIGNMENT((#1364));
#802=ADVANCED_FACE('',(#1365),#1366,.F.);
#803=PRESENTATION_STYLE_ASSIGNMENT((#1367));
#804=PRESENTATION_STYLE_ASSIGNMENT((#1368));
#805=ADVANCED_FACE('',(#1369),#1370,.T.);
#806=PRESENTATION_STYLE_ASSIGNMENT((#1371));
#807=ADVANCED_FACE('',(#1372),#1373,.F.);
#808=PRESENTATION_STYLE_ASSIGNMENT((#1374));
#809=ADVANCED_FACE('',(#1375),#1376,.F.);
#810=PRESENTATION_STYLE_ASSIGNMENT((#1377));
#811=ADVANCED_FACE('',(#1378),#1379,.F.);
#812=PRESENTATION_STYLE_ASSIGNMENT((#1380));
#813=ADVANCED_FACE('',(#1381),#1382,.F.);
#814=PRESENTATION_STYLE_ASSIGNMENT((#1383));
#815=ADVANCED_FACE('',(#1384),#1385,.F.);
#816=PRESENTATION_STYLE_ASSIGNMENT((#1386));
#817=PRESENTATION_STYLE_ASSIGNMENT((#1387));
#818=ADVANCED_FACE('',(#1388),#1389,.T.);
#819=PRESENTATION_STYLE_ASSIGNMENT((#1390));
#820=ADVANCED_FACE('',(#1391),#1392,.F.);
#821=PRESENTATION_STYLE_ASSIGNMENT((#1393));
#822=ADVANCED_FACE('',(#1394),#1395,.F.);
#823=PRESENTATION_STYLE_ASSIGNMENT((#1396));
#824=ADVANCED_FACE('',(#1397),#1398,.F.);
#825=PRESENTATION_STYLE_ASSIGNMENT((#1399));
#826=ADVANCED_FACE('',(#1400),#1401,.F.);
#827=PRESENTATION_STYLE_ASSIGNMENT((#1402));
#828=ADVANCED_FACE('',(#1403),#1404,.F.);
#829=PRESENTATION_STYLE_ASSIGNMENT((#1405));
#830=PRESENTATION_STYLE_ASSIGNMENT((#1406));
#831=ADVANCED_FACE('',(#1407),#1408,.T.);
#832=PRESENTATION_STYLE_ASSIGNMENT((#1409));
#833=ADVANCED_FACE('',(#1410),#1411,.F.);
#834=PRESENTATION_STYLE_ASSIGNMENT((#1412));
#835=ADVANCED_FACE('',(#1413),#1414,.F.);
#836=PRESENTATION_STYLE_ASSIGNMENT((#1415));
#837=ADVANCED_FACE('',(#1416),#1417,.F.);
#838=PRESENTATION_STYLE_ASSIGNMENT((#1418));
#839=ADVANCED_FACE('',(#1419),#1420,.F.);
#840=PRESENTATION_STYLE_ASSIGNMENT((#1421));
#841=ADVANCED_FACE('',(#1422),#1423,.F.);
#842=PRESENTATION_STYLE_ASSIGNMENT((#1424));
#843=PRESENTATION_STYLE_ASSIGNMENT((#1425));
#844=ADVANCED_FACE('',(#1426),#1427,.T.);
#845=PRESENTATION_STYLE_ASSIGNMENT((#1428));
#846=ADVANCED_FACE('',(#1429),#1430,.F.);
#847=PRESENTATION_STYLE_ASSIGNMENT((#1431));
#848=ADVANCED_FACE('',(#1432),#1433,.F.);
#849=PRESENTATION_STYLE_ASSIGNMENT((#1434));
#850=ADVANCED_FACE('',(#1435),#1436,.F.);
#851=PRESENTATION_STYLE_ASSIGNMENT((#1437));
#852=ADVANCED_FACE('',(#1438),#1439,.F.);
#853=PRESENTATION_STYLE_ASSIGNMENT((#1440));
#854=ADVANCED_FACE('',(#1441),#1442,.F.);
#855=PRESENTATION_STYLE_ASSIGNMENT((#1443));
#856=PRESENTATION_STYLE_ASSIGNMENT((#1444));
#857=ADVANCED_FACE('',(#1445),#1446,.T.);
#858=PRESENTATION_STYLE_ASSIGNMENT((#1447));
#859=ADVANCED_FACE('',(#1448),#1449,.F.);
#860=PRESENTATION_STYLE_ASSIGNMENT((#1450));
#861=ADVANCED_FACE('',(#1451),#1452,.F.);
#862=PRESENTATION_STYLE_ASSIGNMENT((#1453));
#863=ADVANCED_FACE('',(#1454),#1455,.F.);
#864=PRESENTATION_STYLE_ASSIGNMENT((#1456));
#865=ADVANCED_FACE('',(#1457),#1458,.F.);
#866=PRESENTATION_STYLE_ASSIGNMENT((#1459));
#867=ADVANCED_FACE('',(#1460),#1461,.F.);
#868=PRESENTATION_STYLE_ASSIGNMENT((#1462));
#869=PRESENTATION_STYLE_ASSIGNMENT((#1463));
#870=ADVANCED_FACE('',(#1464),#1465,.T.);
#871=PRESENTATION_STYLE_ASSIGNMENT((#1466));
#872=ADVANCED_FACE('',(#1467),#1468,.F.);
#873=PRESENTATION_STYLE_ASSIGNMENT((#1469));
#874=ADVANCED_FACE('',(#1470),#1471,.F.);
#875=PRESENTATION_STYLE_ASSIGNMENT((#1472));
#876=ADVANCED_FACE('',(#1473),#1474,.F.);
#877=PRESENTATION_STYLE_ASSIGNMENT((#1475));
#878=ADVANCED_FACE('',(#1476),#1477,.F.);
#879=PRESENTATION_STYLE_ASSIGNMENT((#1478));
#880=ADVANCED_FACE('',(#1479),#1480,.F.);
#881=PRESENTATION_STYLE_ASSIGNMENT((#1481));
#882=PRESENTATION_STYLE_ASSIGNMENT((#1482));
#883=ADVANCED_FACE('',(#1483),#1484,.T.);
#884=PRESENTATION_STYLE_ASSIGNMENT((#1485));
#885=ADVANCED_FACE('',(#1486),#1487,.F.);
#886=PRESENTATION_STYLE_ASSIGNMENT((#1488));
#887=ADVANCED_FACE('',(#1489),#1490,.F.);
#888=PRESENTATION_STYLE_ASSIGNMENT((#1491));
#889=ADVANCED_FACE('',(#1492),#1493,.F.);
#890=PRESENTATION_STYLE_ASSIGNMENT((#1494));
#891=ADVANCED_FACE('',(#1495),#1496,.F.);
#892=PRESENTATION_STYLE_ASSIGNMENT((#1497));
#893=ADVANCED_FACE('',(#1498),#1499,.F.);
#894=PRESENTATION_STYLE_ASSIGNMENT((#1500));
#895=PRESENTATION_STYLE_ASSIGNMENT((#1501));
#896=ADVANCED_FACE('',(#1502),#1503,.T.);
#897=PRESENTATION_STYLE_ASSIGNMENT((#1504));
#898=ADVANCED_FACE('',(#1505),#1506,.T.);
#899=PRESENTATION_STYLE_ASSIGNMENT((#1507));
#900=ADVANCED_FACE('',(#1508),#1509,.F.);
#901=PRESENTATION_STYLE_ASSIGNMENT((#1510));
#902=ADVANCED_FACE('',(#1511),#1512,.T.);
#903=PRESENTATION_STYLE_ASSIGNMENT((#1513));
#904=PRESENTATION_STYLE_ASSIGNMENT((#1514));
#905=ADVANCED_FACE('',(#1515),#1516,.T.);
#906=PRESENTATION_STYLE_ASSIGNMENT((#1517));
#907=ADVANCED_FACE('',(#1518),#1519,.F.);
#908=PRESENTATION_STYLE_ASSIGNMENT((#1520));
#909=ADVANCED_FACE('',(#1521),#1522,.F.);
#910=PRESENTATION_STYLE_ASSIGNMENT((#1523));
#911=ADVANCED_FACE('',(#1524),#1525,.T.);
#912=PRESENTATION_STYLE_ASSIGNMENT((#1526));
#913=ADVANCED_FACE('',(#1527),#1528,.F.);
#914=PRESENTATION_STYLE_ASSIGNMENT((#1529));
#915=ADVANCED_FACE('',(#1530),#1531,.T.);
#916=PRESENTATION_STYLE_ASSIGNMENT((#1532));
#917=PRESENTATION_STYLE_ASSIGNMENT((#1533));
#918=ADVANCED_FACE('',(#1534),#1535,.T.);
#919=PRESENTATION_STYLE_ASSIGNMENT((#1536));
#920=ADVANCED_FACE('',(#1537),#1538,.F.);
#921=PRESENTATION_STYLE_ASSIGNMENT((#1539));
#922=ADVANCED_FACE('',(#1540),#1541,.F.);
#923=PRESENTATION_STYLE_ASSIGNMENT((#1542));
#924=ADVANCED_FACE('',(#1543),#1544,.T.);
#925=PRESENTATION_STYLE_ASSIGNMENT((#1545));
#926=ADVANCED_FACE('',(#1546),#1547,.F.);
#927=PRESENTATION_STYLE_ASSIGNMENT((#1548));
#928=ADVANCED_FACE('',(#1549),#1550,.T.);
#929=PRESENTATION_STYLE_ASSIGNMENT((#1551));
#930=PRESENTATION_STYLE_ASSIGNMENT((#1552));
#931=ADVANCED_FACE('',(#1553),#1554,.T.);
#932=PRESENTATION_STYLE_ASSIGNMENT((#1555));
#933=ADVANCED_FACE('',(#1556),#1557,.F.);
#934=PRESENTATION_STYLE_ASSIGNMENT((#1558));
#935=ADVANCED_FACE('',(#1559),#1560,.F.);
#936=PRESENTATION_STYLE_ASSIGNMENT((#1561));
#937=ADVANCED_FACE('',(#1562),#1563,.T.);
#938=PRESENTATION_STYLE_ASSIGNMENT((#1564));
#939=ADVANCED_FACE('',(#1565),#1566,.F.);
#940=PRESENTATION_STYLE_ASSIGNMENT((#1567));
#941=ADVANCED_FACE('',(#1568),#1569,.T.);
#942=PRESENTATION_STYLE_ASSIGNMENT((#1570));
#943=PRESENTATION_STYLE_ASSIGNMENT((#1571));
#944=ADVANCED_FACE('',(#1572),#1573,.T.);
#945=PRESENTATION_STYLE_ASSIGNMENT((#1574));
#946=ADVANCED_FACE('',(#1575),#1576,.F.);
#947=PRESENTATION_STYLE_ASSIGNMENT((#1577));
#948=ADVANCED_FACE('',(#1578),#1579,.F.);
#949=PRESENTATION_STYLE_ASSIGNMENT((#1580));
#950=ADVANCED_FACE('',(#1581),#1582,.T.);
#951=PRESENTATION_STYLE_ASSIGNMENT((#1583));
#952=ADVANCED_FACE('',(#1584),#1585,.F.);
#953=PRESENTATION_STYLE_ASSIGNMENT((#1586));
#954=ADVANCED_FACE('',(#1587),#1588,.T.);
#955=PRESENTATION_STYLE_ASSIGNMENT((#1589));
#956=PRESENTATION_STYLE_ASSIGNMENT((#1590));
#957=ADVANCED_FACE('',(#1591),#1592,.T.);
#958=PRESENTATION_STYLE_ASSIGNMENT((#1593));
#959=ADVANCED_FACE('',(#1594),#1595,.F.);
#960=PRESENTATION_STYLE_ASSIGNMENT((#1596));
#961=ADVANCED_FACE('',(#1597),#1598,.F.);
#962=PRESENTATION_STYLE_ASSIGNMENT((#1599));
#963=ADVANCED_FACE('',(#1600),#1601,.T.);
#964=PRESENTATION_STYLE_ASSIGNMENT((#1602));
#965=ADVANCED_FACE('',(#1603),#1604,.F.);
#966=PRESENTATION_STYLE_ASSIGNMENT((#1605));
#967=ADVANCED_FACE('',(#1606),#1607,.T.);
#968=PRESENTATION_STYLE_ASSIGNMENT((#1608));
#969=PRESENTATION_STYLE_ASSIGNMENT((#1609));
#970=ADVANCED_FACE('',(#1610),#1611,.T.);
#971=PRESENTATION_STYLE_ASSIGNMENT((#1612));
#972=ADVANCED_FACE('',(#1613),#1614,.F.);
#973=PRESENTATION_STYLE_ASSIGNMENT((#1615));
#974=ADVANCED_FACE('',(#1616),#1617,.F.);
#975=PRESENTATION_STYLE_ASSIGNMENT((#1618));
#976=ADVANCED_FACE('',(#1619),#1620,.T.);
#977=PRESENTATION_STYLE_ASSIGNMENT((#1621));
#978=ADVANCED_FACE('',(#1622),#1623,.F.);
#979=PRESENTATION_STYLE_ASSIGNMENT((#1624));
#980=ADVANCED_FACE('',(#1625),#1626,.T.);
#981=PRESENTATION_STYLE_ASSIGNMENT((#1627));
#982=PRESENTATION_STYLE_ASSIGNMENT((#1628));
#983=ADVANCED_FACE('',(#1629),#1630,.T.);
#984=PRESENTATION_STYLE_ASSIGNMENT((#1631));
#985=ADVANCED_FACE('',(#1632),#1633,.F.);
#986=PRESENTATION_STYLE_ASSIGNMENT((#1634));
#987=ADVANCED_FACE('',(#1635),#1636,.F.);
#988=PRESENTATION_STYLE_ASSIGNMENT((#1637));
#989=ADVANCED_FACE('',(#1638),#1639,.T.);
#990=PRESENTATION_STYLE_ASSIGNMENT((#1640));
#991=ADVANCED_FACE('',(#1641),#1642,.F.);
#992=PRESENTATION_STYLE_ASSIGNMENT((#1643));
#993=ADVANCED_FACE('',(#1644),#1645,.T.);
#994=PRESENTATION_STYLE_ASSIGNMENT((#1646));
#995=PRESENTATION_STYLE_ASSIGNMENT((#1647));
#996=ADVANCED_FACE('',(#1648),#1649,.T.);
#997=PRESENTATION_STYLE_ASSIGNMENT((#1650));
#998=ADVANCED_FACE('',(#1651),#1652,.F.);
#999=PRESENTATION_STYLE_ASSIGNMENT((#1653));
#1000=ADVANCED_FACE('',(#1654),#1655,.F.);
#1001=PRESENTATION_STYLE_ASSIGNMENT((#1656));
#1002=ADVANCED_FACE('',(#1657),#1658,.F.);
#1003=PRESENTATION_STYLE_ASSIGNMENT((#1659));
#1004=ADVANCED_FACE('',(#1660),#1661,.F.);
#1005=PRESENTATION_STYLE_ASSIGNMENT((#1662));
#1006=ADVANCED_FACE('',(#1663),#1664,.F.);
#1007=PRESENTATION_STYLE_ASSIGNMENT((#1665));
#1010=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.0),#1666);
#1013=DIMENSIONAL_EXPONENTS(1.0,0.0,0.0,0.0,0.0,0.0,0.0);
#1015=DIMENSIONAL_EXPONENTS(0.0,0.0,0.0,0.0,0.0,0.0,0.0);
#1021=PRODUCT_CONTEXT('',#133,'mechanical');
#1022=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#484,.NOT_KNOWN.);
#1023=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1024=DIRECTION('',(0.0,0.0,1.0));
#1025=DIRECTION('',(1.0,0.0,0.0));
#1026=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1027=DIRECTION('',(0.0,0.0,1.0));
#1028=DIRECTION('',(1.0,0.0,0.0));
#1029=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1030=DIRECTION('',(0.0,0.0,1.0));
#1031=DIRECTION('',(1.0,0.0,0.0));
#1032=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1033=DIRECTION('',(0.0,0.0,1.0));
#1034=DIRECTION('',(1.0,0.0,0.0));
#1035=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1036=DIRECTION('',(0.0,0.0,1.0));
#1037=DIRECTION('',(1.0,0.0,0.0));
#1038=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1039=DIRECTION('',(0.0,0.0,1.0));
#1040=DIRECTION('',(1.0,0.0,0.0));
#1041=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1042=DIRECTION('',(0.0,0.0,1.0));
#1043=DIRECTION('',(1.0,0.0,0.0));
#1044=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1045=DIRECTION('',(0.0,0.0,1.0));
#1046=DIRECTION('',(1.0,0.0,0.0));
#1047=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1048=DIRECTION('',(0.0,0.0,1.0));
#1049=DIRECTION('',(1.0,0.0,0.0));
#1050=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1051=DIRECTION('',(0.0,0.0,1.0));
#1052=DIRECTION('',(1.0,0.0,0.0));
#1053=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1054=DIRECTION('',(0.0,0.0,1.0));
#1055=DIRECTION('',(1.0,0.0,0.0));
#1056=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1057=DIRECTION('',(0.0,0.0,1.0));
#1058=DIRECTION('',(1.0,0.0,0.0));
#1059=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1060=DIRECTION('',(0.0,0.0,1.0));
#1061=DIRECTION('',(1.0,0.0,0.0));
#1062=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1063=DIRECTION('',(0.0,0.0,1.0));
#1064=DIRECTION('',(1.0,0.0,0.0));
#1065=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1066=DIRECTION('',(0.0,0.0,1.0));
#1067=DIRECTION('',(1.0,0.0,0.0));
#1068=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1069=DIRECTION('',(0.0,0.0,1.0));
#1070=DIRECTION('',(1.0,0.0,0.0));
#1071=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1072=DIRECTION('',(0.0,0.0,1.0));
#1073=DIRECTION('',(1.0,0.0,0.0));
#1076=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.0E-006),#1078,'','');
#1078= (CONVERSION_BASED_UNIT('MILLIMETRE',#1669)LENGTH_UNIT()NAMED_UNIT(#1672));
#1079= (NAMED_UNIT(#1674)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#1080= (NAMED_UNIT(#1674)SOLID_ANGLE_UNIT()SI_UNIT($,.STERADIAN.));
#1082=PRODUCT_CONTEXT('',#138,'mechanical');
#1083=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#504,.NOT_KNOWN.);
#1084=CLOSED_SHELL('',(#688,#690,#692,#694,#696,#698));
#1085=PRODUCT_CONTEXT('',#146,'mechanical');
#1086=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#512,.NOT_KNOWN.);
#1087=CLOSED_SHELL('',(#701,#703,#705,#707,#709,#711));
#1088=PRODUCT_CONTEXT('',#154,'mechanical');
#1089=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#520,.NOT_KNOWN.);
#1090=CLOSED_SHELL('',(#714,#716,#718,#720,#722,#724));
#1091=PRODUCT_CONTEXT('',#162,'mechanical');
#1092=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#528,.NOT_KNOWN.);
#1093=CLOSED_SHELL('',(#727,#729,#731,#733,#735,#737));
#1094=PRODUCT_CONTEXT('',#170,'mechanical');
#1095=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#536,.NOT_KNOWN.);
#1096=CLOSED_SHELL('',(#740,#742,#744,#746,#748,#750));
#1097=PRODUCT_CONTEXT('',#178,'mechanical');
#1098=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#544,.NOT_KNOWN.);
#1099=CLOSED_SHELL('',(#753,#755,#757,#759,#761,#763));
#1100=PRODUCT_CONTEXT('',#186,'mechanical');
#1101=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#552,.NOT_KNOWN.);
#1102=CLOSED_SHELL('',(#766,#768,#770,#772,#774,#776));
#1103=PRODUCT_CONTEXT('',#194,'mechanical');
#1104=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#560,.NOT_KNOWN.);
#1105=CLOSED_SHELL('',(#779,#781,#783,#785,#787,#789));
#1106=PRODUCT_CONTEXT('',#202,'mechanical');
#1107=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#568,.NOT_KNOWN.);
#1108=CLOSED_SHELL('',(#792,#794,#796,#798,#800,#802));
#1109=PRODUCT_CONTEXT('',#210,'mechanical');
#1110=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#576,.NOT_KNOWN.);
#1111=CLOSED_SHELL('',(#805,#807,#809,#811,#813,#815));
#1112=PRODUCT_CONTEXT('',#218,'mechanical');
#1113=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#584,.NOT_KNOWN.);
#1114=CLOSED_SHELL('',(#818,#820,#822,#824,#826,#828));
#1115=PRODUCT_CONTEXT('',#226,'mechanical');
#1116=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#592,.NOT_KNOWN.);
#1117=CLOSED_SHELL('',(#831,#833,#835,#837,#839,#841));
#1118=PRODUCT_CONTEXT('',#234,'mechanical');
#1119=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#600,.NOT_KNOWN.);
#1120=CLOSED_SHELL('',(#844,#846,#848,#850,#852,#854));
#1121=PRODUCT_CONTEXT('',#242,'mechanical');
#1122=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#608,.NOT_KNOWN.);
#1123=CLOSED_SHELL('',(#857,#859,#861,#863,#865,#867));
#1124=PRODUCT_CONTEXT('',#250,'mechanical');
#1125=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#616,.NOT_KNOWN.);
#1126=CLOSED_SHELL('',(#870,#872,#874,#876,#878,#880));
#1127=PRODUCT_CONTEXT('',#258,'mechanical');
#1128=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#624,.NOT_KNOWN.);
#1129=CLOSED_SHELL('',(#883,#885,#887,#889,#891,#893));
#1130=PRODUCT_CONTEXT('',#266,'mechanical');
#1131=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#632,.NOT_KNOWN.);
#1132=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1133=DIRECTION('',(0.0,0.0,1.0));
#1134=DIRECTION('',(1.0,0.0,0.0));
#1135=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1136=DIRECTION('',(0.0,0.0,1.0));
#1137=DIRECTION('',(1.0,0.0,0.0));
#1138=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1139=DIRECTION('',(0.0,0.0,1.0));
#1140=DIRECTION('',(1.0,0.0,0.0));
#1141=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1142=DIRECTION('',(0.0,0.0,1.0));
#1143=DIRECTION('',(1.0,0.0,0.0));
#1146=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.0E-006),#1148,'','');
#1148= (CONVERSION_BASED_UNIT('MILLIMETRE',#1682)LENGTH_UNIT()NAMED_UNIT(#1685));
#1149= (NAMED_UNIT(#1687)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#1150= (NAMED_UNIT(#1687)SOLID_ANGLE_UNIT()SI_UNIT($,.STERADIAN.));
#1152=PRODUCT_CONTEXT('',#273,'mechanical');
#1153=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#644,.NOT_KNOWN.);
#1154=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1155=DIRECTION('',(0.0,0.0,1.0));
#1156=DIRECTION('',(1.0,0.0,0.0));
#1157=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1158=DIRECTION('',(0.0,0.0,1.0));
#1159=DIRECTION('',(1.0,0.0,0.0));
#1162=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.0E-006),#1164,'','');
#1164= (CONVERSION_BASED_UNIT('MILLIMETRE',#1695)LENGTH_UNIT()NAMED_UNIT(#1698));
#1165= (NAMED_UNIT(#1700)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#1166= (NAMED_UNIT(#1700)SOLID_ANGLE_UNIT()SI_UNIT($,.STERADIAN.));
#1168=PRODUCT_CONTEXT('',#278,'mechanical');
#1169=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#649,.NOT_KNOWN.);
#1170=CLOSED_SHELL('',(#896,#898,#900,#902));
#1171=CLOSED_SHELL('',(#905,#907,#909,#911,#913,#915));
#1172=CLOSED_SHELL('',(#918,#920,#922,#924,#926,#928));
#1173=CLOSED_SHELL('',(#931,#933,#935,#937,#939,#941));
#1174=CLOSED_SHELL('',(#944,#946,#948,#950,#952,#954));
#1175=CLOSED_SHELL('',(#957,#959,#961,#963,#965,#967));
#1176=CLOSED_SHELL('',(#970,#972,#974,#976,#978,#980));
#1177=CLOSED_SHELL('',(#983,#985,#987,#989,#991,#993));
#1178=PRODUCT_CONTEXT('',#288,'mechanical');
#1179=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#669,.NOT_KNOWN.);
#1180=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1181=DIRECTION('',(0.0,0.0,1.0));
#1182=DIRECTION('',(1.0,0.0,0.0));
#1183=CARTESIAN_POINT('',(0.0,0.0,0.0));
#1184=DIRECTION('',(0.0,0.0,1.0));
#1185=DIRECTION('',(1.0,0.0,0.0));
#1188=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.0E-006),#1190,'','');
#1190= (CONVERSION_BASED_UNIT('MILLIMETRE',#1708)LENGTH_UNIT()NAMED_UNIT(#1711));
#1191= (NAMED_UNIT(#1713)PLANE_ANGLE_UNIT()SI_UNIT($,.RADIAN.));
#1192= (NAMED_UNIT(#1713)SOLID_ANGLE_UNIT()SI_UNIT($,.STERADIAN.));
#1194=PRODUCT_CONTEXT('',#293,'mechanical');
#1195=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ','NONE',#674,.NOT_KNOWN.);
#1196=CLOSED_SHELL('',(#996,#998,#1000,#1002,#1004,#1006));
#1197=SURFACE_STYLE_USAGE(.BOTH.,#1719);
#1198=FACE_OUTER_BOUND('',#1720,.T.);
#1199=PLANE('',#1721);
#1200=SURFACE_STYLE_USAGE(.BOTH.,#1722);
#1201=FACE_OUTER_BOUND('',#1723,.T.);
#1202=PLANE('',#1724);
#1203=SURFACE_STYLE_USAGE(.BOTH.,#1725);
#1204=FACE_OUTER_BOUND('',#1726,.T.);
#1205=PLANE('',#1727);
#1206=SURFACE_STYLE_USAGE(.BOTH.,#1728);
#1207=FACE_OUTER_BOUND('',#1729,.T.);
#1208=PLANE('',#1730);
#1209=SURFACE_STYLE_USAGE(.BOTH.,#1731);
#1210=FACE_OUTER_BOUND('',#1732,.T.);
#1211=PLANE('',#1733);
#1212=SURFACE_STYLE_USAGE(.BOTH.,#1734);
#1213=FACE_OUTER_BOUND('',#1735,.T.);
#1214=PLANE('',#1736);
#1215=SURFACE_STYLE_USAGE(.BOTH.,#1737);
#1216=SURFACE_STYLE_USAGE(.BOTH.,#1738);
#1217=FACE_OUTER_BOUND('',#1739,.T.);
#1218=PLANE('',#1740);
#1219=SURFACE_STYLE_USAGE(.BOTH.,#1741);
#1220=FACE_OUTER_BOUND('',#1742,.T.);
#1221=PLANE('',#1743);
#1222=SURFACE_STYLE_USAGE(.BOTH.,#1744);
#1223=FACE_OUTER_BOUND('',#1745,.T.);
#1224=PLANE('',#1746);
#1225=SURFACE_STYLE_USAGE(.BOTH.,#1747);
#1226=FACE_OUTER_BOUND('',#1748,.T.);
#1227=PLANE('',#1749);
#1228=SURFACE_STYLE_USAGE(.BOTH.,#1750);
#1229=FACE_OUTER_BOUND('',#1751,.T.);
#1230=PLANE('',#1752);
#1231=SURFACE_STYLE_USAGE(.BOTH.,#1753);
#1232=FACE_OUTER_BOUND('',#1754,.T.);
#1233=PLANE('',#1755);
#1234=SURFACE_STYLE_USAGE(.BOTH.,#1756);
#1235=SURFACE_STYLE_USAGE(.BOTH.,#1757);
#1236=FACE_OUTER_BOUND('',#1758,.T.);
#1237=PLANE('',#1759);
#1238=SURFACE_STYLE_USAGE(.BOTH.,#1760);
#1239=FACE_OUTER_BOUND('',#1761,.T.);
#1240=PLANE('',#1762);
#1241=SURFACE_STYLE_USAGE(.BOTH.,#1763);
#1242=FACE_OUTER_BOUND('',#1764,.T.);
#1243=PLANE('',#1765);
#1244=SURFACE_STYLE_USAGE(.BOTH.,#1766);
#1245=FACE_OUTER_BOUND('',#1767,.T.);
#1246=PLANE('',#1768);
#1247=SURFACE_STYLE_USAGE(.BOTH.,#1769);
#1248=FACE_OUTER_BOUND('',#1770,.T.);
#1249=PLANE('',#1771);
#1250=SURFACE_STYLE_USAGE(.BOTH.,#1772);
#1251=FACE_OUTER_BOUND('',#1773,.T.);
#1252=PLANE('',#1774);
#1253=SURFACE_STYLE_USAGE(.BOTH.,#1775);
#1254=SURFACE_STYLE_USAGE(.BOTH.,#1776);
#1255=FACE_OUTER_BOUND('',#1777,.T.);
#1256=PLANE('',#1778);
#1257=SURFACE_STYLE_USAGE(.BOTH.,#1779);
#1258=FACE_OUTER_BOUND('',#1780,.T.);
#1259=PLANE('',#1781);
#1260=SURFACE_STYLE_USAGE(.BOTH.,#1782);
#1261=FACE_OUTER_BOUND('',#1783,.T.);
#1262=PLANE('',#1784);
#1263=SURFACE_STYLE_USAGE(.BOTH.,#1785);
#1264=FACE_OUTER_BOUND('',#1786,.T.);
#1265=PLANE('',#1787);
#1266=SURFACE_STYLE_USAGE(.BOTH.,#1788);
#1267=FACE_OUTER_BOUND('',#1789,.T.);
#1268=PLANE('',#1790);
#1269=SURFACE_STYLE_USAGE(.BOTH.,#1791);
#1270=FACE_OUTER_BOUND('',#1792,.T.);
#1271=PLANE('',#1793);
#1272=SURFACE_STYLE_USAGE(.BOTH.,#1794);
#1273=SURFACE_STYLE_USAGE(.BOTH.,#1795);
#1274=FACE_OUTER_BOUND('',#1796,.T.);
#1275=PLANE('',#1797);
#1276=SURFACE_STYLE_USAGE(.BOTH.,#1798);
#1277=FACE_OUTER_BOUND('',#1799,.T.);
#1278=PLANE('',#1800);
#1279=SURFACE_STYLE_USAGE(.BOTH.,#1801);
#1280=FACE_OUTER_BOUND('',#1802,.T.);
#1281=PLANE('',#1803);
#1282=SURFACE_STYLE_USAGE(.BOTH.,#1804);
#1283=FACE_OUTER_BOUND('',#1805,.T.);
#1284=PLANE('',#1806);
#1285=SURFACE_STYLE_USAGE(.BOTH.,#1807);
#1286=FACE_OUTER_BOUND('',#1808,.T.);
#1287=PLANE('',#1809);
#1288=SURFACE_STYLE_USAGE(.BOTH.,#1810);
#1289=FACE_OUTER_BOUND('',#1811,.T.);
#1290=PLANE('',#1812);
#1291=SURFACE_STYLE_USAGE(.BOTH.,#1813);
#1292=SURFACE_STYLE_USAGE(.BOTH.,#1814);
#1293=FACE_OUTER_BOUND('',#1815,.T.);
#1294=PLANE('',#1816);
#1295=SURFACE_STYLE_USAGE(.BOTH.,#1817);
#1296=FACE_OUTER_BOUND('',#1818,.T.);
#1297=PLANE('',#1819);
#1298=SURFACE_STYLE_USAGE(.BOTH.,#1820);
#1299=FACE_OUTER_BOUND('',#1821,.T.);
#1300=PLANE('',#1822);
#1301=SURFACE_STYLE_USAGE(.BOTH.,#1823);
#1302=FACE_OUTER_BOUND('',#1824,.T.);
#1303=PLANE('',#1825);
#1304=SURFACE_STYLE_USAGE(.BOTH.,#1826);
#1305=FACE_OUTER_BOUND('',#1827,.T.);
#1306=PLANE('',#1828);
#1307=SURFACE_STYLE_USAGE(.BOTH.,#1829);
#1308=FACE_OUTER_BOUND('',#1830,.T.);
#1309=PLANE('',#1831);
#1310=SURFACE_STYLE_USAGE(.BOTH.,#1832);
#1311=SURFACE_STYLE_USAGE(.BOTH.,#1833);
#1312=FACE_OUTER_BOUND('',#1834,.T.);
#1313=PLANE('',#1835);
#1314=SURFACE_STYLE_USAGE(.BOTH.,#1836);
#1315=FACE_OUTER_BOUND('',#1837,.T.);
#1316=PLANE('',#1838);
#1317=SURFACE_STYLE_USAGE(.BOTH.,#1839);
#1318=FACE_OUTER_BOUND('',#1840,.T.);
#1319=PLANE('',#1841);
#1320=SURFACE_STYLE_USAGE(.BOTH.,#1842);
#1321=FACE_OUTER_BOUND('',#1843,.T.);
#1322=PLANE('',#1844);
#1323=SURFACE_STYLE_USAGE(.BOTH.,#1845);
#1324=FACE_OUTER_BOUND('',#1846,.T.);
#1325=PLANE('',#1847);
#1326=SURFACE_STYLE_USAGE(.BOTH.,#1848);
#1327=FACE_OUTER_BOUND('',#1849,.T.);
#1328=PLANE('',#1850);
#1329=SURFACE_STYLE_USAGE(.BOTH.,#1851);
#1330=SURFACE_STYLE_USAGE(.BOTH.,#1852);
#1331=FACE_OUTER_BOUND('',#1853,.T.);
#1332=PLANE('',#1854);
#1333=SURFACE_STYLE_USAGE(.BOTH.,#1855);
#1334=FACE_OUTER_BOUND('',#1856,.T.);
#1335=PLANE('',#1857);
#1336=SURFACE_STYLE_USAGE(.BOTH.,#1858);
#1337=FACE_OUTER_BOUND('',#1859,.T.);
#1338=PLANE('',#1860);
#1339=SURFACE_STYLE_USAGE(.BOTH.,#1861);
#1340=FACE_OUTER_BOUND('',#1862,.T.);
#1341=PLANE('',#1863);
#1342=SURFACE_STYLE_USAGE(.BOTH.,#1864);
#1343=FACE_OUTER_BOUND('',#1865,.T.);
#1344=PLANE('',#1866);
#1345=SURFACE_STYLE_USAGE(.BOTH.,#1867);
#1346=FACE_OUTER_BOUND('',#1868,.T.);
#1347=PLANE('',#1869);
#1348=SURFACE_STYLE_USAGE(.BOTH.,#1870);
#1349=SURFACE_STYLE_USAGE(.BOTH.,#1871);
#1350=FACE_OUTER_BOUND('',#1872,.T.);
#1351=PLANE('',#1873);
#1352=SURFACE_STYLE_USAGE(.BOTH.,#1874);
#1353=FACE_OUTER_BOUND('',#1875,.T.);
#1354=PLANE('',#1876);
#1355=SURFACE_STYLE_USAGE(.BOTH.,#1877);
#1356=FACE_OUTER_BOUND('',#1878,.T.);
#1357=PLANE('',#1879);
#1358=SURFACE_STYLE_USAGE(.BOTH.,#1880);
#1359=FACE_OUTER_BOUND('',#1881,.T.);
#1360=PLANE('',#1882);
#1361=SURFACE_STYLE_USAGE(.BOTH.,#1883);
#1362=FACE_OUTER_BOUND('',#1884,.T.);
#1363=PLANE('',#1885);
#1364=SURFACE_STYLE_USAGE(.BOTH.,#1886);
#1365=FACE_OUTER_BOUND('',#1887,.T.);
#1366=PLANE('',#1888);
#1367=SURFACE_STYLE_USAGE(.BOTH.,#1889);
#1368=SURFACE_STYLE_USAGE(.BOTH.,#1890);
#1369=FACE_OUTER_BOUND('',#1891,.T.);
#1370=PLANE('',#1892);
#1371=SURFACE_STYLE_USAGE(.BOTH.,#1893);
#1372=FACE_OUTER_BOUND('',#1894,.T.);
#1373=PLANE('',#1895);
#1374=SURFACE_STYLE_USAGE(.BOTH.,#1896);
#1375=FACE_OUTER_BOUND('',#1897,.T.);
#1376=PLANE('',#1898);
#1377=SURFACE_STYLE_USAGE(.BOTH.,#1899);
#1378=FACE_OUTER_BOUND('',#1900,.T.);
#1379=PLANE('',#1901);
#1380=SURFACE_STYLE_USAGE(.BOTH.,#1902);
#1381=FACE_OUTER_BOUND('',#1903,.T.);
#1382=PLANE('',#1904);
#1383=SURFACE_STYLE_USAGE(.BOTH.,#1905);
#1384=FACE_OUTER_BOUND('',#1906,.T.);
#1385=PLANE('',#1907);
#1386=SURFACE_STYLE_USAGE(.BOTH.,#1908);
#1387=SURFACE_STYLE_USAGE(.BOTH.,#1909);
#1388=FACE_OUTER_BOUND('',#1910,.T.);
#1389=PLANE('',#1911);
#1390=SURFACE_STYLE_USAGE(.BOTH.,#1912);
#1391=FACE_OUTER_BOUND('',#1913,.T.);
#1392=PLANE('',#1914);
#1393=SURFACE_STYLE_USAGE(.BOTH.,#1915);
#1394=FACE_OUTER_BOUND('',#1916,.T.);
#1395=PLANE('',#1917);
#1396=SURFACE_STYLE_USAGE(.BOTH.,#1918);
#1397=FACE_OUTER_BOUND('',#1919,.T.);
#1398=PLANE('',#1920);
#1399=SURFACE_STYLE_USAGE(.BOTH.,#1921);
#1400=FACE_OUTER_BOUND('',#1922,.T.);
#1401=PLANE('',#1923);
#1402=SURFACE_STYLE_USAGE(.BOTH.,#1924);
#1403=FACE_OUTER_BOUND('',#1925,.T.);
#1404=PLANE('',#1926);
#1405=SURFACE_STYLE_USAGE(.BOTH.,#1927);
#1406=SURFACE_STYLE_USAGE(.BOTH.,#1928);
#1407=FACE_OUTER_BOUND('',#1929,.T.);
#1408=PLANE('',#1930);
#1409=SURFACE_STYLE_USAGE(.BOTH.,#1931);
#1410=FACE_OUTER_BOUND('',#1932,.T.);
#1411=PLANE('',#1933);
#1412=SURFACE_STYLE_USAGE(.BOTH.,#1934);
#1413=FACE_OUTER_BOUND('',#1935,.T.);
#1414=PLANE('',#1936);
#1415=SURFACE_STYLE_USAGE(.BOTH.,#1937);
#1416=FACE_OUTER_BOUND('',#1938,.T.);
#1417=PLANE('',#1939);
#1418=SURFACE_STYLE_USAGE(.BOTH.,#1940);
#1419=FACE_OUTER_BOUND('',#1941,.T.);
#1420=PLANE('',#1942);
#1421=SURFACE_STYLE_USAGE(.BOTH.,#1943);
#1422=FACE_OUTER_BOUND('',#1944,.T.);
#1423=PLANE('',#1945);
#1424=SURFACE_STYLE_USAGE(.BOTH.,#1946);
#1425=SURFACE_STYLE_USAGE(.BOTH.,#1947);
#1426=FACE_OUTER_BOUND('',#1948,.T.);
#1427=PLANE('',#1949);
#1428=SURFACE_STYLE_USAGE(.BOTH.,#1950);
#1429=FACE_OUTER_BOUND('',#1951,.T.);
#1430=PLANE('',#1952);
#1431=SURFACE_STYLE_USAGE(.BOTH.,#1953);
#1432=FACE_OUTER_BOUND('',#1954,.T.);
#1433=PLANE('',#1955);
#1434=SURFACE_STYLE_USAGE(.BOTH.,#1956);
#1435=FACE_OUTER_BOUND('',#1957,.T.);
#1436=PLANE('',#1958);
#1437=SURFACE_STYLE_USAGE(.BOTH.,#1959);
#1438=FACE_OUTER_BOUND('',#1960,.T.);
#1439=PLANE('',#1961);
#1440=SURFACE_STYLE_USAGE(.BOTH.,#1962);
#1441=FACE_OUTER_BOUND('',#1963,.T.);
#1442=PLANE('',#1964);
#1443=SURFACE_STYLE_USAGE(.BOTH.,#1965);
#1444=SURFACE_STYLE_USAGE(.BOTH.,#1966);
#1445=FACE_OUTER_BOUND('',#1967,.T.);
#1446=PLANE('',#1968);
#1447=SURFACE_STYLE_USAGE(.BOTH.,#1969);
#1448=FACE_OUTER_BOUND('',#1970,.T.);
#1449=PLANE('',#1971);
#1450=SURFACE_STYLE_USAGE(.BOTH.,#1972);
#1451=FACE_OUTER_BOUND('',#1973,.T.);
#1452=PLANE('',#1974);
#1453=SURFACE_STYLE_USAGE(.BOTH.,#1975);
#1454=FACE_OUTER_BOUND('',#1976,.T.);
#1455=PLANE('',#1977);
#1456=SURFACE_STYLE_USAGE(.BOTH.,#1978);
#1457=FACE_OUTER_BOUND('',#1979,.T.);
#1458=PLANE('',#1980);
#1459=SURFACE_STYLE_USAGE(.BOTH.,#1981);
#1460=FACE_OUTER_BOUND('',#1982,.T.);
#1461=PLANE('',#1983);
#1462=SURFACE_STYLE_USAGE(.BOTH.,#1984);
#1463=SURFACE_STYLE_USAGE(.BOTH.,#1985);
#1464=FACE_OUTER_BOUND('',#1986,.T.);
#1465=PLANE('',#1987);
#1466=SURFACE_STYLE_USAGE(.BOTH.,#1988);
#1467=FACE_OUTER_BOUND('',#1989,.T.);
#1468=PLANE('',#1990);
#1469=SURFACE_STYLE_USAGE(.BOTH.,#1991);
#1470=FACE_OUTER_BOUND('',#1992,.T.);
#1471=PLANE('',#1993);
#1472=SURFACE_STYLE_USAGE(.BOTH.,#1994);
#1473=FACE_OUTER_BOUND('',#1995,.T.);
#1474=PLANE('',#1996);
#1475=SURFACE_STYLE_USAGE(.BOTH.,#1997);
#1476=FACE_OUTER_BOUND('',#1998,.T.);
#1477=PLANE('',#1999);
#1478=SURFACE_STYLE_USAGE(.BOTH.,#2000);
#1479=FACE_OUTER_BOUND('',#2001,.T.);
#1480=PLANE('',#2002);
#1481=SURFACE_STYLE_USAGE(.BOTH.,#2003);
#1482=SURFACE_STYLE_USAGE(.BOTH.,#2004);
#1483=FACE_OUTER_BOUND('',#2005,.T.);
#1484=PLANE('',#2006);
#1485=SURFACE_STYLE_USAGE(.BOTH.,#2007);
#1486=FACE_OUTER_BOUND('',#2008,.T.);
#1487=PLANE('',#2009);
#1488=SURFACE_STYLE_USAGE(.BOTH.,#2010);
#1489=FACE_OUTER_BOUND('',#2011,.T.);
#1490=PLANE('',#2012);
#1491=SURFACE_STYLE_USAGE(.BOTH.,#2013);
#1492=FACE_OUTER_BOUND('',#2014,.T.);
#1493=PLANE('',#2015);
#1494=SURFACE_STYLE_USAGE(.BOTH.,#2016);
#1495=FACE_OUTER_BOUND('',#2017,.T.);
#1496=PLANE('',#2018);
#1497=SURFACE_STYLE_USAGE(.BOTH.,#2019);
#1498=FACE_OUTER_BOUND('',#2020,.T.);
#1499=PLANE('',#2021);
#1500=SURFACE_STYLE_USAGE(.BOTH.,#2022);
#1501=SURFACE_STYLE_USAGE(.BOTH.,#2023);
#1502=FACE_OUTER_BOUND('',#2024,.T.);
#1503=CYLINDRICAL_SURFACE('',#2025,0.253999887089593);
#1504=SURFACE_STYLE_USAGE(.BOTH.,#2026);
#1505=FACE_OUTER_BOUND('',#2027,.T.);
#1506=PLANE('',#2028);
#1507=SURFACE_STYLE_USAGE(.BOTH.,#2029);
#1508=FACE_OUTER_BOUND('',#2030,.T.);
#1509=PLANE('',#2031);
#1510=SURFACE_STYLE_USAGE(.BOTH.,#2032);
#1511=FACE_OUTER_BOUND('',#2033,.T.);
#1512=CYLINDRICAL_SURFACE('',#2034,0.253999887089593);
#1513=SURFACE_STYLE_USAGE(.BOTH.,#2035);
#1514=SURFACE_STYLE_USAGE(.BOTH.,#2036);
#1515=FACE_OUTER_BOUND('',#2037,.T.);
#1516=PLANE('',#2038);
#1517=SURFACE_STYLE_USAGE(.BOTH.,#2039);
#1518=FACE_OUTER_BOUND('',#2040,.T.);
#1519=PLANE('',#2041);
#1520=SURFACE_STYLE_USAGE(.BOTH.,#2042);
#1521=FACE_OUTER_BOUND('',#2043,.T.);
#1522=PLANE('',#2044);
#1523=SURFACE_STYLE_USAGE(.BOTH.,#2045);
#1524=FACE_OUTER_BOUND('',#2046,.T.);
#1525=CYLINDRICAL_SURFACE('',#2047,0.0635000610358896);
#1526=SURFACE_STYLE_USAGE(.BOTH.,#2048);
#1527=FACE_OUTER_BOUND('',#2049,.T.);
#1528=PLANE('',#2050);
#1529=SURFACE_STYLE_USAGE(.BOTH.,#2051);
#1530=FACE_OUTER_BOUND('',#2052,.T.);
#1531=CYLINDRICAL_SURFACE('',#2053,0.0635000610358896);
#1532=SURFACE_STYLE_USAGE(.BOTH.,#2054);
#1533=SURFACE_STYLE_USAGE(.BOTH.,#2055);
#1534=FACE_OUTER_BOUND('',#2056,.T.);
#1535=PLANE('',#2057);
#1536=SURFACE_STYLE_USAGE(.BOTH.,#2058);
#1537=FACE_OUTER_BOUND('',#2059,.T.);
#1538=PLANE('',#2060);
#1539=SURFACE_STYLE_USAGE(.BOTH.,#2061);
#1540=FACE_OUTER_BOUND('',#2062,.T.);
#1541=PLANE('',#2063);
#1542=SURFACE_STYLE_USAGE(.BOTH.,#2064);
#1543=FACE_OUTER_BOUND('',#2065,.T.);
#1544=CYLINDRICAL_SURFACE('',#2066,0.0635000610351562);
#1545=SURFACE_STYLE_USAGE(.BOTH.,#2067);
#1546=FACE_OUTER_BOUND('',#2068,.T.);
#1547=PLANE('',#2069);
#1548=SURFACE_STYLE_USAGE(.BOTH.,#2070);
#1549=FACE_OUTER_BOUND('',#2071,.T.);
#1550=CYLINDRICAL_SURFACE('',#2072,0.0635000610351562);
#1551=SURFACE_STYLE_USAGE(.BOTH.,#2073);
#1552=SURFACE_STYLE_USAGE(.BOTH.,#2074);
#1553=FACE_OUTER_BOUND('',#2075,.T.);
#1554=PLANE('',#2076);
#1555=SURFACE_STYLE_USAGE(.BOTH.,#2077);
#1556=FACE_OUTER_BOUND('',#2078,.T.);
#1557=PLANE('',#2079);
#1558=SURFACE_STYLE_USAGE(.BOTH.,#2080);
#1559=FACE_OUTER_BOUND('',#2081,.T.);
#1560=PLANE('',#2082);
#1561=SURFACE_STYLE_USAGE(.BOTH.,#2083);
#1562=FACE_OUTER_BOUND('',#2084,.T.);
#1563=CYLINDRICAL_SURFACE('',#2085,0.0635000610358896);
#1564=SURFACE_STYLE_USAGE(.BOTH.,#2086);
#1565=FACE_OUTER_BOUND('',#2087,.T.);
#1566=PLANE('',#2088);
#1567=SURFACE_STYLE_USAGE(.BOTH.,#2089);
#1568=FACE_OUTER_BOUND('',#2090,.T.);
#1569=CYLINDRICAL_SURFACE('',#2091,0.0635000610358896);
#1570=SURFACE_STYLE_USAGE(.BOTH.,#2092);
#1571=SURFACE_STYLE_USAGE(.BOTH.,#2093);
#1572=FACE_OUTER_BOUND('',#2094,.T.);
#1573=PLANE('',#2095);
#1574=SURFACE_STYLE_USAGE(.BOTH.,#2096);
#1575=FACE_OUTER_BOUND('',#2097,.T.);
#1576=PLANE('',#2098);
#1577=SURFACE_STYLE_USAGE(.BOTH.,#2099);
#1578=FACE_OUTER_BOUND('',#2100,.T.);
#1579=PLANE('',#2101);
#1580=SURFACE_STYLE_USAGE(.BOTH.,#2102);
#1581=FACE_OUTER_BOUND('',#2103,.T.);
#1582=CYLINDRICAL_SURFACE('',#2104,0.0635000610358896);
#1583=SURFACE_STYLE_USAGE(.BOTH.,#2105);
#1584=FACE_OUTER_BOUND('',#2106,.T.);
#1585=PLANE('',#2107);
#1586=SURFACE_STYLE_USAGE(.BOTH.,#2108);
#1587=FACE_OUTER_BOUND('',#2109,.T.);
#1588=CYLINDRICAL_SURFACE('',#2110,0.0635000610358896);
#1589=SURFACE_STYLE_USAGE(.BOTH.,#2111);
#1590=SURFACE_STYLE_USAGE(.BOTH.,#2112);
#1591=FACE_OUTER_BOUND('',#2113,.T.);
#1592=PLANE('',#2114);
#1593=SURFACE_STYLE_USAGE(.BOTH.,#2115);
#1594=FACE_OUTER_BOUND('',#2116,.T.);
#1595=PLANE('',#2117);
#1596=SURFACE_STYLE_USAGE(.BOTH.,#2118);
#1597=FACE_OUTER_BOUND('',#2119,.T.);
#1598=PLANE('',#2120);
#1599=SURFACE_STYLE_USAGE(.BOTH.,#2121);
#1600=FACE_OUTER_BOUND('',#2122,.T.);
#1601=CYLINDRICAL_SURFACE('',#2123,0.0635000610351562);
#1602=SURFACE_STYLE_USAGE(.BOTH.,#2124);
#1603=FACE_OUTER_BOUND('',#2125,.T.);
#1604=PLANE('',#2126);
#1605=SURFACE_STYLE_USAGE(.BOTH.,#2127);
#1606=FACE_OUTER_BOUND('',#2128,.T.);
#1607=CYLINDRICAL_SURFACE('',#2129,0.0635000610351562);
#1608=SURFACE_STYLE_USAGE(.BOTH.,#2130);
#1609=SURFACE_STYLE_USAGE(.BOTH.,#2131);
#1610=FACE_OUTER_BOUND('',#2132,.T.);
#1611=PLANE('',#2133);
#1612=SURFACE_STYLE_USAGE(.BOTH.,#2134);
#1613=FACE_OUTER_BOUND('',#2135,.T.);
#1614=PLANE('',#2136);
#1615=SURFACE_STYLE_USAGE(.BOTH.,#2137);
#1616=FACE_OUTER_BOUND('',#2138,.T.);
#1617=PLANE('',#2139);
#1618=SURFACE_STYLE_USAGE(.BOTH.,#2140);
#1619=FACE_OUTER_BOUND('',#2141,.T.);
#1620=CYLINDRICAL_SURFACE('',#2142,0.0635000610358896);
#1621=SURFACE_STYLE_USAGE(.BOTH.,#2143);
#1622=FACE_OUTER_BOUND('',#2144,.T.);
#1623=PLANE('',#2145);
#1624=SURFACE_STYLE_USAGE(.BOTH.,#2146);
#1625=FACE_OUTER_BOUND('',#2147,.T.);
#1626=CYLINDRICAL_SURFACE('',#2148,0.0635000610358896);
#1627=SURFACE_STYLE_USAGE(.BOTH.,#2149);
#1628=SURFACE_STYLE_USAGE(.BOTH.,#2150);
#1629=FACE_OUTER_BOUND('',#2151,.T.);
#1630=PLANE('',#2152);
#1631=SURFACE_STYLE_USAGE(.BOTH.,#2153);
#1632=FACE_OUTER_BOUND('',#2154,.T.);
#1633=PLANE('',#2155);
#1634=SURFACE_STYLE_USAGE(.BOTH.,#2156);
#1635=FACE_OUTER_BOUND('',#2157,.T.);
#1636=PLANE('',#2158);
#1637=SURFACE_STYLE_USAGE(.BOTH.,#2159);
#1638=FACE_OUTER_BOUND('',#2160,.T.);
#1639=CYLINDRICAL_SURFACE('',#2161,0.0635000610358896);
#1640=SURFACE_STYLE_USAGE(.BOTH.,#2162);
#1641=FACE_OUTER_BOUND('',#2163,.T.);
#1642=PLANE('',#2164);
#1643=SURFACE_STYLE_USAGE(.BOTH.,#2165);
#1644=FACE_OUTER_BOUND('',#2166,.T.);
#1645=CYLINDRICAL_SURFACE('',#2167,0.0635000610351562);
#1646=SURFACE_STYLE_USAGE(.BOTH.,#2168);
#1647=SURFACE_STYLE_USAGE(.BOTH.,#2169);
#1648=FACE_OUTER_BOUND('',#2170,.T.);
#1649=PLANE('',#2171);
#1650=SURFACE_STYLE_USAGE(.BOTH.,#2172);
#1651=FACE_OUTER_BOUND('',#2173,.T.);
#1652=PLANE('',#2174);
#1653=SURFACE_STYLE_USAGE(.BOTH.,#2175);
#1654=FACE_OUTER_BOUND('',#2176,.T.);
#1655=PLANE('',#2177);
#1656=SURFACE_STYLE_USAGE(.BOTH.,#2178);
#1657=FACE_OUTER_BOUND('',#2179,.T.);
#1658=PLANE('',#2180);
#1659=SURFACE_STYLE_USAGE(.BOTH.,#2181);
#1660=FACE_OUTER_BOUND('',#2182,.T.);
#1661=PLANE('',#2183);
#1662=SURFACE_STYLE_USAGE(.BOTH.,#2184);
#1663=FACE_OUTER_BOUND('',#2185,.T.);
#1664=PLANE('',#2186);
#1665=SURFACE_STYLE_USAGE(.BOTH.,#2187);
#1666= (NAMED_UNIT(#1013)LENGTH_UNIT()SI_UNIT(.MILLI.,.METRE.));
#1669=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.0),#2189);
#1672=DIMENSIONAL_EXPONENTS(1.0,0.0,0.0,0.0,0.0,0.0,0.0);
#1674=DIMENSIONAL_EXPONENTS(0.0,0.0,0.0,0.0,0.0,0.0,0.0);
#1682=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.0),#2190);
#1685=DIMENSIONAL_EXPONENTS(1.0,0.0,0.0,0.0,0.0,0.0,0.0);
#1687=DIMENSIONAL_EXPONENTS(0.0,0.0,0.0,0.0,0.0,0.0,0.0);
#1695=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.0),#2191);
#1698=DIMENSIONAL_EXPONENTS(1.0,0.0,0.0,0.0,0.0,0.0,0.0);
#1700=DIMENSIONAL_EXPONENTS(0.0,0.0,0.0,0.0,0.0,0.0,0.0);
#1708=LENGTH_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.0),#2192);
#1711=DIMENSIONAL_EXPONENTS(1.0,0.0,0.0,0.0,0.0,0.0,0.0);
#1713=DIMENSIONAL_EXPONENTS(0.0,0.0,0.0,0.0,0.0,0.0,0.0);
#1719=SURFACE_SIDE_STYLE('',(#2193));
#1720=EDGE_LOOP('',(#2194,#2195,#2196,#2197));
#1721=AXIS2_PLACEMENT_3D('',#2198,#2199,#2200);
#1722=SURFACE_SIDE_STYLE('',(#2201));
#1723=EDGE_LOOP('',(#2202,#2203,#2204,#2205));
#1724=AXIS2_PLACEMENT_3D('',#2206,#2207,#2208);
#1725=SURFACE_SIDE_STYLE('',(#2209));
#1726=EDGE_LOOP('',(#2210,#2211,#2212,#2213));
#1727=AXIS2_PLACEMENT_3D('',#2214,#2215,#2216);
#1728=SURFACE_SIDE_STYLE('',(#2217));
#1729=EDGE_LOOP('',(#2218,#2219,#2220,#2221));
#1730=AXIS2_PLACEMENT_3D('',#2222,#2223,#2224);
#1731=SURFACE_SIDE_STYLE('',(#2225));
#1732=EDGE_LOOP('',(#2226,#2227,#2228,#2229));
#1733=AXIS2_PLACEMENT_3D('',#2230,#2231,#2232);
#1734=SURFACE_SIDE_STYLE('',(#2233));
#1735=EDGE_LOOP('',(#2234,#2235,#2236,#2237));
#1736=AXIS2_PLACEMENT_3D('',#2238,#2239,#2240);
#1737=SURFACE_SIDE_STYLE('',(#2241));
#1738=SURFACE_SIDE_STYLE('',(#2242));
#1739=EDGE_LOOP('',(#2243,#2244,#2245,#2246));
#1740=AXIS2_PLACEMENT_3D('',#2247,#2248,#2249);
#1741=SURFACE_SIDE_STYLE('',(#2250));
#1742=EDGE_LOOP('',(#2251,#2252,#2253,#2254));
#1743=AXIS2_PLACEMENT_3D('',#2255,#2256,#2257);
#1744=SURFACE_SIDE_STYLE('',(#2258));
#1745=EDGE_LOOP('',(#2259,#2260,#2261,#2262));
#1746=AXIS2_PLACEMENT_3D('',#2263,#2264,#2265);
#1747=SURFACE_SIDE_STYLE('',(#2266));
#1748=EDGE_LOOP('',(#2267,#2268,#2269,#2270));
#1749=AXIS2_PLACEMENT_3D('',#2271,#2272,#2273);
#1750=SURFACE_SIDE_STYLE('',(#2274));
#1751=EDGE_LOOP('',(#2275,#2276,#2277,#2278));
#1752=AXIS2_PLACEMENT_3D('',#2279,#2280,#2281);
#1753=SURFACE_SIDE_STYLE('',(#2282));
#1754=EDGE_LOOP('',(#2283,#2284,#2285,#2286));
#1755=AXIS2_PLACEMENT_3D('',#2287,#2288,#2289);
#1756=SURFACE_SIDE_STYLE('',(#2290));
#1757=SURFACE_SIDE_STYLE('',(#2291));
#1758=EDGE_LOOP('',(#2292,#2293,#2294,#2295));
#1759=AXIS2_PLACEMENT_3D('',#2296,#2297,#2298);
#1760=SURFACE_SIDE_STYLE('',(#2299));
#1761=EDGE_LOOP('',(#2300,#2301,#2302,#2303));
#1762=AXIS2_PLACEMENT_3D('',#2304,#2305,#2306);
#1763=SURFACE_SIDE_STYLE('',(#2307));
#1764=EDGE_LOOP('',(#2308,#2309,#2310,#2311));
#1765=AXIS2_PLACEMENT_3D('',#2312,#2313,#2314);
#1766=SURFACE_SIDE_STYLE('',(#2315));
#1767=EDGE_LOOP('',(#2316,#2317,#2318,#2319));
#1768=AXIS2_PLACEMENT_3D('',#2320,#2321,#2322);
#1769=SURFACE_SIDE_STYLE('',(#2323));
#1770=EDGE_LOOP('',(#2324,#2325,#2326,#2327));
#1771=AXIS2_PLACEMENT_3D('',#2328,#2329,#2330);
#1772=SURFACE_SIDE_STYLE('',(#2331));
#1773=EDGE_LOOP('',(#2332,#2333,#2334,#2335));
#1774=AXIS2_PLACEMENT_3D('',#2336,#2337,#2338);
#1775=SURFACE_SIDE_STYLE('',(#2339));
#1776=SURFACE_SIDE_STYLE('',(#2340));
#1777=EDGE_LOOP('',(#2341,#2342,#2343,#2344));
#1778=AXIS2_PLACEMENT_3D('',#2345,#2346,#2347);
#1779=SURFACE_SIDE_STYLE('',(#2348));
#1780=EDGE_LOOP('',(#2349,#2350,#2351,#2352));
#1781=AXIS2_PLACEMENT_3D('',#2353,#2354,#2355);
#1782=SURFACE_SIDE_STYLE('',(#2356));
#1783=EDGE_LOOP('',(#2357,#2358,#2359,#2360));
#1784=AXIS2_PLACEMENT_3D('',#2361,#2362,#2363);
#1785=SURFACE_SIDE_STYLE('',(#2364));
#1786=EDGE_LOOP('',(#2365,#2366,#2367,#2368));
#1787=AXIS2_PLACEMENT_3D('',#2369,#2370,#2371);
#1788=SURFACE_SIDE_STYLE('',(#2372));
#1789=EDGE_LOOP('',(#2373,#2374,#2375,#2376));
#1790=AXIS2_PLACEMENT_3D('',#2377,#2378,#2379);
#1791=SURFACE_SIDE_STYLE('',(#2380));
#1792=EDGE_LOOP('',(#2381,#2382,#2383,#2384));
#1793=AXIS2_PLACEMENT_3D('',#2385,#2386,#2387);
#1794=SURFACE_SIDE_STYLE('',(#2388));
#1795=SURFACE_SIDE_STYLE('',(#2389));
#1796=EDGE_LOOP('',(#2390,#2391,#2392,#2393));
#1797=AXIS2_PLACEMENT_3D('',#2394,#2395,#2396);
#1798=SURFACE_SIDE_STYLE('',(#2397));
#1799=EDGE_LOOP('',(#2398,#2399,#2400,#2401));
#1800=AXIS2_PLACEMENT_3D('',#2402,#2403,#2404);
#1801=SURFACE_SIDE_STYLE('',(#2405));
#1802=EDGE_LOOP('',(#2406,#2407,#2408,#2409));
#1803=AXIS2_PLACEMENT_3D('',#2410,#2411,#2412);
#1804=SURFACE_SIDE_STYLE('',(#2413));
#1805=EDGE_LOOP('',(#2414,#2415,#2416,#2417));
#1806=AXIS2_PLACEMENT_3D('',#2418,#2419,#2420);
#1807=SURFACE_SIDE_STYLE('',(#2421));
#1808=EDGE_LOOP('',(#2422,#2423,#2424,#2425));
#1809=AXIS2_PLACEMENT_3D('',#2426,#2427,#2428);
#1810=SURFACE_SIDE_STYLE('',(#2429));
#1811=EDGE_LOOP('',(#2430,#2431,#2432,#2433));
#1812=AXIS2_PLACEMENT_3D('',#2434,#2435,#2436);
#1813=SURFACE_SIDE_STYLE('',(#2437));
#1814=SURFACE_SIDE_STYLE('',(#2438));
#1815=EDGE_LOOP('',(#2439,#2440,#2441,#2442));
#1816=AXIS2_PLACEMENT_3D('',#2443,#2444,#2445);
#1817=SURFACE_SIDE_STYLE('',(#2446));
#1818=EDGE_LOOP('',(#2447,#2448,#2449,#2450));
#1819=AXIS2_PLACEMENT_3D('',#2451,#2452,#2453);
#1820=SURFACE_SIDE_STYLE('',(#2454));
#1821=EDGE_LOOP('',(#2455,#2456,#2457,#2458));
#1822=AXIS2_PLACEMENT_3D('',#2459,#2460,#2461);
#1823=SURFACE_SIDE_STYLE('',(#2462));
#1824=EDGE_LOOP('',(#2463,#2464,#2465,#2466));
#1825=AXIS2_PLACEMENT_3D('',#2467,#2468,#2469);
#1826=SURFACE_SIDE_STYLE('',(#2470));
#1827=EDGE_LOOP('',(#2471,#2472,#2473,#2474));
#1828=AXIS2_PLACEMENT_3D('',#2475,#2476,#2477);
#1829=SURFACE_SIDE_STYLE('',(#2478));
#1830=EDGE_LOOP('',(#2479,#2480,#2481,#2482));
#1831=AXIS2_PLACEMENT_3D('',#2483,#2484,#2485);
#1832=SURFACE_SIDE_STYLE('',(#2486));
#1833=SURFACE_SIDE_STYLE('',(#2487));
#1834=EDGE_LOOP('',(#2488,#2489,#2490,#2491));
#1835=AXIS2_PLACEMENT_3D('',#2492,#2493,#2494);
#1836=SURFACE_SIDE_STYLE('',(#2495));
#1837=EDGE_LOOP('',(#2496,#2497,#2498,#2499));
#1838=AXIS2_PLACEMENT_3D('',#2500,#2501,#2502);
#1839=SURFACE_SIDE_STYLE('',(#2503));
#1840=EDGE_LOOP('',(#2504,#2505,#2506,#2507));
#1841=AXIS2_PLACEMENT_3D('',#2508,#2509,#2510);
#1842=SURFACE_SIDE_STYLE('',(#2511));
#1843=EDGE_LOOP('',(#2512,#2513,#2514,#2515));
#1844=AXIS2_PLACEMENT_3D('',#2516,#2517,#2518);
#1845=SURFACE_SIDE_STYLE('',(#2519));
#1846=EDGE_LOOP('',(#2520,#2521,#2522,#2523));
#1847=AXIS2_PLACEMENT_3D('',#2524,#2525,#2526);
#1848=SURFACE_SIDE_STYLE('',(#2527));
#1849=EDGE_LOOP('',(#2528,#2529,#2530,#2531));
#1850=AXIS2_PLACEMENT_3D('',#2532,#2533,#2534);
#1851=SURFACE_SIDE_STYLE('',(#2535));
#1852=SURFACE_SIDE_STYLE('',(#2536));
#1853=EDGE_LOOP('',(#2537,#2538,#2539,#2540));
#1854=AXIS2_PLACEMENT_3D('',#2541,#2542,#2543);
#1855=SURFACE_SIDE_STYLE('',(#2544));
#1856=EDGE_LOOP('',(#2545,#2546,#2547,#2548));
#1857=AXIS2_PLACEMENT_3D('',#2549,#2550,#2551);
#1858=SURFACE_SIDE_STYLE('',(#2552));
#1859=EDGE_LOOP('',(#2553,#2554,#2555,#2556));
#1860=AXIS2_PLACEMENT_3D('',#2557,#2558,#2559);
#1861=SURFACE_SIDE_STYLE('',(#2560));
#1862=EDGE_LOOP('',(#2561,#2562,#2563,#2564));
#1863=AXIS2_PLACEMENT_3D('',#2565,#2566,#2567);
#1864=SURFACE_SIDE_STYLE('',(#2568));
#1865=EDGE_LOOP('',(#2569,#2570,#2571,#2572));
#1866=AXIS2_PLACEMENT_3D('',#2573,#2574,#2575);
#1867=SURFACE_SIDE_STYLE('',(#2576));
#1868=EDGE_LOOP('',(#2577,#2578,#2579,#2580));
#1869=AXIS2_PLACEMENT_3D('',#2581,#2582,#2583);
#1870=SURFACE_SIDE_STYLE('',(#2584));
#1871=SURFACE_SIDE_STYLE('',(#2585));
#1872=EDGE_LOOP('',(#2586,#2587,#2588,#2589));
#1873=AXIS2_PLACEMENT_3D('',#2590,#2591,#2592);
#1874=SURFACE_SIDE_STYLE('',(#2593));
#1875=EDGE_LOOP('',(#2594,#2595,#2596,#2597));
#1876=AXIS2_PLACEMENT_3D('',#2598,#2599,#2600);
#1877=SURFACE_SIDE_STYLE('',(#2601));
#1878=EDGE_LOOP('',(#2602,#2603,#2604,#2605));
#1879=AXIS2_PLACEMENT_3D('',#2606,#2607,#2608);
#1880=SURFACE_SIDE_STYLE('',(#2609));
#1881=EDGE_LOOP('',(#2610,#2611,#2612,#2613));
#1882=AXIS2_PLACEMENT_3D('',#2614,#2615,#2616);
#1883=SURFACE_SIDE_STYLE('',(#2617));
#1884=EDGE_LOOP('',(#2618,#2619,#2620,#2621));
#1885=AXIS2_PLACEMENT_3D('',#2622,#2623,#2624);
#1886=SURFACE_SIDE_STYLE('',(#2625));
#1887=EDGE_LOOP('',(#2626,#2627,#2628,#2629));
#1888=AXIS2_PLACEMENT_3D('',#2630,#2631,#2632);
#1889=SURFACE_SIDE_STYLE('',(#2633));
#1890=SURFACE_SIDE_STYLE('',(#2634));
#1891=EDGE_LOOP('',(#2635,#2636,#2637,#2638));
#1892=AXIS2_PLACEMENT_3D('',#2639,#2640,#2641);
#1893=SURFACE_SIDE_STYLE('',(#2642));
#1894=EDGE_LOOP('',(#2643,#2644,#2645,#2646));
#1895=AXIS2_PLACEMENT_3D('',#2647,#2648,#2649);
#1896=SURFACE_SIDE_STYLE('',(#2650));
#1897=EDGE_LOOP('',(#2651,#2652,#2653,#2654));
#1898=AXIS2_PLACEMENT_3D('',#2655,#2656,#2657);
#1899=SURFACE_SIDE_STYLE('',(#2658));
#1900=EDGE_LOOP('',(#2659,#2660,#2661,#2662));
#1901=AXIS2_PLACEMENT_3D('',#2663,#2664,#2665);
#1902=SURFACE_SIDE_STYLE('',(#2666));
#1903=EDGE_LOOP('',(#2667,#2668,#2669,#2670));
#1904=AXIS2_PLACEMENT_3D('',#2671,#2672,#2673);
#1905=SURFACE_SIDE_STYLE('',(#2674));
#1906=EDGE_LOOP('',(#2675,#2676,#2677,#2678));
#1907=AXIS2_PLACEMENT_3D('',#2679,#2680,#2681);
#1908=SURFACE_SIDE_STYLE('',(#2682));
#1909=SURFACE_SIDE_STYLE('',(#2683));
#1910=EDGE_LOOP('',(#2684,#2685,#2686,#2687));
#1911=AXIS2_PLACEMENT_3D('',#2688,#2689,#2690);
#1912=SURFACE_SIDE_STYLE('',(#2691));
#1913=EDGE_LOOP('',(#2692,#2693,#2694,#2695));
#1914=AXIS2_PLACEMENT_3D('',#2696,#2697,#2698);
#1915=SURFACE_SIDE_STYLE('',(#2699));
#1916=EDGE_LOOP('',(#2700,#2701,#2702,#2703));
#1917=AXIS2_PLACEMENT_3D('',#2704,#2705,#2706);
#1918=SURFACE_SIDE_STYLE('',(#2707));
#1919=EDGE_LOOP('',(#2708,#2709,#2710,#2711));
#1920=AXIS2_PLACEMENT_3D('',#2712,#2713,#2714);
#1921=SURFACE_SIDE_STYLE('',(#2715));
#1922=EDGE_LOOP('',(#2716,#2717,#2718,#2719));
#1923=AXIS2_PLACEMENT_3D('',#2720,#2721,#2722);
#1924=SURFACE_SIDE_STYLE('',(#2723));
#1925=EDGE_LOOP('',(#2724,#2725,#2726,#2727));
#1926=AXIS2_PLACEMENT_3D('',#2728,#2729,#2730);
#1927=SURFACE_SIDE_STYLE('',(#2731));
#1928=SURFACE_SIDE_STYLE('',(#2732));
#1929=EDGE_LOOP('',(#2733,#2734,#2735,#2736));
#1930=AXIS2_PLACEMENT_3D('',#2737,#2738,#2739);
#1931=SURFACE_SIDE_STYLE('',(#2740));
#1932=EDGE_LOOP('',(#2741,#2742,#2743,#2744));
#1933=AXIS2_PLACEMENT_3D('',#2745,#2746,#2747);
#1934=SURFACE_SIDE_STYLE('',(#2748));
#1935=EDGE_LOOP('',(#2749,#2750,#2751,#2752));
#1936=AXIS2_PLACEMENT_3D('',#2753,#2754,#2755);
#1937=SURFACE_SIDE_STYLE('',(#2756));
#1938=EDGE_LOOP('',(#2757,#2758,#2759,#2760));
#1939=AXIS2_PLACEMENT_3D('',#2761,#2762,#2763);
#1940=SURFACE_SIDE_STYLE('',(#2764));
#1941=EDGE_LOOP('',(#2765,#2766,#2767,#2768));
#1942=AXIS2_PLACEMENT_3D('',#2769,#2770,#2771);
#1943=SURFACE_SIDE_STYLE('',(#2772));
#1944=EDGE_LOOP('',(#2773,#2774,#2775,#2776));
#1945=AXIS2_PLACEMENT_3D('',#2777,#2778,#2779);
#1946=SURFACE_SIDE_STYLE('',(#2780));
#1947=SURFACE_SIDE_STYLE('',(#2781));
#1948=EDGE_LOOP('',(#2782,#2783,#2784,#2785));
#1949=AXIS2_PLACEMENT_3D('',#2786,#2787,#2788);
#1950=SURFACE_SIDE_STYLE('',(#2789));
#1951=EDGE_LOOP('',(#2790,#2791,#2792,#2793));
#1952=AXIS2_PLACEMENT_3D('',#2794,#2795,#2796);
#1953=SURFACE_SIDE_STYLE('',(#2797));
#1954=EDGE_LOOP('',(#2798,#2799,#2800,#2801));
#1955=AXIS2_PLACEMENT_3D('',#2802,#2803,#2804);
#1956=SURFACE_SIDE_STYLE('',(#2805));
#1957=EDGE_LOOP('',(#2806,#2807,#2808,#2809));
#1958=AXIS2_PLACEMENT_3D('',#2810,#2811,#2812);
#1959=SURFACE_SIDE_STYLE('',(#2813));
#1960=EDGE_LOOP('',(#2814,#2815,#2816,#2817));
#1961=AXIS2_PLACEMENT_3D('',#2818,#2819,#2820);
#1962=SURFACE_SIDE_STYLE('',(#2821));
#1963=EDGE_LOOP('',(#2822,#2823,#2824,#2825));
#1964=AXIS2_PLACEMENT_3D('',#2826,#2827,#2828);
#1965=SURFACE_SIDE_STYLE('',(#2829));
#1966=SURFACE_SIDE_STYLE('',(#2830));
#1967=EDGE_LOOP('',(#2831,#2832,#2833,#2834));
#1968=AXIS2_PLACEMENT_3D('',#2835,#2836,#2837);
#1969=SURFACE_SIDE_STYLE('',(#2838));
#1970=EDGE_LOOP('',(#2839,#2840,#2841,#2842));
#1971=AXIS2_PLACEMENT_3D('',#2843,#2844,#2845);
#1972=SURFACE_SIDE_STYLE('',(#2846));
#1973=EDGE_LOOP('',(#2847,#2848,#2849,#2850));
#1974=AXIS2_PLACEMENT_3D('',#2851,#2852,#2853);
#1975=SURFACE_SIDE_STYLE('',(#2854));
#1976=EDGE_LOOP('',(#2855,#2856,#2857,#2858));
#1977=AXIS2_PLACEMENT_3D('',#2859,#2860,#2861);
#1978=SURFACE_SIDE_STYLE('',(#2862));
#1979=EDGE_LOOP('',(#2863,#2864,#2865,#2866));
#1980=AXIS2_PLACEMENT_3D('',#2867,#2868,#2869);
#1981=SURFACE_SIDE_STYLE('',(#2870));
#1982=EDGE_LOOP('',(#2871,#2872,#2873,#2874));
#1983=AXIS2_PLACEMENT_3D('',#2875,#2876,#2877);
#1984=SURFACE_SIDE_STYLE('',(#2878));
#1985=SURFACE_SIDE_STYLE('',(#2879));
#1986=EDGE_LOOP('',(#2880,#2881,#2882,#2883));
#1987=AXIS2_PLACEMENT_3D('',#2884,#2885,#2886);
#1988=SURFACE_SIDE_STYLE('',(#2887));
#1989=EDGE_LOOP('',(#2888,#2889,#2890,#2891));
#1990=AXIS2_PLACEMENT_3D('',#2892,#2893,#2894);
#1991=SURFACE_SIDE_STYLE('',(#2895));
#1992=EDGE_LOOP('',(#2896,#2897,#2898,#2899));
#1993=AXIS2_PLACEMENT_3D('',#2900,#2901,#2902);
#1994=SURFACE_SIDE_STYLE('',(#2903));
#1995=EDGE_LOOP('',(#2904,#2905,#2906,#2907));
#1996=AXIS2_PLACEMENT_3D('',#2908,#2909,#2910);
#1997=SURFACE_SIDE_STYLE('',(#2911));
#1998=EDGE_LOOP('',(#2912,#2913,#2914,#2915));
#1999=AXIS2_PLACEMENT_3D('',#2916,#2917,#2918);
#2000=SURFACE_SIDE_STYLE('',(#2919));
#2001=EDGE_LOOP('',(#2920,#2921,#2922,#2923));
#2002=AXIS2_PLACEMENT_3D('',#2924,#2925,#2926);
#2003=SURFACE_SIDE_STYLE('',(#2927));
#2004=SURFACE_SIDE_STYLE('',(#2928));
#2005=EDGE_LOOP('',(#2929,#2930,#2931,#2932));
#2006=AXIS2_PLACEMENT_3D('',#2933,#2934,#2935);
#2007=SURFACE_SIDE_STYLE('',(#2936));
#2008=EDGE_LOOP('',(#2937,#2938,#2939,#2940));
#2009=AXIS2_PLACEMENT_3D('',#2941,#2942,#2943);
#2010=SURFACE_SIDE_STYLE('',(#2944));
#2011=EDGE_LOOP('',(#2945,#2946,#2947,#2948));
#2012=AXIS2_PLACEMENT_3D('',#2949,#2950,#2951);
#2013=SURFACE_SIDE_STYLE('',(#2952));
#2014=EDGE_LOOP('',(#2953,#2954,#2955,#2956));
#2015=AXIS2_PLACEMENT_3D('',#2957,#2958,#2959);
#2016=SURFACE_SIDE_STYLE('',(#2960));
#2017=EDGE_LOOP('',(#2961,#2962,#2963,#2964));
#2018=AXIS2_PLACEMENT_3D('',#2965,#2966,#2967);
#2019=SURFACE_SIDE_STYLE('',(#2968));
#2020=EDGE_LOOP('',(#2969,#2970,#2971,#2972));
#2021=AXIS2_PLACEMENT_3D('',#2973,#2974,#2975);
#2022=SURFACE_SIDE_STYLE('',(#2976));
#2023=SURFACE_SIDE_STYLE('',(#2977));
#2024=EDGE_LOOP('',(#2978,#2979,#2980,#2981));
#2025=AXIS2_PLACEMENT_3D('',#2982,#2983,#2984);
#2026=SURFACE_SIDE_STYLE('',(#2985));
#2027=EDGE_LOOP('',(#2986,#2987));
#2028=AXIS2_PLACEMENT_3D('',#2988,#2989,#2990);
#2029=SURFACE_SIDE_STYLE('',(#2991));
#2030=EDGE_LOOP('',(#2992,#2993));
#2031=AXIS2_PLACEMENT_3D('',#2994,#2995,#2996);
#2032=SURFACE_SIDE_STYLE('',(#2997));
#2033=EDGE_LOOP('',(#2998,#2999,#3000,#3001));
#2034=AXIS2_PLACEMENT_3D('',#3002,#3003,#3004);
#2035=SURFACE_SIDE_STYLE('',(#3005));
#2036=SURFACE_SIDE_STYLE('',(#3006));
#2037=EDGE_LOOP('',(#3007,#3008,#3009,#3010));
#2038=AXIS2_PLACEMENT_3D('',#3011,#3012,#3013);
#2039=SURFACE_SIDE_STYLE('',(#3014));
#2040=EDGE_LOOP('',(#3015,#3016,#3017,#3018));
#2041=AXIS2_PLACEMENT_3D('',#3019,#3020,#3021);
#2042=SURFACE_SIDE_STYLE('',(#3022));
#2043=EDGE_LOOP('',(#3023,#3024,#3025,#3026));
#2044=AXIS2_PLACEMENT_3D('',#3027,#3028,#3029);
#2045=SURFACE_SIDE_STYLE('',(#3030));
#2046=EDGE_LOOP('',(#3031,#3032,#3033,#3034));
#2047=AXIS2_PLACEMENT_3D('',#3035,#3036,#3037);
#2048=SURFACE_SIDE_STYLE('',(#3038));
#2049=EDGE_LOOP('',(#3039,#3040,#3041,#3042));
#2050=AXIS2_PLACEMENT_3D('',#3043,#3044,#3045);
#2051=SURFACE_SIDE_STYLE('',(#3046));
#2052=EDGE_LOOP('',(#3047,#3048,#3049,#3050));
#2053=AXIS2_PLACEMENT_3D('',#3051,#3052,#3053);
#2054=SURFACE_SIDE_STYLE('',(#3054));
#2055=SURFACE_SIDE_STYLE('',(#3055));
#2056=EDGE_LOOP('',(#3056,#3057,#3058,#3059));
#2057=AXIS2_PLACEMENT_3D('',#3060,#3061,#3062);
#2058=SURFACE_SIDE_STYLE('',(#3063));
#2059=EDGE_LOOP('',(#3064,#3065,#3066,#3067));
#2060=AXIS2_PLACEMENT_3D('',#3068,#3069,#3070);
#2061=SURFACE_SIDE_STYLE('',(#3071));
#2062=EDGE_LOOP('',(#3072,#3073,#3074,#3075));
#2063=AXIS2_PLACEMENT_3D('',#3076,#3077,#3078);
#2064=SURFACE_SIDE_STYLE('',(#3079));
#2065=EDGE_LOOP('',(#3080,#3081,#3082,#3083));
#2066=AXIS2_PLACEMENT_3D('',#3084,#3085,#3086);
#2067=SURFACE_SIDE_STYLE('',(#3087));
#2068=EDGE_LOOP('',(#3088,#3089,#3090,#3091));
#2069=AXIS2_PLACEMENT_3D('',#3092,#3093,#3094);
#2070=SURFACE_SIDE_STYLE('',(#3095));
#2071=EDGE_LOOP('',(#3096,#3097,#3098,#3099));
#2072=AXIS2_PLACEMENT_3D('',#3100,#3101,#3102);
#2073=SURFACE_SIDE_STYLE('',(#3103));
#2074=SURFACE_SIDE_STYLE('',(#3104));
#2075=EDGE_LOOP('',(#3105,#3106,#3107,#3108));
#2076=AXIS2_PLACEMENT_3D('',#3109,#3110,#3111);
#2077=SURFACE_SIDE_STYLE('',(#3112));
#2078=EDGE_LOOP('',(#3113,#3114,#3115,#3116));
#2079=AXIS2_PLACEMENT_3D('',#3117,#3118,#3119);
#2080=SURFACE_SIDE_STYLE('',(#3120));
#2081=EDGE_LOOP('',(#3121,#3122,#3123,#3124));
#2082=AXIS2_PLACEMENT_3D('',#3125,#3126,#3127);
#2083=SURFACE_SIDE_STYLE('',(#3128));
#2084=EDGE_LOOP('',(#3129,#3130,#3131,#3132));
#2085=AXIS2_PLACEMENT_3D('',#3133,#3134,#3135);
#2086=SURFACE_SIDE_STYLE('',(#3136));
#2087=EDGE_LOOP('',(#3137,#3138,#3139,#3140));
#2088=AXIS2_PLACEMENT_3D('',#3141,#3142,#3143);
#2089=SURFACE_SIDE_STYLE('',(#3144));
#2090=EDGE_LOOP('',(#3145,#3146,#3147,#3148));
#2091=AXIS2_PLACEMENT_3D('',#3149,#3150,#3151);
#2092=SURFACE_SIDE_STYLE('',(#3152));
#2093=SURFACE_SIDE_STYLE('',(#3153));
#2094=EDGE_LOOP('',(#3154,#3155,#3156,#3157));
#2095=AXIS2_PLACEMENT_3D('',#3158,#3159,#3160);
#2096=SURFACE_SIDE_STYLE('',(#3161));
#2097=EDGE_LOOP('',(#3162,#3163,#3164,#3165));
#2098=AXIS2_PLACEMENT_3D('',#3166,#3167,#3168);
#2099=SURFACE_SIDE_STYLE('',(#3169));
#2100=EDGE_LOOP('',(#3170,#3171,#3172,#3173));
#2101=AXIS2_PLACEMENT_3D('',#3174,#3175,#3176);
#2102=SURFACE_SIDE_STYLE('',(#3177));
#2103=EDGE_LOOP('',(#3178,#3179,#3180,#3181));
#2104=AXIS2_PLACEMENT_3D('',#3182,#3183,#3184);
#2105=SURFACE_SIDE_STYLE('',(#3185));
#2106=EDGE_LOOP('',(#3186,#3187,#3188,#3189));
#2107=AXIS2_PLACEMENT_3D('',#3190,#3191,#3192);
#2108=SURFACE_SIDE_STYLE('',(#3193));
#2109=EDGE_LOOP('',(#3194,#3195,#3196,#3197));
#2110=AXIS2_PLACEMENT_3D('',#3198,#3199,#3200);
#2111=SURFACE_SIDE_STYLE('',(#3201));
#2112=SURFACE_SIDE_STYLE('',(#3202));
#2113=EDGE_LOOP('',(#3203,#3204,#3205,#3206));
#2114=AXIS2_PLACEMENT_3D('',#3207,#3208,#3209);
#2115=SURFACE_SIDE_STYLE('',(#3210));
#2116=EDGE_LOOP('',(#3211,#3212,#3213,#3214));
#2117=AXIS2_PLACEMENT_3D('',#3215,#3216,#3217);
#2118=SURFACE_SIDE_STYLE('',(#3218));
#2119=EDGE_LOOP('',(#3219,#3220,#3221,#3222));
#2120=AXIS2_PLACEMENT_3D('',#3223,#3224,#3225);
#2121=SURFACE_SIDE_STYLE('',(#3226));
#2122=EDGE_LOOP('',(#3227,#3228,#3229,#3230));
#2123=AXIS2_PLACEMENT_3D('',#3231,#3232,#3233);
#2124=SURFACE_SIDE_STYLE('',(#3234));
#2125=EDGE_LOOP('',(#3235,#3236,#3237,#3238));
#2126=AXIS2_PLACEMENT_3D('',#3239,#3240,#3241);
#2127=SURFACE_SIDE_STYLE('',(#3242));
#2128=EDGE_LOOP('',(#3243,#3244,#3245,#3246));
#2129=AXIS2_PLACEMENT_3D('',#3247,#3248,#3249);
#2130=SURFACE_SIDE_STYLE('',(#3250));
#2131=SURFACE_SIDE_STYLE('',(#3251));
#2132=EDGE_LOOP('',(#3252,#3253,#3254,#3255));
#2133=AXIS2_PLACEMENT_3D('',#3256,#3257,#3258);
#2134=SURFACE_SIDE_STYLE('',(#3259));
#2135=EDGE_LOOP('',(#3260,#3261,#3262,#3263));
#2136=AXIS2_PLACEMENT_3D('',#3264,#3265,#3266);
#2137=SURFACE_SIDE_STYLE('',(#3267));
#2138=EDGE_LOOP('',(#3268,#3269,#3270,#3271));
#2139=AXIS2_PLACEMENT_3D('',#3272,#3273,#3274);
#2140=SURFACE_SIDE_STYLE('',(#3275));
#2141=EDGE_LOOP('',(#3276,#3277,#3278,#3279));
#2142=AXIS2_PLACEMENT_3D('',#3280,#3281,#3282);
#2143=SURFACE_SIDE_STYLE('',(#3283));
#2144=EDGE_LOOP('',(#3284,#3285,#3286,#3287));
#2145=AXIS2_PLACEMENT_3D('',#3288,#3289,#3290);
#2146=SURFACE_SIDE_STYLE('',(#3291));
#2147=EDGE_LOOP('',(#3292,#3293,#3294,#3295));
#2148=AXIS2_PLACEMENT_3D('',#3296,#3297,#3298);
#2149=SURFACE_SIDE_STYLE('',(#3299));
#2150=SURFACE_SIDE_STYLE('',(#3300));
#2151=EDGE_LOOP('',(#3301,#3302,#3303,#3304));
#2152=AXIS2_PLACEMENT_3D('',#3305,#3306,#3307);
#2153=SURFACE_SIDE_STYLE('',(#3308));
#2154=EDGE_LOOP('',(#3309,#3310,#3311,#3312));
#2155=AXIS2_PLACEMENT_3D('',#3313,#3314,#3315);
#2156=SURFACE_SIDE_STYLE('',(#3316));
#2157=EDGE_LOOP('',(#3317,#3318,#3319,#3320));
#2158=AXIS2_PLACEMENT_3D('',#3321,#3322,#3323);
#2159=SURFACE_SIDE_STYLE('',(#3324));
#2160=EDGE_LOOP('',(#3325,#3326,#3327,#3328));
#2161=AXIS2_PLACEMENT_3D('',#3329,#3330,#3331);
#2162=SURFACE_SIDE_STYLE('',(#3332));
#2163=EDGE_LOOP('',(#3333,#3334,#3335,#3336));
#2164=AXIS2_PLACEMENT_3D('',#3337,#3338,#3339);
#2165=SURFACE_SIDE_STYLE('',(#3340));
#2166=EDGE_LOOP('',(#3341,#3342,#3343,#3344));
#2167=AXIS2_PLACEMENT_3D('',#3345,#3346,#3347);
#2168=SURFACE_SIDE_STYLE('',(#3348));
#2169=SURFACE_SIDE_STYLE('',(#3349));
#2170=EDGE_LOOP('',(#3350,#3351,#3352,#3353));
#2171=AXIS2_PLACEMENT_3D('',#3354,#3355,#3356);
#2172=SURFACE_SIDE_STYLE('',(#3357));
#2173=EDGE_LOOP('',(#3358,#3359,#3360,#3361));
#2174=AXIS2_PLACEMENT_3D('',#3362,#3363,#3364);
#2175=SURFACE_SIDE_STYLE('',(#3365));
#2176=EDGE_LOOP('',(#3366,#3367,#3368,#3369));
#2177=AXIS2_PLACEMENT_3D('',#3370,#3371,#3372);
#2178=SURFACE_SIDE_STYLE('',(#3373));
#2179=EDGE_LOOP('',(#3374,#3375,#3376,#3377));
#2180=AXIS2_PLACEMENT_3D('',#3378,#3379,#3380);
#2181=SURFACE_SIDE_STYLE('',(#3381));
#2182=EDGE_LOOP('',(#3382,#3383,#3384,#3385));
#2183=AXIS2_PLACEMENT_3D('',#3386,#3387,#3388);
#2184=SURFACE_SIDE_STYLE('',(#3389));
#2185=EDGE_LOOP('',(#3390,#3391,#3392,#3393));
#2186=AXIS2_PLACEMENT_3D('',#3394,#3395,#3396);
#2187=SURFACE_SIDE_STYLE('',(#3397));
#2189= (NAMED_UNIT(#1672)LENGTH_UNIT()SI_UNIT(.MILLI.,.METRE.));
#2190= (NAMED_UNIT(#1685)LENGTH_UNIT()SI_UNIT(.MILLI.,.METRE.));
#2191= (NAMED_UNIT(#1698)LENGTH_UNIT()SI_UNIT(.MILLI.,.METRE.));
#2192= (NAMED_UNIT(#1711)LENGTH_UNIT()SI_UNIT(.MILLI.,.METRE.));
#2193=SURFACE_STYLE_FILL_AREA(#3402);
#2194=ORIENTED_EDGE('',*,*,#3403,.F.);
#2195=ORIENTED_EDGE('',*,*,#3404,.F.);
#2196=ORIENTED_EDGE('',*,*,#3405,.F.);
#2197=ORIENTED_EDGE('',*,*,#3406,.F.);
#2198=CARTESIAN_POINT('',(-8.89,-4.39,0.2895600001));
#2199=DIRECTION('',(0.0,0.0,1.0));
#2200=DIRECTION('',(1.0,0.0,0.0));
#2201=SURFACE_STYLE_FILL_AREA(#3407);
#2202=ORIENTED_EDGE('',*,*,#3408,.T.);
#2203=ORIENTED_EDGE('',*,*,#3409,.T.);
#2204=ORIENTED_EDGE('',*,*,#3410,.T.);
#2205=ORIENTED_EDGE('',*,*,#3411,.T.);
#2206=CARTESIAN_POINT('',(-8.89,-4.39,0.2590800001));
#2207=DIRECTION('',(0.0,0.0,1.0));
#2208=DIRECTION('',(1.0,0.0,0.0));
#2209=SURFACE_STYLE_FILL_AREA(#3412);
#2210=ORIENTED_EDGE('',*,*,#3406,.T.);
#2211=ORIENTED_EDGE('',*,*,#3413,.T.);
#2212=ORIENTED_EDGE('',*,*,#3409,.F.);
#2213=ORIENTED_EDGE('',*,*,#3414,.F.);
#2214=CARTESIAN_POINT('',(-8.525,-3.405,0.2590800001));
#2215=DIRECTION('',(-1.0,0.0,0.0));
#2216=DIRECTION('',(0.0,0.0,1.0));
#2217=SURFACE_STYLE_FILL_AREA(#3415);
#2218=ORIENTED_EDGE('',*,*,#3405,.T.);
#2219=ORIENTED_EDGE('',*,*,#3416,.T.);
#2220=ORIENTED_EDGE('',*,*,#3410,.F.);
#2221=ORIENTED_EDGE('',*,*,#3413,.F.);
#2222=CARTESIAN_POINT('',(-8.525,-5.375,0.2590800001));
#2223=DIRECTION('',(0.0,1.0,0.0));
#2224=DIRECTION('',(0.0,0.0,1.0));
#2225=SURFACE_STYLE_FILL_AREA(#3417);
#2226=ORIENTED_EDGE('',*,*,#3404,.T.);
#2227=ORIENTED_EDGE('',*,*,#3418,.T.);
#2228=ORIENTED_EDGE('',*,*,#3411,.F.);
#2229=ORIENTED_EDGE('',*,*,#3416,.F.);
#2230=CARTESIAN_POINT('',(-9.255,-5.375,0.2590800001));
#2231=DIRECTION('',(1.0,0.0,0.0));
#2232=DIRECTION('',(0.0,0.0,-1.0));
#2233=SURFACE_STYLE_FILL_AREA(#3419);
#2234=ORIENTED_EDGE('',*,*,#3403,.T.);
#2235=ORIENTED_EDGE('',*,*,#3414,.T.);
#2236=ORIENTED_EDGE('',*,*,#3408,.F.);
#2237=ORIENTED_EDGE('',*,*,#3418,.F.);
#2238=CARTESIAN_POINT('',(-9.255,-3.405,0.2590800001));
#2239=DIRECTION('',(0.0,-1.0,0.0));
#2240=DIRECTION('',(0.0,0.0,-1.0));
#2241=SURFACE_STYLE_FILL_AREA(#3420);
#2242=SURFACE_STYLE_FILL_AREA(#3421);
#2243=ORIENTED_EDGE('',*,*,#3422,.F.);
#2244=ORIENTED_EDGE('',*,*,#3423,.F.);
#2245=ORIENTED_EDGE('',*,*,#3424,.F.);
#2246=ORIENTED_EDGE('',*,*,#3425,.F.);
#2247=CARTESIAN_POINT('',(-6.35,-4.39,0.2895600001));
#2248=DIRECTION('',(0.0,0.0,1.0));
#2249=DIRECTION('',(1.0,0.0,0.0));
#2250=SURFACE_STYLE_FILL_AREA(#3426);
#2251=ORIENTED_EDGE('',*,*,#3427,.T.);
#2252=ORIENTED_EDGE('',*,*,#3428,.T.);
#2253=ORIENTED_EDGE('',*,*,#3429,.T.);
#2254=ORIENTED_EDGE('',*,*,#3430,.T.);
#2255=CARTESIAN_POINT('',(-6.35,-4.39,0.2590800001));
#2256=DIRECTION('',(0.0,0.0,1.0));
#2257=DIRECTION('',(1.0,0.0,0.0));
#2258=SURFACE_STYLE_FILL_AREA(#3431);
#2259=ORIENTED_EDGE('',*,*,#3425,.T.);
#2260=ORIENTED_EDGE('',*,*,#3432,.T.);
#2261=ORIENTED_EDGE('',*,*,#3428,.F.);
#2262=ORIENTED_EDGE('',*,*,#3433,.F.);
#2263=CARTESIAN_POINT('',(-5.985,-3.405,0.2590800001));
#2264=DIRECTION('',(-1.0,0.0,0.0));
#2265=DIRECTION('',(0.0,0.0,1.0));
#2266=SURFACE_STYLE_FILL_AREA(#3434);
#2267=ORIENTED_EDGE('',*,*,#3424,.T.);
#2268=ORIENTED_EDGE('',*,*,#3435,.T.);
#2269=ORIENTED_EDGE('',*,*,#3429,.F.);
#2270=ORIENTED_EDGE('',*,*,#3432,.F.);
#2271=CARTESIAN_POINT('',(-5.985,-5.375,0.2590800001));
#2272=DIRECTION('',(0.0,1.0,0.0));
#2273=DIRECTION('',(0.0,0.0,1.0));
#2274=SURFACE_STYLE_FILL_AREA(#3436);
#2275=ORIENTED_EDGE('',*,*,#3423,.T.);
#2276=ORIENTED_EDGE('',*,*,#3437,.T.);
#2277=ORIENTED_EDGE('',*,*,#3430,.F.);
#2278=ORIENTED_EDGE('',*,*,#3435,.F.);
#2279=CARTESIAN_POINT('',(-6.715,-5.375,0.2590800001));
#2280=DIRECTION('',(1.0,0.0,0.0));
#2281=DIRECTION('',(0.0,0.0,-1.0));
#2282=SURFACE_STYLE_FILL_AREA(#3438);
#2283=ORIENTED_EDGE('',*,*,#3422,.T.);
#2284=ORIENTED_EDGE('',*,*,#3433,.T.);
#2285=ORIENTED_EDGE('',*,*,#3427,.F.);
#2286=ORIENTED_EDGE('',*,*,#3437,.F.);
#2287=CARTESIAN_POINT('',(-6.715,-3.405,0.2590800001));
#2288=DIRECTION('',(0.0,-1.0,0.0));
#2289=DIRECTION('',(0.0,0.0,-1.0));
#2290=SURFACE_STYLE_FILL_AREA(#3439);
#2291=SURFACE_STYLE_FILL_AREA(#3440);
#2292=ORIENTED_EDGE('',*,*,#3441,.F.);
#2293=ORIENTED_EDGE('',*,*,#3442,.F.);
#2294=ORIENTED_EDGE('',*,*,#3443,.F.);
#2295=ORIENTED_EDGE('',*,*,#3444,.F.);
#2296=CARTESIAN_POINT('',(-3.81,-4.39,0.2895600001));
#2297=DIRECTION('',(0.0,0.0,1.0));
#2298=DIRECTION('',(1.0,0.0,0.0));
#2299=SURFACE_STYLE_FILL_AREA(#3445);
#2300=ORIENTED_EDGE('',*,*,#3446,.T.);
#2301=ORIENTED_EDGE('',*,*,#3447,.T.);
#2302=ORIENTED_EDGE('',*,*,#3448,.T.);
#2303=ORIENTED_EDGE('',*,*,#3449,.T.);
#2304=CARTESIAN_POINT('',(-3.81,-4.39,0.2590800001));
#2305=DIRECTION('',(0.0,0.0,1.0));
#2306=DIRECTION('',(1.0,0.0,0.0));
#2307=SURFACE_STYLE_FILL_AREA(#3450);
#2308=ORIENTED_EDGE('',*,*,#3444,.T.);
#2309=ORIENTED_EDGE('',*,*,#3451,.T.);
#2310=ORIENTED_EDGE('',*,*,#3447,.F.);
#2311=ORIENTED_EDGE('',*,*,#3452,.F.);
#2312=CARTESIAN_POINT('',(-3.445,-3.405,0.2590800001));
#2313=DIRECTION('',(-1.0,0.0,0.0));
#2314=DIRECTION('',(0.0,0.0,1.0));
#2315=SURFACE_STYLE_FILL_AREA(#3453);
#2316=ORIENTED_EDGE('',*,*,#3443,.T.);
#2317=ORIENTED_EDGE('',*,*,#3454,.T.);
#2318=ORIENTED_EDGE('',*,*,#3448,.F.);
#2319=ORIENTED_EDGE('',*,*,#3451,.F.);
#2320=CARTESIAN_POINT('',(-3.445,-5.375,0.2590800001));
#2321=DIRECTION('',(0.0,1.0,0.0));
#2322=DIRECTION('',(0.0,0.0,1.0));
#2323=SURFACE_STYLE_FILL_AREA(#3455);
#2324=ORIENTED_EDGE('',*,*,#3442,.T.);
#2325=ORIENTED_EDGE('',*,*,#3456,.T.);
#2326=ORIENTED_EDGE('',*,*,#3449,.F.);
#2327=ORIENTED_EDGE('',*,*,#3454,.F.);
#2328=CARTESIAN_POINT('',(-4.175,-5.375,0.2590800001));
#2329=DIRECTION('',(1.0,0.0,0.0));
#2330=DIRECTION('',(0.0,0.0,-1.0));
#2331=SURFACE_STYLE_FILL_AREA(#3457);
#2332=ORIENTED_EDGE('',*,*,#3441,.T.);
#2333=ORIENTED_EDGE('',*,*,#3452,.T.);
#2334=ORIENTED_EDGE('',*,*,#3446,.F.);
#2335=ORIENTED_EDGE('',*,*,#3456,.F.);
#2336=CARTESIAN_POINT('',(-4.175,-3.405,0.2590800001));
#2337=DIRECTION('',(0.0,-1.0,0.0));
#2338=DIRECTION('',(0.0,0.0,-1.0));
#2339=SURFACE_STYLE_FILL_AREA(#3458);
#2340=SURFACE_STYLE_FILL_AREA(#3459);
#2341=ORIENTED_EDGE('',*,*,#3460,.F.);
#2342=ORIENTED_EDGE('',*,*,#3461,.F.);
#2343=ORIENTED_EDGE('',*,*,#3462,.F.);
#2344=ORIENTED_EDGE('',*,*,#3463,.F.);
#2345=CARTESIAN_POINT('',(-1.27,-4.39,0.2895600001));
#2346=DIRECTION('',(0.0,0.0,1.0));
#2347=DIRECTION('',(1.0,0.0,0.0));
#2348=SURFACE_STYLE_FILL_AREA(#3464);
#2349=ORIENTED_EDGE('',*,*,#3465,.T.);
#2350=ORIENTED_EDGE('',*,*,#3466,.T.);
#2351=ORIENTED_EDGE('',*,*,#3467,.T.);
#2352=ORIENTED_EDGE('',*,*,#3468,.T.);
#2353=CARTESIAN_POINT('',(-1.27,-4.39,0.2590800001));
#2354=DIRECTION('',(0.0,0.0,1.0));
#2355=DIRECTION('',(1.0,0.0,0.0));
#2356=SURFACE_STYLE_FILL_AREA(#3469);
#2357=ORIENTED_EDGE('',*,*,#3463,.T.);
#2358=ORIENTED_EDGE('',*,*,#3470,.T.);
#2359=ORIENTED_EDGE('',*,*,#3466,.F.);
#2360=ORIENTED_EDGE('',*,*,#3471,.F.);
#2361=CARTESIAN_POINT('',(-0.905,-3.405,0.2590800001));
#2362=DIRECTION('',(-1.0,0.0,0.0));
#2363=DIRECTION('',(0.0,0.0,1.0));
#2364=SURFACE_STYLE_FILL_AREA(#3472);
#2365=ORIENTED_EDGE('',*,*,#3462,.T.);
#2366=ORIENTED_EDGE('',*,*,#3473,.T.);
#2367=ORIENTED_EDGE('',*,*,#3467,.F.);
#2368=ORIENTED_EDGE('',*,*,#3470,.F.);
#2369=CARTESIAN_POINT('',(-0.905,-5.375,0.2590800001));
#2370=DIRECTION('',(0.0,1.0,0.0));
#2371=DIRECTION('',(0.0,0.0,1.0));
#2372=SURFACE_STYLE_FILL_AREA(#3474);
#2373=ORIENTED_EDGE('',*,*,#3461,.T.);
#2374=ORIENTED_EDGE('',*,*,#3475,.T.);
#2375=ORIENTED_EDGE('',*,*,#3468,.F.);
#2376=ORIENTED_EDGE('',*,*,#3473,.F.);
#2377=CARTESIAN_POINT('',(-1.635,-5.375,0.2590800001));
#2378=DIRECTION('',(1.0,0.0,0.0));
#2379=DIRECTION('',(0.0,0.0,-1.0));
#2380=SURFACE_STYLE_FILL_AREA(#3476);
#2381=ORIENTED_EDGE('',*,*,#3460,.T.);
#2382=ORIENTED_EDGE('',*,*,#3471,.T.);
#2383=ORIENTED_EDGE('',*,*,#3465,.F.);
#2384=ORIENTED_EDGE('',*,*,#3475,.F.);
#2385=CARTESIAN_POINT('',(-1.635,-3.405,0.2590800001));
#2386=DIRECTION('',(0.0,-1.0,0.0));
#2387=DIRECTION('',(0.0,0.0,-1.0));
#2388=SURFACE_STYLE_FILL_AREA(#3477);
#2389=SURFACE_STYLE_FILL_AREA(#3478);
#2390=ORIENTED_EDGE('',*,*,#3479,.F.);
#2391=ORIENTED_EDGE('',*,*,#3480,.F.);
#2392=ORIENTED_EDGE('',*,*,#3481,.F.);
#2393=ORIENTED_EDGE('',*,*,#3482,.F.);
#2394=CARTESIAN_POINT('',(1.27,-4.39,0.2895600001));
#2395=DIRECTION('',(0.0,0.0,1.0));
#2396=DIRECTION('',(1.0,0.0,0.0));
#2397=SURFACE_STYLE_FILL_AREA(#3483);
#2398=ORIENTED_EDGE('',*,*,#3484,.T.);
#2399=ORIENTED_EDGE('',*,*,#3485,.T.);
#2400=ORIENTED_EDGE('',*,*,#3486,.T.);
#2401=ORIENTED_EDGE('',*,*,#3487,.T.);
#2402=CARTESIAN_POINT('',(1.27,-4.39,0.2590800001));
#2403=DIRECTION('',(0.0,0.0,1.0));
#2404=DIRECTION('',(1.0,0.0,0.0));
#2405=SURFACE_STYLE_FILL_AREA(#3488);
#2406=ORIENTED_EDGE('',*,*,#3482,.T.);
#2407=ORIENTED_EDGE('',*,*,#3489,.T.);
#2408=ORIENTED_EDGE('',*,*,#3485,.F.);
#2409=ORIENTED_EDGE('',*,*,#3490,.F.);
#2410=CARTESIAN_POINT('',(1.635,-3.405,0.2590800001));
#2411=DIRECTION('',(-1.0,0.0,0.0));
#2412=DIRECTION('',(0.0,0.0,1.0));
#2413=SURFACE_STYLE_FILL_AREA(#3491);
#2414=ORIENTED_EDGE('',*,*,#3481,.T.);
#2415=ORIENTED_EDGE('',*,*,#3492,.T.);
#2416=ORIENTED_EDGE('',*,*,#3486,.F.);
#2417=ORIENTED_EDGE('',*,*,#3489,.F.);
#2418=CARTESIAN_POINT('',(1.635,-5.375,0.2590800001));
#2419=DIRECTION('',(0.0,1.0,0.0));
#2420=DIRECTION('',(0.0,0.0,1.0));
#2421=SURFACE_STYLE_FILL_AREA(#3493);
#2422=ORIENTED_EDGE('',*,*,#3480,.T.);
#2423=ORIENTED_EDGE('',*,*,#3494,.T.);
#2424=ORIENTED_EDGE('',*,*,#3487,.F.);
#2425=ORIENTED_EDGE('',*,*,#3492,.F.);
#2426=CARTESIAN_POINT('',(0.905,-5.375,0.2590800001));
#2427=DIRECTION('',(1.0,0.0,0.0));
#2428=DIRECTION('',(0.0,0.0,-1.0));
#2429=SURFACE_STYLE_FILL_AREA(#3495);
#2430=ORIENTED_EDGE('',*,*,#3479,.T.);
#2431=ORIENTED_EDGE('',*,*,#3490,.T.);
#2432=ORIENTED_EDGE('',*,*,#3484,.F.);
#2433=ORIENTED_EDGE('',*,*,#3494,.F.);
#2434=CARTESIAN_POINT('',(0.905,-3.405,0.2590800001));
#2435=DIRECTION('',(0.0,-1.0,0.0));
#2436=DIRECTION('',(0.0,0.0,-1.0));
#2437=SURFACE_STYLE_FILL_AREA(#3496);
#2438=SURFACE_STYLE_FILL_AREA(#3497);
#2439=ORIENTED_EDGE('',*,*,#3498,.F.);
#2440=ORIENTED_EDGE('',*,*,#3499,.F.);
#2441=ORIENTED_EDGE('',*,*,#3500,.F.);
#2442=ORIENTED_EDGE('',*,*,#3501,.F.);
#2443=CARTESIAN_POINT('',(3.81,-4.39,0.2895600001));
#2444=DIRECTION('',(0.0,0.0,1.0));
#2445=DIRECTION('',(1.0,0.0,0.0));
#2446=SURFACE_STYLE_FILL_AREA(#3502);
#2447=ORIENTED_EDGE('',*,*,#3503,.T.);
#2448=ORIENTED_EDGE('',*,*,#3504,.T.);
#2449=ORIENTED_EDGE('',*,*,#3505,.T.);
#2450=ORIENTED_EDGE('',*,*,#3506,.T.);
#2451=CARTESIAN_POINT('',(3.81,-4.39,0.2590800001));
#2452=DIRECTION('',(0.0,0.0,1.0));
#2453=DIRECTION('',(1.0,0.0,0.0));
#2454=SURFACE_STYLE_FILL_AREA(#3507);
#2455=ORIENTED_EDGE('',*,*,#3501,.T.);
#2456=ORIENTED_EDGE('',*,*,#3508,.T.);
#2457=ORIENTED_EDGE('',*,*,#3504,.F.);
#2458=ORIENTED_EDGE('',*,*,#3509,.F.);
#2459=CARTESIAN_POINT('',(4.175,-3.405,0.2590800001));
#2460=DIRECTION('',(-1.0,0.0,0.0));
#2461=DIRECTION('',(0.0,0.0,1.0));
#2462=SURFACE_STYLE_FILL_AREA(#3510);
#2463=ORIENTED_EDGE('',*,*,#3500,.T.);
#2464=ORIENTED_EDGE('',*,*,#3511,.T.);
#2465=ORIENTED_EDGE('',*,*,#3505,.F.);
#2466=ORIENTED_EDGE('',*,*,#3508,.F.);
#2467=CARTESIAN_POINT('',(4.175,-5.375,0.2590800001));
#2468=DIRECTION('',(0.0,1.0,0.0));
#2469=DIRECTION('',(0.0,0.0,1.0));
#2470=SURFACE_STYLE_FILL_AREA(#3512);
#2471=ORIENTED_EDGE('',*,*,#3499,.T.);
#2472=ORIENTED_EDGE('',*,*,#3513,.T.);
#2473=ORIENTED_EDGE('',*,*,#3506,.F.);
#2474=ORIENTED_EDGE('',*,*,#3511,.F.);
#2475=CARTESIAN_POINT('',(3.445,-5.375,0.2590800001));
#2476=DIRECTION('',(1.0,0.0,0.0));
#2477=DIRECTION('',(0.0,0.0,-1.0));
#2478=SURFACE_STYLE_FILL_AREA(#3514);
#2479=ORIENTED_EDGE('',*,*,#3498,.T.);
#2480=ORIENTED_EDGE('',*,*,#3509,.T.);
#2481=ORIENTED_EDGE('',*,*,#3503,.F.);
#2482=ORIENTED_EDGE('',*,*,#3513,.F.);
#2483=CARTESIAN_POINT('',(3.445,-3.405,0.2590800001));
#2484=DIRECTION('',(0.0,-1.0,0.0));
#2485=DIRECTION('',(0.0,0.0,-1.0));
#2486=SURFACE_STYLE_FILL_AREA(#3515);
#2487=SURFACE_STYLE_FILL_AREA(#3516);
#2488=ORIENTED_EDGE('',*,*,#3517,.F.);
#2489=ORIENTED_EDGE('',*,*,#3518,.F.);
#2490=ORIENTED_EDGE('',*,*,#3519,.F.);
#2491=ORIENTED_EDGE('',*,*,#3520,.F.);
#2492=CARTESIAN_POINT('',(6.35,-4.39,0.2895600001));
#2493=DIRECTION('',(0.0,0.0,1.0));
#2494=DIRECTION('',(1.0,0.0,0.0));
#2495=SURFACE_STYLE_FILL_AREA(#3521);
#2496=ORIENTED_EDGE('',*,*,#3522,.T.);
#2497=ORIENTED_EDGE('',*,*,#3523,.T.);
#2498=ORIENTED_EDGE('',*,*,#3524,.T.);
#2499=ORIENTED_EDGE('',*,*,#3525,.T.);
#2500=CARTESIAN_POINT('',(6.35,-4.39,0.2590800001));
#2501=DIRECTION('',(0.0,0.0,1.0));
#2502=DIRECTION('',(1.0,0.0,0.0));
#2503=SURFACE_STYLE_FILL_AREA(#3526);
#2504=ORIENTED_EDGE('',*,*,#3520,.T.);
#2505=ORIENTED_EDGE('',*,*,#3527,.T.);
#2506=ORIENTED_EDGE('',*,*,#3523,.F.);
#2507=ORIENTED_EDGE('',*,*,#3528,.F.);
#2508=CARTESIAN_POINT('',(6.715,-3.405,0.2590800001));
#2509=DIRECTION('',(-1.0,0.0,0.0));
#2510=DIRECTION('',(0.0,0.0,1.0));
#2511=SURFACE_STYLE_FILL_AREA(#3529);
#2512=ORIENTED_EDGE('',*,*,#3519,.T.);
#2513=ORIENTED_EDGE('',*,*,#3530,.T.);
#2514=ORIENTED_EDGE('',*,*,#3524,.F.);
#2515=ORIENTED_EDGE('',*,*,#3527,.F.);
#2516=CARTESIAN_POINT('',(6.715,-5.375,0.2590800001));
#2517=DIRECTION('',(0.0,1.0,0.0));
#2518=DIRECTION('',(0.0,0.0,1.0));
#2519=SURFACE_STYLE_FILL_AREA(#3531);
#2520=ORIENTED_EDGE('',*,*,#3518,.T.);
#2521=ORIENTED_EDGE('',*,*,#3532,.T.);
#2522=ORIENTED_EDGE('',*,*,#3525,.F.);
#2523=ORIENTED_EDGE('',*,*,#3530,.F.);
#2524=CARTESIAN_POINT('',(5.985,-5.375,0.2590800001));
#2525=DIRECTION('',(1.0,0.0,0.0));
#2526=DIRECTION('',(0.0,0.0,-1.0));
#2527=SURFACE_STYLE_FILL_AREA(#3533);
#2528=ORIENTED_EDGE('',*,*,#3517,.T.);
#2529=ORIENTED_EDGE('',*,*,#3528,.T.);
#2530=ORIENTED_EDGE('',*,*,#3522,.F.);
#2531=ORIENTED_EDGE('',*,*,#3532,.F.);
#2532=CARTESIAN_POINT('',(5.985,-3.405,0.2590800001));
#2533=DIRECTION('',(0.0,-1.0,0.0));
#2534=DIRECTION('',(0.0,0.0,-1.0));
#2535=SURFACE_STYLE_FILL_AREA(#3534);
#2536=SURFACE_STYLE_FILL_AREA(#3535);
#2537=ORIENTED_EDGE('',*,*,#3536,.F.);
#2538=ORIENTED_EDGE('',*,*,#3537,.F.);
#2539=ORIENTED_EDGE('',*,*,#3538,.F.);
#2540=ORIENTED_EDGE('',*,*,#3539,.F.);
#2541=CARTESIAN_POINT('',(8.89,-4.39,0.2895600001));
#2542=DIRECTION('',(0.0,0.0,1.0));
#2543=DIRECTION('',(1.0,0.0,0.0));
#2544=SURFACE_STYLE_FILL_AREA(#3540);
#2545=ORIENTED_EDGE('',*,*,#3541,.T.);
#2546=ORIENTED_EDGE('',*,*,#3542,.T.);
#2547=ORIENTED_EDGE('',*,*,#3543,.T.);
#2548=ORIENTED_EDGE('',*,*,#3544,.T.);
#2549=CARTESIAN_POINT('',(8.89,-4.39,0.2590800001));
#2550=DIRECTION('',(0.0,0.0,1.0));
#2551=DIRECTION('',(1.0,0.0,0.0));
#2552=SURFACE_STYLE_FILL_AREA(#3545);
#2553=ORIENTED_EDGE('',*,*,#3539,.T.);
#2554=ORIENTED_EDGE('',*,*,#3546,.T.);
#2555=ORIENTED_EDGE('',*,*,#3542,.F.);
#2556=ORIENTED_EDGE('',*,*,#3547,.F.);
#2557=CARTESIAN_POINT('',(9.255,-3.405,0.2590800001));
#2558=DIRECTION('',(-1.0,0.0,0.0));
#2559=DIRECTION('',(0.0,0.0,1.0));
#2560=SURFACE_STYLE_FILL_AREA(#3548);
#2561=ORIENTED_EDGE('',*,*,#3538,.T.);
#2562=ORIENTED_EDGE('',*,*,#3549,.T.);
#2563=ORIENTED_EDGE('',*,*,#3543,.F.);
#2564=ORIENTED_EDGE('',*,*,#3546,.F.);
#2565=CARTESIAN_POINT('',(9.255,-5.375,0.2590800001));
#2566=DIRECTION('',(0.0,1.0,0.0));
#2567=DIRECTION('',(0.0,0.0,1.0));
#2568=SURFACE_STYLE_FILL_AREA(#3550);
#2569=ORIENTED_EDGE('',*,*,#3537,.T.);
#2570=ORIENTED_EDGE('',*,*,#3551,.T.);
#2571=ORIENTED_EDGE('',*,*,#3544,.F.);
#2572=ORIENTED_EDGE('',*,*,#3549,.F.);
#2573=CARTESIAN_POINT('',(8.525,-5.375,0.2590800001));
#2574=DIRECTION('',(1.0,0.0,0.0));
#2575=DIRECTION('',(0.0,0.0,-1.0));
#2576=SURFACE_STYLE_FILL_AREA(#3552);
#2577=ORIENTED_EDGE('',*,*,#3536,.T.);
#2578=ORIENTED_EDGE('',*,*,#3547,.T.);
#2579=ORIENTED_EDGE('',*,*,#3541,.F.);
#2580=ORIENTED_EDGE('',*,*,#3551,.F.);
#2581=CARTESIAN_POINT('',(8.525,-3.405,0.2590800001));
#2582=DIRECTION('',(0.0,-1.0,0.0));
#2583=DIRECTION('',(0.0,0.0,-1.0));
#2584=SURFACE_STYLE_FILL_AREA(#3553);
#2585=SURFACE_STYLE_FILL_AREA(#3554);
#2586=ORIENTED_EDGE('',*,*,#3555,.F.);
#2587=ORIENTED_EDGE('',*,*,#3556,.F.);
#2588=ORIENTED_EDGE('',*,*,#3557,.F.);
#2589=ORIENTED_EDGE('',*,*,#3558,.F.);
#2590=CARTESIAN_POINT('',(8.89,4.39,0.2895600001));
#2591=DIRECTION('',(0.0,0.0,1.0));
#2592=DIRECTION('',(1.0,0.0,0.0));
#2593=SURFACE_STYLE_FILL_AREA(#3559);
#2594=ORIENTED_EDGE('',*,*,#3560,.T.);
#2595=ORIENTED_EDGE('',*,*,#3561,.T.);
#2596=ORIENTED_EDGE('',*,*,#3562,.T.);
#2597=ORIENTED_EDGE('',*,*,#3563,.T.);
#2598=CARTESIAN_POINT('',(8.89,4.39,0.2590800001));
#2599=DIRECTION('',(0.0,0.0,1.0));
#2600=DIRECTION('',(1.0,0.0,0.0));
#2601=SURFACE_STYLE_FILL_AREA(#3564);
#2602=ORIENTED_EDGE('',*,*,#3558,.T.);
#2603=ORIENTED_EDGE('',*,*,#3565,.T.);
#2604=ORIENTED_EDGE('',*,*,#3561,.F.);
#2605=ORIENTED_EDGE('',*,*,#3566,.F.);
#2606=CARTESIAN_POINT('',(9.255,5.375,0.2590800001));
#2607=DIRECTION('',(-1.0,0.0,0.0));
#2608=DIRECTION('',(0.0,0.0,1.0));
#2609=SURFACE_STYLE_FILL_AREA(#3567);
#2610=ORIENTED_EDGE('',*,*,#3557,.T.);
#2611=ORIENTED_EDGE('',*,*,#3568,.T.);
#2612=ORIENTED_EDGE('',*,*,#3562,.F.);
#2613=ORIENTED_EDGE('',*,*,#3565,.F.);
#2614=CARTESIAN_POINT('',(9.255,3.405,0.2590800001));
#2615=DIRECTION('',(0.0,1.0,0.0));
#2616=DIRECTION('',(0.0,0.0,1.0));
#2617=SURFACE_STYLE_FILL_AREA(#3569);
#2618=ORIENTED_EDGE('',*,*,#3556,.T.);
#2619=ORIENTED_EDGE('',*,*,#3570,.T.);
#2620=ORIENTED_EDGE('',*,*,#3563,.F.);
#2621=ORIENTED_EDGE('',*,*,#3568,.F.);
#2622=CARTESIAN_POINT('',(8.525,3.405,0.2590800001));
#2623=DIRECTION('',(1.0,0.0,0.0));
#2624=DIRECTION('',(0.0,0.0,-1.0));
#2625=SURFACE_STYLE_FILL_AREA(#3571);
#2626=ORIENTED_EDGE('',*,*,#3555,.T.);
#2627=ORIENTED_EDGE('',*,*,#3566,.T.);
#2628=ORIENTED_EDGE('',*,*,#3560,.F.);
#2629=ORIENTED_EDGE('',*,*,#3570,.F.);
#2630=CARTESIAN_POINT('',(8.525,5.375,0.2590800001));
#2631=DIRECTION('',(0.0,-1.0,0.0));
#2632=DIRECTION('',(0.0,0.0,-1.0));
#2633=SURFACE_STYLE_FILL_AREA(#3572);
#2634=SURFACE_STYLE_FILL_AREA(#3573);
#2635=ORIENTED_EDGE('',*,*,#3574,.F.);
#2636=ORIENTED_EDGE('',*,*,#3575,.F.);
#2637=ORIENTED_EDGE('',*,*,#3576,.F.);
#2638=ORIENTED_EDGE('',*,*,#3577,.F.);
#2639=CARTESIAN_POINT('',(6.35,4.39,0.2895600001));
#2640=DIRECTION('',(0.0,0.0,1.0));
#2641=DIRECTION('',(1.0,0.0,0.0));
#2642=SURFACE_STYLE_FILL_AREA(#3578);
#2643=ORIENTED_EDGE('',*,*,#3579,.T.);
#2644=ORIENTED_EDGE('',*,*,#3580,.T.);
#2645=ORIENTED_EDGE('',*,*,#3581,.T.);
#2646=ORIENTED_EDGE('',*,*,#3582,.T.);
#2647=CARTESIAN_POINT('',(6.35,4.39,0.2590800001));
#2648=DIRECTION('',(0.0,0.0,1.0));
#2649=DIRECTION('',(1.0,0.0,0.0));
#2650=SURFACE_STYLE_FILL_AREA(#3583);
#2651=ORIENTED_EDGE('',*,*,#3577,.T.);
#2652=ORIENTED_EDGE('',*,*,#3584,.T.);
#2653=ORIENTED_EDGE('',*,*,#3580,.F.);
#2654=ORIENTED_EDGE('',*,*,#3585,.F.);
#2655=CARTESIAN_POINT('',(6.715,5.375,0.2590800001));
#2656=DIRECTION('',(-1.0,0.0,0.0));
#2657=DIRECTION('',(0.0,0.0,1.0));
#2658=SURFACE_STYLE_FILL_AREA(#3586);
#2659=ORIENTED_EDGE('',*,*,#3576,.T.);
#2660=ORIENTED_EDGE('',*,*,#3587,.T.);
#2661=ORIENTED_EDGE('',*,*,#3581,.F.);
#2662=ORIENTED_EDGE('',*,*,#3584,.F.);
#2663=CARTESIAN_POINT('',(6.715,3.405,0.2590800001));
#2664=DIRECTION('',(0.0,1.0,0.0));
#2665=DIRECTION('',(0.0,0.0,1.0));
#2666=SURFACE_STYLE_FILL_AREA(#3588);
#2667=ORIENTED_EDGE('',*,*,#3575,.T.);
#2668=ORIENTED_EDGE('',*,*,#3589,.T.);
#2669=ORIENTED_EDGE('',*,*,#3582,.F.);
#2670=ORIENTED_EDGE('',*,*,#3587,.F.);
#2671=CARTESIAN_POINT('',(5.985,3.405,0.2590800001));
#2672=DIRECTION('',(1.0,0.0,0.0));
#2673=DIRECTION('',(0.0,0.0,-1.0));
#2674=SURFACE_STYLE_FILL_AREA(#3590);
#2675=ORIENTED_EDGE('',*,*,#3574,.T.);
#2676=ORIENTED_EDGE('',*,*,#3585,.T.);
#2677=ORIENTED_EDGE('',*,*,#3579,.F.);
#2678=ORIENTED_EDGE('',*,*,#3589,.F.);
#2679=CARTESIAN_POINT('',(5.985,5.375,0.2590800001));
#2680=DIRECTION('',(0.0,-1.0,0.0));
#2681=DIRECTION('',(0.0,0.0,-1.0));
#2682=SURFACE_STYLE_FILL_AREA(#3591);
#2683=SURFACE_STYLE_FILL_AREA(#3592);
#2684=ORIENTED_EDGE('',*,*,#3593,.F.);
#2685=ORIENTED_EDGE('',*,*,#3594,.F.);
#2686=ORIENTED_EDGE('',*,*,#3595,.F.);
#2687=ORIENTED_EDGE('',*,*,#3596,.F.);
#2688=CARTESIAN_POINT('',(3.81,4.39,0.2895600001));
#2689=DIRECTION('',(0.0,0.0,1.0));
#2690=DIRECTION('',(1.0,0.0,0.0));
#2691=SURFACE_STYLE_FILL_AREA(#3597);
#2692=ORIENTED_EDGE('',*,*,#3598,.T.);
#2693=ORIENTED_EDGE('',*,*,#3599,.T.);
#2694=ORIENTED_EDGE('',*,*,#3600,.T.);
#2695=ORIENTED_EDGE('',*,*,#3601,.T.);
#2696=CARTESIAN_POINT('',(3.81,4.39,0.2590800001));
#2697=DIRECTION('',(0.0,0.0,1.0));
#2698=DIRECTION('',(1.0,0.0,0.0));
#2699=SURFACE_STYLE_FILL_AREA(#3602);
#2700=ORIENTED_EDGE('',*,*,#3596,.T.);
#2701=ORIENTED_EDGE('',*,*,#3603,.T.);
#2702=ORIENTED_EDGE('',*,*,#3599,.F.);
#2703=ORIENTED_EDGE('',*,*,#3604,.F.);
#2704=CARTESIAN_POINT('',(4.175,5.375,0.2590800001));
#2705=DIRECTION('',(-1.0,0.0,0.0));
#2706=DIRECTION('',(0.0,0.0,1.0));
#2707=SURFACE_STYLE_FILL_AREA(#3605);
#2708=ORIENTED_EDGE('',*,*,#3595,.T.);
#2709=ORIENTED_EDGE('',*,*,#3606,.T.);
#2710=ORIENTED_EDGE('',*,*,#3600,.F.);
#2711=ORIENTED_EDGE('',*,*,#3603,.F.);
#2712=CARTESIAN_POINT('',(4.175,3.405,0.2590800001));
#2713=DIRECTION('',(0.0,1.0,0.0));
#2714=DIRECTION('',(0.0,0.0,1.0));
#2715=SURFACE_STYLE_FILL_AREA(#3607);
#2716=ORIENTED_EDGE('',*,*,#3594,.T.);
#2717=ORIENTED_EDGE('',*,*,#3608,.T.);
#2718=ORIENTED_EDGE('',*,*,#3601,.F.);
#2719=ORIENTED_EDGE('',*,*,#3606,.F.);
#2720=CARTESIAN_POINT('',(3.445,3.405,0.2590800001));
#2721=DIRECTION('',(1.0,0.0,0.0));
#2722=DIRECTION('',(0.0,0.0,-1.0));
#2723=SURFACE_STYLE_FILL_AREA(#3609);
#2724=ORIENTED_EDGE('',*,*,#3593,.T.);
#2725=ORIENTED_EDGE('',*,*,#3604,.T.);
#2726=ORIENTED_EDGE('',*,*,#3598,.F.);
#2727=ORIENTED_EDGE('',*,*,#3608,.F.);
#2728=CARTESIAN_POINT('',(3.445,5.375,0.2590800001));
#2729=DIRECTION('',(0.0,-1.0,0.0));
#2730=DIRECTION('',(0.0,0.0,-1.0));
#2731=SURFACE_STYLE_FILL_AREA(#3610);
#2732=SURFACE_STYLE_FILL_AREA(#3611);
#2733=ORIENTED_EDGE('',*,*,#3612,.F.);
#2734=ORIENTED_EDGE('',*,*,#3613,.F.);
#2735=ORIENTED_EDGE('',*,*,#3614,.F.);
#2736=ORIENTED_EDGE('',*,*,#3615,.F.);
#2737=CARTESIAN_POINT('',(1.27,4.39,0.2895600001));
#2738=DIRECTION('',(0.0,0.0,1.0));
#2739=DIRECTION('',(1.0,0.0,0.0));
#2740=SURFACE_STYLE_FILL_AREA(#3616);
#2741=ORIENTED_EDGE('',*,*,#3617,.T.);
#2742=ORIENTED_EDGE('',*,*,#3618,.T.);
#2743=ORIENTED_EDGE('',*,*,#3619,.T.);
#2744=ORIENTED_EDGE('',*,*,#3620,.T.);
#2745=CARTESIAN_POINT('',(1.27,4.39,0.2590800001));
#2746=DIRECTION('',(0.0,0.0,1.0));
#2747=DIRECTION('',(1.0,0.0,0.0));
#2748=SURFACE_STYLE_FILL_AREA(#3621);
#2749=ORIENTED_EDGE('',*,*,#3615,.T.);
#2750=ORIENTED_EDGE('',*,*,#3622,.T.);
#2751=ORIENTED_EDGE('',*,*,#3618,.F.);
#2752=ORIENTED_EDGE('',*,*,#3623,.F.);
#2753=CARTESIAN_POINT('',(1.635,5.375,0.2590800001));
#2754=DIRECTION('',(-1.0,0.0,0.0));
#2755=DIRECTION('',(0.0,0.0,1.0));
#2756=SURFACE_STYLE_FILL_AREA(#3624);
#2757=ORIENTED_EDGE('',*,*,#3614,.T.);
#2758=ORIENTED_EDGE('',*,*,#3625,.T.);
#2759=ORIENTED_EDGE('',*,*,#3619,.F.);
#2760=ORIENTED_EDGE('',*,*,#3622,.F.);
#2761=CARTESIAN_POINT('',(1.635,3.405,0.2590800001));
#2762=DIRECTION('',(0.0,1.0,0.0));
#2763=DIRECTION('',(0.0,0.0,1.0));
#2764=SURFACE_STYLE_FILL_AREA(#3626);
#2765=ORIENTED_EDGE('',*,*,#3613,.T.);
#2766=ORIENTED_EDGE('',*,*,#3627,.T.);
#2767=ORIENTED_EDGE('',*,*,#3620,.F.);
#2768=ORIENTED_EDGE('',*,*,#3625,.F.);
#2769=CARTESIAN_POINT('',(0.905,3.405,0.2590800001));
#2770=DIRECTION('',(1.0,0.0,0.0));
#2771=DIRECTION('',(0.0,0.0,-1.0));
#2772=SURFACE_STYLE_FILL_AREA(#3628);
#2773=ORIENTED_EDGE('',*,*,#3612,.T.);
#2774=ORIENTED_EDGE('',*,*,#3623,.T.);
#2775=ORIENTED_EDGE('',*,*,#3617,.F.);
#2776=ORIENTED_EDGE('',*,*,#3627,.F.);
#2777=CARTESIAN_POINT('',(0.905,5.375,0.2590800001));
#2778=DIRECTION('',(0.0,-1.0,0.0));
#2779=DIRECTION('',(0.0,0.0,-1.0));
#2780=SURFACE_STYLE_FILL_AREA(#3629);
#2781=SURFACE_STYLE_FILL_AREA(#3630);
#2782=ORIENTED_EDGE('',*,*,#3631,.F.);
#2783=ORIENTED_EDGE('',*,*,#3632,.F.);
#2784=ORIENTED_EDGE('',*,*,#3633,.F.);
#2785=ORIENTED_EDGE('',*,*,#3634,.F.);
#2786=CARTESIAN_POINT('',(-1.27,4.39,0.2895600001));
#2787=DIRECTION('',(0.0,0.0,1.0));
#2788=DIRECTION('',(1.0,0.0,0.0));
#2789=SURFACE_STYLE_FILL_AREA(#3635);
#2790=ORIENTED_EDGE('',*,*,#3636,.T.);
#2791=ORIENTED_EDGE('',*,*,#3637,.T.);
#2792=ORIENTED_EDGE('',*,*,#3638,.T.);
#2793=ORIENTED_EDGE('',*,*,#3639,.T.);
#2794=CARTESIAN_POINT('',(-1.27,4.39,0.2590800001));
#2795=DIRECTION('',(0.0,0.0,1.0));
#2796=DIRECTION('',(1.0,0.0,0.0));
#2797=SURFACE_STYLE_FILL_AREA(#3640);
#2798=ORIENTED_EDGE('',*,*,#3634,.T.);
#2799=ORIENTED_EDGE('',*,*,#3641,.T.);
#2800=ORIENTED_EDGE('',*,*,#3637,.F.);
#2801=ORIENTED_EDGE('',*,*,#3642,.F.);
#2802=CARTESIAN_POINT('',(-0.905,5.375,0.2590800001));
#2803=DIRECTION('',(-1.0,0.0,0.0));
#2804=DIRECTION('',(0.0,0.0,1.0));
#2805=SURFACE_STYLE_FILL_AREA(#3643);
#2806=ORIENTED_EDGE('',*,*,#3633,.T.);
#2807=ORIENTED_EDGE('',*,*,#3644,.T.);
#2808=ORIENTED_EDGE('',*,*,#3638,.F.);
#2809=ORIENTED_EDGE('',*,*,#3641,.F.);
#2810=CARTESIAN_POINT('',(-0.905,3.405,0.2590800001));
#2811=DIRECTION('',(0.0,1.0,0.0));
#2812=DIRECTION('',(0.0,0.0,1.0));
#2813=SURFACE_STYLE_FILL_AREA(#3645);
#2814=ORIENTED_EDGE('',*,*,#3632,.T.);
#2815=ORIENTED_EDGE('',*,*,#3646,.T.);
#2816=ORIENTED_EDGE('',*,*,#3639,.F.);
#2817=ORIENTED_EDGE('',*,*,#3644,.F.);
#2818=CARTESIAN_POINT('',(-1.635,3.405,0.2590800001));
#2819=DIRECTION('',(1.0,0.0,0.0));
#2820=DIRECTION('',(0.0,0.0,-1.0));
#2821=SURFACE_STYLE_FILL_AREA(#3647);
#2822=ORIENTED_EDGE('',*,*,#3631,.T.);
#2823=ORIENTED_EDGE('',*,*,#3642,.T.);
#2824=ORIENTED_EDGE('',*,*,#3636,.F.);
#2825=ORIENTED_EDGE('',*,*,#3646,.F.);
#2826=CARTESIAN_POINT('',(-1.635,5.375,0.2590800001));
#2827=DIRECTION('',(0.0,-1.0,0.0));
#2828=DIRECTION('',(0.0,0.0,-1.0));
#2829=SURFACE_STYLE_FILL_AREA(#3648);
#2830=SURFACE_STYLE_FILL_AREA(#3649);
#2831=ORIENTED_EDGE('',*,*,#3650,.F.);
#2832=ORIENTED_EDGE('',*,*,#3651,.F.);
#2833=ORIENTED_EDGE('',*,*,#3652,.F.);
#2834=ORIENTED_EDGE('',*,*,#3653,.F.);
#2835=CARTESIAN_POINT('',(-3.81,4.39,0.2895600001));
#2836=DIRECTION('',(0.0,0.0,1.0));
#2837=DIRECTION('',(1.0,0.0,0.0));
#2838=SURFACE_STYLE_FILL_AREA(#3654);
#2839=ORIENTED_EDGE('',*,*,#3655,.T.);
#2840=ORIENTED_EDGE('',*,*,#3656,.T.);
#2841=ORIENTED_EDGE('',*,*,#3657,.T.);
#2842=ORIENTED_EDGE('',*,*,#3658,.T.);
#2843=CARTESIAN_POINT('',(-3.81,4.39,0.2590800001));
#2844=DIRECTION('',(0.0,0.0,1.0));
#2845=DIRECTION('',(1.0,0.0,0.0));
#2846=SURFACE_STYLE_FILL_AREA(#3659);
#2847=ORIENTED_EDGE('',*,*,#3653,.T.);
#2848=ORIENTED_EDGE('',*,*,#3660,.T.);
#2849=ORIENTED_EDGE('',*,*,#3656,.F.);
#2850=ORIENTED_EDGE('',*,*,#3661,.F.);
#2851=CARTESIAN_POINT('',(-3.445,5.375,0.2590800001));
#2852=DIRECTION('',(-1.0,0.0,0.0));
#2853=DIRECTION('',(0.0,0.0,1.0));
#2854=SURFACE_STYLE_FILL_AREA(#3662);
#2855=ORIENTED_EDGE('',*,*,#3652,.T.);
#2856=ORIENTED_EDGE('',*,*,#3663,.T.);
#2857=ORIENTED_EDGE('',*,*,#3657,.F.);
#2858=ORIENTED_EDGE('',*,*,#3660,.F.);
#2859=CARTESIAN_POINT('',(-3.445,3.405,0.2590800001));
#2860=DIRECTION('',(0.0,1.0,0.0));
#2861=DIRECTION('',(0.0,0.0,1.0));
#2862=SURFACE_STYLE_FILL_AREA(#3664);
#2863=ORIENTED_EDGE('',*,*,#3651,.T.);
#2864=ORIENTED_EDGE('',*,*,#3665,.T.);
#2865=ORIENTED_EDGE('',*,*,#3658,.F.);
#2866=ORIENTED_EDGE('',*,*,#3663,.F.);
#2867=CARTESIAN_POINT('',(-4.175,3.405,0.2590800001));
#2868=DIRECTION('',(1.0,0.0,0.0));
#2869=DIRECTION('',(0.0,0.0,-1.0));
#2870=SURFACE_STYLE_FILL_AREA(#3666);
#2871=ORIENTED_EDGE('',*,*,#3650,.T.);
#2872=ORIENTED_EDGE('',*,*,#3661,.T.);
#2873=ORIENTED_EDGE('',*,*,#3655,.F.);
#2874=ORIENTED_EDGE('',*,*,#3665,.F.);
#2875=CARTESIAN_POINT('',(-4.175,5.375,0.2590800001));
#2876=DIRECTION('',(0.0,-1.0,0.0));
#2877=DIRECTION('',(0.0,0.0,-1.0));
#2878=SURFACE_STYLE_FILL_AREA(#3667);
#2879=SURFACE_STYLE_FILL_AREA(#3668);
#2880=ORIENTED_EDGE('',*,*,#3669,.F.);
#2881=ORIENTED_EDGE('',*,*,#3670,.F.);
#2882=ORIENTED_EDGE('',*,*,#3671,.F.);
#2883=ORIENTED_EDGE('',*,*,#3672,.F.);
#2884=CARTESIAN_POINT('',(-6.35,4.39,0.2895600001));
#2885=DIRECTION('',(0.0,0.0,1.0));
#2886=DIRECTION('',(1.0,0.0,0.0));
#2887=SURFACE_STYLE_FILL_AREA(#3673);
#2888=ORIENTED_EDGE('',*,*,#3674,.T.);
#2889=ORIENTED_EDGE('',*,*,#3675,.T.);
#2890=ORIENTED_EDGE('',*,*,#3676,.T.);
#2891=ORIENTED_EDGE('',*,*,#3677,.T.);
#2892=CARTESIAN_POINT('',(-6.35,4.39,0.2590800001));
#2893=DIRECTION('',(0.0,0.0,1.0));
#2894=DIRECTION('',(1.0,0.0,0.0));
#2895=SURFACE_STYLE_FILL_AREA(#3678);
#2896=ORIENTED_EDGE('',*,*,#3672,.T.);
#2897=ORIENTED_EDGE('',*,*,#3679,.T.);
#2898=ORIENTED_EDGE('',*,*,#3675,.F.);
#2899=ORIENTED_EDGE('',*,*,#3680,.F.);
#2900=CARTESIAN_POINT('',(-5.985,5.375,0.2590800001));
#2901=DIRECTION('',(-1.0,0.0,0.0));
#2902=DIRECTION('',(0.0,0.0,1.0));
#2903=SURFACE_STYLE_FILL_AREA(#3681);
#2904=ORIENTED_EDGE('',*,*,#3671,.T.);
#2905=ORIENTED_EDGE('',*,*,#3682,.T.);
#2906=ORIENTED_EDGE('',*,*,#3676,.F.);
#2907=ORIENTED_EDGE('',*,*,#3679,.F.);
#2908=CARTESIAN_POINT('',(-5.985,3.405,0.2590800001));
#2909=DIRECTION('',(0.0,1.0,0.0));
#2910=DIRECTION('',(0.0,0.0,1.0));
#2911=SURFACE_STYLE_FILL_AREA(#3683);
#2912=ORIENTED_EDGE('',*,*,#3670,.T.);
#2913=ORIENTED_EDGE('',*,*,#3684,.T.);
#2914=ORIENTED_EDGE('',*,*,#3677,.F.);
#2915=ORIENTED_EDGE('',*,*,#3682,.F.);
#2916=CARTESIAN_POINT('',(-6.715,3.405,0.2590800001));
#2917=DIRECTION('',(1.0,0.0,0.0));
#2918=DIRECTION('',(0.0,0.0,-1.0));
#2919=SURFACE_STYLE_FILL_AREA(#3685);
#2920=ORIENTED_EDGE('',*,*,#3669,.T.);
#2921=ORIENTED_EDGE('',*,*,#3680,.T.);
#2922=ORIENTED_EDGE('',*,*,#3674,.F.);
#2923=ORIENTED_EDGE('',*,*,#3684,.F.);
#2924=CARTESIAN_POINT('',(-6.715,5.375,0.2590800001));
#2925=DIRECTION('',(0.0,-1.0,0.0));
#2926=DIRECTION('',(0.0,0.0,-1.0));
#2927=SURFACE_STYLE_FILL_AREA(#3686);
#2928=SURFACE_STYLE_FILL_AREA(#3687);
#2929=ORIENTED_EDGE('',*,*,#3688,.F.);
#2930=ORIENTED_EDGE('',*,*,#3689,.F.);
#2931=ORIENTED_EDGE('',*,*,#3690,.F.);
#2932=ORIENTED_EDGE('',*,*,#3691,.F.);
#2933=CARTESIAN_POINT('',(-8.89,4.39,0.2895600001));
#2934=DIRECTION('',(0.0,0.0,1.0));
#2935=DIRECTION('',(1.0,0.0,0.0));
#2936=SURFACE_STYLE_FILL_AREA(#3692);
#2937=ORIENTED_EDGE('',*,*,#3693,.T.);
#2938=ORIENTED_EDGE('',*,*,#3694,.T.);
#2939=ORIENTED_EDGE('',*,*,#3695,.T.);
#2940=ORIENTED_EDGE('',*,*,#3696,.T.);
#2941=CARTESIAN_POINT('',(-8.89,4.39,0.2590800001));
#2942=DIRECTION('',(0.0,0.0,1.0));
#2943=DIRECTION('',(1.0,0.0,0.0));
#2944=SURFACE_STYLE_FILL_AREA(#3697);
#2945=ORIENTED_EDGE('',*,*,#3691,.T.);
#2946=ORIENTED_EDGE('',*,*,#3698,.T.);
#2947=ORIENTED_EDGE('',*,*,#3694,.F.);
#2948=ORIENTED_EDGE('',*,*,#3699,.F.);
#2949=CARTESIAN_POINT('',(-8.525,5.375,0.2590800001));
#2950=DIRECTION('',(-1.0,0.0,0.0));
#2951=DIRECTION('',(0.0,0.0,1.0));
#2952=SURFACE_STYLE_FILL_AREA(#3700);
#2953=ORIENTED_EDGE('',*,*,#3690,.T.);
#2954=ORIENTED_EDGE('',*,*,#3701,.T.);
#2955=ORIENTED_EDGE('',*,*,#3695,.F.);
#2956=ORIENTED_EDGE('',*,*,#3698,.F.);
#2957=CARTESIAN_POINT('',(-8.525,3.405,0.2590800001));
#2958=DIRECTION('',(0.0,1.0,0.0));
#2959=DIRECTION('',(0.0,0.0,1.0));
#2960=SURFACE_STYLE_FILL_AREA(#3702);
#2961=ORIENTED_EDGE('',*,*,#3689,.T.);
#2962=ORIENTED_EDGE('',*,*,#3703,.T.);
#2963=ORIENTED_EDGE('',*,*,#3696,.F.);
#2964=ORIENTED_EDGE('',*,*,#3701,.F.);
#2965=CARTESIAN_POINT('',(-9.255,3.405,0.2590800001));
#2966=DIRECTION('',(1.0,0.0,0.0));
#2967=DIRECTION('',(0.0,0.0,-1.0));
#2968=SURFACE_STYLE_FILL_AREA(#3704);
#2969=ORIENTED_EDGE('',*,*,#3688,.T.);
#2970=ORIENTED_EDGE('',*,*,#3699,.T.);
#2971=ORIENTED_EDGE('',*,*,#3693,.F.);
#2972=ORIENTED_EDGE('',*,*,#3703,.F.);
#2973=CARTESIAN_POINT('',(-9.255,5.375,0.2590800001));
#2974=DIRECTION('',(0.0,-1.0,0.0));
#2975=DIRECTION('',(0.0,0.0,-1.0));
#2976=SURFACE_STYLE_FILL_AREA(#3705);
#2977=SURFACE_STYLE_FILL_AREA(#3706);
#2978=ORIENTED_EDGE('',*,*,#3707,.F.);
#2979=ORIENTED_EDGE('',*,*,#3708,.T.);
#2980=ORIENTED_EDGE('',*,*,#3709,.F.);
#2981=ORIENTED_EDGE('',*,*,#3710,.F.);
#2982=CARTESIAN_POINT('',(-8.88999964294879,-5.75600006103516,0.3151600001));
#2983=DIRECTION('',(0.0,-0.0,1.0));
#2984=DIRECTION('',(0.999999999999278,1.20147998783305E-006,0.0));
#2985=SURFACE_STYLE_FILL_AREA(#3711);
#2986=ORIENTED_EDGE('',*,*,#3710,.T.);
#2987=ORIENTED_EDGE('',*,*,#3712,.T.);
#2988=CARTESIAN_POINT('',(-8.88999964294879,-5.75600006103516,0.3278600001));
#2989=DIRECTION('',(0.0,0.0,1.0));
#2990=DIRECTION('',(1.0,0.0,0.0));
#2991=SURFACE_STYLE_FILL_AREA(#3713);
#2992=ORIENTED_EDGE('',*,*,#3708,.F.);
#2993=ORIENTED_EDGE('',*,*,#3714,.F.);
#2994=CARTESIAN_POINT('',(-8.88999964294879,-5.75600006103516,0.3151600001));
#2995=DIRECTION('',(0.0,0.0,1.0));
#2996=DIRECTION('',(1.0,0.0,0.0));
#2997=SURFACE_STYLE_FILL_AREA(#3715);
#2998=ORIENTED_EDGE('',*,*,#3707,.T.);
#2999=ORIENTED_EDGE('',*,*,#3712,.F.);
#3000=ORIENTED_EDGE('',*,*,#3709,.T.);
#3001=ORIENTED_EDGE('',*,*,#3714,.T.);
#3002=CARTESIAN_POINT('',(-8.88999964294879,-5.75600006103516,0.3151600001));
#3003=DIRECTION('',(0.0,-0.0,1.0));
#3004=DIRECTION('',(0.999999999999278,1.20147998783305E-006,0.0));
#3005=SURFACE_STYLE_FILL_AREA(#3716);
#3006=SURFACE_STYLE_FILL_AREA(#3717);
#3007=ORIENTED_EDGE('',*,*,#3718,.F.);
#3008=ORIENTED_EDGE('',*,*,#3719,.F.);
#3009=ORIENTED_EDGE('',*,*,#3720,.F.);
#3010=ORIENTED_EDGE('',*,*,#3721,.F.);
#3011=CARTESIAN_POINT('',(9.73749970244823,-3.4,0.3278600001));
#3012=DIRECTION('',(0.0,0.0,1.0));
#3013=DIRECTION('',(1.0,0.0,0.0));
#3014=SURFACE_STYLE_FILL_AREA(#3722);
#3015=ORIENTED_EDGE('',*,*,#3723,.T.);
#3016=ORIENTED_EDGE('',*,*,#3724,.T.);
#3017=ORIENTED_EDGE('',*,*,#3725,.T.);
#3018=ORIENTED_EDGE('',*,*,#3726,.T.);
#3019=CARTESIAN_POINT('',(9.73749970244823,-3.4,0.3151600001));
#3020=DIRECTION('',(0.0,0.0,1.0));
#3021=DIRECTION('',(1.0,0.0,0.0));
#3022=SURFACE_STYLE_FILL_AREA(#3727);
#3023=ORIENTED_EDGE('',*,*,#3721,.T.);
#3024=ORIENTED_EDGE('',*,*,#3728,.T.);
#3025=ORIENTED_EDGE('',*,*,#3724,.F.);
#3026=ORIENTED_EDGE('',*,*,#3729,.F.);
#3027=CARTESIAN_POINT('',(9.45,-3.33649993896484,0.3151600001));
#3028=DIRECTION('',(0.0,-1.0,0.0));
#3029=DIRECTION('',(0.0,0.0,-1.0));
#3030=SURFACE_STYLE_FILL_AREA(#3730);
#3031=ORIENTED_EDGE('',*,*,#3720,.T.);
#3032=ORIENTED_EDGE('',*,*,#3731,.T.);
#3033=ORIENTED_EDGE('',*,*,#3725,.F.);
#3034=ORIENTED_EDGE('',*,*,#3728,.F.);
#3035=CARTESIAN_POINT('',(10.0249996948235,-3.4,0.3151600001));
#3036=DIRECTION('',(0.0,-0.0,1.0));
#3037=DIRECTION('',(4.80592474324742E-006,0.999999999988451,0.0));
#3038=SURFACE_STYLE_FILL_AREA(#3732);
#3039=ORIENTED_EDGE('',*,*,#3719,.T.);
#3040=ORIENTED_EDGE('',*,*,#3733,.T.);
#3041=ORIENTED_EDGE('',*,*,#3726,.F.);
#3042=ORIENTED_EDGE('',*,*,#3731,.F.);
#3043=CARTESIAN_POINT('',(10.025,-3.46350006103516,0.3151600001));
#3044=DIRECTION('',(0.0,1.0,0.0));
#3045=DIRECTION('',(0.0,0.0,1.0));
#3046=SURFACE_STYLE_FILL_AREA(#3734);
#3047=ORIENTED_EDGE('',*,*,#3718,.T.);
#3048=ORIENTED_EDGE('',*,*,#3729,.T.);
#3049=ORIENTED_EDGE('',*,*,#3723,.F.);
#3050=ORIENTED_EDGE('',*,*,#3733,.F.);
#3051=CARTESIAN_POINT('',(9.44999969482495,-3.4,0.3151600001));
#3052=DIRECTION('',(0.0,0.0,1.0));
#3053=DIRECTION('',(4.8059016478333E-006,-0.999999999988452,0.0));
#3054=SURFACE_STYLE_FILL_AREA(#3735);
#3055=SURFACE_STYLE_FILL_AREA(#3736);
#3056=ORIENTED_EDGE('',*,*,#3737,.F.);
#3057=ORIENTED_EDGE('',*,*,#3738,.F.);
#3058=ORIENTED_EDGE('',*,*,#3739,.F.);
#3059=ORIENTED_EDGE('',*,*,#3740,.F.);
#3060=CARTESIAN_POINT('',(10.0249996948242,0.0,0.3278600001));
#3061=DIRECTION('',(0.0,0.0,1.0));
#3062=DIRECTION('',(1.0,0.0,0.0));
#3063=SURFACE_STYLE_FILL_AREA(#3741);
#3064=ORIENTED_EDGE('',*,*,#3742,.T.);
#3065=ORIENTED_EDGE('',*,*,#3743,.T.);
#3066=ORIENTED_EDGE('',*,*,#3744,.T.);
#3067=ORIENTED_EDGE('',*,*,#3745,.T.);
#3068=CARTESIAN_POINT('',(10.0249996948242,0.0,0.3151600001));
#3069=DIRECTION('',(0.0,0.0,1.0));
#3070=DIRECTION('',(1.0,0.0,0.0));
#3071=SURFACE_STYLE_FILL_AREA(#3746);
#3072=ORIENTED_EDGE('',*,*,#3740,.T.);
#3073=ORIENTED_EDGE('',*,*,#3747,.T.);
#3074=ORIENTED_EDGE('',*,*,#3743,.F.);
#3075=ORIENTED_EDGE('',*,*,#3748,.F.);
#3076=CARTESIAN_POINT('',(9.96149963378906,-3.4,0.3151600001));
#3077=DIRECTION('',(1.0,0.0,0.0));
#3078=DIRECTION('',(0.0,0.0,-1.0));
#3079=SURFACE_STYLE_FILL_AREA(#3749);
#3080=ORIENTED_EDGE('',*,*,#3739,.T.);
#3081=ORIENTED_EDGE('',*,*,#3750,.T.);
#3082=ORIENTED_EDGE('',*,*,#3744,.F.);
#3083=ORIENTED_EDGE('',*,*,#3747,.F.);
#3084=CARTESIAN_POINT('',(10.0249996948242,3.40000000000073,0.3151600001));
#3085=DIRECTION('',(0.0,0.0,1.0));
#3086=DIRECTION('',(-1.0,-1.15477070628082E-011,0.0));
#3087=SURFACE_STYLE_FILL_AREA(#3751);
#3088=ORIENTED_EDGE('',*,*,#3738,.T.);
#3089=ORIENTED_EDGE('',*,*,#3752,.T.);
#3090=ORIENTED_EDGE('',*,*,#3745,.F.);
#3091=ORIENTED_EDGE('',*,*,#3750,.F.);
#3092=CARTESIAN_POINT('',(10.0884997558594,3.4,0.3151600001));
#3093=DIRECTION('',(-1.0,0.0,0.0));
#3094=DIRECTION('',(0.0,0.0,1.0));
#3095=SURFACE_STYLE_FILL_AREA(#3753);
#3096=ORIENTED_EDGE('',*,*,#3737,.T.);
#3097=ORIENTED_EDGE('',*,*,#3748,.T.);
#3098=ORIENTED_EDGE('',*,*,#3742,.F.);
#3099=ORIENTED_EDGE('',*,*,#3752,.F.);
#3100=CARTESIAN_POINT('',(10.0249996948242,-3.40000000000073,0.3151600001));
#3101=DIRECTION('',(0.0,-0.0,1.0));
#3102=DIRECTION('',(1.0,1.15477070628082E-011,0.0));
#3103=SURFACE_STYLE_FILL_AREA(#3754);
#3104=SURFACE_STYLE_FILL_AREA(#3755);
#3105=ORIENTED_EDGE('',*,*,#3756,.F.);
#3106=ORIENTED_EDGE('',*,*,#3757,.F.);
#3107=ORIENTED_EDGE('',*,*,#3758,.F.);
#3108=ORIENTED_EDGE('',*,*,#3759,.F.);
#3109=CARTESIAN_POINT('',(9.73749970244822,3.4,0.3278600001));
#3110=DIRECTION('',(0.0,0.0,1.0));
#3111=DIRECTION('',(1.0,0.0,0.0));
#3112=SURFACE_STYLE_FILL_AREA(#3760);
#3113=ORIENTED_EDGE('',*,*,#3761,.T.);
#3114=ORIENTED_EDGE('',*,*,#3762,.T.);
#3115=ORIENTED_EDGE('',*,*,#3763,.T.);
#3116=ORIENTED_EDGE('',*,*,#3764,.T.);
#3117=CARTESIAN_POINT('',(9.73749970244822,3.4,0.3151600001));
#3118=DIRECTION('',(0.0,0.0,1.0));
#3119=DIRECTION('',(1.0,0.0,0.0));
#3120=SURFACE_STYLE_FILL_AREA(#3765);
#3121=ORIENTED_EDGE('',*,*,#3759,.T.);
#3122=ORIENTED_EDGE('',*,*,#3766,.T.);
#3123=ORIENTED_EDGE('',*,*,#3762,.F.);
#3124=ORIENTED_EDGE('',*,*,#3767,.F.);
#3125=CARTESIAN_POINT('',(10.025,3.33649993896484,0.3151600001));
#3126=DIRECTION('',(0.0,1.0,0.0));
#3127=DIRECTION('',(0.0,0.0,1.0));
#3128=SURFACE_STYLE_FILL_AREA(#3768);
#3129=ORIENTED_EDGE('',*,*,#3758,.T.);
#3130=ORIENTED_EDGE('',*,*,#3769,.T.);
#3131=ORIENTED_EDGE('',*,*,#3763,.F.);
#3132=ORIENTED_EDGE('',*,*,#3766,.F.);
#3133=CARTESIAN_POINT('',(9.44999969482495,3.4,0.3151600001));
#3134=DIRECTION('',(0.0,0.0,1.0));
#3135=DIRECTION('',(4.8059016478333E-006,-0.999999999988452,0.0));
#3136=SURFACE_STYLE_FILL_AREA(#3770);
#3137=ORIENTED_EDGE('',*,*,#3757,.T.);
#3138=ORIENTED_EDGE('',*,*,#3771,.T.);
#3139=ORIENTED_EDGE('',*,*,#3764,.F.);
#3140=ORIENTED_EDGE('',*,*,#3769,.F.);
#3141=CARTESIAN_POINT('',(9.45,3.46350006103516,0.3151600001));
#3142=DIRECTION('',(0.0,-1.0,0.0));
#3143=DIRECTION('',(0.0,0.0,-1.0));
#3144=SURFACE_STYLE_FILL_AREA(#3772);
#3145=ORIENTED_EDGE('',*,*,#3756,.T.);
#3146=ORIENTED_EDGE('',*,*,#3767,.T.);
#3147=ORIENTED_EDGE('',*,*,#3761,.F.);
#3148=ORIENTED_EDGE('',*,*,#3771,.F.);
#3149=CARTESIAN_POINT('',(10.0249996948235,3.4,0.3151600001));
#3150=DIRECTION('',(0.0,-0.0,1.0));
#3151=DIRECTION('',(4.80592474324742E-006,0.999999999988451,0.0));
#3152=SURFACE_STYLE_FILL_AREA(#3773);
#3153=SURFACE_STYLE_FILL_AREA(#3774);
#3154=ORIENTED_EDGE('',*,*,#3775,.F.);
#3155=ORIENTED_EDGE('',*,*,#3776,.F.);
#3156=ORIENTED_EDGE('',*,*,#3777,.F.);
#3157=ORIENTED_EDGE('',*,*,#3778,.F.);
#3158=CARTESIAN_POINT('',(-9.73749970244823,-3.4,0.3278600001));
#3159=DIRECTION('',(0.0,0.0,1.0));
#3160=DIRECTION('',(1.0,0.0,0.0));
#3161=SURFACE_STYLE_FILL_AREA(#3779);
#3162=ORIENTED_EDGE('',*,*,#3780,.T.);
#3163=ORIENTED_EDGE('',*,*,#3781,.T.);
#3164=ORIENTED_EDGE('',*,*,#3782,.T.);
#3165=ORIENTED_EDGE('',*,*,#3783,.T.);
#3166=CARTESIAN_POINT('',(-9.73749970244823,-3.4,0.3151600001));
#3167=DIRECTION('',(0.0,0.0,1.0));
#3168=DIRECTION('',(1.0,0.0,0.0));
#3169=SURFACE_STYLE_FILL_AREA(#3784);
#3170=ORIENTED_EDGE('',*,*,#3778,.T.);
#3171=ORIENTED_EDGE('',*,*,#3785,.T.);
#3172=ORIENTED_EDGE('',*,*,#3781,.F.);
#3173=ORIENTED_EDGE('',*,*,#3786,.F.);
#3174=CARTESIAN_POINT('',(-9.45,-3.46350006103516,0.3151600001));
#3175=DIRECTION('',(0.0,1.0,0.0));
#3176=DIRECTION('',(0.0,0.0,1.0));
#3177=SURFACE_STYLE_FILL_AREA(#3787);
#3178=ORIENTED_EDGE('',*,*,#3777,.T.);
#3179=ORIENTED_EDGE('',*,*,#3788,.T.);
#3180=ORIENTED_EDGE('',*,*,#3782,.F.);
#3181=ORIENTED_EDGE('',*,*,#3785,.F.);
#3182=CARTESIAN_POINT('',(-10.0249996948235,-3.4,0.3151600001));
#3183=DIRECTION('',(0.0,0.0,1.0));
#3184=DIRECTION('',(-4.80592474324742E-006,-0.999999999988451,0.0));
#3185=SURFACE_STYLE_FILL_AREA(#3789);
#3186=ORIENTED_EDGE('',*,*,#3776,.T.);
#3187=ORIENTED_EDGE('',*,*,#3790,.T.);
#3188=ORIENTED_EDGE('',*,*,#3783,.F.);
#3189=ORIENTED_EDGE('',*,*,#3788,.F.);
#3190=CARTESIAN_POINT('',(-10.025,-3.33649993896484,0.3151600001));
#3191=DIRECTION('',(0.0,-1.0,0.0));
#3192=DIRECTION('',(0.0,0.0,-1.0));
#3193=SURFACE_STYLE_FILL_AREA(#3791);
#3194=ORIENTED_EDGE('',*,*,#3775,.T.);
#3195=ORIENTED_EDGE('',*,*,#3786,.T.);
#3196=ORIENTED_EDGE('',*,*,#3780,.F.);
#3197=ORIENTED_EDGE('',*,*,#3790,.F.);
#3198=CARTESIAN_POINT('',(-9.44999969482495,-3.4,0.3151600001));
#3199=DIRECTION('',(0.0,0.0,1.0));
#3200=DIRECTION('',(-4.8059016478333E-006,0.999999999988452,0.0));
#3201=SURFACE_STYLE_FILL_AREA(#3792);
#3202=SURFACE_STYLE_FILL_AREA(#3793);
#3203=ORIENTED_EDGE('',*,*,#3794,.F.);
#3204=ORIENTED_EDGE('',*,*,#3795,.F.);
#3205=ORIENTED_EDGE('',*,*,#3796,.F.);
#3206=ORIENTED_EDGE('',*,*,#3797,.F.);
#3207=CARTESIAN_POINT('',(-10.0249996948242,0.0,0.3278600001));
#3208=DIRECTION('',(0.0,0.0,1.0));
#3209=DIRECTION('',(1.0,0.0,0.0));
#3210=SURFACE_STYLE_FILL_AREA(#3798);
#3211=ORIENTED_EDGE('',*,*,#3799,.T.);
#3212=ORIENTED_EDGE('',*,*,#3800,.T.);
#3213=ORIENTED_EDGE('',*,*,#3801,.T.);
#3214=ORIENTED_EDGE('',*,*,#3802,.T.);
#3215=CARTESIAN_POINT('',(-10.0249996948242,0.0,0.3151600001));
#3216=DIRECTION('',(0.0,0.0,1.0));
#3217=DIRECTION('',(1.0,0.0,0.0));
#3218=SURFACE_STYLE_FILL_AREA(#3803);
#3219=ORIENTED_EDGE('',*,*,#3797,.T.);
#3220=ORIENTED_EDGE('',*,*,#3804,.T.);
#3221=ORIENTED_EDGE('',*,*,#3800,.F.);
#3222=ORIENTED_EDGE('',*,*,#3805,.F.);
#3223=CARTESIAN_POINT('',(-10.0884997558594,-3.4,0.3151600001));
#3224=DIRECTION('',(1.0,0.0,0.0));
#3225=DIRECTION('',(0.0,0.0,-1.0));
#3226=SURFACE_STYLE_FILL_AREA(#3806);
#3227=ORIENTED_EDGE('',*,*,#3796,.T.);
#3228=ORIENTED_EDGE('',*,*,#3807,.T.);
#3229=ORIENTED_EDGE('',*,*,#3801,.F.);
#3230=ORIENTED_EDGE('',*,*,#3804,.F.);
#3231=CARTESIAN_POINT('',(-10.0249996948242,3.40000000000073,0.3151600001));
#3232=DIRECTION('',(0.0,0.0,1.0));
#3233=DIRECTION('',(-1.0,-1.15477070628082E-011,0.0));
#3234=SURFACE_STYLE_FILL_AREA(#3808);
#3235=ORIENTED_EDGE('',*,*,#3795,.T.);
#3236=ORIENTED_EDGE('',*,*,#3809,.T.);
#3237=ORIENTED_EDGE('',*,*,#3802,.F.);
#3238=ORIENTED_EDGE('',*,*,#3807,.F.);
#3239=CARTESIAN_POINT('',(-9.96149963378906,3.4,0.3151600001));
#3240=DIRECTION('',(-1.0,0.0,0.0));
#3241=DIRECTION('',(0.0,0.0,1.0));
#3242=SURFACE_STYLE_FILL_AREA(#3810);
#3243=ORIENTED_EDGE('',*,*,#3794,.T.);
#3244=ORIENTED_EDGE('',*,*,#3805,.T.);
#3245=ORIENTED_EDGE('',*,*,#3799,.F.);
#3246=ORIENTED_EDGE('',*,*,#3809,.F.);
#3247=CARTESIAN_POINT('',(-10.0249996948242,-3.40000000000073,0.3151600001));
#3248=DIRECTION('',(0.0,-0.0,1.0));
#3249=DIRECTION('',(1.0,1.15477070628082E-011,0.0));
#3250=SURFACE_STYLE_FILL_AREA(#3811);
#3251=SURFACE_STYLE_FILL_AREA(#3812);
#3252=ORIENTED_EDGE('',*,*,#3813,.F.);
#3253=ORIENTED_EDGE('',*,*,#3814,.F.);
#3254=ORIENTED_EDGE('',*,*,#3815,.F.);
#3255=ORIENTED_EDGE('',*,*,#3816,.F.);
#3256=CARTESIAN_POINT('',(-9.73749970244822,3.4,0.3278600001));
#3257=DIRECTION('',(0.0,0.0,1.0));
#3258=DIRECTION('',(1.0,0.0,0.0));
#3259=SURFACE_STYLE_FILL_AREA(#3817);
#3260=ORIENTED_EDGE('',*,*,#3818,.T.);
#3261=ORIENTED_EDGE('',*,*,#3819,.T.);
#3262=ORIENTED_EDGE('',*,*,#3820,.T.);
#3263=ORIENTED_EDGE('',*,*,#3821,.T.);
#3264=CARTESIAN_POINT('',(-9.73749970244822,3.4,0.3151600001));
#3265=DIRECTION('',(0.0,0.0,1.0));
#3266=DIRECTION('',(1.0,0.0,0.0));
#3267=SURFACE_STYLE_FILL_AREA(#3822);
#3268=ORIENTED_EDGE('',*,*,#3816,.T.);
#3269=ORIENTED_EDGE('',*,*,#3823,.T.);
#3270=ORIENTED_EDGE('',*,*,#3819,.F.);
#3271=ORIENTED_EDGE('',*,*,#3824,.F.);
#3272=CARTESIAN_POINT('',(-10.025,3.46350006103516,0.3151600001));
#3273=DIRECTION('',(0.0,-1.0,0.0));
#3274=DIRECTION('',(0.0,0.0,-1.0));
#3275=SURFACE_STYLE_FILL_AREA(#3825);
#3276=ORIENTED_EDGE('',*,*,#3815,.T.);
#3277=ORIENTED_EDGE('',*,*,#3826,.T.);
#3278=ORIENTED_EDGE('',*,*,#3820,.F.);
#3279=ORIENTED_EDGE('',*,*,#3823,.F.);
#3280=CARTESIAN_POINT('',(-9.44999969482495,3.4,0.3151600001));
#3281=DIRECTION('',(0.0,0.0,1.0));
#3282=DIRECTION('',(-4.8059016478333E-006,0.999999999988452,0.0));
#3283=SURFACE_STYLE_FILL_AREA(#3827);
#3284=ORIENTED_EDGE('',*,*,#3814,.T.);
#3285=ORIENTED_EDGE('',*,*,#3828,.T.);
#3286=ORIENTED_EDGE('',*,*,#3821,.F.);
#3287=ORIENTED_EDGE('',*,*,#3826,.F.);
#3288=CARTESIAN_POINT('',(-9.45,3.33649993896484,0.3151600001));
#3289=DIRECTION('',(0.0,1.0,0.0));
#3290=DIRECTION('',(0.0,0.0,1.0));
#3291=SURFACE_STYLE_FILL_AREA(#3829);
#3292=ORIENTED_EDGE('',*,*,#3813,.T.);
#3293=ORIENTED_EDGE('',*,*,#3824,.T.);
#3294=ORIENTED_EDGE('',*,*,#3818,.F.);
#3295=ORIENTED_EDGE('',*,*,#3828,.F.);
#3296=CARTESIAN_POINT('',(-10.0249996948235,3.4,0.3151600001));
#3297=DIRECTION('',(0.0,0.0,1.0));
#3298=DIRECTION('',(-4.80592474324742E-006,-0.999999999988451,0.0));
#3299=SURFACE_STYLE_FILL_AREA(#3830);
#3300=SURFACE_STYLE_FILL_AREA(#3831);
#3301=ORIENTED_EDGE('',*,*,#3832,.F.);
#3302=ORIENTED_EDGE('',*,*,#3833,.F.);
#3303=ORIENTED_EDGE('',*,*,#3834,.F.);
#3304=ORIENTED_EDGE('',*,*,#3835,.F.);
#3305=CARTESIAN_POINT('',(-9.44999969482422,-4.35624984881021,0.3278600001));
#3306=DIRECTION('',(0.0,0.0,1.0));
#3307=DIRECTION('',(1.0,0.0,0.0));
#3308=SURFACE_STYLE_FILL_AREA(#3836);
#3309=ORIENTED_EDGE('',*,*,#3837,.T.);
#3310=ORIENTED_EDGE('',*,*,#3838,.T.);
#3311=ORIENTED_EDGE('',*,*,#3839,.T.);
#3312=ORIENTED_EDGE('',*,*,#3840,.T.);
#3313=CARTESIAN_POINT('',(-9.44999969482422,-4.35624984881021,0.3151600001));
#3314=DIRECTION('',(0.0,0.0,1.0));
#3315=DIRECTION('',(1.0,0.0,0.0));
#3316=SURFACE_STYLE_FILL_AREA(#3841);
#3317=ORIENTED_EDGE('',*,*,#3835,.T.);
#3318=ORIENTED_EDGE('',*,*,#3842,.T.);
#3319=ORIENTED_EDGE('',*,*,#3838,.F.);
#3320=ORIENTED_EDGE('',*,*,#3843,.F.);
#3321=CARTESIAN_POINT('',(-9.38649963378906,-3.4,0.3151600001));
#3322=DIRECTION('',(-1.0,0.0,0.0));
#3323=DIRECTION('',(0.0,0.0,1.0));
#3324=SURFACE_STYLE_FILL_AREA(#3844);
#3325=ORIENTED_EDGE('',*,*,#3834,.T.);
#3326=ORIENTED_EDGE('',*,*,#3845,.T.);
#3327=ORIENTED_EDGE('',*,*,#3839,.F.);
#3328=ORIENTED_EDGE('',*,*,#3842,.F.);
#3329=CARTESIAN_POINT('',(-9.44999969482422,-5.31249969482422,0.3151600001));
#3330=DIRECTION('',(0.0,0.0,1.0));
#3331=DIRECTION('',(0.999999999988452,-4.80591319554036E-006,0.0));
#3332=SURFACE_STYLE_FILL_AREA(#3846);
#3333=ORIENTED_EDGE('',*,*,#3833,.T.);
#3334=ORIENTED_EDGE('',*,*,#3847,.T.);
#3335=ORIENTED_EDGE('',*,*,#3840,.F.);
#3336=ORIENTED_EDGE('',*,*,#3845,.F.);
#3337=CARTESIAN_POINT('',(-9.51349975585937,-5.3125,0.3151600001));
#3338=DIRECTION('',(1.0,0.0,0.0));
#3339=DIRECTION('',(0.0,0.0,-1.0));
#3340=SURFACE_STYLE_FILL_AREA(#3848);
#3341=ORIENTED_EDGE('',*,*,#3832,.T.);
#3342=ORIENTED_EDGE('',*,*,#3843,.T.);
#3343=ORIENTED_EDGE('',*,*,#3837,.F.);
#3344=ORIENTED_EDGE('',*,*,#3847,.F.);
#3345=CARTESIAN_POINT('',(-9.44999969482422,-3.39999999999927,0.3151600001));
#3346=DIRECTION('',(0.0,0.0,1.0));
#3347=DIRECTION('',(-1.0,-1.15477070628082E-011,0.0));
#3348=SURFACE_STYLE_FILL_AREA(#3849);
#3349=SURFACE_STYLE_FILL_AREA(#3850);
#3350=ORIENTED_EDGE('',*,*,#3851,.F.);
#3351=ORIENTED_EDGE('',*,*,#3852,.F.);
#3352=ORIENTED_EDGE('',*,*,#3853,.F.);
#3353=ORIENTED_EDGE('',*,*,#3854,.F.);
#3354=CARTESIAN_POINT('',(0.0,-2.58525071149715E-017,4.9895600001));
#3355=DIRECTION('',(0.0,0.0,1.0));
#3356=DIRECTION('',(1.0,0.0,0.0));
#3357=SURFACE_STYLE_FILL_AREA(#3855);
#3358=ORIENTED_EDGE('',*,*,#3856,.T.);
#3359=ORIENTED_EDGE('',*,*,#3857,.T.);
#3360=ORIENTED_EDGE('',*,*,#3858,.T.);
#3361=ORIENTED_EDGE('',*,*,#3859,.T.);
#3362=CARTESIAN_POINT('',(0.0,-2.58525071149715E-017,0.2895600001));
#3363=DIRECTION('',(0.0,0.0,1.0));
#3364=DIRECTION('',(1.0,0.0,0.0));
#3365=SURFACE_STYLE_FILL_AREA(#3860);
#3366=ORIENTED_EDGE('',*,*,#3854,.T.);
#3367=ORIENTED_EDGE('',*,*,#3861,.T.);
#3368=ORIENTED_EDGE('',*,*,#3857,.F.);
#3369=ORIENTED_EDGE('',*,*,#3862,.F.);
#3370=CARTESIAN_POINT('',(-9.96,-3.3225,0.2895600001));
#3371=DIRECTION('',(1.0,0.0,0.0));
#3372=DIRECTION('',(0.0,0.0,-1.0));
#3373=SURFACE_STYLE_FILL_AREA(#3863);
#3374=ORIENTED_EDGE('',*,*,#3853,.T.);
#3375=ORIENTED_EDGE('',*,*,#3864,.T.);
#3376=ORIENTED_EDGE('',*,*,#3858,.F.);
#3377=ORIENTED_EDGE('',*,*,#3861,.F.);
#3378=CARTESIAN_POINT('',(-9.96,3.3225,0.2895600001));
#3379=DIRECTION('',(0.0,-1.0,0.0));
#3380=DIRECTION('',(0.0,0.0,-1.0));
#3381=SURFACE_STYLE_FILL_AREA(#3865);
#3382=ORIENTED_EDGE('',*,*,#3852,.T.);
#3383=ORIENTED_EDGE('',*,*,#3866,.T.);
#3384=ORIENTED_EDGE('',*,*,#3859,.F.);
#3385=ORIENTED_EDGE('',*,*,#3864,.F.);
#3386=CARTESIAN_POINT('',(9.96,3.3225,0.2895600001));
#3387=DIRECTION('',(-1.0,0.0,0.0));
#3388=DIRECTION('',(0.0,0.0,1.0));
#3389=SURFACE_STYLE_FILL_AREA(#3867);
#3390=ORIENTED_EDGE('',*,*,#3851,.T.);
#3391=ORIENTED_EDGE('',*,*,#3862,.T.);
#3392=ORIENTED_EDGE('',*,*,#3856,.F.);
#3393=ORIENTED_EDGE('',*,*,#3866,.F.);
#3394=CARTESIAN_POINT('',(9.96,-3.3225,0.2895600001));
#3395=DIRECTION('',(0.0,1.0,0.0));
#3396=DIRECTION('',(0.0,0.0,1.0));
#3397=SURFACE_STYLE_FILL_AREA(#3868);
#3402=FILL_AREA_STYLE('',(#3869));
#3403=EDGE_CURVE('',#3870,#3871,#3872,.T.);
#3404=EDGE_CURVE('',#3873,#3870,#3874,.T.);
#3405=EDGE_CURVE('',#3875,#3873,#3876,.T.);
#3406=EDGE_CURVE('',#3871,#3875,#3877,.T.);
#3407=FILL_AREA_STYLE('',(#3878));
#3408=EDGE_CURVE('',#3879,#3880,#3881,.T.);
#3409=EDGE_CURVE('',#3880,#3882,#3883,.T.);
#3410=EDGE_CURVE('',#3882,#3884,#3885,.T.);
#3411=EDGE_CURVE('',#3884,#3879,#3886,.T.);
#3412=FILL_AREA_STYLE('',(#3887));
#3413=EDGE_CURVE('',#3875,#3882,#3888,.T.);
#3414=EDGE_CURVE('',#3871,#3880,#3889,.T.);
#3415=FILL_AREA_STYLE('',(#3890));
#3416=EDGE_CURVE('',#3873,#3884,#3891,.T.);
#3417=FILL_AREA_STYLE('',(#3892));
#3418=EDGE_CURVE('',#3870,#3879,#3893,.T.);
#3419=FILL_AREA_STYLE('',(#3894));
#3420=FILL_AREA_STYLE('',(#3895));
#3421=FILL_AREA_STYLE('',(#3896));
#3422=EDGE_CURVE('',#3897,#3898,#3899,.T.);
#3423=EDGE_CURVE('',#3900,#3897,#3901,.T.);
#3424=EDGE_CURVE('',#3902,#3900,#3903,.T.);
#3425=EDGE_CURVE('',#3898,#3902,#3904,.T.);
#3426=FILL_AREA_STYLE('',(#3905));
#3427=EDGE_CURVE('',#3906,#3907,#3908,.T.);
#3428=EDGE_CURVE('',#3907,#3909,#3910,.T.);
#3429=EDGE_CURVE('',#3909,#3911,#3912,.T.);
#3430=EDGE_CURVE('',#3911,#3906,#3913,.T.);
#3431=FILL_AREA_STYLE('',(#3914));
#3432=EDGE_CURVE('',#3902,#3909,#3915,.T.);
#3433=EDGE_CURVE('',#3898,#3907,#3916,.T.);
#3434=FILL_AREA_STYLE('',(#3917));
#3435=EDGE_CURVE('',#3900,#3911,#3918,.T.);
#3436=FILL_AREA_STYLE('',(#3919));
#3437=EDGE_CURVE('',#3897,#3906,#3920,.T.);
#3438=FILL_AREA_STYLE('',(#3921));
#3439=FILL_AREA_STYLE('',(#3922));
#3440=FILL_AREA_STYLE('',(#3923));
#3441=EDGE_CURVE('',#3924,#3925,#3926,.T.);
#3442=EDGE_CURVE('',#3927,#3924,#3928,.T.);
#3443=EDGE_CURVE('',#3929,#3927,#3930,.T.);
#3444=EDGE_CURVE('',#3925,#3929,#3931,.T.);
#3445=FILL_AREA_STYLE('',(#3932));
#3446=EDGE_CURVE('',#3933,#3934,#3935,.T.);
#3447=EDGE_CURVE('',#3934,#3936,#3937,.T.);
#3448=EDGE_CURVE('',#3936,#3938,#3939,.T.);
#3449=EDGE_CURVE('',#3938,#3933,#3940,.T.);
#3450=FILL_AREA_STYLE('',(#3941));
#3451=EDGE_CURVE('',#3929,#3936,#3942,.T.);
#3452=EDGE_CURVE('',#3925,#3934,#3943,.T.);
#3453=FILL_AREA_STYLE('',(#3944));
#3454=EDGE_CURVE('',#3927,#3938,#3945,.T.);
#3455=FILL_AREA_STYLE('',(#3946));
#3456=EDGE_CURVE('',#3924,#3933,#3947,.T.);
#3457=FILL_AREA_STYLE('',(#3948));
#3458=FILL_AREA_STYLE('',(#3949));
#3459=FILL_AREA_STYLE('',(#3950));
#3460=EDGE_CURVE('',#3951,#3952,#3953,.T.);
#3461=EDGE_CURVE('',#3954,#3951,#3955,.T.);
#3462=EDGE_CURVE('',#3956,#3954,#3957,.T.);
#3463=EDGE_CURVE('',#3952,#3956,#3958,.T.);
#3464=FILL_AREA_STYLE('',(#3959));
#3465=EDGE_CURVE('',#3960,#3961,#3962,.T.);
#3466=EDGE_CURVE('',#3961,#3963,#3964,.T.);
#3467=EDGE_CURVE('',#3963,#3965,#3966,.T.);
#3468=EDGE_CURVE('',#3965,#3960,#3967,.T.);
#3469=FILL_AREA_STYLE('',(#3968));
#3470=EDGE_CURVE('',#3956,#3963,#3969,.T.);
#3471=EDGE_CURVE('',#3952,#3961,#3970,.T.);
#3472=FILL_AREA_STYLE('',(#3971));
#3473=EDGE_CURVE('',#3954,#3965,#3972,.T.);
#3474=FILL_AREA_STYLE('',(#3973));
#3475=EDGE_CURVE('',#3951,#3960,#3974,.T.);
#3476=FILL_AREA_STYLE('',(#3975));
#3477=FILL_AREA_STYLE('',(#3976));
#3478=FILL_AREA_STYLE('',(#3977));
#3479=EDGE_CURVE('',#3978,#3979,#3980,.T.);
#3480=EDGE_CURVE('',#3981,#3978,#3982,.T.);
#3481=EDGE_CURVE('',#3983,#3981,#3984,.T.);
#3482=EDGE_CURVE('',#3979,#3983,#3985,.T.);
#3483=FILL_AREA_STYLE('',(#3986));
#3484=EDGE_CURVE('',#3987,#3988,#3989,.T.);
#3485=EDGE_CURVE('',#3988,#3990,#3991,.T.);
#3486=EDGE_CURVE('',#3990,#3992,#3993,.T.);
#3487=EDGE_CURVE('',#3992,#3987,#3994,.T.);
#3488=FILL_AREA_STYLE('',(#3995));
#3489=EDGE_CURVE('',#3983,#3990,#3996,.T.);
#3490=EDGE_CURVE('',#3979,#3988,#3997,.T.);
#3491=FILL_AREA_STYLE('',(#3998));
#3492=EDGE_CURVE('',#3981,#3992,#3999,.T.);
#3493=FILL_AREA_STYLE('',(#4000));
#3494=EDGE_CURVE('',#3978,#3987,#4001,.T.);
#3495=FILL_AREA_STYLE('',(#4002));
#3496=FILL_AREA_STYLE('',(#4003));
#3497=FILL_AREA_STYLE('',(#4004));
#3498=EDGE_CURVE('',#4005,#4006,#4007,.T.);
#3499=EDGE_CURVE('',#4008,#4005,#4009,.T.);
#3500=EDGE_CURVE('',#4010,#4008,#4011,.T.);
#3501=EDGE_CURVE('',#4006,#4010,#4012,.T.);
#3502=FILL_AREA_STYLE('',(#4013));
#3503=EDGE_CURVE('',#4014,#4015,#4016,.T.);
#3504=EDGE_CURVE('',#4015,#4017,#4018,.T.);
#3505=EDGE_CURVE('',#4017,#4019,#4020,.T.);
#3506=EDGE_CURVE('',#4019,#4014,#4021,.T.);
#3507=FILL_AREA_STYLE('',(#4022));
#3508=EDGE_CURVE('',#4010,#4017,#4023,.T.);
#3509=EDGE_CURVE('',#4006,#4015,#4024,.T.);
#3510=FILL_AREA_STYLE('',(#4025));
#3511=EDGE_CURVE('',#4008,#4019,#4026,.T.);
#3512=FILL_AREA_STYLE('',(#4027));
#3513=EDGE_CURVE('',#4005,#4014,#4028,.T.);
#3514=FILL_AREA_STYLE('',(#4029));
#3515=FILL_AREA_STYLE('',(#4030));
#3516=FILL_AREA_STYLE('',(#4031));
#3517=EDGE_CURVE('',#4032,#4033,#4034,.T.);
#3518=EDGE_CURVE('',#4035,#4032,#4036,.T.);
#3519=EDGE_CURVE('',#4037,#4035,#4038,.T.);
#3520=EDGE_CURVE('',#4033,#4037,#4039,.T.);
#3521=FILL_AREA_STYLE('',(#4040));
#3522=EDGE_CURVE('',#4041,#4042,#4043,.T.);
#3523=EDGE_CURVE('',#4042,#4044,#4045,.T.);
#3524=EDGE_CURVE('',#4044,#4046,#4047,.T.);
#3525=EDGE_CURVE('',#4046,#4041,#4048,.T.);
#3526=FILL_AREA_STYLE('',(#4049));
#3527=EDGE_CURVE('',#4037,#4044,#4050,.T.);
#3528=EDGE_CURVE('',#4033,#4042,#4051,.T.);
#3529=FILL_AREA_STYLE('',(#4052));
#3530=EDGE_CURVE('',#4035,#4046,#4053,.T.);
#3531=FILL_AREA_STYLE('',(#4054));
#3532=EDGE_CURVE('',#4032,#4041,#4055,.T.);
#3533=FILL_AREA_STYLE('',(#4056));
#3534=FILL_AREA_STYLE('',(#4057));
#3535=FILL_AREA_STYLE('',(#4058));
#3536=EDGE_CURVE('',#4059,#4060,#4061,.T.);
#3537=EDGE_CURVE('',#4062,#4059,#4063,.T.);
#3538=EDGE_CURVE('',#4064,#4062,#4065,.T.);
#3539=EDGE_CURVE('',#4060,#4064,#4066,.T.);
#3540=FILL_AREA_STYLE('',(#4067));
#3541=EDGE_CURVE('',#4068,#4069,#4070,.T.);
#3542=EDGE_CURVE('',#4069,#4071,#4072,.T.);
#3543=EDGE_CURVE('',#4071,#4073,#4074,.T.);
#3544=EDGE_CURVE('',#4073,#4068,#4075,.T.);
#3545=FILL_AREA_STYLE('',(#4076));
#3546=EDGE_CURVE('',#4064,#4071,#4077,.T.);
#3547=EDGE_CURVE('',#4060,#4069,#4078,.T.);
#3548=FILL_AREA_STYLE('',(#4079));
#3549=EDGE_CURVE('',#4062,#4073,#4080,.T.);
#3550=FILL_AREA_STYLE('',(#4081));
#3551=EDGE_CURVE('',#4059,#4068,#4082,.T.);
#3552=FILL_AREA_STYLE('',(#4083));
#3553=FILL_AREA_STYLE('',(#4084));
#3554=FILL_AREA_STYLE('',(#4085));
#3555=EDGE_CURVE('',#4086,#4087,#4088,.T.);
#3556=EDGE_CURVE('',#4089,#4086,#4090,.T.);
#3557=EDGE_CURVE('',#4091,#4089,#4092,.T.);
#3558=EDGE_CURVE('',#4087,#4091,#4093,.T.);
#3559=FILL_AREA_STYLE('',(#4094));
#3560=EDGE_CURVE('',#4095,#4096,#4097,.T.);
#3561=EDGE_CURVE('',#4096,#4098,#4099,.T.);
#3562=EDGE_CURVE('',#4098,#4100,#4101,.T.);
#3563=EDGE_CURVE('',#4100,#4095,#4102,.T.);
#3564=FILL_AREA_STYLE('',(#4103));
#3565=EDGE_CURVE('',#4091,#4098,#4104,.T.);
#3566=EDGE_CURVE('',#4087,#4096,#4105,.T.);
#3567=FILL_AREA_STYLE('',(#4106));
#3568=EDGE_CURVE('',#4089,#4100,#4107,.T.);
#3569=FILL_AREA_STYLE('',(#4108));
#3570=EDGE_CURVE('',#4086,#4095,#4109,.T.);
#3571=FILL_AREA_STYLE('',(#4110));
#3572=FILL_AREA_STYLE('',(#4111));
#3573=FILL_AREA_STYLE('',(#4112));
#3574=EDGE_CURVE('',#4113,#4114,#4115,.T.);
#3575=EDGE_CURVE('',#4116,#4113,#4117,.T.);
#3576=EDGE_CURVE('',#4118,#4116,#4119,.T.);
#3577=EDGE_CURVE('',#4114,#4118,#4120,.T.);
#3578=FILL_AREA_STYLE('',(#4121));
#3579=EDGE_CURVE('',#4122,#4123,#4124,.T.);
#3580=EDGE_CURVE('',#4123,#4125,#4126,.T.);
#3581=EDGE_CURVE('',#4125,#4127,#4128,.T.);
#3582=EDGE_CURVE('',#4127,#4122,#4129,.T.);
#3583=FILL_AREA_STYLE('',(#4130));
#3584=EDGE_CURVE('',#4118,#4125,#4131,.T.);
#3585=EDGE_CURVE('',#4114,#4123,#4132,.T.);
#3586=FILL_AREA_STYLE('',(#4133));
#3587=EDGE_CURVE('',#4116,#4127,#4134,.T.);
#3588=FILL_AREA_STYLE('',(#4135));
#3589=EDGE_CURVE('',#4113,#4122,#4136,.T.);
#3590=FILL_AREA_STYLE('',(#4137));
#3591=FILL_AREA_STYLE('',(#4138));
#3592=FILL_AREA_STYLE('',(#4139));
#3593=EDGE_CURVE('',#4140,#4141,#4142,.T.);
#3594=EDGE_CURVE('',#4143,#4140,#4144,.T.);
#3595=EDGE_CURVE('',#4145,#4143,#4146,.T.);
#3596=EDGE_CURVE('',#4141,#4145,#4147,.T.);
#3597=FILL_AREA_STYLE('',(#4148));
#3598=EDGE_CURVE('',#4149,#4150,#4151,.T.);
#3599=EDGE_CURVE('',#4150,#4152,#4153,.T.);
#3600=EDGE_CURVE('',#4152,#4154,#4155,.T.);
#3601=EDGE_CURVE('',#4154,#4149,#4156,.T.);
#3602=FILL_AREA_STYLE('',(#4157));
#3603=EDGE_CURVE('',#4145,#4152,#4158,.T.);
#3604=EDGE_CURVE('',#4141,#4150,#4159,.T.);
#3605=FILL_AREA_STYLE('',(#4160));
#3606=EDGE_CURVE('',#4143,#4154,#4161,.T.);
#3607=FILL_AREA_STYLE('',(#4162));
#3608=EDGE_CURVE('',#4140,#4149,#4163,.T.);
#3609=FILL_AREA_STYLE('',(#4164));
#3610=FILL_AREA_STYLE('',(#4165));
#3611=FILL_AREA_STYLE('',(#4166));
#3612=EDGE_CURVE('',#4167,#4168,#4169,.T.);
#3613=EDGE_CURVE('',#4170,#4167,#4171,.T.);
#3614=EDGE_CURVE('',#4172,#4170,#4173,.T.);
#3615=EDGE_CURVE('',#4168,#4172,#4174,.T.);
#3616=FILL_AREA_STYLE('',(#4175));
#3617=EDGE_CURVE('',#4176,#4177,#4178,.T.);
#3618=EDGE_CURVE('',#4177,#4179,#4180,.T.);
#3619=EDGE_CURVE('',#4179,#4181,#4182,.T.);
#3620=EDGE_CURVE('',#4181,#4176,#4183,.T.);
#3621=FILL_AREA_STYLE('',(#4184));
#3622=EDGE_CURVE('',#4172,#4179,#4185,.T.);
#3623=EDGE_CURVE('',#4168,#4177,#4186,.T.);
#3624=FILL_AREA_STYLE('',(#4187));
#3625=EDGE_CURVE('',#4170,#4181,#4188,.T.);
#3626=FILL_AREA_STYLE('',(#4189));
#3627=EDGE_CURVE('',#4167,#4176,#4190,.T.);
#3628=FILL_AREA_STYLE('',(#4191));
#3629=FILL_AREA_STYLE('',(#4192));
#3630=FILL_AREA_STYLE('',(#4193));
#3631=EDGE_CURVE('',#4194,#4195,#4196,.T.);
#3632=EDGE_CURVE('',#4197,#4194,#4198,.T.);
#3633=EDGE_CURVE('',#4199,#4197,#4200,.T.);
#3634=EDGE_CURVE('',#4195,#4199,#4201,.T.);
#3635=FILL_AREA_STYLE('',(#4202));
#3636=EDGE_CURVE('',#4203,#4204,#4205,.T.);
#3637=EDGE_CURVE('',#4204,#4206,#4207,.T.);
#3638=EDGE_CURVE('',#4206,#4208,#4209,.T.);
#3639=EDGE_CURVE('',#4208,#4203,#4210,.T.);
#3640=FILL_AREA_STYLE('',(#4211));
#3641=EDGE_CURVE('',#4199,#4206,#4212,.T.);
#3642=EDGE_CURVE('',#4195,#4204,#4213,.T.);
#3643=FILL_AREA_STYLE('',(#4214));
#3644=EDGE_CURVE('',#4197,#4208,#4215,.T.);
#3645=FILL_AREA_STYLE('',(#4216));
#3646=EDGE_CURVE('',#4194,#4203,#4217,.T.);
#3647=FILL_AREA_STYLE('',(#4218));
#3648=FILL_AREA_STYLE('',(#4219));
#3649=FILL_AREA_STYLE('',(#4220));
#3650=EDGE_CURVE('',#4221,#4222,#4223,.T.);
#3651=EDGE_CURVE('',#4224,#4221,#4225,.T.);
#3652=EDGE_CURVE('',#4226,#4224,#4227,.T.);
#3653=EDGE_CURVE('',#4222,#4226,#4228,.T.);
#3654=FILL_AREA_STYLE('',(#4229));
#3655=EDGE_CURVE('',#4230,#4231,#4232,.T.);
#3656=EDGE_CURVE('',#4231,#4233,#4234,.T.);
#3657=EDGE_CURVE('',#4233,#4235,#4236,.T.);
#3658=EDGE_CURVE('',#4235,#4230,#4237,.T.);
#3659=FILL_AREA_STYLE('',(#4238));
#3660=EDGE_CURVE('',#4226,#4233,#4239,.T.);
#3661=EDGE_CURVE('',#4222,#4231,#4240,.T.);
#3662=FILL_AREA_STYLE('',(#4241));
#3663=EDGE_CURVE('',#4224,#4235,#4242,.T.);
#3664=FILL_AREA_STYLE('',(#4243));
#3665=EDGE_CURVE('',#4221,#4230,#4244,.T.);
#3666=FILL_AREA_STYLE('',(#4245));
#3667=FILL_AREA_STYLE('',(#4246));
#3668=FILL_AREA_STYLE('',(#4247));
#3669=EDGE_CURVE('',#4248,#4249,#4250,.T.);
#3670=EDGE_CURVE('',#4251,#4248,#4252,.T.);
#3671=EDGE_CURVE('',#4253,#4251,#4254,.T.);
#3672=EDGE_CURVE('',#4249,#4253,#4255,.T.);
#3673=FILL_AREA_STYLE('',(#4256));
#3674=EDGE_CURVE('',#4257,#4258,#4259,.T.);
#3675=EDGE_CURVE('',#4258,#4260,#4261,.T.);
#3676=EDGE_CURVE('',#4260,#4262,#4263,.T.);
#3677=EDGE_CURVE('',#4262,#4257,#4264,.T.);
#3678=FILL_AREA_STYLE('',(#4265));
#3679=EDGE_CURVE('',#4253,#4260,#4266,.T.);
#3680=EDGE_CURVE('',#4249,#4258,#4267,.T.);
#3681=FILL_AREA_STYLE('',(#4268));
#3682=EDGE_CURVE('',#4251,#4262,#4269,.T.);
#3683=FILL_AREA_STYLE('',(#4270));
#3684=EDGE_CURVE('',#4248,#4257,#4271,.T.);
#3685=FILL_AREA_STYLE('',(#4272));
#3686=FILL_AREA_STYLE('',(#4273));
#3687=FILL_AREA_STYLE('',(#4274));
#3688=EDGE_CURVE('',#4275,#4276,#4277,.T.);
#3689=EDGE_CURVE('',#4278,#4275,#4279,.T.);
#3690=EDGE_CURVE('',#4280,#4278,#4281,.T.);
#3691=EDGE_CURVE('',#4276,#4280,#4282,.T.);
#3692=FILL_AREA_STYLE('',(#4283));
#3693=EDGE_CURVE('',#4284,#4285,#4286,.T.);
#3694=EDGE_CURVE('',#4285,#4287,#4288,.T.);
#3695=EDGE_CURVE('',#4287,#4289,#4290,.T.);
#3696=EDGE_CURVE('',#4289,#4284,#4291,.T.);
#3697=FILL_AREA_STYLE('',(#4292));
#3698=EDGE_CURVE('',#4280,#4287,#4293,.T.);
#3699=EDGE_CURVE('',#4276,#4285,#4294,.T.);
#3700=FILL_AREA_STYLE('',(#4295));
#3701=EDGE_CURVE('',#4278,#4289,#4296,.T.);
#3702=FILL_AREA_STYLE('',(#4297));
#3703=EDGE_CURVE('',#4275,#4284,#4298,.T.);
#3704=FILL_AREA_STYLE('',(#4299));
#3705=FILL_AREA_STYLE('',(#4300));
#3706=FILL_AREA_STYLE('',(#4301));
#3707=EDGE_CURVE('',#4302,#4303,#4304,.T.);
#3708=EDGE_CURVE('',#4302,#4305,#4306,.T.);
#3709=EDGE_CURVE('',#4307,#4305,#4308,.T.);
#3710=EDGE_CURVE('',#4303,#4307,#4309,.T.);
#3711=FILL_AREA_STYLE('',(#4310));
#3712=EDGE_CURVE('',#4307,#4303,#4311,.T.);
#3713=FILL_AREA_STYLE('',(#4312));
#3714=EDGE_CURVE('',#4305,#4302,#4313,.T.);
#3715=FILL_AREA_STYLE('',(#4314));
#3716=FILL_AREA_STYLE('',(#4315));
#3717=FILL_AREA_STYLE('',(#4316));
#3718=EDGE_CURVE('',#4317,#4318,#4319,.T.);
#3719=EDGE_CURVE('',#4320,#4317,#4321,.T.);
#3720=EDGE_CURVE('',#4322,#4320,#4323,.T.);
#3721=EDGE_CURVE('',#4318,#4322,#4324,.T.);
#3722=FILL_AREA_STYLE('',(#4325));
#3723=EDGE_CURVE('',#4326,#4327,#4328,.T.);
#3724=EDGE_CURVE('',#4327,#4329,#4330,.T.);
#3725=EDGE_CURVE('',#4329,#4331,#4332,.T.);
#3726=EDGE_CURVE('',#4331,#4326,#4333,.T.);
#3727=FILL_AREA_STYLE('',(#4334));
#3728=EDGE_CURVE('',#4322,#4329,#4335,.T.);
#3729=EDGE_CURVE('',#4318,#4327,#4336,.T.);
#3730=FILL_AREA_STYLE('',(#4337));
#3731=EDGE_CURVE('',#4320,#4331,#4338,.T.);
#3732=FILL_AREA_STYLE('',(#4339));
#3733=EDGE_CURVE('',#4317,#4326,#4340,.T.);
#3734=FILL_AREA_STYLE('',(#4341));
#3735=FILL_AREA_STYLE('',(#4342));
#3736=FILL_AREA_STYLE('',(#4343));
#3737=EDGE_CURVE('',#4344,#4345,#4346,.T.);
#3738=EDGE_CURVE('',#4347,#4344,#4348,.T.);
#3739=EDGE_CURVE('',#4349,#4347,#4350,.T.);
#3740=EDGE_CURVE('',#4345,#4349,#4351,.T.);
#3741=FILL_AREA_STYLE('',(#4352));
#3742=EDGE_CURVE('',#4353,#4354,#4355,.T.);
#3743=EDGE_CURVE('',#4354,#4356,#4357,.T.);
#3744=EDGE_CURVE('',#4356,#4358,#4359,.T.);
#3745=EDGE_CURVE('',#4358,#4353,#4360,.T.);
#3746=FILL_AREA_STYLE('',(#4361));
#3747=EDGE_CURVE('',#4349,#4356,#4362,.T.);
#3748=EDGE_CURVE('',#4345,#4354,#4363,.T.);
#3749=FILL_AREA_STYLE('',(#4364));
#3750=EDGE_CURVE('',#4347,#4358,#4365,.T.);
#3751=FILL_AREA_STYLE('',(#4366));
#3752=EDGE_CURVE('',#4344,#4353,#4367,.T.);
#3753=FILL_AREA_STYLE('',(#4368));
#3754=FILL_AREA_STYLE('',(#4369));
#3755=FILL_AREA_STYLE('',(#4370));
#3756=EDGE_CURVE('',#4371,#4372,#4373,.T.);
#3757=EDGE_CURVE('',#4374,#4371,#4375,.T.);
#3758=EDGE_CURVE('',#4376,#4374,#4377,.T.);
#3759=EDGE_CURVE('',#4372,#4376,#4378,.T.);
#3760=FILL_AREA_STYLE('',(#4379));
#3761=EDGE_CURVE('',#4380,#4381,#4382,.T.);
#3762=EDGE_CURVE('',#4381,#4383,#4384,.T.);
#3763=EDGE_CURVE('',#4383,#4385,#4386,.T.);
#3764=EDGE_CURVE('',#4385,#4380,#4387,.T.);
#3765=FILL_AREA_STYLE('',(#4388));
#3766=EDGE_CURVE('',#4376,#4383,#4389,.T.);
#3767=EDGE_CURVE('',#4372,#4381,#4390,.T.);
#3768=FILL_AREA_STYLE('',(#4391));
#3769=EDGE_CURVE('',#4374,#4385,#4392,.T.);
#3770=FILL_AREA_STYLE('',(#4393));
#3771=EDGE_CURVE('',#4371,#4380,#4394,.T.);
#3772=FILL_AREA_STYLE('',(#4395));
#3773=FILL_AREA_STYLE('',(#4396));
#3774=FILL_AREA_STYLE('',(#4397));
#3775=EDGE_CURVE('',#4398,#4399,#4400,.T.);
#3776=EDGE_CURVE('',#4401,#4398,#4402,.T.);
#3777=EDGE_CURVE('',#4403,#4401,#4404,.T.);
#3778=EDGE_CURVE('',#4399,#4403,#4405,.T.);
#3779=FILL_AREA_STYLE('',(#4406));
#3780=EDGE_CURVE('',#4407,#4408,#4409,.T.);
#3781=EDGE_CURVE('',#4408,#4410,#4411,.T.);
#3782=EDGE_CURVE('',#4410,#4412,#4413,.T.);
#3783=EDGE_CURVE('',#4412,#4407,#4414,.T.);
#3784=FILL_AREA_STYLE('',(#4415));
#3785=EDGE_CURVE('',#4403,#4410,#4416,.T.);
#3786=EDGE_CURVE('',#4399,#4408,#4417,.T.);
#3787=FILL_AREA_STYLE('',(#4418));
#3788=EDGE_CURVE('',#4401,#4412,#4419,.T.);
#3789=FILL_AREA_STYLE('',(#4420));
#3790=EDGE_CURVE('',#4398,#4407,#4421,.T.);
#3791=FILL_AREA_STYLE('',(#4422));
#3792=FILL_AREA_STYLE('',(#4423));
#3793=FILL_AREA_STYLE('',(#4424));
#3794=EDGE_CURVE('',#4425,#4426,#4427,.T.);
#3795=EDGE_CURVE('',#4428,#4425,#4429,.T.);
#3796=EDGE_CURVE('',#4430,#4428,#4431,.T.);
#3797=EDGE_CURVE('',#4426,#4430,#4432,.T.);
#3798=FILL_AREA_STYLE('',(#4433));
#3799=EDGE_CURVE('',#4434,#4435,#4436,.T.);
#3800=EDGE_CURVE('',#4435,#4437,#4438,.T.);
#3801=EDGE_CURVE('',#4437,#4439,#4440,.T.);
#3802=EDGE_CURVE('',#4439,#4434,#4441,.T.);
#3803=FILL_AREA_STYLE('',(#4442));
#3804=EDGE_CURVE('',#4430,#4437,#4443,.T.);
#3805=EDGE_CURVE('',#4426,#4435,#4444,.T.);
#3806=FILL_AREA_STYLE('',(#4445));
#3807=EDGE_CURVE('',#4428,#4439,#4446,.T.);
#3808=FILL_AREA_STYLE('',(#4447));
#3809=EDGE_CURVE('',#4425,#4434,#4448,.T.);
#3810=FILL_AREA_STYLE('',(#4449));
#3811=FILL_AREA_STYLE('',(#4450));
#3812=FILL_AREA_STYLE('',(#4451));
#3813=EDGE_CURVE('',#4452,#4453,#4454,.T.);
#3814=EDGE_CURVE('',#4455,#4452,#4456,.T.);
#3815=EDGE_CURVE('',#4457,#4455,#4458,.T.);
#3816=EDGE_CURVE('',#4453,#4457,#4459,.T.);
#3817=FILL_AREA_STYLE('',(#4460));
#3818=EDGE_CURVE('',#4461,#4462,#4463,.T.);
#3819=EDGE_CURVE('',#4462,#4464,#4465,.T.);
#3820=EDGE_CURVE('',#4464,#4466,#4467,.T.);
#3821=EDGE_CURVE('',#4466,#4461,#4468,.T.);
#3822=FILL_AREA_STYLE('',(#4469));
#3823=EDGE_CURVE('',#4457,#4464,#4470,.T.);
#3824=EDGE_CURVE('',#4453,#4462,#4471,.T.);
#3825=FILL_AREA_STYLE('',(#4472));
#3826=EDGE_CURVE('',#4455,#4466,#4473,.T.);
#3827=FILL_AREA_STYLE('',(#4474));
#3828=EDGE_CURVE('',#4452,#4461,#4475,.T.);
#3829=FILL_AREA_STYLE('',(#4476));
#3830=FILL_AREA_STYLE('',(#4477));
#3831=FILL_AREA_STYLE('',(#4478));
#3832=EDGE_CURVE('',#4479,#4480,#4481,.T.);
#3833=EDGE_CURVE('',#4482,#4479,#4483,.T.);
#3834=EDGE_CURVE('',#4484,#4482,#4485,.T.);
#3835=EDGE_CURVE('',#4480,#4484,#4486,.T.);
#3836=FILL_AREA_STYLE('',(#4487));
#3837=EDGE_CURVE('',#4488,#4489,#4490,.T.);
#3838=EDGE_CURVE('',#4489,#4491,#4492,.T.);
#3839=EDGE_CURVE('',#4491,#4493,#4494,.T.);
#3840=EDGE_CURVE('',#4493,#4488,#4495,.T.);
#3841=FILL_AREA_STYLE('',(#4496));
#3842=EDGE_CURVE('',#4484,#4491,#4497,.T.);
#3843=EDGE_CURVE('',#4480,#4489,#4498,.T.);
#3844=FILL_AREA_STYLE('',(#4499));
#3845=EDGE_CURVE('',#4482,#4493,#4500,.T.);
#3846=FILL_AREA_STYLE('',(#4501));
#3847=EDGE_CURVE('',#4479,#4488,#4502,.T.);
#3848=FILL_AREA_STYLE('',(#4503));
#3849=FILL_AREA_STYLE('',(#4504));
#3850=FILL_AREA_STYLE('',(#4505));
#3851=EDGE_CURVE('',#4506,#4507,#4508,.T.);
#3852=EDGE_CURVE('',#4509,#4506,#4510,.T.);
#3853=EDGE_CURVE('',#4511,#4509,#4512,.T.);
#3854=EDGE_CURVE('',#4507,#4511,#4513,.T.);
#3855=FILL_AREA_STYLE('',(#4514));
#3856=EDGE_CURVE('',#4515,#4516,#4517,.T.);
#3857=EDGE_CURVE('',#4516,#4518,#4519,.T.);
#3858=EDGE_CURVE('',#4518,#4520,#4521,.T.);
#3859=EDGE_CURVE('',#4520,#4515,#4522,.T.);
#3860=FILL_AREA_STYLE('',(#4523));
#3861=EDGE_CURVE('',#4511,#4518,#4524,.T.);
#3862=EDGE_CURVE('',#4507,#4516,#4525,.T.);
#3863=FILL_AREA_STYLE('',(#4526));
#3864=EDGE_CURVE('',#4509,#4520,#4527,.T.);
#3865=FILL_AREA_STYLE('',(#4528));
#3866=EDGE_CURVE('',#4506,#4515,#4529,.T.);
#3867=FILL_AREA_STYLE('',(#4530));
#3868=FILL_AREA_STYLE('',(#4531));
#3869=FILL_AREA_STYLE_COLOUR('',#4532);
#3870=VERTEX_POINT('',#4533);
#3871=VERTEX_POINT('',#4534);
#3872=LINE('',#4535,#4536);
#3873=VERTEX_POINT('',#4537);
#3874=LINE('',#4538,#4539);
#3875=VERTEX_POINT('',#4540);
#3876=LINE('',#4541,#4542);
#3877=LINE('',#4543,#4544);
#3878=FILL_AREA_STYLE_COLOUR('',#4545);
#3879=VERTEX_POINT('',#4546);
#3880=VERTEX_POINT('',#4547);
#3881=LINE('',#4548,#4549);
#3882=VERTEX_POINT('',#4550);
#3883=LINE('',#4551,#4552);
#3884=VERTEX_POINT('',#4553);
#3885=LINE('',#4554,#4555);
#3886=LINE('',#4556,#4557);
#3887=FILL_AREA_STYLE_COLOUR('',#4558);
#3888=LINE('',#4559,#4560);
#3889=LINE('',#4561,#4562);
#3890=FILL_AREA_STYLE_COLOUR('',#4563);
#3891=LINE('',#4564,#4565);
#3892=FILL_AREA_STYLE_COLOUR('',#4566);
#3893=LINE('',#4567,#4568);
#3894=FILL_AREA_STYLE_COLOUR('',#4569);
#3895=FILL_AREA_STYLE_COLOUR('',#4570);
#3896=FILL_AREA_STYLE_COLOUR('',#4571);
#3897=VERTEX_POINT('',#4572);
#3898=VERTEX_POINT('',#4573);
#3899=LINE('',#4574,#4575);
#3900=VERTEX_POINT('',#4576);
#3901=LINE('',#4577,#4578);
#3902=VERTEX_POINT('',#4579);
#3903=LINE('',#4580,#4581);
#3904=LINE('',#4582,#4583);
#3905=FILL_AREA_STYLE_COLOUR('',#4584);
#3906=VERTEX_POINT('',#4585);
#3907=VERTEX_POINT('',#4586);
#3908=LINE('',#4587,#4588);
#3909=VERTEX_POINT('',#4589);
#3910=LINE('',#4590,#4591);
#3911=VERTEX_POINT('',#4592);
#3912=LINE('',#4593,#4594);
#3913=LINE('',#4595,#4596);
#3914=FILL_AREA_STYLE_COLOUR('',#4597);
#3915=LINE('',#4598,#4599);
#3916=LINE('',#4600,#4601);
#3917=FILL_AREA_STYLE_COLOUR('',#4602);
#3918=LINE('',#4603,#4604);
#3919=FILL_AREA_STYLE_COLOUR('',#4605);
#3920=LINE('',#4606,#4607);
#3921=FILL_AREA_STYLE_COLOUR('',#4608);
#3922=FILL_AREA_STYLE_COLOUR('',#4609);
#3923=FILL_AREA_STYLE_COLOUR('',#4610);
#3924=VERTEX_POINT('',#4611);
#3925=VERTEX_POINT('',#4612);
#3926=LINE('',#4613,#4614);
#3927=VERTEX_POINT('',#4615);
#3928=LINE('',#4616,#4617);
#3929=VERTEX_POINT('',#4618);
#3930=LINE('',#4619,#4620);
#3931=LINE('',#4621,#4622);
#3932=FILL_AREA_STYLE_COLOUR('',#4623);
#3933=VERTEX_POINT('',#4624);
#3934=VERTEX_POINT('',#4625);
#3935=LINE('',#4626,#4627);
#3936=VERTEX_POINT('',#4628);
#3937=LINE('',#4629,#4630);
#3938=VERTEX_POINT('',#4631);
#3939=LINE('',#4632,#4633);
#3940=LINE('',#4634,#4635);
#3941=FILL_AREA_STYLE_COLOUR('',#4636);
#3942=LINE('',#4637,#4638);
#3943=LINE('',#4639,#4640);
#3944=FILL_AREA_STYLE_COLOUR('',#4641);
#3945=LINE('',#4642,#4643);
#3946=FILL_AREA_STYLE_COLOUR('',#4644);
#3947=LINE('',#4645,#4646);
#3948=FILL_AREA_STYLE_COLOUR('',#4647);
#3949=FILL_AREA_STYLE_COLOUR('',#4648);
#3950=FILL_AREA_STYLE_COLOUR('',#4649);
#3951=VERTEX_POINT('',#4650);
#3952=VERTEX_POINT('',#4651);
#3953=LINE('',#4652,#4653);
#3954=VERTEX_POINT('',#4654);
#3955=LINE('',#4655,#4656);
#3956=VERTEX_POINT('',#4657);
#3957=LINE('',#4658,#4659);
#3958=LINE('',#4660,#4661);
#3959=FILL_AREA_STYLE_COLOUR('',#4662);
#3960=VERTEX_POINT('',#4663);
#3961=VERTEX_POINT('',#4664);
#3962=LINE('',#4665,#4666);
#3963=VERTEX_POINT('',#4667);
#3964=LINE('',#4668,#4669);
#3965=VERTEX_POINT('',#4670);
#3966=LINE('',#4671,#4672);
#3967=LINE('',#4673,#4674);
#3968=FILL_AREA_STYLE_COLOUR('',#4675);
#3969=LINE('',#4676,#4677);
#3970=LINE('',#4678,#4679);
#3971=FILL_AREA_STYLE_COLOUR('',#4680);
#3972=LINE('',#4681,#4682);
#3973=FILL_AREA_STYLE_COLOUR('',#4683);
#3974=LINE('',#4684,#4685);
#3975=FILL_AREA_STYLE_COLOUR('',#4686);
#3976=FILL_AREA_STYLE_COLOUR('',#4687);
#3977=FILL_AREA_STYLE_COLOUR('',#4688);
#3978=VERTEX_POINT('',#4689);
#3979=VERTEX_POINT('',#4690);
#3980=LINE('',#4691,#4692);
#3981=VERTEX_POINT('',#4693);
#3982=LINE('',#4694,#4695);
#3983=VERTEX_POINT('',#4696);
#3984=LINE('',#4697,#4698);
#3985=LINE('',#4699,#4700);
#3986=FILL_AREA_STYLE_COLOUR('',#4701);
#3987=VERTEX_POINT('',#4702);
#3988=VERTEX_POINT('',#4703);
#3989=LINE('',#4704,#4705);
#3990=VERTEX_POINT('',#4706);
#3991=LINE('',#4707,#4708);
#3992=VERTEX_POINT('',#4709);
#3993=LINE('',#4710,#4711);
#3994=LINE('',#4712,#4713);
#3995=FILL_AREA_STYLE_COLOUR('',#4714);
#3996=LINE('',#4715,#4716);
#3997=LINE('',#4717,#4718);
#3998=FILL_AREA_STYLE_COLOUR('',#4719);
#3999=LINE('',#4720,#4721);
#4000=FILL_AREA_STYLE_COLOUR('',#4722);
#4001=LINE('',#4723,#4724);
#4002=FILL_AREA_STYLE_COLOUR('',#4725);
#4003=FILL_AREA_STYLE_COLOUR('',#4726);
#4004=FILL_AREA_STYLE_COLOUR('',#4727);
#4005=VERTEX_POINT('',#4728);
#4006=VERTEX_POINT('',#4729);
#4007=LINE('',#4730,#4731);
#4008=VERTEX_POINT('',#4732);
#4009=LINE('',#4733,#4734);
#4010=VERTEX_POINT('',#4735);
#4011=LINE('',#4736,#4737);
#4012=LINE('',#4738,#4739);
#4013=FILL_AREA_STYLE_COLOUR('',#4740);
#4014=VERTEX_POINT('',#4741);
#4015=VERTEX_POINT('',#4742);
#4016=LINE('',#4743,#4744);
#4017=VERTEX_POINT('',#4745);
#4018=LINE('',#4746,#4747);
#4019=VERTEX_POINT('',#4748);
#4020=LINE('',#4749,#4750);
#4021=LINE('',#4751,#4752);
#4022=FILL_AREA_STYLE_COLOUR('',#4753);
#4023=LINE('',#4754,#4755);
#4024=LINE('',#4756,#4757);
#4025=FILL_AREA_STYLE_COLOUR('',#4758);
#4026=LINE('',#4759,#4760);
#4027=FILL_AREA_STYLE_COLOUR('',#4761);
#4028=LINE('',#4762,#4763);
#4029=FILL_AREA_STYLE_COLOUR('',#4764);
#4030=FILL_AREA_STYLE_COLOUR('',#4765);
#4031=FILL_AREA_STYLE_COLOUR('',#4766);
#4032=VERTEX_POINT('',#4767);
#4033=VERTEX_POINT('',#4768);
#4034=LINE('',#4769,#4770);
#4035=VERTEX_POINT('',#4771);
#4036=LINE('',#4772,#4773);
#4037=VERTEX_POINT('',#4774);
#4038=LINE('',#4775,#4776);
#4039=LINE('',#4777,#4778);
#4040=FILL_AREA_STYLE_COLOUR('',#4779);
#4041=VERTEX_POINT('',#4780);
#4042=VERTEX_POINT('',#4781);
#4043=LINE('',#4782,#4783);
#4044=VERTEX_POINT('',#4784);
#4045=LINE('',#4785,#4786);
#4046=VERTEX_POINT('',#4787);
#4047=LINE('',#4788,#4789);
#4048=LINE('',#4790,#4791);
#4049=FILL_AREA_STYLE_COLOUR('',#4792);
#4050=LINE('',#4793,#4794);
#4051=LINE('',#4795,#4796);
#4052=FILL_AREA_STYLE_COLOUR('',#4797);
#4053=LINE('',#4798,#4799);
#4054=FILL_AREA_STYLE_COLOUR('',#4800);
#4055=LINE('',#4801,#4802);
#4056=FILL_AREA_STYLE_COLOUR('',#4803);
#4057=FILL_AREA_STYLE_COLOUR('',#4804);
#4058=FILL_AREA_STYLE_COLOUR('',#4805);
#4059=VERTEX_POINT('',#4806);
#4060=VERTEX_POINT('',#4807);
#4061=LINE('',#4808,#4809);
#4062=VERTEX_POINT('',#4810);
#4063=LINE('',#4811,#4812);
#4064=VERTEX_POINT('',#4813);
#4065=LINE('',#4814,#4815);
#4066=LINE('',#4816,#4817);
#4067=FILL_AREA_STYLE_COLOUR('',#4818);
#4068=VERTEX_POINT('',#4819);
#4069=VERTEX_POINT('',#4820);
#4070=LINE('',#4821,#4822);
#4071=VERTEX_POINT('',#4823);
#4072=LINE('',#4824,#4825);
#4073=VERTEX_POINT('',#4826);
#4074=LINE('',#4827,#4828);
#4075=LINE('',#4829,#4830);
#4076=FILL_AREA_STYLE_COLOUR('',#4831);
#4077=LINE('',#4832,#4833);
#4078=LINE('',#4834,#4835);
#4079=FILL_AREA_STYLE_COLOUR('',#4836);
#4080=LINE('',#4837,#4838);
#4081=FILL_AREA_STYLE_COLOUR('',#4839);
#4082=LINE('',#4840,#4841);
#4083=FILL_AREA_STYLE_COLOUR('',#4842);
#4084=FILL_AREA_STYLE_COLOUR('',#4843);
#4085=FILL_AREA_STYLE_COLOUR('',#4844);
#4086=VERTEX_POINT('',#4845);
#4087=VERTEX_POINT('',#4846);
#4088=LINE('',#4847,#4848);
#4089=VERTEX_POINT('',#4849);
#4090=LINE('',#4850,#4851);
#4091=VERTEX_POINT('',#4852);
#4092=LINE('',#4853,#4854);
#4093=LINE('',#4855,#4856);
#4094=FILL_AREA_STYLE_COLOUR('',#4857);
#4095=VERTEX_POINT('',#4858);
#4096=VERTEX_POINT('',#4859);
#4097=LINE('',#4860,#4861);
#4098=VERTEX_POINT('',#4862);
#4099=LINE('',#4863,#4864);
#4100=VERTEX_POINT('',#4865);
#4101=LINE('',#4866,#4867);
#4102=LINE('',#4868,#4869);
#4103=FILL_AREA_STYLE_COLOUR('',#4870);
#4104=LINE('',#4871,#4872);
#4105=LINE('',#4873,#4874);
#4106=FILL_AREA_STYLE_COLOUR('',#4875);
#4107=LINE('',#4876,#4877);
#4108=FILL_AREA_STYLE_COLOUR('',#4878);
#4109=LINE('',#4879,#4880);
#4110=FILL_AREA_STYLE_COLOUR('',#4881);
#4111=FILL_AREA_STYLE_COLOUR('',#4882);
#4112=FILL_AREA_STYLE_COLOUR('',#4883);
#4113=VERTEX_POINT('',#4884);
#4114=VERTEX_POINT('',#4885);
#4115=LINE('',#4886,#4887);
#4116=VERTEX_POINT('',#4888);
#4117=LINE('',#4889,#4890);
#4118=VERTEX_POINT('',#4891);
#4119=LINE('',#4892,#4893);
#4120=LINE('',#4894,#4895);
#4121=FILL_AREA_STYLE_COLOUR('',#4896);
#4122=VERTEX_POINT('',#4897);
#4123=VERTEX_POINT('',#4898);
#4124=LINE('',#4899,#4900);
#4125=VERTEX_POINT('',#4901);
#4126=LINE('',#4902,#4903);
#4127=VERTEX_POINT('',#4904);
#4128=LINE('',#4905,#4906);
#4129=LINE('',#4907,#4908);
#4130=FILL_AREA_STYLE_COLOUR('',#4909);
#4131=LINE('',#4910,#4911);
#4132=LINE('',#4912,#4913);
#4133=FILL_AREA_STYLE_COLOUR('',#4914);
#4134=LINE('',#4915,#4916);
#4135=FILL_AREA_STYLE_COLOUR('',#4917);
#4136=LINE('',#4918,#4919);
#4137=FILL_AREA_STYLE_COLOUR('',#4920);
#4138=FILL_AREA_STYLE_COLOUR('',#4921);
#4139=FILL_AREA_STYLE_COLOUR('',#4922);
#4140=VERTEX_POINT('',#4923);
#4141=VERTEX_POINT('',#4924);
#4142=LINE('',#4925,#4926);
#4143=VERTEX_POINT('',#4927);
#4144=LINE('',#4928,#4929);
#4145=VERTEX_POINT('',#4930);
#4146=LINE('',#4931,#4932);
#4147=LINE('',#4933,#4934);
#4148=FILL_AREA_STYLE_COLOUR('',#4935);
#4149=VERTEX_POINT('',#4936);
#4150=VERTEX_POINT('',#4937);
#4151=LINE('',#4938,#4939);
#4152=VERTEX_POINT('',#4940);
#4153=LINE('',#4941,#4942);
#4154=VERTEX_POINT('',#4943);
#4155=LINE('',#4944,#4945);
#4156=LINE('',#4946,#4947);
#4157=FILL_AREA_STYLE_COLOUR('',#4948);
#4158=LINE('',#4949,#4950);
#4159=LINE('',#4951,#4952);
#4160=FILL_AREA_STYLE_COLOUR('',#4953);
#4161=LINE('',#4954,#4955);
#4162=FILL_AREA_STYLE_COLOUR('',#4956);
#4163=LINE('',#4957,#4958);
#4164=FILL_AREA_STYLE_COLOUR('',#4959);
#4165=FILL_AREA_STYLE_COLOUR('',#4960);
#4166=FILL_AREA_STYLE_COLOUR('',#4961);
#4167=VERTEX_POINT('',#4962);
#4168=VERTEX_POINT('',#4963);
#4169=LINE('',#4964,#4965);
#4170=VERTEX_POINT('',#4966);
#4171=LINE('',#4967,#4968);
#4172=VERTEX_POINT('',#4969);
#4173=LINE('',#4970,#4971);
#4174=LINE('',#4972,#4973);
#4175=FILL_AREA_STYLE_COLOUR('',#4974);
#4176=VERTEX_POINT('',#4975);
#4177=VERTEX_POINT('',#4976);
#4178=LINE('',#4977,#4978);
#4179=VERTEX_POINT('',#4979);
#4180=LINE('',#4980,#4981);
#4181=VERTEX_POINT('',#4982);
#4182=LINE('',#4983,#4984);
#4183=LINE('',#4985,#4986);
#4184=FILL_AREA_STYLE_COLOUR('',#4987);
#4185=LINE('',#4988,#4989);
#4186=LINE('',#4990,#4991);
#4187=FILL_AREA_STYLE_COLOUR('',#4992);
#4188=LINE('',#4993,#4994);
#4189=FILL_AREA_STYLE_COLOUR('',#4995);
#4190=LINE('',#4996,#4997);
#4191=FILL_AREA_STYLE_COLOUR('',#4998);
#4192=FILL_AREA_STYLE_COLOUR('',#4999);
#4193=FILL_AREA_STYLE_COLOUR('',#5000);
#4194=VERTEX_POINT('',#5001);
#4195=VERTEX_POINT('',#5002);
#4196=LINE('',#5003,#5004);
#4197=VERTEX_POINT('',#5005);
#4198=LINE('',#5006,#5007);
#4199=VERTEX_POINT('',#5008);
#4200=LINE('',#5009,#5010);
#4201=LINE('',#5011,#5012);
#4202=FILL_AREA_STYLE_COLOUR('',#5013);
#4203=VERTEX_POINT('',#5014);
#4204=VERTEX_POINT('',#5015);
#4205=LINE('',#5016,#5017);
#4206=VERTEX_POINT('',#5018);
#4207=LINE('',#5019,#5020);
#4208=VERTEX_POINT('',#5021);
#4209=LINE('',#5022,#5023);
#4210=LINE('',#5024,#5025);
#4211=FILL_AREA_STYLE_COLOUR('',#5026);
#4212=LINE('',#5027,#5028);
#4213=LINE('',#5029,#5030);
#4214=FILL_AREA_STYLE_COLOUR('',#5031);
#4215=LINE('',#5032,#5033);
#4216=FILL_AREA_STYLE_COLOUR('',#5034);
#4217=LINE('',#5035,#5036);
#4218=FILL_AREA_STYLE_COLOUR('',#5037);
#4219=FILL_AREA_STYLE_COLOUR('',#5038);
#4220=FILL_AREA_STYLE_COLOUR('',#5039);
#4221=VERTEX_POINT('',#5040);
#4222=VERTEX_POINT('',#5041);
#4223=LINE('',#5042,#5043);
#4224=VERTEX_POINT('',#5044);
#4225=LINE('',#5045,#5046);
#4226=VERTEX_POINT('',#5047);
#4227=LINE('',#5048,#5049);
#4228=LINE('',#5050,#5051);
#4229=FILL_AREA_STYLE_COLOUR('',#5052);
#4230=VERTEX_POINT('',#5053);
#4231=VERTEX_POINT('',#5054);
#4232=LINE('',#5055,#5056);
#4233=VERTEX_POINT('',#5057);
#4234=LINE('',#5058,#5059);
#4235=VERTEX_POINT('',#5060);
#4236=LINE('',#5061,#5062);
#4237=LINE('',#5063,#5064);
#4238=FILL_AREA_STYLE_COLOUR('',#5065);
#4239=LINE('',#5066,#5067);
#4240=LINE('',#5068,#5069);
#4241=FILL_AREA_STYLE_COLOUR('',#5070);
#4242=LINE('',#5071,#5072);
#4243=FILL_AREA_STYLE_COLOUR('',#5073);
#4244=LINE('',#5074,#5075);
#4245=FILL_AREA_STYLE_COLOUR('',#5076);
#4246=FILL_AREA_STYLE_COLOUR('',#5077);
#4247=FILL_AREA_STYLE_COLOUR('',#5078);
#4248=VERTEX_POINT('',#5079);
#4249=VERTEX_POINT('',#5080);
#4250=LINE('',#5081,#5082);
#4251=VERTEX_POINT('',#5083);
#4252=LINE('',#5084,#5085);
#4253=VERTEX_POINT('',#5086);
#4254=LINE('',#5087,#5088);
#4255=LINE('',#5089,#5090);
#4256=FILL_AREA_STYLE_COLOUR('',#5091);
#4257=VERTEX_POINT('',#5092);
#4258=VERTEX_POINT('',#5093);
#4259=LINE('',#5094,#5095);
#4260=VERTEX_POINT('',#5096);
#4261=LINE('',#5097,#5098);
#4262=VERTEX_POINT('',#5099);
#4263=LINE('',#5100,#5101);
#4264=LINE('',#5102,#5103);
#4265=FILL_AREA_STYLE_COLOUR('',#5104);
#4266=LINE('',#5105,#5106);
#4267=LINE('',#5107,#5108);
#4268=FILL_AREA_STYLE_COLOUR('',#5109);
#4269=LINE('',#5110,#5111);
#4270=FILL_AREA_STYLE_COLOUR('',#5112);
#4271=LINE('',#5113,#5114);
#4272=FILL_AREA_STYLE_COLOUR('',#5115);
#4273=FILL_AREA_STYLE_COLOUR('',#5116);
#4274=FILL_AREA_STYLE_COLOUR('',#5117);
#4275=VERTEX_POINT('',#5118);
#4276=VERTEX_POINT('',#5119);
#4277=LINE('',#5120,#5121);
#4278=VERTEX_POINT('',#5122);
#4279=LINE('',#5123,#5124);
#4280=VERTEX_POINT('',#5125);
#4281=LINE('',#5126,#5127);
#4282=LINE('',#5128,#5129);
#4283=FILL_AREA_STYLE_COLOUR('',#5130);
#4284=VERTEX_POINT('',#5131);
#4285=VERTEX_POINT('',#5132);
#4286=LINE('',#5133,#5134);
#4287=VERTEX_POINT('',#5135);
#4288=LINE('',#5136,#5137);
#4289=VERTEX_POINT('',#5138);
#4290=LINE('',#5139,#5140);
#4291=LINE('',#5141,#5142);
#4292=FILL_AREA_STYLE_COLOUR('',#5143);
#4293=LINE('',#5144,#5145);
#4294=LINE('',#5146,#5147);
#4295=FILL_AREA_STYLE_COLOUR('',#5148);
#4296=LINE('',#5149,#5150);
#4297=FILL_AREA_STYLE_COLOUR('',#5151);
#4298=LINE('',#5152,#5153);
#4299=FILL_AREA_STYLE_COLOUR('',#5154);
#4300=FILL_AREA_STYLE_COLOUR('',#5155);
#4301=FILL_AREA_STYLE_COLOUR('',#5156);
#4302=VERTEX_POINT('',#5157);
#4303=VERTEX_POINT('',#5158);
#4304=LINE('',#5159,#5160);
#4305=VERTEX_POINT('',#5161);
#4306=CIRCLE('',#5162,0.253999887089593);
#4307=VERTEX_POINT('',#5163);
#4308=LINE('',#5164,#5165);
#4309=CIRCLE('',#5166,0.253999887089593);
#4310=FILL_AREA_STYLE_COLOUR('',#5167);
#4311=CIRCLE('',#5168,0.253999887089593);
#4312=FILL_AREA_STYLE_COLOUR('',#5169);
#4313=CIRCLE('',#5170,0.253999887089593);
#4314=FILL_AREA_STYLE_COLOUR('',#5171);
#4315=FILL_AREA_STYLE_COLOUR('',#5172);
#4316=FILL_AREA_STYLE_COLOUR('',#5173);
#4317=VERTEX_POINT('',#5174);
#4318=VERTEX_POINT('',#5175);
#4319=CIRCLE('',#5176,0.0635000610358896);
#4320=VERTEX_POINT('',#5177);
#4321=LINE('',#5178,#5179);
#4322=VERTEX_POINT('',#5180);
#4323=CIRCLE('',#5181,0.0635000610358896);
#4324=LINE('',#5182,#5183);
#4325=FILL_AREA_STYLE_COLOUR('',#5184);
#4326=VERTEX_POINT('',#5185);
#4327=VERTEX_POINT('',#5186);
#4328=CIRCLE('',#5187,0.0635000610358896);
#4329=VERTEX_POINT('',#5188);
#4330=LINE('',#5189,#5190);
#4331=VERTEX_POINT('',#5191);
#4332=CIRCLE('',#5192,0.0635000610358896);
#4333=LINE('',#5193,#5194);
#4334=FILL_AREA_STYLE_COLOUR('',#5195);
#4335=LINE('',#5196,#5197);
#4336=LINE('',#5198,#5199);
#4337=FILL_AREA_STYLE_COLOUR('',#5200);
#4338=LINE('',#5201,#5202);
#4339=FILL_AREA_STYLE_COLOUR('',#5203);
#4340=LINE('',#5204,#5205);
#4341=FILL_AREA_STYLE_COLOUR('',#5206);
#4342=FILL_AREA_STYLE_COLOUR('',#5207);
#4343=FILL_AREA_STYLE_COLOUR('',#5208);
#4344=VERTEX_POINT('',#5209);
#4345=VERTEX_POINT('',#5210);
#4346=CIRCLE('',#5211,0.0635000610351562);
#4347=VERTEX_POINT('',#5212);
#4348=LINE('',#5213,#5214);
#4349=VERTEX_POINT('',#5215);
#4350=CIRCLE('',#5216,0.0635000610351562);
#4351=LINE('',#5217,#5218);
#4352=FILL_AREA_STYLE_COLOUR('',#5219);
#4353=VERTEX_POINT('',#5220);
#4354=VERTEX_POINT('',#5221);
#4355=CIRCLE('',#5222,0.0635000610351562);
#4356=VERTEX_POINT('',#5223);
#4357=LINE('',#5224,#5225);
#4358=VERTEX_POINT('',#5226);
#4359=CIRCLE('',#5227,0.0635000610351562);
#4360=LINE('',#5228,#5229);
#4361=FILL_AREA_STYLE_COLOUR('',#5230);
#4362=LINE('',#5231,#5232);
#4363=LINE('',#5233,#5234);
#4364=FILL_AREA_STYLE_COLOUR('',#5235);
#4365=LINE('',#5236,#5237);
#4366=FILL_AREA_STYLE_COLOUR('',#5238);
#4367=LINE('',#5239,#5240);
#4368=FILL_AREA_STYLE_COLOUR('',#5241);
#4369=FILL_AREA_STYLE_COLOUR('',#5242);
#4370=FILL_AREA_STYLE_COLOUR('',#5243);
#4371=VERTEX_POINT('',#5244);
#4372=VERTEX_POINT('',#5245);
#4373=CIRCLE('',#5246,0.0635000610358896);
#4374=VERTEX_POINT('',#5247);
#4375=LINE('',#5248,#5249);
#4376=VERTEX_POINT('',#5250);
#4377=CIRCLE('',#5251,0.0635000610358896);
#4378=LINE('',#5252,#5253);
#4379=FILL_AREA_STYLE_COLOUR('',#5254);
#4380=VERTEX_POINT('',#5255);
#4381=VERTEX_POINT('',#5256);
#4382=CIRCLE('',#5257,0.0635000610358896);
#4383=VERTEX_POINT('',#5258);
#4384=LINE('',#5259,#5260);
#4385=VERTEX_POINT('',#5261);
#4386=CIRCLE('',#5262,0.0635000610358896);
#4387=LINE('',#5263,#5264);
#4388=FILL_AREA_STYLE_COLOUR('',#5265);
#4389=LINE('',#5266,#5267);
#4390=LINE('',#5268,#5269);
#4391=FILL_AREA_STYLE_COLOUR('',#5270);
#4392=LINE('',#5271,#5272);
#4393=FILL_AREA_STYLE_COLOUR('',#5273);
#4394=LINE('',#5274,#5275);
#4395=FILL_AREA_STYLE_COLOUR('',#5276);
#4396=FILL_AREA_STYLE_COLOUR('',#5277);
#4397=FILL_AREA_STYLE_COLOUR('',#5278);
#4398=VERTEX_POINT('',#5279);
#4399=VERTEX_POINT('',#5280);
#4400=CIRCLE('',#5281,0.0635000610358896);
#4401=VERTEX_POINT('',#5282);
#4402=LINE('',#5283,#5284);
#4403=VERTEX_POINT('',#5285);
#4404=CIRCLE('',#5286,0.0635000610358896);
#4405=LINE('',#5287,#5288);
#4406=FILL_AREA_STYLE_COLOUR('',#5289);
#4407=VERTEX_POINT('',#5290);
#4408=VERTEX_POINT('',#5291);
#4409=CIRCLE('',#5292,0.0635000610358896);
#4410=VERTEX_POINT('',#5293);
#4411=LINE('',#5294,#5295);
#4412=VERTEX_POINT('',#5296);
#4413=CIRCLE('',#5297,0.0635000610358896);
#4414=LINE('',#5298,#5299);
#4415=FILL_AREA_STYLE_COLOUR('',#5300);
#4416=LINE('',#5301,#5302);
#4417=LINE('',#5303,#5304);
#4418=FILL_AREA_STYLE_COLOUR('',#5305);
#4419=LINE('',#5306,#5307);
#4420=FILL_AREA_STYLE_COLOUR('',#5308);
#4421=LINE('',#5309,#5310);
#4422=FILL_AREA_STYLE_COLOUR('',#5311);
#4423=FILL_AREA_STYLE_COLOUR('',#5312);
#4424=FILL_AREA_STYLE_COLOUR('',#5313);
#4425=VERTEX_POINT('',#5314);
#4426=VERTEX_POINT('',#5315);
#4427=CIRCLE('',#5316,0.0635000610351562);
#4428=VERTEX_POINT('',#5317);
#4429=LINE('',#5318,#5319);
#4430=VERTEX_POINT('',#5320);
#4431=CIRCLE('',#5321,0.0635000610351562);
#4432=LINE('',#5322,#5323);
#4433=FILL_AREA_STYLE_COLOUR('',#5324);
#4434=VERTEX_POINT('',#5325);
#4435=VERTEX_POINT('',#5326);
#4436=CIRCLE('',#5327,0.0635000610351562);
#4437=VERTEX_POINT('',#5328);
#4438=LINE('',#5329,#5330);
#4439=VERTEX_POINT('',#5331);
#4440=CIRCLE('',#5332,0.0635000610351562);
#4441=LINE('',#5333,#5334);
#4442=FILL_AREA_STYLE_COLOUR('',#5335);
#4443=LINE('',#5336,#5337);
#4444=LINE('',#5338,#5339);
#4445=FILL_AREA_STYLE_COLOUR('',#5340);
#4446=LINE('',#5341,#5342);
#4447=FILL_AREA_STYLE_COLOUR('',#5343);
#4448=LINE('',#5344,#5345);
#4449=FILL_AREA_STYLE_COLOUR('',#5346);
#4450=FILL_AREA_STYLE_COLOUR('',#5347);
#4451=FILL_AREA_STYLE_COLOUR('',#5348);
#4452=VERTEX_POINT('',#5349);
#4453=VERTEX_POINT('',#5350);
#4454=CIRCLE('',#5351,0.0635000610358896);
#4455=VERTEX_POINT('',#5352);
#4456=LINE('',#5353,#5354);
#4457=VERTEX_POINT('',#5355);
#4458=CIRCLE('',#5356,0.0635000610358896);
#4459=LINE('',#5357,#5358);
#4460=FILL_AREA_STYLE_COLOUR('',#5359);
#4461=VERTEX_POINT('',#5360);
#4462=VERTEX_POINT('',#5361);
#4463=CIRCLE('',#5362,0.0635000610358896);
#4464=VERTEX_POINT('',#5363);
#4465=LINE('',#5364,#5365);
#4466=VERTEX_POINT('',#5366);
#4467=CIRCLE('',#5367,0.0635000610358896);
#4468=LINE('',#5368,#5369);
#4469=FILL_AREA_STYLE_COLOUR('',#5370);
#4470=LINE('',#5371,#5372);
#4471=LINE('',#5373,#5374);
#4472=FILL_AREA_STYLE_COLOUR('',#5375);
#4473=LINE('',#5376,#5377);
#4474=FILL_AREA_STYLE_COLOUR('',#5378);
#4475=LINE('',#5379,#5380);
#4476=FILL_AREA_STYLE_COLOUR('',#5381);
#4477=FILL_AREA_STYLE_COLOUR('',#5382);
#4478=FILL_AREA_STYLE_COLOUR('',#5383);
#4479=VERTEX_POINT('',#5384);
#4480=VERTEX_POINT('',#5385);
#4481=CIRCLE('',#5386,0.0635000610351562);
#4482=VERTEX_POINT('',#5387);
#4483=LINE('',#5388,#5389);
#4484=VERTEX_POINT('',#5390);
#4485=CIRCLE('',#5391,0.0635000610358896);
#4486=LINE('',#5392,#5393);
#4487=FILL_AREA_STYLE_COLOUR('',#5394);
#4488=VERTEX_POINT('',#5395);
#4489=VERTEX_POINT('',#5396);
#4490=CIRCLE('',#5397,0.0635000610351562);
#4491=VERTEX_POINT('',#5398);
#4492=LINE('',#5399,#5400);
#4493=VERTEX_POINT('',#5401);
#4494=CIRCLE('',#5402,0.0635000610358896);
#4495=LINE('',#5403,#5404);
#4496=FILL_AREA_STYLE_COLOUR('',#5405);
#4497=LINE('',#5406,#5407);
#4498=LINE('',#5408,#5409);
#4499=FILL_AREA_STYLE_COLOUR('',#5410);
#4500=LINE('',#5411,#5412);
#4501=FILL_AREA_STYLE_COLOUR('',#5413);
#4502=LINE('',#5414,#5415);
#4503=FILL_AREA_STYLE_COLOUR('',#5416);
#4504=FILL_AREA_STYLE_COLOUR('',#5417);
#4505=FILL_AREA_STYLE_COLOUR('',#5418);
#4506=VERTEX_POINT('',#5419);
#4507=VERTEX_POINT('',#5420);
#4508=LINE('',#5421,#5422);
#4509=VERTEX_POINT('',#5423);
#4510=LINE('',#5424,#5425);
#4511=VERTEX_POINT('',#5426);
#4512=LINE('',#5427,#5428);
#4513=LINE('',#5429,#5430);
#4514=FILL_AREA_STYLE_COLOUR('',#5431);
#4515=VERTEX_POINT('',#5432);
#4516=VERTEX_POINT('',#5433);
#4517=LINE('',#5434,#5435);
#4518=VERTEX_POINT('',#5436);
#4519=LINE('',#5437,#5438);
#4520=VERTEX_POINT('',#5439);
#4521=LINE('',#5440,#5441);
#4522=LINE('',#5442,#5443);
#4523=FILL_AREA_STYLE_COLOUR('',#5444);
#4524=LINE('',#5445,#5446);
#4525=LINE('',#5447,#5448);
#4526=FILL_AREA_STYLE_COLOUR('',#5449);
#4527=LINE('',#5450,#5451);
#4528=FILL_AREA_STYLE_COLOUR('',#5452);
#4529=LINE('',#5453,#5454);
#4530=FILL_AREA_STYLE_COLOUR('',#5455);
#4531=FILL_AREA_STYLE_COLOUR('',#5456);
#4532=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4533=CARTESIAN_POINT('',(-9.255,-3.405,0.2895600001));
#4534=CARTESIAN_POINT('',(-8.525,-3.405,0.2895600001));
#4535=CARTESIAN_POINT('',(-8.89,-3.405,0.2895600001));
#4536=VECTOR('',#5457,0.01);
#4537=CARTESIAN_POINT('',(-9.255,-5.375,0.2895600001));
#4538=CARTESIAN_POINT('',(-9.255,-4.39,0.2895600001));
#4539=VECTOR('',#5458,0.01);
#4540=CARTESIAN_POINT('',(-8.525,-5.375,0.2895600001));
#4541=CARTESIAN_POINT('',(-8.89,-5.375,0.2895600001));
#4542=VECTOR('',#5459,0.01);
#4543=CARTESIAN_POINT('',(-8.525,-4.39,0.2895600001));
#4544=VECTOR('',#5460,0.01);
#4545=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4546=CARTESIAN_POINT('',(-9.255,-3.405,0.2590800001));
#4547=CARTESIAN_POINT('',(-8.525,-3.405,0.2590800001));
#4548=CARTESIAN_POINT('',(-9.255,-3.405,0.2590800001));
#4549=VECTOR('',#5461,0.01);
#4550=CARTESIAN_POINT('',(-8.525,-5.375,0.2590800001));
#4551=CARTESIAN_POINT('',(-8.525,-3.405,0.2590800001));
#4552=VECTOR('',#5462,0.01);
#4553=CARTESIAN_POINT('',(-9.255,-5.375,0.2590800001));
#4554=CARTESIAN_POINT('',(-8.525,-5.375,0.2590800001));
#4555=VECTOR('',#5463,0.01);
#4556=CARTESIAN_POINT('',(-9.255,-5.375,0.2590800001));
#4557=VECTOR('',#5464,0.01);
#4558=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4559=CARTESIAN_POINT('',(-8.525,-5.375,0.2743200001));
#4560=VECTOR('',#5465,0.01);
#4561=CARTESIAN_POINT('',(-8.525,-3.405,0.2743200001));
#4562=VECTOR('',#5466,0.01);
#4563=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4564=CARTESIAN_POINT('',(-9.255,-5.375,0.2743200001));
#4565=VECTOR('',#5467,0.01);
#4566=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4567=CARTESIAN_POINT('',(-9.255,-3.405,0.2743200001));
#4568=VECTOR('',#5468,0.01);
#4569=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4570=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4571=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4572=CARTESIAN_POINT('',(-6.715,-3.405,0.2895600001));
#4573=CARTESIAN_POINT('',(-5.985,-3.405,0.2895600001));
#4574=CARTESIAN_POINT('',(-6.35,-3.405,0.2895600001));
#4575=VECTOR('',#5469,0.01);
#4576=CARTESIAN_POINT('',(-6.715,-5.375,0.2895600001));
#4577=CARTESIAN_POINT('',(-6.715,-4.39,0.2895600001));
#4578=VECTOR('',#5470,0.01);
#4579=CARTESIAN_POINT('',(-5.985,-5.375,0.2895600001));
#4580=CARTESIAN_POINT('',(-6.35,-5.375,0.2895600001));
#4581=VECTOR('',#5471,0.01);
#4582=CARTESIAN_POINT('',(-5.985,-4.39,0.2895600001));
#4583=VECTOR('',#5472,0.01);
#4584=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4585=CARTESIAN_POINT('',(-6.715,-3.405,0.2590800001));
#4586=CARTESIAN_POINT('',(-5.985,-3.405,0.2590800001));
#4587=CARTESIAN_POINT('',(-6.715,-3.405,0.2590800001));
#4588=VECTOR('',#5473,0.01);
#4589=CARTESIAN_POINT('',(-5.985,-5.375,0.2590800001));
#4590=CARTESIAN_POINT('',(-5.985,-3.405,0.2590800001));
#4591=VECTOR('',#5474,0.01);
#4592=CARTESIAN_POINT('',(-6.715,-5.375,0.2590800001));
#4593=CARTESIAN_POINT('',(-5.985,-5.375,0.2590800001));
#4594=VECTOR('',#5475,0.01);
#4595=CARTESIAN_POINT('',(-6.715,-5.375,0.2590800001));
#4596=VECTOR('',#5476,0.01);
#4597=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4598=CARTESIAN_POINT('',(-5.985,-5.375,0.2743200001));
#4599=VECTOR('',#5477,0.01);
#4600=CARTESIAN_POINT('',(-5.985,-3.405,0.2743200001));
#4601=VECTOR('',#5478,0.01);
#4602=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4603=CARTESIAN_POINT('',(-6.715,-5.375,0.2743200001));
#4604=VECTOR('',#5479,0.01);
#4605=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4606=CARTESIAN_POINT('',(-6.715,-3.405,0.2743200001));
#4607=VECTOR('',#5480,0.01);
#4608=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4609=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4610=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4611=CARTESIAN_POINT('',(-4.175,-3.405,0.2895600001));
#4612=CARTESIAN_POINT('',(-3.445,-3.405,0.2895600001));
#4613=CARTESIAN_POINT('',(-3.81,-3.405,0.2895600001));
#4614=VECTOR('',#5481,0.01);
#4615=CARTESIAN_POINT('',(-4.175,-5.375,0.2895600001));
#4616=CARTESIAN_POINT('',(-4.175,-4.39,0.2895600001));
#4617=VECTOR('',#5482,0.01);
#4618=CARTESIAN_POINT('',(-3.445,-5.375,0.2895600001));
#4619=CARTESIAN_POINT('',(-3.81,-5.375,0.2895600001));
#4620=VECTOR('',#5483,0.01);
#4621=CARTESIAN_POINT('',(-3.445,-4.39,0.2895600001));
#4622=VECTOR('',#5484,0.01);
#4623=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4624=CARTESIAN_POINT('',(-4.175,-3.405,0.2590800001));
#4625=CARTESIAN_POINT('',(-3.445,-3.405,0.2590800001));
#4626=CARTESIAN_POINT('',(-4.175,-3.405,0.2590800001));
#4627=VECTOR('',#5485,0.01);
#4628=CARTESIAN_POINT('',(-3.445,-5.375,0.2590800001));
#4629=CARTESIAN_POINT('',(-3.445,-3.405,0.2590800001));
#4630=VECTOR('',#5486,0.01);
#4631=CARTESIAN_POINT('',(-4.175,-5.375,0.2590800001));
#4632=CARTESIAN_POINT('',(-3.445,-5.375,0.2590800001));
#4633=VECTOR('',#5487,0.01);
#4634=CARTESIAN_POINT('',(-4.175,-5.375,0.2590800001));
#4635=VECTOR('',#5488,0.01);
#4636=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4637=CARTESIAN_POINT('',(-3.445,-5.375,0.2743200001));
#4638=VECTOR('',#5489,0.01);
#4639=CARTESIAN_POINT('',(-3.445,-3.405,0.2743200001));
#4640=VECTOR('',#5490,0.01);
#4641=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4642=CARTESIAN_POINT('',(-4.175,-5.375,0.2743200001));
#4643=VECTOR('',#5491,0.01);
#4644=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4645=CARTESIAN_POINT('',(-4.175,-3.405,0.2743200001));
#4646=VECTOR('',#5492,0.01);
#4647=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4648=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4649=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4650=CARTESIAN_POINT('',(-1.635,-3.405,0.2895600001));
#4651=CARTESIAN_POINT('',(-0.905,-3.405,0.2895600001));
#4652=CARTESIAN_POINT('',(-1.27,-3.405,0.2895600001));
#4653=VECTOR('',#5493,0.01);
#4654=CARTESIAN_POINT('',(-1.635,-5.375,0.2895600001));
#4655=CARTESIAN_POINT('',(-1.635,-4.39,0.2895600001));
#4656=VECTOR('',#5494,0.01);
#4657=CARTESIAN_POINT('',(-0.905,-5.375,0.2895600001));
#4658=CARTESIAN_POINT('',(-1.27,-5.375,0.2895600001));
#4659=VECTOR('',#5495,0.01);
#4660=CARTESIAN_POINT('',(-0.905,-4.39,0.2895600001));
#4661=VECTOR('',#5496,0.01);
#4662=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4663=CARTESIAN_POINT('',(-1.635,-3.405,0.2590800001));
#4664=CARTESIAN_POINT('',(-0.905,-3.405,0.2590800001));
#4665=CARTESIAN_POINT('',(-1.635,-3.405,0.2590800001));
#4666=VECTOR('',#5497,0.01);
#4667=CARTESIAN_POINT('',(-0.905,-5.375,0.2590800001));
#4668=CARTESIAN_POINT('',(-0.905,-3.405,0.2590800001));
#4669=VECTOR('',#5498,0.01);
#4670=CARTESIAN_POINT('',(-1.635,-5.375,0.2590800001));
#4671=CARTESIAN_POINT('',(-0.905,-5.375,0.2590800001));
#4672=VECTOR('',#5499,0.01);
#4673=CARTESIAN_POINT('',(-1.635,-5.375,0.2590800001));
#4674=VECTOR('',#5500,0.01);
#4675=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4676=CARTESIAN_POINT('',(-0.905,-5.375,0.2743200001));
#4677=VECTOR('',#5501,0.01);
#4678=CARTESIAN_POINT('',(-0.905,-3.405,0.2743200001));
#4679=VECTOR('',#5502,0.01);
#4680=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4681=CARTESIAN_POINT('',(-1.635,-5.375,0.2743200001));
#4682=VECTOR('',#5503,0.01);
#4683=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4684=CARTESIAN_POINT('',(-1.635,-3.405,0.2743200001));
#4685=VECTOR('',#5504,0.01);
#4686=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4687=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4688=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4689=CARTESIAN_POINT('',(0.905,-3.405,0.2895600001));
#4690=CARTESIAN_POINT('',(1.635,-3.405,0.2895600001));
#4691=CARTESIAN_POINT('',(1.27,-3.405,0.2895600001));
#4692=VECTOR('',#5505,0.01);
#4693=CARTESIAN_POINT('',(0.905,-5.375,0.2895600001));
#4694=CARTESIAN_POINT('',(0.905,-4.39,0.2895600001));
#4695=VECTOR('',#5506,0.01);
#4696=CARTESIAN_POINT('',(1.635,-5.375,0.2895600001));
#4697=CARTESIAN_POINT('',(1.27,-5.375,0.2895600001));
#4698=VECTOR('',#5507,0.01);
#4699=CARTESIAN_POINT('',(1.635,-4.39,0.2895600001));
#4700=VECTOR('',#5508,0.01);
#4701=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4702=CARTESIAN_POINT('',(0.905,-3.405,0.2590800001));
#4703=CARTESIAN_POINT('',(1.635,-3.405,0.2590800001));
#4704=CARTESIAN_POINT('',(0.905,-3.405,0.2590800001));
#4705=VECTOR('',#5509,0.01);
#4706=CARTESIAN_POINT('',(1.635,-5.375,0.2590800001));
#4707=CARTESIAN_POINT('',(1.635,-3.405,0.2590800001));
#4708=VECTOR('',#5510,0.01);
#4709=CARTESIAN_POINT('',(0.905,-5.375,0.2590800001));
#4710=CARTESIAN_POINT('',(1.635,-5.375,0.2590800001));
#4711=VECTOR('',#5511,0.01);
#4712=CARTESIAN_POINT('',(0.905,-5.375,0.2590800001));
#4713=VECTOR('',#5512,0.01);
#4714=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4715=CARTESIAN_POINT('',(1.635,-5.375,0.2743200001));
#4716=VECTOR('',#5513,0.01);
#4717=CARTESIAN_POINT('',(1.635,-3.405,0.2743200001));
#4718=VECTOR('',#5514,0.01);
#4719=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4720=CARTESIAN_POINT('',(0.905,-5.375,0.2743200001));
#4721=VECTOR('',#5515,0.01);
#4722=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4723=CARTESIAN_POINT('',(0.905,-3.405,0.2743200001));
#4724=VECTOR('',#5516,0.01);
#4725=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4726=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4727=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4728=CARTESIAN_POINT('',(3.445,-3.405,0.2895600001));
#4729=CARTESIAN_POINT('',(4.175,-3.405,0.2895600001));
#4730=CARTESIAN_POINT('',(3.81,-3.405,0.2895600001));
#4731=VECTOR('',#5517,0.01);
#4732=CARTESIAN_POINT('',(3.445,-5.375,0.2895600001));
#4733=CARTESIAN_POINT('',(3.445,-4.39,0.2895600001));
#4734=VECTOR('',#5518,0.01);
#4735=CARTESIAN_POINT('',(4.175,-5.375,0.2895600001));
#4736=CARTESIAN_POINT('',(3.81,-5.375,0.2895600001));
#4737=VECTOR('',#5519,0.01);
#4738=CARTESIAN_POINT('',(4.175,-4.39,0.2895600001));
#4739=VECTOR('',#5520,0.01);
#4740=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4741=CARTESIAN_POINT('',(3.445,-3.405,0.2590800001));
#4742=CARTESIAN_POINT('',(4.175,-3.405,0.2590800001));
#4743=CARTESIAN_POINT('',(3.445,-3.405,0.2590800001));
#4744=VECTOR('',#5521,0.01);
#4745=CARTESIAN_POINT('',(4.175,-5.375,0.2590800001));
#4746=CARTESIAN_POINT('',(4.175,-3.405,0.2590800001));
#4747=VECTOR('',#5522,0.01);
#4748=CARTESIAN_POINT('',(3.445,-5.375,0.2590800001));
#4749=CARTESIAN_POINT('',(4.175,-5.375,0.2590800001));
#4750=VECTOR('',#5523,0.01);
#4751=CARTESIAN_POINT('',(3.445,-5.375,0.2590800001));
#4752=VECTOR('',#5524,0.01);
#4753=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4754=CARTESIAN_POINT('',(4.175,-5.375,0.2743200001));
#4755=VECTOR('',#5525,0.01);
#4756=CARTESIAN_POINT('',(4.175,-3.405,0.2743200001));
#4757=VECTOR('',#5526,0.01);
#4758=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4759=CARTESIAN_POINT('',(3.445,-5.375,0.2743200001));
#4760=VECTOR('',#5527,0.01);
#4761=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4762=CARTESIAN_POINT('',(3.445,-3.405,0.2743200001));
#4763=VECTOR('',#5528,0.01);
#4764=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4765=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4766=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4767=CARTESIAN_POINT('',(5.985,-3.405,0.2895600001));
#4768=CARTESIAN_POINT('',(6.715,-3.405,0.2895600001));
#4769=CARTESIAN_POINT('',(6.35,-3.405,0.2895600001));
#4770=VECTOR('',#5529,0.01);
#4771=CARTESIAN_POINT('',(5.985,-5.375,0.2895600001));
#4772=CARTESIAN_POINT('',(5.985,-4.39,0.2895600001));
#4773=VECTOR('',#5530,0.01);
#4774=CARTESIAN_POINT('',(6.715,-5.375,0.2895600001));
#4775=CARTESIAN_POINT('',(6.35,-5.375,0.2895600001));
#4776=VECTOR('',#5531,0.01);
#4777=CARTESIAN_POINT('',(6.715,-4.39,0.2895600001));
#4778=VECTOR('',#5532,0.01);
#4779=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4780=CARTESIAN_POINT('',(5.985,-3.405,0.2590800001));
#4781=CARTESIAN_POINT('',(6.715,-3.405,0.2590800001));
#4782=CARTESIAN_POINT('',(5.985,-3.405,0.2590800001));
#4783=VECTOR('',#5533,0.01);
#4784=CARTESIAN_POINT('',(6.715,-5.375,0.2590800001));
#4785=CARTESIAN_POINT('',(6.715,-3.405,0.2590800001));
#4786=VECTOR('',#5534,0.01);
#4787=CARTESIAN_POINT('',(5.985,-5.375,0.2590800001));
#4788=CARTESIAN_POINT('',(6.715,-5.375,0.2590800001));
#4789=VECTOR('',#5535,0.01);
#4790=CARTESIAN_POINT('',(5.985,-5.375,0.2590800001));
#4791=VECTOR('',#5536,0.01);
#4792=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4793=CARTESIAN_POINT('',(6.715,-5.375,0.2743200001));
#4794=VECTOR('',#5537,0.01);
#4795=CARTESIAN_POINT('',(6.715,-3.405,0.2743200001));
#4796=VECTOR('',#5538,0.01);
#4797=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4798=CARTESIAN_POINT('',(5.985,-5.375,0.2743200001));
#4799=VECTOR('',#5539,0.01);
#4800=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4801=CARTESIAN_POINT('',(5.985,-3.405,0.2743200001));
#4802=VECTOR('',#5540,0.01);
#4803=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4804=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4805=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4806=CARTESIAN_POINT('',(8.525,-3.405,0.2895600001));
#4807=CARTESIAN_POINT('',(9.255,-3.405,0.2895600001));
#4808=CARTESIAN_POINT('',(8.89,-3.405,0.2895600001));
#4809=VECTOR('',#5541,0.01);
#4810=CARTESIAN_POINT('',(8.525,-5.375,0.2895600001));
#4811=CARTESIAN_POINT('',(8.525,-4.39,0.2895600001));
#4812=VECTOR('',#5542,0.01);
#4813=CARTESIAN_POINT('',(9.255,-5.375,0.2895600001));
#4814=CARTESIAN_POINT('',(8.89,-5.375,0.2895600001));
#4815=VECTOR('',#5543,0.01);
#4816=CARTESIAN_POINT('',(9.255,-4.39,0.2895600001));
#4817=VECTOR('',#5544,0.01);
#4818=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4819=CARTESIAN_POINT('',(8.525,-3.405,0.2590800001));
#4820=CARTESIAN_POINT('',(9.255,-3.405,0.2590800001));
#4821=CARTESIAN_POINT('',(8.525,-3.405,0.2590800001));
#4822=VECTOR('',#5545,0.01);
#4823=CARTESIAN_POINT('',(9.255,-5.375,0.2590800001));
#4824=CARTESIAN_POINT('',(9.255,-3.405,0.2590800001));
#4825=VECTOR('',#5546,0.01);
#4826=CARTESIAN_POINT('',(8.525,-5.375,0.2590800001));
#4827=CARTESIAN_POINT('',(9.255,-5.375,0.2590800001));
#4828=VECTOR('',#5547,0.01);
#4829=CARTESIAN_POINT('',(8.525,-5.375,0.2590800001));
#4830=VECTOR('',#5548,0.01);
#4831=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4832=CARTESIAN_POINT('',(9.255,-5.375,0.2743200001));
#4833=VECTOR('',#5549,0.01);
#4834=CARTESIAN_POINT('',(9.255,-3.405,0.2743200001));
#4835=VECTOR('',#5550,0.01);
#4836=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4837=CARTESIAN_POINT('',(8.525,-5.375,0.2743200001));
#4838=VECTOR('',#5551,0.01);
#4839=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4840=CARTESIAN_POINT('',(8.525,-3.405,0.2743200001));
#4841=VECTOR('',#5552,0.01);
#4842=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4843=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4844=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4845=CARTESIAN_POINT('',(8.525,5.375,0.2895600001));
#4846=CARTESIAN_POINT('',(9.255,5.375,0.2895600001));
#4847=CARTESIAN_POINT('',(8.89,5.375,0.2895600001));
#4848=VECTOR('',#5553,0.01);
#4849=CARTESIAN_POINT('',(8.525,3.405,0.2895600001));
#4850=CARTESIAN_POINT('',(8.525,4.39,0.2895600001));
#4851=VECTOR('',#5554,0.01);
#4852=CARTESIAN_POINT('',(9.255,3.405,0.2895600001));
#4853=CARTESIAN_POINT('',(8.89,3.405,0.2895600001));
#4854=VECTOR('',#5555,0.01);
#4855=CARTESIAN_POINT('',(9.255,4.39,0.2895600001));
#4856=VECTOR('',#5556,0.01);
#4857=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4858=CARTESIAN_POINT('',(8.525,5.375,0.2590800001));
#4859=CARTESIAN_POINT('',(9.255,5.375,0.2590800001));
#4860=CARTESIAN_POINT('',(8.525,5.375,0.2590800001));
#4861=VECTOR('',#5557,0.01);
#4862=CARTESIAN_POINT('',(9.255,3.405,0.2590800001));
#4863=CARTESIAN_POINT('',(9.255,5.375,0.2590800001));
#4864=VECTOR('',#5558,0.01);
#4865=CARTESIAN_POINT('',(8.525,3.405,0.2590800001));
#4866=CARTESIAN_POINT('',(9.255,3.405,0.2590800001));
#4867=VECTOR('',#5559,0.01);
#4868=CARTESIAN_POINT('',(8.525,3.405,0.2590800001));
#4869=VECTOR('',#5560,0.01);
#4870=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4871=CARTESIAN_POINT('',(9.255,3.405,0.2743200001));
#4872=VECTOR('',#5561,0.01);
#4873=CARTESIAN_POINT('',(9.255,5.375,0.2743200001));
#4874=VECTOR('',#5562,0.01);
#4875=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4876=CARTESIAN_POINT('',(8.525,3.405,0.2743200001));
#4877=VECTOR('',#5563,0.01);
#4878=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4879=CARTESIAN_POINT('',(8.525,5.375,0.2743200001));
#4880=VECTOR('',#5564,0.01);
#4881=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4882=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4883=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4884=CARTESIAN_POINT('',(5.985,5.375,0.2895600001));
#4885=CARTESIAN_POINT('',(6.715,5.375,0.2895600001));
#4886=CARTESIAN_POINT('',(6.35,5.375,0.2895600001));
#4887=VECTOR('',#5565,0.01);
#4888=CARTESIAN_POINT('',(5.985,3.405,0.2895600001));
#4889=CARTESIAN_POINT('',(5.985,4.39,0.2895600001));
#4890=VECTOR('',#5566,0.01);
#4891=CARTESIAN_POINT('',(6.715,3.405,0.2895600001));
#4892=CARTESIAN_POINT('',(6.35,3.405,0.2895600001));
#4893=VECTOR('',#5567,0.01);
#4894=CARTESIAN_POINT('',(6.715,4.39,0.2895600001));
#4895=VECTOR('',#5568,0.01);
#4896=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4897=CARTESIAN_POINT('',(5.985,5.375,0.2590800001));
#4898=CARTESIAN_POINT('',(6.715,5.375,0.2590800001));
#4899=CARTESIAN_POINT('',(5.985,5.375,0.2590800001));
#4900=VECTOR('',#5569,0.01);
#4901=CARTESIAN_POINT('',(6.715,3.405,0.2590800001));
#4902=CARTESIAN_POINT('',(6.715,5.375,0.2590800001));
#4903=VECTOR('',#5570,0.01);
#4904=CARTESIAN_POINT('',(5.985,3.405,0.2590800001));
#4905=CARTESIAN_POINT('',(6.715,3.405,0.2590800001));
#4906=VECTOR('',#5571,0.01);
#4907=CARTESIAN_POINT('',(5.985,3.405,0.2590800001));
#4908=VECTOR('',#5572,0.01);
#4909=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4910=CARTESIAN_POINT('',(6.715,3.405,0.2743200001));
#4911=VECTOR('',#5573,0.01);
#4912=CARTESIAN_POINT('',(6.715,5.375,0.2743200001));
#4913=VECTOR('',#5574,0.01);
#4914=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4915=CARTESIAN_POINT('',(5.985,3.405,0.2743200001));
#4916=VECTOR('',#5575,0.01);
#4917=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4918=CARTESIAN_POINT('',(5.985,5.375,0.2743200001));
#4919=VECTOR('',#5576,0.01);
#4920=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4921=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4922=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4923=CARTESIAN_POINT('',(3.445,5.375,0.2895600001));
#4924=CARTESIAN_POINT('',(4.175,5.375,0.2895600001));
#4925=CARTESIAN_POINT('',(3.81,5.375,0.2895600001));
#4926=VECTOR('',#5577,0.01);
#4927=CARTESIAN_POINT('',(3.445,3.405,0.2895600001));
#4928=CARTESIAN_POINT('',(3.445,4.39,0.2895600001));
#4929=VECTOR('',#5578,0.01);
#4930=CARTESIAN_POINT('',(4.175,3.405,0.2895600001));
#4931=CARTESIAN_POINT('',(3.81,3.405,0.2895600001));
#4932=VECTOR('',#5579,0.01);
#4933=CARTESIAN_POINT('',(4.175,4.39,0.2895600001));
#4934=VECTOR('',#5580,0.01);
#4935=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4936=CARTESIAN_POINT('',(3.445,5.375,0.2590800001));
#4937=CARTESIAN_POINT('',(4.175,5.375,0.2590800001));
#4938=CARTESIAN_POINT('',(3.445,5.375,0.2590800001));
#4939=VECTOR('',#5581,0.01);
#4940=CARTESIAN_POINT('',(4.175,3.405,0.2590800001));
#4941=CARTESIAN_POINT('',(4.175,5.375,0.2590800001));
#4942=VECTOR('',#5582,0.01);
#4943=CARTESIAN_POINT('',(3.445,3.405,0.2590800001));
#4944=CARTESIAN_POINT('',(4.175,3.405,0.2590800001));
#4945=VECTOR('',#5583,0.01);
#4946=CARTESIAN_POINT('',(3.445,3.405,0.2590800001));
#4947=VECTOR('',#5584,0.01);
#4948=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4949=CARTESIAN_POINT('',(4.175,3.405,0.2743200001));
#4950=VECTOR('',#5585,0.01);
#4951=CARTESIAN_POINT('',(4.175,5.375,0.2743200001));
#4952=VECTOR('',#5586,0.01);
#4953=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4954=CARTESIAN_POINT('',(3.445,3.405,0.2743200001));
#4955=VECTOR('',#5587,0.01);
#4956=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4957=CARTESIAN_POINT('',(3.445,5.375,0.2743200001));
#4958=VECTOR('',#5588,0.01);
#4959=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4960=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4961=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4962=CARTESIAN_POINT('',(0.905,5.375,0.2895600001));
#4963=CARTESIAN_POINT('',(1.635,5.375,0.2895600001));
#4964=CARTESIAN_POINT('',(1.27,5.375,0.2895600001));
#4965=VECTOR('',#5589,0.01);
#4966=CARTESIAN_POINT('',(0.905,3.405,0.2895600001));
#4967=CARTESIAN_POINT('',(0.905,4.39,0.2895600001));
#4968=VECTOR('',#5590,0.01);
#4969=CARTESIAN_POINT('',(1.635,3.405,0.2895600001));
#4970=CARTESIAN_POINT('',(1.27,3.405,0.2895600001));
#4971=VECTOR('',#5591,0.01);
#4972=CARTESIAN_POINT('',(1.635,4.39,0.2895600001));
#4973=VECTOR('',#5592,0.01);
#4974=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4975=CARTESIAN_POINT('',(0.905,5.375,0.2590800001));
#4976=CARTESIAN_POINT('',(1.635,5.375,0.2590800001));
#4977=CARTESIAN_POINT('',(0.905,5.375,0.2590800001));
#4978=VECTOR('',#5593,0.01);
#4979=CARTESIAN_POINT('',(1.635,3.405,0.2590800001));
#4980=CARTESIAN_POINT('',(1.635,5.375,0.2590800001));
#4981=VECTOR('',#5594,0.01);
#4982=CARTESIAN_POINT('',(0.905,3.405,0.2590800001));
#4983=CARTESIAN_POINT('',(1.635,3.405,0.2590800001));
#4984=VECTOR('',#5595,0.01);
#4985=CARTESIAN_POINT('',(0.905,3.405,0.2590800001));
#4986=VECTOR('',#5596,0.01);
#4987=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4988=CARTESIAN_POINT('',(1.635,3.405,0.2743200001));
#4989=VECTOR('',#5597,0.01);
#4990=CARTESIAN_POINT('',(1.635,5.375,0.2743200001));
#4991=VECTOR('',#5598,0.01);
#4992=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4993=CARTESIAN_POINT('',(0.905,3.405,0.2743200001));
#4994=VECTOR('',#5599,0.01);
#4995=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4996=CARTESIAN_POINT('',(0.905,5.375,0.2743200001));
#4997=VECTOR('',#5600,0.01);
#4998=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#4999=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5000=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5001=CARTESIAN_POINT('',(-1.635,5.375,0.2895600001));
#5002=CARTESIAN_POINT('',(-0.905,5.375,0.2895600001));
#5003=CARTESIAN_POINT('',(-1.27,5.375,0.2895600001));
#5004=VECTOR('',#5601,0.01);
#5005=CARTESIAN_POINT('',(-1.635,3.405,0.2895600001));
#5006=CARTESIAN_POINT('',(-1.635,4.39,0.2895600001));
#5007=VECTOR('',#5602,0.01);
#5008=CARTESIAN_POINT('',(-0.905,3.405,0.2895600001));
#5009=CARTESIAN_POINT('',(-1.27,3.405,0.2895600001));
#5010=VECTOR('',#5603,0.01);
#5011=CARTESIAN_POINT('',(-0.905,4.39,0.2895600001));
#5012=VECTOR('',#5604,0.01);
#5013=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5014=CARTESIAN_POINT('',(-1.635,5.375,0.2590800001));
#5015=CARTESIAN_POINT('',(-0.905,5.375,0.2590800001));
#5016=CARTESIAN_POINT('',(-1.635,5.375,0.2590800001));
#5017=VECTOR('',#5605,0.01);
#5018=CARTESIAN_POINT('',(-0.905,3.405,0.2590800001));
#5019=CARTESIAN_POINT('',(-0.905,5.375,0.2590800001));
#5020=VECTOR('',#5606,0.01);
#5021=CARTESIAN_POINT('',(-1.635,3.405,0.2590800001));
#5022=CARTESIAN_POINT('',(-0.905,3.405,0.2590800001));
#5023=VECTOR('',#5607,0.01);
#5024=CARTESIAN_POINT('',(-1.635,3.405,0.2590800001));
#5025=VECTOR('',#5608,0.01);
#5026=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5027=CARTESIAN_POINT('',(-0.905,3.405,0.2743200001));
#5028=VECTOR('',#5609,0.01);
#5029=CARTESIAN_POINT('',(-0.905,5.375,0.2743200001));
#5030=VECTOR('',#5610,0.01);
#5031=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5032=CARTESIAN_POINT('',(-1.635,3.405,0.2743200001));
#5033=VECTOR('',#5611,0.01);
#5034=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5035=CARTESIAN_POINT('',(-1.635,5.375,0.2743200001));
#5036=VECTOR('',#5612,0.01);
#5037=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5038=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5039=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5040=CARTESIAN_POINT('',(-4.175,5.375,0.2895600001));
#5041=CARTESIAN_POINT('',(-3.445,5.375,0.2895600001));
#5042=CARTESIAN_POINT('',(-3.81,5.375,0.2895600001));
#5043=VECTOR('',#5613,0.01);
#5044=CARTESIAN_POINT('',(-4.175,3.405,0.2895600001));
#5045=CARTESIAN_POINT('',(-4.175,4.39,0.2895600001));
#5046=VECTOR('',#5614,0.01);
#5047=CARTESIAN_POINT('',(-3.445,3.405,0.2895600001));
#5048=CARTESIAN_POINT('',(-3.81,3.405,0.2895600001));
#5049=VECTOR('',#5615,0.01);
#5050=CARTESIAN_POINT('',(-3.445,4.39,0.2895600001));
#5051=VECTOR('',#5616,0.01);
#5052=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5053=CARTESIAN_POINT('',(-4.175,5.375,0.2590800001));
#5054=CARTESIAN_POINT('',(-3.445,5.375,0.2590800001));
#5055=CARTESIAN_POINT('',(-4.175,5.375,0.2590800001));
#5056=VECTOR('',#5617,0.01);
#5057=CARTESIAN_POINT('',(-3.445,3.405,0.2590800001));
#5058=CARTESIAN_POINT('',(-3.445,5.375,0.2590800001));
#5059=VECTOR('',#5618,0.01);
#5060=CARTESIAN_POINT('',(-4.175,3.405,0.2590800001));
#5061=CARTESIAN_POINT('',(-3.445,3.405,0.2590800001));
#5062=VECTOR('',#5619,0.01);
#5063=CARTESIAN_POINT('',(-4.175,3.405,0.2590800001));
#5064=VECTOR('',#5620,0.01);
#5065=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5066=CARTESIAN_POINT('',(-3.445,3.405,0.2743200001));
#5067=VECTOR('',#5621,0.01);
#5068=CARTESIAN_POINT('',(-3.445,5.375,0.2743200001));
#5069=VECTOR('',#5622,0.01);
#5070=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5071=CARTESIAN_POINT('',(-4.175,3.405,0.2743200001));
#5072=VECTOR('',#5623,0.01);
#5073=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5074=CARTESIAN_POINT('',(-4.175,5.375,0.2743200001));
#5075=VECTOR('',#5624,0.01);
#5076=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5077=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5078=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5079=CARTESIAN_POINT('',(-6.715,5.375,0.2895600001));
#5080=CARTESIAN_POINT('',(-5.985,5.375,0.2895600001));
#5081=CARTESIAN_POINT('',(-6.35,5.375,0.2895600001));
#5082=VECTOR('',#5625,0.01);
#5083=CARTESIAN_POINT('',(-6.715,3.405,0.2895600001));
#5084=CARTESIAN_POINT('',(-6.715,4.39,0.2895600001));
#5085=VECTOR('',#5626,0.01);
#5086=CARTESIAN_POINT('',(-5.985,3.405,0.2895600001));
#5087=CARTESIAN_POINT('',(-6.35,3.405,0.2895600001));
#5088=VECTOR('',#5627,0.01);
#5089=CARTESIAN_POINT('',(-5.985,4.39,0.2895600001));
#5090=VECTOR('',#5628,0.01);
#5091=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5092=CARTESIAN_POINT('',(-6.715,5.375,0.2590800001));
#5093=CARTESIAN_POINT('',(-5.985,5.375,0.2590800001));
#5094=CARTESIAN_POINT('',(-6.715,5.375,0.2590800001));
#5095=VECTOR('',#5629,0.01);
#5096=CARTESIAN_POINT('',(-5.985,3.405,0.2590800001));
#5097=CARTESIAN_POINT('',(-5.985,5.375,0.2590800001));
#5098=VECTOR('',#5630,0.01);
#5099=CARTESIAN_POINT('',(-6.715,3.405,0.2590800001));
#5100=CARTESIAN_POINT('',(-5.985,3.405,0.2590800001));
#5101=VECTOR('',#5631,0.01);
#5102=CARTESIAN_POINT('',(-6.715,3.405,0.2590800001));
#5103=VECTOR('',#5632,0.01);
#5104=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5105=CARTESIAN_POINT('',(-5.985,3.405,0.2743200001));
#5106=VECTOR('',#5633,0.01);
#5107=CARTESIAN_POINT('',(-5.985,5.375,0.2743200001));
#5108=VECTOR('',#5634,0.01);
#5109=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5110=CARTESIAN_POINT('',(-6.715,3.405,0.2743200001));
#5111=VECTOR('',#5635,0.01);
#5112=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5113=CARTESIAN_POINT('',(-6.715,5.375,0.2743200001));
#5114=VECTOR('',#5636,0.01);
#5115=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5116=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5117=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5118=CARTESIAN_POINT('',(-9.255,5.375,0.2895600001));
#5119=CARTESIAN_POINT('',(-8.525,5.375,0.2895600001));
#5120=CARTESIAN_POINT('',(-8.89,5.375,0.2895600001));
#5121=VECTOR('',#5637,0.01);
#5122=CARTESIAN_POINT('',(-9.255,3.405,0.2895600001));
#5123=CARTESIAN_POINT('',(-9.255,4.39,0.2895600001));
#5124=VECTOR('',#5638,0.01);
#5125=CARTESIAN_POINT('',(-8.525,3.405,0.2895600001));
#5126=CARTESIAN_POINT('',(-8.89,3.405,0.2895600001));
#5127=VECTOR('',#5639,0.01);
#5128=CARTESIAN_POINT('',(-8.525,4.39,0.2895600001));
#5129=VECTOR('',#5640,0.01);
#5130=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5131=CARTESIAN_POINT('',(-9.255,5.375,0.2590800001));
#5132=CARTESIAN_POINT('',(-8.525,5.375,0.2590800001));
#5133=CARTESIAN_POINT('',(-9.255,5.375,0.2590800001));
#5134=VECTOR('',#5641,0.01);
#5135=CARTESIAN_POINT('',(-8.525,3.405,0.2590800001));
#5136=CARTESIAN_POINT('',(-8.525,5.375,0.2590800001));
#5137=VECTOR('',#5642,0.01);
#5138=CARTESIAN_POINT('',(-9.255,3.405,0.2590800001));
#5139=CARTESIAN_POINT('',(-8.525,3.405,0.2590800001));
#5140=VECTOR('',#5643,0.01);
#5141=CARTESIAN_POINT('',(-9.255,3.405,0.2590800001));
#5142=VECTOR('',#5644,0.01);
#5143=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5144=CARTESIAN_POINT('',(-8.525,3.405,0.2743200001));
#5145=VECTOR('',#5645,0.01);
#5146=CARTESIAN_POINT('',(-8.525,5.375,0.2743200001));
#5147=VECTOR('',#5646,0.01);
#5148=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5149=CARTESIAN_POINT('',(-9.255,3.405,0.2743200001));
#5150=VECTOR('',#5647,0.01);
#5151=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5152=CARTESIAN_POINT('',(-9.255,5.375,0.2743200001));
#5153=VECTOR('',#5648,0.01);
#5154=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5155=COLOUR_RGB('',0.149019613862038,1.0,0.149019613862038);
#5156=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5157=CARTESIAN_POINT('',(-8.63599975585938,-5.75599975585938,0.3151600001));
#5158=CARTESIAN_POINT('',(-8.63599975585938,-5.75599975585938,0.3278600001));
#5159=CARTESIAN_POINT('',(-8.63599975585938,-5.75599975585938,0.3151600001));
#5160=VECTOR('',#5649,1.0);
#5161=CARTESIAN_POINT('',(-9.1439995300382,-5.75600036621094,0.3151600001));
#5162=AXIS2_PLACEMENT_3D('',#5650,#5651,#5652);
#5163=CARTESIAN_POINT('',(-9.1439995300382,-5.75600036621094,0.3278600001));
#5164=CARTESIAN_POINT('',(-9.1439995300382,-5.75600036621094,0.3151600001));
#5165=VECTOR('',#5653,1.0);
#5166=AXIS2_PLACEMENT_3D('',#5654,#5655,#5656);
#5167=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5168=AXIS2_PLACEMENT_3D('',#5657,#5658,#5659);
#5169=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5170=AXIS2_PLACEMENT_3D('',#5660,#5661,#5662);
#5171=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5172=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5173=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5174=CARTESIAN_POINT('',(9.45,-3.46350006103516,0.3278600001));
#5175=CARTESIAN_POINT('',(9.45,-3.33649993896484,0.3278600001));
#5176=AXIS2_PLACEMENT_3D('',#5663,#5664,#5665);
#5177=CARTESIAN_POINT('',(10.025,-3.46350006103516,0.3278600001));
#5178=CARTESIAN_POINT('',(9.7375,-3.46350006103516,0.3278600001));
#5179=VECTOR('',#5666,0.01);
#5180=CARTESIAN_POINT('',(10.025,-3.33649993896484,0.3278600001));
#5181=AXIS2_PLACEMENT_3D('',#5667,#5668,#5669);
#5182=CARTESIAN_POINT('',(9.7375,-3.33649993896484,0.3278600001));
#5183=VECTOR('',#5670,0.01);
#5184=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5185=CARTESIAN_POINT('',(9.45,-3.46350006103516,0.3151600001));
#5186=CARTESIAN_POINT('',(9.45,-3.33649993896484,0.3151600001));
#5187=AXIS2_PLACEMENT_3D('',#5671,#5672,#5673);
#5188=CARTESIAN_POINT('',(10.025,-3.33649993896484,0.3151600001));
#5189=CARTESIAN_POINT('',(9.45,-3.33649993896484,0.3151600001));
#5190=VECTOR('',#5674,0.01);
#5191=CARTESIAN_POINT('',(10.025,-3.46350006103516,0.3151600001));
#5192=AXIS2_PLACEMENT_3D('',#5675,#5676,#5677);
#5193=CARTESIAN_POINT('',(10.025,-3.46350006103516,0.3151600001));
#5194=VECTOR('',#5678,0.01);
#5195=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5196=CARTESIAN_POINT('',(10.025,-3.33649993896484,0.3215100001));
#5197=VECTOR('',#5679,0.01);
#5198=CARTESIAN_POINT('',(9.45,-3.33649993896484,0.3215100001));
#5199=VECTOR('',#5680,0.01);
#5200=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5201=CARTESIAN_POINT('',(10.025,-3.46350006103516,0.3215100001));
#5202=VECTOR('',#5681,0.01);
#5203=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5204=CARTESIAN_POINT('',(9.45,-3.46350006103516,0.3215100001));
#5205=VECTOR('',#5682,0.01);
#5206=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5207=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5208=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5209=CARTESIAN_POINT('',(10.0884997558594,-3.4,0.3278600001));
#5210=CARTESIAN_POINT('',(9.96149963378906,-3.4,0.3278600001));
#5211=AXIS2_PLACEMENT_3D('',#5683,#5684,#5685);
#5212=CARTESIAN_POINT('',(10.0884997558594,3.4,0.3278600001));
#5213=CARTESIAN_POINT('',(10.0884997558594,0.0,0.3278600001));
#5214=VECTOR('',#5686,0.01);
#5215=CARTESIAN_POINT('',(9.96149963378906,3.4,0.3278600001));
#5216=AXIS2_PLACEMENT_3D('',#5687,#5688,#5689);
#5217=CARTESIAN_POINT('',(9.96149963378906,0.0,0.3278600001));
#5218=VECTOR('',#5690,0.01);
#5219=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5220=CARTESIAN_POINT('',(10.0884997558594,-3.4,0.3151600001));
#5221=CARTESIAN_POINT('',(9.96149963378906,-3.4,0.3151600001));
#5222=AXIS2_PLACEMENT_3D('',#5691,#5692,#5693);
#5223=CARTESIAN_POINT('',(9.96149963378906,3.4,0.3151600001));
#5224=CARTESIAN_POINT('',(9.96149963378906,-3.4,0.3151600001));
#5225=VECTOR('',#5694,0.01);
#5226=CARTESIAN_POINT('',(10.0884997558594,3.4,0.3151600001));
#5227=AXIS2_PLACEMENT_3D('',#5695,#5696,#5697);
#5228=CARTESIAN_POINT('',(10.0884997558594,3.4,0.3151600001));
#5229=VECTOR('',#5698,0.01);
#5230=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5231=CARTESIAN_POINT('',(9.96149963378906,3.4,0.3215100001));
#5232=VECTOR('',#5699,0.01);
#5233=CARTESIAN_POINT('',(9.96149963378906,-3.4,0.3215100001));
#5234=VECTOR('',#5700,0.01);
#5235=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5236=CARTESIAN_POINT('',(10.0884997558594,3.4,0.3215100001));
#5237=VECTOR('',#5701,0.01);
#5238=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5239=CARTESIAN_POINT('',(10.0884997558594,-3.4,0.3215100001));
#5240=VECTOR('',#5702,0.01);
#5241=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5242=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5243=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5244=CARTESIAN_POINT('',(10.025,3.46350006103516,0.3278600001));
#5245=CARTESIAN_POINT('',(10.025,3.33649993896484,0.3278600001));
#5246=AXIS2_PLACEMENT_3D('',#5703,#5704,#5705);
#5247=CARTESIAN_POINT('',(9.45,3.46350006103516,0.3278600001));
#5248=CARTESIAN_POINT('',(9.7375,3.46350006103516,0.3278600001));
#5249=VECTOR('',#5706,0.01);
#5250=CARTESIAN_POINT('',(9.45,3.33649993896484,0.3278600001));
#5251=AXIS2_PLACEMENT_3D('',#5707,#5708,#5709);
#5252=CARTESIAN_POINT('',(9.7375,3.33649993896484,0.3278600001));
#5253=VECTOR('',#5710,0.01);
#5254=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5255=CARTESIAN_POINT('',(10.025,3.46350006103516,0.3151600001));
#5256=CARTESIAN_POINT('',(10.025,3.33649993896484,0.3151600001));
#5257=AXIS2_PLACEMENT_3D('',#5711,#5712,#5713);
#5258=CARTESIAN_POINT('',(9.45,3.33649993896484,0.3151600001));
#5259=CARTESIAN_POINT('',(10.025,3.33649993896484,0.3151600001));
#5260=VECTOR('',#5714,0.01);
#5261=CARTESIAN_POINT('',(9.45,3.46350006103516,0.3151600001));
#5262=AXIS2_PLACEMENT_3D('',#5715,#5716,#5717);
#5263=CARTESIAN_POINT('',(9.45,3.46350006103516,0.3151600001));
#5264=VECTOR('',#5718,0.01);
#5265=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5266=CARTESIAN_POINT('',(9.45,3.33649993896484,0.3215100001));
#5267=VECTOR('',#5719,0.01);
#5268=CARTESIAN_POINT('',(10.025,3.33649993896484,0.3215100001));
#5269=VECTOR('',#5720,0.01);
#5270=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5271=CARTESIAN_POINT('',(9.45,3.46350006103516,0.3215100001));
#5272=VECTOR('',#5721,0.01);
#5273=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5274=CARTESIAN_POINT('',(10.025,3.46350006103516,0.3215100001));
#5275=VECTOR('',#5722,0.01);
#5276=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5277=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5278=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5279=CARTESIAN_POINT('',(-9.45,-3.33649993896484,0.3278600001));
#5280=CARTESIAN_POINT('',(-9.45,-3.46350006103516,0.3278600001));
#5281=AXIS2_PLACEMENT_3D('',#5723,#5724,#5725);
#5282=CARTESIAN_POINT('',(-10.025,-3.33649993896484,0.3278600001));
#5283=CARTESIAN_POINT('',(-9.7375,-3.33649993896484,0.3278600001));
#5284=VECTOR('',#5726,0.01);
#5285=CARTESIAN_POINT('',(-10.025,-3.46350006103516,0.3278600001));
#5286=AXIS2_PLACEMENT_3D('',#5727,#5728,#5729);
#5287=CARTESIAN_POINT('',(-9.7375,-3.46350006103516,0.3278600001));
#5288=VECTOR('',#5730,0.01);
#5289=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5290=CARTESIAN_POINT('',(-9.45,-3.33649993896484,0.3151600001));
#5291=CARTESIAN_POINT('',(-9.45,-3.46350006103516,0.3151600001));
#5292=AXIS2_PLACEMENT_3D('',#5731,#5732,#5733);
#5293=CARTESIAN_POINT('',(-10.025,-3.46350006103516,0.3151600001));
#5294=CARTESIAN_POINT('',(-9.45,-3.46350006103516,0.3151600001));
#5295=VECTOR('',#5734,0.01);
#5296=CARTESIAN_POINT('',(-10.025,-3.33649993896484,0.3151600001));
#5297=AXIS2_PLACEMENT_3D('',#5735,#5736,#5737);
#5298=CARTESIAN_POINT('',(-10.025,-3.33649993896484,0.3151600001));
#5299=VECTOR('',#5738,0.01);
#5300=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5301=CARTESIAN_POINT('',(-10.025,-3.46350006103516,0.3215100001));
#5302=VECTOR('',#5739,0.01);
#5303=CARTESIAN_POINT('',(-9.45,-3.46350006103516,0.3215100001));
#5304=VECTOR('',#5740,0.01);
#5305=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5306=CARTESIAN_POINT('',(-10.025,-3.33649993896484,0.3215100001));
#5307=VECTOR('',#5741,0.01);
#5308=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5309=CARTESIAN_POINT('',(-9.45,-3.33649993896484,0.3215100001));
#5310=VECTOR('',#5742,0.01);
#5311=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5312=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5313=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5314=CARTESIAN_POINT('',(-9.96149963378906,-3.4,0.3278600001));
#5315=CARTESIAN_POINT('',(-10.0884997558594,-3.4,0.3278600001));
#5316=AXIS2_PLACEMENT_3D('',#5743,#5744,#5745);
#5317=CARTESIAN_POINT('',(-9.96149963378906,3.4,0.3278600001));
#5318=CARTESIAN_POINT('',(-9.96149963378906,0.0,0.3278600001));
#5319=VECTOR('',#5746,0.01);
#5320=CARTESIAN_POINT('',(-10.0884997558594,3.4,0.3278600001));
#5321=AXIS2_PLACEMENT_3D('',#5747,#5748,#5749);
#5322=CARTESIAN_POINT('',(-10.0884997558594,0.0,0.3278600001));
#5323=VECTOR('',#5750,0.01);
#5324=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5325=CARTESIAN_POINT('',(-9.96149963378906,-3.4,0.3151600001));
#5326=CARTESIAN_POINT('',(-10.0884997558594,-3.4,0.3151600001));
#5327=AXIS2_PLACEMENT_3D('',#5751,#5752,#5753);
#5328=CARTESIAN_POINT('',(-10.0884997558594,3.4,0.3151600001));
#5329=CARTESIAN_POINT('',(-10.0884997558594,-3.4,0.3151600001));
#5330=VECTOR('',#5754,0.01);
#5331=CARTESIAN_POINT('',(-9.96149963378906,3.4,0.3151600001));
#5332=AXIS2_PLACEMENT_3D('',#5755,#5756,#5757);
#5333=CARTESIAN_POINT('',(-9.96149963378906,3.4,0.3151600001));
#5334=VECTOR('',#5758,0.01);
#5335=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5336=CARTESIAN_POINT('',(-10.0884997558594,3.4,0.3215100001));
#5337=VECTOR('',#5759,0.01);
#5338=CARTESIAN_POINT('',(-10.0884997558594,-3.4,0.3215100001));
#5339=VECTOR('',#5760,0.01);
#5340=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5341=CARTESIAN_POINT('',(-9.96149963378906,3.4,0.3215100001));
#5342=VECTOR('',#5761,0.01);
#5343=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5344=CARTESIAN_POINT('',(-9.96149963378906,-3.4,0.3215100001));
#5345=VECTOR('',#5762,0.01);
#5346=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5347=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5348=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5349=CARTESIAN_POINT('',(-10.025,3.33649993896484,0.3278600001));
#5350=CARTESIAN_POINT('',(-10.025,3.46350006103516,0.3278600001));
#5351=AXIS2_PLACEMENT_3D('',#5763,#5764,#5765);
#5352=CARTESIAN_POINT('',(-9.45,3.33649993896484,0.3278600001));
#5353=CARTESIAN_POINT('',(-9.7375,3.33649993896484,0.3278600001));
#5354=VECTOR('',#5766,0.01);
#5355=CARTESIAN_POINT('',(-9.45,3.46350006103516,0.3278600001));
#5356=AXIS2_PLACEMENT_3D('',#5767,#5768,#5769);
#5357=CARTESIAN_POINT('',(-9.7375,3.46350006103516,0.3278600001));
#5358=VECTOR('',#5770,0.01);
#5359=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5360=CARTESIAN_POINT('',(-10.025,3.33649993896484,0.3151600001));
#5361=CARTESIAN_POINT('',(-10.025,3.46350006103516,0.3151600001));
#5362=AXIS2_PLACEMENT_3D('',#5771,#5772,#5773);
#5363=CARTESIAN_POINT('',(-9.45,3.46350006103516,0.3151600001));
#5364=CARTESIAN_POINT('',(-10.025,3.46350006103516,0.3151600001));
#5365=VECTOR('',#5774,0.01);
#5366=CARTESIAN_POINT('',(-9.45,3.33649993896484,0.3151600001));
#5367=AXIS2_PLACEMENT_3D('',#5775,#5776,#5777);
#5368=CARTESIAN_POINT('',(-9.45,3.33649993896484,0.3151600001));
#5369=VECTOR('',#5778,0.01);
#5370=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5371=CARTESIAN_POINT('',(-9.45,3.46350006103516,0.3215100001));
#5372=VECTOR('',#5779,0.01);
#5373=CARTESIAN_POINT('',(-10.025,3.46350006103516,0.3215100001));
#5374=VECTOR('',#5780,0.01);
#5375=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5376=CARTESIAN_POINT('',(-9.45,3.33649993896484,0.3215100001));
#5377=VECTOR('',#5781,0.01);
#5378=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5379=CARTESIAN_POINT('',(-10.025,3.33649993896484,0.3215100001));
#5380=VECTOR('',#5782,0.01);
#5381=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5382=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5383=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5384=CARTESIAN_POINT('',(-9.51349975585937,-3.4,0.3278600001));
#5385=CARTESIAN_POINT('',(-9.38649963378906,-3.4,0.3278600001));
#5386=AXIS2_PLACEMENT_3D('',#5783,#5784,#5785);
#5387=CARTESIAN_POINT('',(-9.51349975585937,-5.3125,0.3278600001));
#5388=CARTESIAN_POINT('',(-9.51349975585937,-4.35625,0.3278600001));
#5389=VECTOR('',#5786,0.01);
#5390=CARTESIAN_POINT('',(-9.38649963378906,-5.3125,0.3278600001));
#5391=AXIS2_PLACEMENT_3D('',#5787,#5788,#5789);
#5392=CARTESIAN_POINT('',(-9.38649963378906,-4.35625,0.3278600001));
#5393=VECTOR('',#5790,0.01);
#5394=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5395=CARTESIAN_POINT('',(-9.51349975585937,-3.4,0.3151600001));
#5396=CARTESIAN_POINT('',(-9.38649963378906,-3.4,0.3151600001));
#5397=AXIS2_PLACEMENT_3D('',#5791,#5792,#5793);
#5398=CARTESIAN_POINT('',(-9.38649963378906,-5.3125,0.3151600001));
#5399=CARTESIAN_POINT('',(-9.38649963378906,-3.4,0.3151600001));
#5400=VECTOR('',#5794,0.01);
#5401=CARTESIAN_POINT('',(-9.51349975585937,-5.3125,0.3151600001));
#5402=AXIS2_PLACEMENT_3D('',#5795,#5796,#5797);
#5403=CARTESIAN_POINT('',(-9.51349975585937,-5.3125,0.3151600001));
#5404=VECTOR('',#5798,0.01);
#5405=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5406=CARTESIAN_POINT('',(-9.38649963378906,-5.3125,0.3215100001));
#5407=VECTOR('',#5799,0.01);
#5408=CARTESIAN_POINT('',(-9.38649963378906,-3.4,0.3215100001));
#5409=VECTOR('',#5800,0.01);
#5410=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5411=CARTESIAN_POINT('',(-9.51349975585937,-5.3125,0.3215100001));
#5412=VECTOR('',#5801,0.01);
#5413=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5414=CARTESIAN_POINT('',(-9.51349975585937,-3.4,0.3215100001));
#5415=VECTOR('',#5802,0.01);
#5416=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5417=COLOUR_RGB('',0.388235300779343,0.545098066329956,0.227450981736183);
#5418=COLOUR_RGB('',0.266666680574417,0.266666680574417,0.266666680574417);
#5419=CARTESIAN_POINT('',(9.96,-3.3225,4.9895600001));
#5420=CARTESIAN_POINT('',(-9.96,-3.3225,4.9895600001));
#5421=CARTESIAN_POINT('',(0.0,-3.3225,4.9895600001));
#5422=VECTOR('',#5803,0.01);
#5423=CARTESIAN_POINT('',(9.96,3.3225,4.9895600001));
#5424=CARTESIAN_POINT('',(9.96,0.0,4.9895600001));
#5425=VECTOR('',#5804,0.01);
#5426=CARTESIAN_POINT('',(-9.96,3.3225,4.9895600001));
#5427=CARTESIAN_POINT('',(0.0,3.3225,4.9895600001));
#5428=VECTOR('',#5805,0.01);
#5429=CARTESIAN_POINT('',(-9.96,0.0,4.9895600001));
#5430=VECTOR('',#5806,0.01);
#5431=COLOUR_RGB('',0.266666680574417,0.266666680574417,0.266666680574417);
#5432=CARTESIAN_POINT('',(9.96,-3.3225,0.2895600001));
#5433=CARTESIAN_POINT('',(-9.96,-3.3225,0.2895600001));
#5434=CARTESIAN_POINT('',(9.96,-3.3225,0.2895600001));
#5435=VECTOR('',#5807,0.01);
#5436=CARTESIAN_POINT('',(-9.96,3.3225,0.2895600001));
#5437=CARTESIAN_POINT('',(-9.96,-3.3225,0.2895600001));
#5438=VECTOR('',#5808,0.01);
#5439=CARTESIAN_POINT('',(9.96,3.3225,0.2895600001));
#5440=CARTESIAN_POINT('',(-9.96,3.3225,0.2895600001));
#5441=VECTOR('',#5809,0.01);
#5442=CARTESIAN_POINT('',(9.96,3.3225,0.2895600001));
#5443=VECTOR('',#5810,0.01);
#5444=COLOUR_RGB('',0.266666680574417,0.266666680574417,0.266666680574417);
#5445=CARTESIAN_POINT('',(-9.96,3.3225,2.6395600001));
#5446=VECTOR('',#5811,0.01);
#5447=CARTESIAN_POINT('',(-9.96,-3.3225,2.6395600001));
#5448=VECTOR('',#5812,0.01);
#5449=COLOUR_RGB('',0.266666680574417,0.266666680574417,0.266666680574417);
#5450=CARTESIAN_POINT('',(9.96,3.3225,2.6395600001));
#5451=VECTOR('',#5813,0.01);
#5452=COLOUR_RGB('',0.266666680574417,0.266666680574417,0.266666680574417);
#5453=CARTESIAN_POINT('',(9.96,-3.3225,2.6395600001));
#5454=VECTOR('',#5814,0.01);
#5455=COLOUR_RGB('',0.266666680574417,0.266666680574417,0.266666680574417);
#5456=COLOUR_RGB('',0.266666680574417,0.266666680574417,0.266666680574417);
#5457=DIRECTION('',(1.0,0.0,0.0));
#5458=DIRECTION('',(0.0,1.0,0.0));
#5459=DIRECTION('',(-1.0,0.0,0.0));
#5460=DIRECTION('',(0.0,-1.0,0.0));
#5461=DIRECTION('',(1.0,0.0,0.0));
#5462=DIRECTION('',(0.0,-1.0,0.0));
#5463=DIRECTION('',(-1.0,0.0,0.0));
#5464=DIRECTION('',(0.0,1.0,0.0));
#5465=DIRECTION('',(-0.0,-0.0,-1.0));
#5466=DIRECTION('',(-0.0,-0.0,-1.0));
#5467=DIRECTION('',(-0.0,-0.0,-1.0));
#5468=DIRECTION('',(-0.0,-0.0,-1.0));
#5469=DIRECTION('',(1.0,0.0,0.0));
#5470=DIRECTION('',(0.0,1.0,0.0));
#5471=DIRECTION('',(-1.0,0.0,0.0));
#5472=DIRECTION('',(0.0,-1.0,0.0));
#5473=DIRECTION('',(1.0,0.0,0.0));
#5474=DIRECTION('',(0.0,-1.0,0.0));
#5475=DIRECTION('',(-1.0,0.0,0.0));
#5476=DIRECTION('',(0.0,1.0,0.0));
#5477=DIRECTION('',(-0.0,-0.0,-1.0));
#5478=DIRECTION('',(-0.0,-0.0,-1.0));
#5479=DIRECTION('',(-0.0,-0.0,-1.0));
#5480=DIRECTION('',(-0.0,-0.0,-1.0));
#5481=DIRECTION('',(1.0,0.0,0.0));
#5482=DIRECTION('',(0.0,1.0,0.0));
#5483=DIRECTION('',(-1.0,0.0,0.0));
#5484=DIRECTION('',(0.0,-1.0,0.0));
#5485=DIRECTION('',(1.0,0.0,0.0));
#5486=DIRECTION('',(0.0,-1.0,0.0));
#5487=DIRECTION('',(-1.0,0.0,0.0));
#5488=DIRECTION('',(0.0,1.0,0.0));
#5489=DIRECTION('',(-0.0,-0.0,-1.0));
#5490=DIRECTION('',(-0.0,-0.0,-1.0));
#5491=DIRECTION('',(-0.0,-0.0,-1.0));
#5492=DIRECTION('',(-0.0,-0.0,-1.0));
#5493=DIRECTION('',(1.0,0.0,0.0));
#5494=DIRECTION('',(0.0,1.0,0.0));
#5495=DIRECTION('',(-1.0,0.0,0.0));
#5496=DIRECTION('',(0.0,-1.0,0.0));
#5497=DIRECTION('',(1.0,0.0,0.0));
#5498=DIRECTION('',(0.0,-1.0,0.0));
#5499=DIRECTION('',(-1.0,0.0,0.0));
#5500=DIRECTION('',(0.0,1.0,0.0));
#5501=DIRECTION('',(-0.0,-0.0,-1.0));
#5502=DIRECTION('',(-0.0,-0.0,-1.0));
#5503=DIRECTION('',(-0.0,-0.0,-1.0));
#5504=DIRECTION('',(-0.0,-0.0,-1.0));
#5505=DIRECTION('',(1.0,0.0,0.0));
#5506=DIRECTION('',(0.0,1.0,0.0));
#5507=DIRECTION('',(-1.0,0.0,0.0));
#5508=DIRECTION('',(0.0,-1.0,0.0));
#5509=DIRECTION('',(1.0,0.0,0.0));
#5510=DIRECTION('',(0.0,-1.0,0.0));
#5511=DIRECTION('',(-1.0,0.0,0.0));
#5512=DIRECTION('',(0.0,1.0,0.0));
#5513=DIRECTION('',(-0.0,-0.0,-1.0));
#5514=DIRECTION('',(-0.0,-0.0,-1.0));
#5515=DIRECTION('',(-0.0,-0.0,-1.0));
#5516=DIRECTION('',(-0.0,-0.0,-1.0));
#5517=DIRECTION('',(1.0,0.0,0.0));
#5518=DIRECTION('',(0.0,1.0,0.0));
#5519=DIRECTION('',(-1.0,0.0,0.0));
#5520=DIRECTION('',(0.0,-1.0,0.0));
#5521=DIRECTION('',(1.0,0.0,0.0));
#5522=DIRECTION('',(0.0,-1.0,0.0));
#5523=DIRECTION('',(-1.0,0.0,0.0));
#5524=DIRECTION('',(0.0,1.0,0.0));
#5525=DIRECTION('',(-0.0,-0.0,-1.0));
#5526=DIRECTION('',(-0.0,-0.0,-1.0));
#5527=DIRECTION('',(-0.0,-0.0,-1.0));
#5528=DIRECTION('',(-0.0,-0.0,-1.0));
#5529=DIRECTION('',(1.0,0.0,0.0));
#5530=DIRECTION('',(0.0,1.0,0.0));
#5531=DIRECTION('',(-1.0,0.0,0.0));
#5532=DIRECTION('',(0.0,-1.0,0.0));
#5533=DIRECTION('',(1.0,0.0,0.0));
#5534=DIRECTION('',(0.0,-1.0,0.0));
#5535=DIRECTION('',(-1.0,0.0,0.0));
#5536=DIRECTION('',(0.0,1.0,0.0));
#5537=DIRECTION('',(-0.0,-0.0,-1.0));
#5538=DIRECTION('',(-0.0,-0.0,-1.0));
#5539=DIRECTION('',(-0.0,-0.0,-1.0));
#5540=DIRECTION('',(-0.0,-0.0,-1.0));
#5541=DIRECTION('',(1.0,0.0,0.0));
#5542=DIRECTION('',(0.0,1.0,0.0));
#5543=DIRECTION('',(-1.0,0.0,0.0));
#5544=DIRECTION('',(0.0,-1.0,0.0));
#5545=DIRECTION('',(1.0,0.0,0.0));
#5546=DIRECTION('',(0.0,-1.0,0.0));
#5547=DIRECTION('',(-1.0,0.0,0.0));
#5548=DIRECTION('',(0.0,1.0,0.0));
#5549=DIRECTION('',(-0.0,-0.0,-1.0));
#5550=DIRECTION('',(-0.0,-0.0,-1.0));
#5551=DIRECTION('',(-0.0,-0.0,-1.0));
#5552=DIRECTION('',(-0.0,-0.0,-1.0));
#5553=DIRECTION('',(1.0,0.0,0.0));
#5554=DIRECTION('',(0.0,1.0,0.0));
#5555=DIRECTION('',(-1.0,0.0,0.0));
#5556=DIRECTION('',(0.0,-1.0,0.0));
#5557=DIRECTION('',(1.0,0.0,0.0));
#5558=DIRECTION('',(0.0,-1.0,0.0));
#5559=DIRECTION('',(-1.0,0.0,0.0));
#5560=DIRECTION('',(0.0,1.0,0.0));
#5561=DIRECTION('',(-0.0,-0.0,-1.0));
#5562=DIRECTION('',(-0.0,-0.0,-1.0));
#5563=DIRECTION('',(-0.0,-0.0,-1.0));
#5564=DIRECTION('',(-0.0,-0.0,-1.0));
#5565=DIRECTION('',(1.0,0.0,0.0));
#5566=DIRECTION('',(0.0,1.0,0.0));
#5567=DIRECTION('',(-1.0,0.0,0.0));
#5568=DIRECTION('',(0.0,-1.0,0.0));
#5569=DIRECTION('',(1.0,0.0,0.0));
#5570=DIRECTION('',(0.0,-1.0,0.0));
#5571=DIRECTION('',(-1.0,0.0,0.0));
#5572=DIRECTION('',(0.0,1.0,0.0));
#5573=DIRECTION('',(-0.0,-0.0,-1.0));
#5574=DIRECTION('',(-0.0,-0.0,-1.0));
#5575=DIRECTION('',(-0.0,-0.0,-1.0));
#5576=DIRECTION('',(-0.0,-0.0,-1.0));
#5577=DIRECTION('',(1.0,0.0,0.0));
#5578=DIRECTION('',(0.0,1.0,0.0));
#5579=DIRECTION('',(-1.0,0.0,0.0));
#5580=DIRECTION('',(0.0,-1.0,0.0));
#5581=DIRECTION('',(1.0,0.0,0.0));
#5582=DIRECTION('',(0.0,-1.0,0.0));
#5583=DIRECTION('',(-1.0,0.0,0.0));
#5584=DIRECTION('',(0.0,1.0,0.0));
#5585=DIRECTION('',(-0.0,-0.0,-1.0));
#5586=DIRECTION('',(-0.0,-0.0,-1.0));
#5587=DIRECTION('',(-0.0,-0.0,-1.0));
#5588=DIRECTION('',(-0.0,-0.0,-1.0));
#5589=DIRECTION('',(1.0,0.0,0.0));
#5590=DIRECTION('',(0.0,1.0,0.0));
#5591=DIRECTION('',(-1.0,0.0,0.0));
#5592=DIRECTION('',(0.0,-1.0,0.0));
#5593=DIRECTION('',(1.0,0.0,0.0));
#5594=DIRECTION('',(0.0,-1.0,0.0));
#5595=DIRECTION('',(-1.0,0.0,0.0));
#5596=DIRECTION('',(0.0,1.0,0.0));
#5597=DIRECTION('',(-0.0,-0.0,-1.0));
#5598=DIRECTION('',(-0.0,-0.0,-1.0));
#5599=DIRECTION('',(-0.0,-0.0,-1.0));
#5600=DIRECTION('',(-0.0,-0.0,-1.0));
#5601=DIRECTION('',(1.0,0.0,0.0));
#5602=DIRECTION('',(0.0,1.0,0.0));
#5603=DIRECTION('',(-1.0,0.0,0.0));
#5604=DIRECTION('',(0.0,-1.0,0.0));
#5605=DIRECTION('',(1.0,0.0,0.0));
#5606=DIRECTION('',(0.0,-1.0,0.0));
#5607=DIRECTION('',(-1.0,0.0,0.0));
#5608=DIRECTION('',(0.0,1.0,0.0));
#5609=DIRECTION('',(-0.0,-0.0,-1.0));
#5610=DIRECTION('',(-0.0,-0.0,-1.0));
#5611=DIRECTION('',(-0.0,-0.0,-1.0));
#5612=DIRECTION('',(-0.0,-0.0,-1.0));
#5613=DIRECTION('',(1.0,0.0,0.0));
#5614=DIRECTION('',(0.0,1.0,0.0));
#5615=DIRECTION('',(-1.0,0.0,0.0));
#5616=DIRECTION('',(0.0,-1.0,0.0));
#5617=DIRECTION('',(1.0,0.0,0.0));
#5618=DIRECTION('',(0.0,-1.0,0.0));
#5619=DIRECTION('',(-1.0,0.0,0.0));
#5620=DIRECTION('',(0.0,1.0,0.0));
#5621=DIRECTION('',(-0.0,-0.0,-1.0));
#5622=DIRECTION('',(-0.0,-0.0,-1.0));
#5623=DIRECTION('',(-0.0,-0.0,-1.0));
#5624=DIRECTION('',(-0.0,-0.0,-1.0));
#5625=DIRECTION('',(1.0,0.0,0.0));
#5626=DIRECTION('',(0.0,1.0,0.0));
#5627=DIRECTION('',(-1.0,0.0,0.0));
#5628=DIRECTION('',(0.0,-1.0,0.0));
#5629=DIRECTION('',(1.0,0.0,0.0));
#5630=DIRECTION('',(0.0,-1.0,0.0));
#5631=DIRECTION('',(-1.0,0.0,0.0));
#5632=DIRECTION('',(0.0,1.0,0.0));
#5633=DIRECTION('',(-0.0,-0.0,-1.0));
#5634=DIRECTION('',(-0.0,-0.0,-1.0));
#5635=DIRECTION('',(-0.0,-0.0,-1.0));
#5636=DIRECTION('',(-0.0,-0.0,-1.0));
#5637=DIRECTION('',(1.0,0.0,0.0));
#5638=DIRECTION('',(0.0,1.0,0.0));
#5639=DIRECTION('',(-1.0,0.0,0.0));
#5640=DIRECTION('',(0.0,-1.0,0.0));
#5641=DIRECTION('',(1.0,0.0,0.0));
#5642=DIRECTION('',(0.0,-1.0,0.0));
#5643=DIRECTION('',(-1.0,0.0,0.0));
#5644=DIRECTION('',(0.0,1.0,0.0));
#5645=DIRECTION('',(-0.0,-0.0,-1.0));
#5646=DIRECTION('',(-0.0,-0.0,-1.0));
#5647=DIRECTION('',(-0.0,-0.0,-1.0));
#5648=DIRECTION('',(-0.0,-0.0,-1.0));
#5649=DIRECTION('',(0.0,0.0,1.0));
#5650=CARTESIAN_POINT('',(-8.88999964294879,-5.75600006103516,0.3151600001));
#5651=DIRECTION('',(0.0,-0.0,1.0));
#5652=DIRECTION('',(0.999999999999278,1.20147998783305E-006,0.0));
#5653=DIRECTION('',(-0.0,-0.0,-1.0));
#5654=CARTESIAN_POINT('',(-8.88999964294879,-5.75600006103516,0.3278600001));
#5655=DIRECTION('',(0.0,-0.0,1.0));
#5656=DIRECTION('',(0.999999999999278,1.20147998783305E-006,0.0));
#5657=CARTESIAN_POINT('',(-8.88999964294879,-5.75600006103516,0.3278600001));
#5658=DIRECTION('',(0.0,-0.0,1.0));
#5659=DIRECTION('',(0.999999999999278,1.20147998783305E-006,0.0));
#5660=CARTESIAN_POINT('',(-8.88999964294879,-5.75600006103516,0.3151600001));
#5661=DIRECTION('',(0.0,-0.0,1.0));
#5662=DIRECTION('',(0.999999999999278,1.20147998783305E-006,0.0));
#5663=CARTESIAN_POINT('',(9.44999969482495,-3.4,0.3278600001));
#5664=DIRECTION('',(0.0,0.0,-1.0));
#5665=DIRECTION('',(4.8059016478333E-006,-0.999999999988452,0.0));
#5666=DIRECTION('',(-1.0,0.0,0.0));
#5667=CARTESIAN_POINT('',(10.0249996948235,-3.4,0.3278600001));
#5668=DIRECTION('',(0.0,0.0,-1.0));
#5669=DIRECTION('',(4.80592474324742E-006,0.999999999988451,0.0));
#5670=DIRECTION('',(1.0,0.0,0.0));
#5671=CARTESIAN_POINT('',(9.44999969482495,-3.4,0.3151600001));
#5672=DIRECTION('',(0.0,0.0,-1.0));
#5673=DIRECTION('',(4.8059016478333E-006,-0.999999999988452,0.0));
#5674=DIRECTION('',(1.0,0.0,0.0));
#5675=CARTESIAN_POINT('',(10.0249996948235,-3.4,0.3151600001));
#5676=DIRECTION('',(0.0,0.0,-1.0));
#5677=DIRECTION('',(4.80592474324742E-006,0.999999999988451,0.0));
#5678=DIRECTION('',(-1.0,0.0,0.0));
#5679=DIRECTION('',(-0.0,-0.0,-1.0));
#5680=DIRECTION('',(-0.0,-0.0,-1.0));
#5681=DIRECTION('',(-0.0,-0.0,-1.0));
#5682=DIRECTION('',(-0.0,-0.0,-1.0));
#5683=CARTESIAN_POINT('',(10.0249996948242,-3.40000000000073,0.3278600001));
#5684=DIRECTION('',(0.0,0.0,-1.0));
#5685=DIRECTION('',(1.0,1.15477070628082E-011,0.0));
#5686=DIRECTION('',(0.0,-1.0,0.0));
#5687=CARTESIAN_POINT('',(10.0249996948242,3.40000000000073,0.3278600001));
#5688=DIRECTION('',(-0.0,0.0,-1.0));
#5689=DIRECTION('',(-1.0,-1.15477070628082E-011,0.0));
#5690=DIRECTION('',(0.0,1.0,0.0));
#5691=CARTESIAN_POINT('',(10.0249996948242,-3.40000000000073,0.3151600001));
#5692=DIRECTION('',(0.0,0.0,-1.0));
#5693=DIRECTION('',(1.0,1.15477070628082E-011,0.0));
#5694=DIRECTION('',(0.0,1.0,0.0));
#5695=CARTESIAN_POINT('',(10.0249996948242,3.40000000000073,0.3151600001));
#5696=DIRECTION('',(-0.0,0.0,-1.0));
#5697=DIRECTION('',(-1.0,-1.15477070628082E-011,0.0));
#5698=DIRECTION('',(0.0,-1.0,0.0));
#5699=DIRECTION('',(-0.0,-0.0,-1.0));
#5700=DIRECTION('',(-0.0,-0.0,-1.0));
#5701=DIRECTION('',(-0.0,-0.0,-1.0));
#5702=DIRECTION('',(-0.0,-0.0,-1.0));
#5703=CARTESIAN_POINT('',(10.0249996948235,3.4,0.3278600001));
#5704=DIRECTION('',(0.0,0.0,-1.0));
#5705=DIRECTION('',(4.80592474324742E-006,0.999999999988451,0.0));
#5706=DIRECTION('',(1.0,0.0,0.0));
#5707=CARTESIAN_POINT('',(9.44999969482495,3.4,0.3278600001));
#5708=DIRECTION('',(0.0,0.0,-1.0));
#5709=DIRECTION('',(4.8059016478333E-006,-0.999999999988452,0.0));
#5710=DIRECTION('',(-1.0,0.0,0.0));
#5711=CARTESIAN_POINT('',(10.0249996948235,3.4,0.3151600001));
#5712=DIRECTION('',(0.0,0.0,-1.0));
#5713=DIRECTION('',(4.80592474324742E-006,0.999999999988451,0.0));
#5714=DIRECTION('',(-1.0,0.0,0.0));
#5715=CARTESIAN_POINT('',(9.44999969482495,3.4,0.3151600001));
#5716=DIRECTION('',(0.0,0.0,-1.0));
#5717=DIRECTION('',(4.8059016478333E-006,-0.999999999988452,0.0));
#5718=DIRECTION('',(1.0,0.0,0.0));
#5719=DIRECTION('',(-0.0,-0.0,-1.0));
#5720=DIRECTION('',(-0.0,-0.0,-1.0));
#5721=DIRECTION('',(-0.0,-0.0,-1.0));
#5722=DIRECTION('',(-0.0,-0.0,-1.0));
#5723=CARTESIAN_POINT('',(-9.44999969482495,-3.4,0.3278600001));
#5724=DIRECTION('',(0.0,0.0,-1.0));
#5725=DIRECTION('',(-4.8059016478333E-006,0.999999999988452,0.0));
#5726=DIRECTION('',(1.0,0.0,0.0));
#5727=CARTESIAN_POINT('',(-10.0249996948235,-3.4,0.3278600001));
#5728=DIRECTION('',(-0.0,0.0,-1.0));
#5729=DIRECTION('',(-4.80592474324742E-006,-0.999999999988451,0.0));
#5730=DIRECTION('',(-1.0,0.0,0.0));
#5731=CARTESIAN_POINT('',(-9.44999969482495,-3.4,0.3151600001));
#5732=DIRECTION('',(0.0,0.0,-1.0));
#5733=DIRECTION('',(-4.8059016478333E-006,0.999999999988452,0.0));
#5734=DIRECTION('',(-1.0,0.0,0.0));
#5735=CARTESIAN_POINT('',(-10.0249996948235,-3.4,0.3151600001));
#5736=DIRECTION('',(-0.0,0.0,-1.0));
#5737=DIRECTION('',(-4.80592474324742E-006,-0.999999999988451,0.0));
#5738=DIRECTION('',(1.0,0.0,0.0));
#5739=DIRECTION('',(-0.0,-0.0,-1.0));
#5740=DIRECTION('',(-0.0,-0.0,-1.0));
#5741=DIRECTION('',(-0.0,-0.0,-1.0));
#5742=DIRECTION('',(-0.0,-0.0,-1.0));
#5743=CARTESIAN_POINT('',(-10.0249996948242,-3.40000000000073,0.3278600001));
#5744=DIRECTION('',(0.0,0.0,-1.0));
#5745=DIRECTION('',(1.0,1.15477070628082E-011,0.0));
#5746=DIRECTION('',(0.0,-1.0,0.0));
#5747=CARTESIAN_POINT('',(-10.0249996948242,3.40000000000073,0.3278600001));
#5748=DIRECTION('',(-0.0,0.0,-1.0));
#5749=DIRECTION('',(-1.0,-1.15477070628082E-011,0.0));
#5750=DIRECTION('',(0.0,1.0,0.0));
#5751=CARTESIAN_POINT('',(-10.0249996948242,-3.40000000000073,0.3151600001));
#5752=DIRECTION('',(0.0,0.0,-1.0));
#5753=DIRECTION('',(1.0,1.15477070628082E-011,0.0));
#5754=DIRECTION('',(0.0,1.0,0.0));
#5755=CARTESIAN_POINT('',(-10.0249996948242,3.40000000000073,0.3151600001));
#5756=DIRECTION('',(-0.0,0.0,-1.0));
#5757=DIRECTION('',(-1.0,-1.15477070628082E-011,0.0));
#5758=DIRECTION('',(0.0,-1.0,0.0));
#5759=DIRECTION('',(-0.0,-0.0,-1.0));
#5760=DIRECTION('',(-0.0,-0.0,-1.0));
#5761=DIRECTION('',(-0.0,-0.0,-1.0));
#5762=DIRECTION('',(-0.0,-0.0,-1.0));
#5763=CARTESIAN_POINT('',(-10.0249996948235,3.4,0.3278600001));
#5764=DIRECTION('',(-0.0,0.0,-1.0));
#5765=DIRECTION('',(-4.80592474324742E-006,-0.999999999988451,0.0));
#5766=DIRECTION('',(-1.0,0.0,0.0));
#5767=CARTESIAN_POINT('',(-9.44999969482495,3.4,0.3278600001));
#5768=DIRECTION('',(0.0,0.0,-1.0));
#5769=DIRECTION('',(-4.8059016478333E-006,0.999999999988452,0.0));
#5770=DIRECTION('',(1.0,0.0,0.0));
#5771=CARTESIAN_POINT('',(-10.0249996948235,3.4,0.3151600001));
#5772=DIRECTION('',(-0.0,0.0,-1.0));
#5773=DIRECTION('',(-4.80592474324742E-006,-0.999999999988451,0.0));
#5774=DIRECTION('',(1.0,0.0,0.0));
#5775=CARTESIAN_POINT('',(-9.44999969482495,3.4,0.3151600001));
#5776=DIRECTION('',(0.0,0.0,-1.0));
#5777=DIRECTION('',(-4.8059016478333E-006,0.999999999988452,0.0));
#5778=DIRECTION('',(-1.0,0.0,0.0));
#5779=DIRECTION('',(-0.0,-0.0,-1.0));
#5780=DIRECTION('',(-0.0,-0.0,-1.0));
#5781=DIRECTION('',(-0.0,-0.0,-1.0));
#5782=DIRECTION('',(-0.0,-0.0,-1.0));
#5783=CARTESIAN_POINT('',(-9.44999969482422,-3.39999999999927,0.3278600001));
#5784=DIRECTION('',(-0.0,0.0,-1.0));
#5785=DIRECTION('',(-1.0,-1.15477070628082E-011,0.0));
#5786=DIRECTION('',(0.0,1.0,0.0));
#5787=CARTESIAN_POINT('',(-9.44999969482422,-5.31249969482422,0.3278600001));
#5788=DIRECTION('',(0.0,0.0,-1.0));
#5789=DIRECTION('',(0.999999999988452,-4.80591319554036E-006,0.0));
#5790=DIRECTION('',(0.0,-1.0,0.0));
#5791=CARTESIAN_POINT('',(-9.44999969482422,-3.39999999999927,0.3151600001));
#5792=DIRECTION('',(-0.0,0.0,-1.0));
#5793=DIRECTION('',(-1.0,-1.15477070628082E-011,0.0));
#5794=DIRECTION('',(0.0,-1.0,0.0));
#5795=CARTESIAN_POINT('',(-9.44999969482422,-5.31249969482422,0.3151600001));
#5796=DIRECTION('',(0.0,0.0,-1.0));
#5797=DIRECTION('',(0.999999999988452,-4.80591319554036E-006,0.0));
#5798=DIRECTION('',(0.0,1.0,0.0));
#5799=DIRECTION('',(-0.0,-0.0,-1.0));
#5800=DIRECTION('',(-0.0,-0.0,-1.0));
#5801=DIRECTION('',(-0.0,-0.0,-1.0));
#5802=DIRECTION('',(-0.0,-0.0,-1.0));
#5803=DIRECTION('',(-1.0,0.0,0.0));
#5804=DIRECTION('',(0.0,-1.0,0.0));
#5805=DIRECTION('',(1.0,0.0,0.0));
#5806=DIRECTION('',(0.0,1.0,0.0));
#5807=DIRECTION('',(-1.0,0.0,0.0));
#5808=DIRECTION('',(0.0,1.0,0.0));
#5809=DIRECTION('',(1.0,0.0,0.0));
#5810=DIRECTION('',(0.0,-1.0,0.0));
#5811=DIRECTION('',(-0.0,-0.0,-1.0));
#5812=DIRECTION('',(-0.0,-0.0,-1.0));
#5813=DIRECTION('',(-0.0,-0.0,-1.0));
#5814=DIRECTION('',(-0.0,-0.0,-1.0));
ENDSEC;
END-ISO-10303-21;
