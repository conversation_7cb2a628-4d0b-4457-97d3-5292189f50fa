#!/usr/bin/env python3
"""
Clean all debug print statements from step_viewer.py
"""

import re

def clean_debug_prints():
    """Remove all debug print statements from step_viewer.py"""
    
    with open('step_viewer.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_lines = len(content.split('\n'))
    
    # Patterns to remove (debug prints)
    debug_patterns = [
        r'^\s*print\(f?"?TARGET.*?\).*$',
        r'^\s*print\(f?"?DEBUG.*?\).*$', 
        r'^\s*print\(f?"?FAIL.*?\).*$',
        r'^\s*print\(f?"?OK.*?\).*$',
        r'^\s*print\(f?"?RED.*?\).*$',
        r'^\s*print\(f?"?BLUE.*?\).*$',
        r'^\s*print\(f?"?CRITICAL.*?\).*$',
        r'^\s*print\(f?"?🔍.*?\).*$',
        r'^\s*print\(f?"?🔢.*?\).*$',
        r'^\s*print\(f?"?✅.*?\).*$',
        r'^\s*print\(f?"?❌.*?\).*$',
        r'^\s*print\(f?"?🖱️.*?\).*$',
        r'^\s*print\(f?"?🔄.*?\).*$',
        r'^\s*print\(f?"?🔧.*?\).*$',
        r'^\s*print\(f?"?\[DEBUG\].*?\).*$',
    ]
    
    lines = content.split('\n')
    cleaned_lines = []
    removed_count = 0
    
    for line in lines:
        should_remove = False
        for pattern in debug_patterns:
            if re.match(pattern, line, re.MULTILINE):
                should_remove = True
                removed_count += 1
                break
        
        if not should_remove:
            cleaned_lines.append(line)
    
    cleaned_content = '\n'.join(cleaned_lines)
    
    # Write cleaned content back
    with open('step_viewer.py', 'w', encoding='utf-8') as f:
        f.write(cleaned_content)
    
    final_lines = len(cleaned_lines)
    
    print(f"🧹 CLEANUP COMPLETE:")
    print(f"   Original lines: {original_lines}")
    print(f"   Debug prints removed: {removed_count}")
    print(f"   Final lines: {final_lines}")
    print(f"   Lines saved: {original_lines - final_lines}")

if __name__ == "__main__":
    clean_debug_prints()
