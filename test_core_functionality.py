#!/usr/bin/env python3
"""
Core Functionality Test - Tests without GUI
"""

import os
import sys
import traceback

def test_imports():
    """Test all core imports"""
    print("=== TESTING IMPORTS ===")
    
    modules = ['step_loader', 'vtk_renderer', 'gui_components']
    results = {}
    
    for module in modules:
        try:
            print(f"Testing {module}...")
            exec(f"import {module}")
            print(f"✅ {module} - OK")
            results[module] = True
        except Exception as e:
            print(f"❌ {module} - FAILED: {e}")
            results[module] = False
            
    # Test step_viewer separately (might have GUI dependencies)
    try:
        print("Testing step_viewer...")
        import step_viewer
        print("✅ step_viewer - OK")
        results['step_viewer'] = True
    except Exception as e:
        print(f"❌ step_viewer - FAILED: {e}")
        results['step_viewer'] = False
        
    return results

def test_step_loading():
    """Test STEP file loading"""
    print("\n=== TESTING STEP LOADING ===")
    
    try:
        from step_loader import STEPLoader
        loader = STEPLoader()
        
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if os.path.exists(step_file):
            print(f"Loading STEP file: {step_file}")
            success, message = loader.load_step_file(step_file)
            
            if success:
                print(f"✅ STEP loading - OK: {message}")
                
                # Check geometry
                if hasattr(loader, 'current_polydata') and loader.current_polydata:
                    points = loader.current_polydata.GetNumberOfPoints()
                    cells = loader.current_polydata.GetNumberOfCells()
                    print(f"   Geometry: {points} points, {cells} cells")
                    
                    if points == 8 and cells == 6:
                        print("   ⚠️  Using fake cube geometry (OpenCASCADE not available)")
                        return "fake_geometry"
                    else:
                        print("   ✅ Real STEP geometry loaded")
                        return "real_geometry"
                else:
                    print("   ❌ No geometry data")
                    return False
            else:
                print(f"❌ STEP loading - FAILED: {message}")
                return False
        else:
            print(f"❌ STEP file not found: {step_file}")
            return False
            
    except Exception as e:
        print(f"❌ STEP loading test failed: {e}")
        traceback.print_exc()
        return False

def test_unified_system():
    """Test unified transformation system"""
    print("\n=== TESTING UNIFIED SYSTEM ===")
    
    try:
        # Import without creating GUI
        import step_viewer
        
        # Check if the class has the unified_transform method
        if hasattr(step_viewer.StepViewerTDK, 'unified_transform'):
            print("✅ unified_transform method found")
            
            # Check helper methods
            helper_methods = ['_unified_load', '_unified_rotate', '_unified_move']
            for method in helper_methods:
                if hasattr(step_viewer.StepViewerTDK, method):
                    print(f"✅ {method} method found")
                else:
                    print(f"❌ {method} method missing")
                    
            return True
        else:
            print("❌ unified_transform method not found")
            return False
            
    except Exception as e:
        print(f"❌ Unified system test failed: {e}")
        traceback.print_exc()
        return False

def test_vtk_basic():
    """Test basic VTK functionality"""
    print("\n=== TESTING VTK BASIC ===")
    
    try:
        import vtk
        print("✅ VTK import - OK")
        
        # Test basic VTK objects
        renderer = vtk.vtkRenderer()
        render_window = vtk.vtkRenderWindow()
        render_window.AddRenderer(renderer)
        
        print("✅ VTK renderer creation - OK")
        
        # Test basic geometry
        cube = vtk.vtkCubeSource()
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(cube.GetOutputPort())
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        
        renderer.AddActor(actor)
        print("✅ VTK geometry creation - OK")
        
        return True
        
    except Exception as e:
        print(f"❌ VTK basic test failed: {e}")
        traceback.print_exc()
        return False

def test_method_integration():
    """Test that all methods call unified_transform correctly"""
    print("\n=== TESTING METHOD INTEGRATION ===")
    
    try:
        import step_viewer
        import inspect
        
        # Get the source code of key methods
        cls = step_viewer.StepViewerTDK
        
        methods_to_check = ['rotate_shape', 'move_shape']
        
        for method_name in methods_to_check:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                source = inspect.getsource(method)
                
                if 'unified_transform' in source:
                    print(f"✅ {method_name} calls unified_transform")
                else:
                    print(f"❌ {method_name} does NOT call unified_transform")
            else:
                print(f"❌ {method_name} method not found")
                
        return True
        
    except Exception as e:
        print(f"❌ Method integration test failed: {e}")
        traceback.print_exc()
        return False

def test_file_structure():
    """Test file structure and dependencies"""
    print("\n=== TESTING FILE STRUCTURE ===")
    
    required_files = [
        'step_viewer.py',
        'step_loader.py', 
        'vtk_renderer.py',
        'gui_components.py',
        'SOIC16P127_1270X940X610L89X51.STEP'
    ]
    
    all_present = True
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} - {size} bytes")
        else:
            print(f"❌ {file} - MISSING")
            all_present = False
            
    return all_present

def run_all_tests():
    """Run all tests and generate report"""
    print("🚀 CORE FUNCTIONALITY TEST SUITE")
    print("=" * 50)
    
    results = {}
    
    # Run tests
    results['imports'] = test_imports()
    results['file_structure'] = test_file_structure()
    results['step_loading'] = test_step_loading()
    results['vtk_basic'] = test_vtk_basic()
    results['unified_system'] = test_unified_system()
    results['method_integration'] = test_method_integration()
    
    # Generate report
    print("\n" + "=" * 50)
    print("TEST RESULTS SUMMARY")
    print("=" * 50)
    
    total_tests = 0
    passed_tests = 0
    
    for test_name, result in results.items():
        if isinstance(result, dict):
            # Import results
            for module, status in result.items():
                total_tests += 1
                if status:
                    passed_tests += 1
                    print(f"✅ {module} import")
                else:
                    print(f"❌ {module} import")
        elif isinstance(result, str):
            # Special cases like fake_geometry
            total_tests += 1
            if result == "fake_geometry":
                passed_tests += 1
                print(f"⚠️  STEP loading (fake geometry - OpenCASCADE missing)")
            elif result == "real_geometry":
                passed_tests += 1
                print(f"✅ STEP loading (real geometry)")
        elif result:
            total_tests += 1
            passed_tests += 1
            print(f"✅ {test_name}")
        else:
            total_tests += 1
            print(f"❌ {test_name}")
    
    print(f"\nSUMMARY: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
        return True
    else:
        print("⚠️  Some tests failed - see details above")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
