#!/usr/bin/env python3
"""
Test actual functionality - buttons, rotations, numbers, origin movement
"""

import sys
import os
import time
import subprocess
from unittest.mock import Mock, patch

def test_button_rotation_numbers():
    """Test that button rotations use correct numbers and call unified system"""
    print("TESTING BUTTON ROTATION NUMBERS")
    print("=" * 40)
    
    test_code = '''
import step_viewer
import sys

# Create test viewer with mocked VTK components
class TestViewer(step_viewer.StepViewerTDK):
    def __init__(self):
        self.active_viewer = "top"
        self.vtk_renderer_left = Mock()
        self.vtk_renderer_right = Mock()
        self.rotation_calls = []
        self.movement_calls = []
        
    def _apply_unified_rotation(self, axis, degrees):
        self.rotation_calls.append((axis, degrees))
        print(f"ROTATION CALLED: axis={axis}, degrees={degrees}")
        return True
        
    def _apply_unified_movement(self, axis, amount):
        self.movement_calls.append((axis, amount))
        print(f"MOVEMENT CALLED: axis={axis}, amount={amount}")
        return True

viewer = TestViewer()

# Test button rotations with specific numbers
print("Testing rotate_shape with different values...")

# Test X-axis rotation
viewer.rotate_shape('x', 15)
if viewer.rotation_calls[-1] == ('x', 15):
    print("PASS: X-axis 15 degrees - correct")
else:
    print("FAIL: X-axis 15 degrees - incorrect:", viewer.rotation_calls[-1])

# Test Y-axis rotation  
viewer.rotate_shape('y', -30)
if viewer.rotation_calls[-1] == ('y', -30):
    print("PASS: Y-axis -30 degrees - correct")
else:
    print("FAIL: Y-axis -30 degrees - incorrect:", viewer.rotation_calls[-1])

# Test Z-axis rotation
viewer.rotate_shape('z', 45)
if viewer.rotation_calls[-1] == ('z', 45):
    print("PASS: Z-axis 45 degrees - correct")
else:
    print("FAIL: Z-axis 45 degrees - incorrect:", viewer.rotation_calls[-1])

print("Testing move_shape with different values...")

# Test X-axis movement
viewer.move_shape('x', 5.5)
if viewer.movement_calls[-1] == ('x', 5.5):
    print("PASS: X-axis 5.5 units - correct")
else:
    print("FAIL: X-axis 5.5 units - incorrect:", viewer.movement_calls[-1])

# Test Y-axis movement
viewer.move_shape('y', -2.3)
if viewer.movement_calls[-1] == ('y', -2.3):
    print("PASS: Y-axis -2.3 units - correct")
else:
    print("FAIL: Y-axis -2.3 units - incorrect:", viewer.movement_calls[-1])

# Test Z-axis movement
viewer.move_shape('z', 10.0)
if viewer.movement_calls[-1] == ('z', 10.0):
    print("PASS: Z-axis 10.0 units - correct")
else:
    print("FAIL: Z-axis 10.0 units - incorrect:", viewer.movement_calls[-1])

print("SUCCESS: All button number tests passed")
'''
    
    try:
        result = subprocess.run([
            sys.executable, '-c', test_code
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and "SUCCESS" in result.stdout:
            print("PASS: Button numbers are correct")
            print("Details:")
            for line in result.stdout.strip().split('\n'):
                if 'PASS:' in line or 'FAIL:' in line or 'CALLED:' in line:
                    print("  ", line)
            return True
        else:
            print("FAIL: Button number test failed")
            if result.stderr:
                print("Error:", result.stderr)
            return False
            
    except Exception as e:
        print("FAIL: Test exception:", e)
        return False

def test_unified_system_integration():
    """Test that unified system actually gets called with correct parameters"""
    print("\nTESTING UNIFIED SYSTEM INTEGRATION")
    print("=" * 40)
    
    test_code = '''
import step_viewer

class TestViewer(step_viewer.StepViewerTDK):
    def __init__(self):
        self.active_viewer = "top"
        self.vtk_renderer_left = Mock()
        self.vtk_renderer_right = Mock()
        self.unified_calls = []
        
    def unified_transform(self, operation, **kwargs):
        self.unified_calls.append((operation, kwargs))
        print(f"UNIFIED_TRANSFORM CALLED: {operation}, {kwargs}")
        return True

from unittest.mock import Mock
viewer = TestViewer()

# Test that button methods call unified_transform
print("Testing button -> unified_transform integration...")

# Test rotate_shape calls unified_transform
viewer.rotate_shape('x', 90)
last_call = viewer.unified_calls[-1]
if last_call[0] == 'rotate' and last_call[1].get('axis') == 'x' and last_call[1].get('degrees') == 90:
    print("PASS: rotate_shape -> unified_transform with correct params")
else:
    print("FAIL: rotate_shape -> unified_transform incorrect:", last_call)

# Test move_shape calls unified_transform  
viewer.move_shape('y', 7.5)
last_call = viewer.unified_calls[-1]
if last_call[0] == 'move' and last_call[1].get('axis') == 'y' and last_call[1].get('amount') == 7.5:
    print("PASS: move_shape -> unified_transform with correct params")
else:
    print("FAIL: move_shape -> unified_transform incorrect:", last_call)

print("SUCCESS: Unified system integration working")
'''
    
    try:
        result = subprocess.run([
            sys.executable, '-c', test_code
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and "SUCCESS" in result.stdout:
            print("PASS: Unified system integration works")
            print("Details:")
            for line in result.stdout.strip().split('\n'):
                if 'PASS:' in line or 'FAIL:' in line or 'CALLED:' in line:
                    print("  ", line)
            return True
        else:
            print("FAIL: Integration test failed")
            if result.stderr:
                print("Error:", result.stderr)
            return False
            
    except Exception as e:
        print("FAIL: Test exception:", e)
        return False

def test_step_file_origin_handling():
    """Test that STEP file origin is handled correctly"""
    print("\nTESTING STEP FILE ORIGIN HANDLING")
    print("=" * 40)
    
    test_code = '''
from step_loader import STEPLoader
import os

loader = STEPLoader()
step_file = "SOIC16P127_1270X940X610L89X51.STEP"

if os.path.exists(step_file):
    success, message = loader.load_step_file(step_file)
    if success:
        print("STEP file loaded successfully")
        
        # Check if origin data was extracted
        if hasattr(loader, 'axis2_placement_data') and loader.axis2_placement_data:
            origin_data = loader.axis2_placement_data
            print(f"PASS: Origin data extracted: {origin_data}")
            
            # Check if coordinates are reasonable
            point = origin_data.get('point', [0, 0, 0])
            if len(point) == 3 and all(isinstance(x, (int, float)) for x in point):
                print(f"PASS: Origin coordinates valid: {point}")
            else:
                print(f"FAIL: Origin coordinates invalid: {point}")
                
        else:
            print("FAIL: No origin data extracted")
            
        # Check geometry
        if hasattr(loader, 'current_polydata') and loader.current_polydata:
            points = loader.current_polydata.GetNumberOfPoints()
            cells = loader.current_polydata.GetNumberOfCells()
            print(f"PASS: Geometry created: {points} points, {cells} cells")
            
            if points > 8:  # More than a simple cube
                print("PASS: Real geometry (not just fake cube)")
            else:
                print("INFO: Using fallback geometry (OpenCASCADE not available)")
        else:
            print("FAIL: No geometry created")
            
    else:
        print(f"FAIL: STEP loading failed: {message}")
else:
    print("FAIL: STEP file not found")

print("SUCCESS: Origin handling test completed")
'''
    
    try:
        result = subprocess.run([
            sys.executable, '-c', test_code
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and "SUCCESS" in result.stdout:
            print("PASS: Origin handling works")
            print("Details:")
            for line in result.stdout.strip().split('\n'):
                if any(keyword in line for keyword in ['PASS:', 'FAIL:', 'INFO:', 'Origin', 'Geometry']):
                    print("  ", line)
            return True
        else:
            print("FAIL: Origin handling test failed")
            return False
            
    except Exception as e:
        print("FAIL: Test exception:", e)
        return False

def test_viewer_switching():
    """Test that active viewer switching works correctly"""
    print("\nTESTING VIEWER SWITCHING")
    print("=" * 40)
    
    test_code = '''
import step_viewer
from unittest.mock import Mock

class TestViewer(step_viewer.StepViewerTDK):
    def __init__(self):
        self.active_viewer = "top"
        self.vtk_renderer_left = Mock()
        self.vtk_renderer_right = Mock()
        self.operations = []
        
    def _apply_unified_rotation(self, axis, degrees):
        self.operations.append(f"{self.active_viewer}: rotate {axis} {degrees}")
        return True
        
    def _apply_unified_movement(self, axis, amount):
        self.operations.append(f"{self.active_viewer}: move {axis} {amount}")
        return True

viewer = TestViewer()

# Test operations on top viewer
viewer.active_viewer = "top"
viewer.rotate_shape('x', 10)
viewer.move_shape('y', 5)

# Test operations on bottom viewer  
viewer.active_viewer = "bottom"
viewer.rotate_shape('z', 20)
viewer.move_shape('x', 3)

print("Operations performed:")
for op in viewer.operations:
    print(f"  {op}")

# Check that operations were applied to correct viewers
if "top: rotate x 10" in viewer.operations:
    print("PASS: Top viewer rotation correct")
else:
    print("FAIL: Top viewer rotation incorrect")
    
if "bottom: rotate z 20" in viewer.operations:
    print("PASS: Bottom viewer rotation correct")
else:
    print("FAIL: Bottom viewer rotation incorrect")

print("SUCCESS: Viewer switching test completed")
'''
    
    try:
        result = subprocess.run([
            sys.executable, '-c', test_code
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and "SUCCESS" in result.stdout:
            print("PASS: Viewer switching works")
            print("Details:")
            for line in result.stdout.strip().split('\n'):
                if any(keyword in line for keyword in ['PASS:', 'FAIL:', 'Operations', ':']):
                    print("  ", line)
            return True
        else:
            print("FAIL: Viewer switching test failed")
            return False
            
    except Exception as e:
        print("FAIL: Test exception:", e)
        return False

def main():
    """Run all functional tests"""
    print("COMPREHENSIVE FUNCTIONAL TESTING")
    print("=" * 60)
    print("Testing actual button behavior, numbers, origin movement...")
    print()
    
    results = []
    results.append(test_button_rotation_numbers())
    results.append(test_unified_system_integration())
    results.append(test_step_file_origin_handling())
    results.append(test_viewer_switching())
    
    print("\n" + "=" * 60)
    print("FUNCTIONAL TEST RESULTS")
    print("=" * 60)
    
    test_names = [
        "Button rotation numbers",
        "Unified system integration", 
        "STEP file origin handling",
        "Viewer switching"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "PASS" if result else "FAIL"
        print(f"{status}: {name}")
        if result:
            passed += 1
    
    print(f"\nSUMMARY: {passed}/{len(results)} functional tests passed")
    
    if passed == len(results):
        print("\nALL FUNCTIONAL TESTS PASSED!")
        print("The system correctly handles:")
        print("- Button rotations with correct numbers")
        print("- Unified system integration")
        print("- STEP file origin extraction")
        print("- Viewer switching")
        return True
    else:
        print(f"\n{len(results)-passed} functional tests failed")
        print("The system needs fixes for actual functionality")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
