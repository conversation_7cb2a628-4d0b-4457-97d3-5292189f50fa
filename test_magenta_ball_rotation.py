#!/usr/bin/env python3
"""
Test magenta ball rotation with the model
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.getcwd())

def test_magenta_ball_rotation():
    """Test that the magenta ball rotates with the model"""
    print("🔄 TESTING MAGENTA BALL ROTATION")
    print("="*50)
    
    try:
        # Import the main viewer
        from step_viewer import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
        
        # Create Qt application
        app = QApplication(sys.argv)
        
        # Create viewer
        viewer = StepViewerTDK()
        print("✅ StepViewerTDK created successfully")
        
        # Check if we have a STEP file to load
        step_files = [f for f in os.listdir('.') if f.endswith('.STEP') or f.endswith('.step')]
        if not step_files:
            print("❌ No STEP files found in current directory")
            return False
            
        step_file = step_files[0]
        print(f"📁 Using STEP file: {step_file}")
        
        # Load the STEP file
        print(f"\n🔄 Loading STEP file: {step_file}")
        success = viewer.load_step_file_direct(step_file)
        
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        print("✅ STEP file loaded successfully")
        
        # Check initial magenta ball position
        print(f"\n🔍 INITIAL MAGENTA BALL STATE:")
        print("-"*40)
        
        if hasattr(viewer, 'vtk_renderer_left') and hasattr(viewer.vtk_renderer_left, 'part_origin_sphere'):
            ball = viewer.vtk_renderer_left.part_origin_sphere
            if ball:
                initial_pos = ball.GetPosition()
                initial_orient = ball.GetOrientation()
                print(f"✅ Initial position: ({initial_pos[0]:.3f}, {initial_pos[1]:.3f}, {initial_pos[2]:.3f})")
                print(f"✅ Initial orientation: ({initial_orient[0]:.1f}°, {initial_orient[1]:.1f}°, {initial_orient[2]:.1f}°)")
            else:
                print("❌ Magenta ball not found")
                return False
        else:
            print("❌ Renderer or ball not found")
            return False
            
        # Test rotation using button method
        print(f"\n🔄 TESTING ROTATION:")
        print("-"*40)
        print("🔧 Applying X+15° rotation using rotate_shape method...")
        
        # Apply rotation
        viewer.rotate_shape('x', 15.0)
        
        # Check magenta ball position after rotation
        print(f"\n🔍 MAGENTA BALL STATE AFTER ROTATION:")
        print("-"*40)
        
        final_pos = ball.GetPosition()
        final_orient = ball.GetOrientation()
        print(f"✅ Final position: ({final_pos[0]:.3f}, {final_pos[1]:.3f}, {final_pos[2]:.3f})")
        print(f"✅ Final orientation: ({final_orient[0]:.1f}°, {final_orient[1]:.1f}°, {final_orient[2]:.1f}°)")
        
        # Calculate changes
        pos_change = [
            final_pos[0] - initial_pos[0],
            final_pos[1] - initial_pos[1], 
            final_pos[2] - initial_pos[2]
        ]
        orient_change = [
            final_orient[0] - initial_orient[0],
            final_orient[1] - initial_orient[1],
            final_orient[2] - initial_orient[2]
        ]
        
        print(f"\n📊 CHANGES:")
        print("-"*40)
        print(f"Position change: ({pos_change[0]:.3f}, {pos_change[1]:.3f}, {pos_change[2]:.3f})")
        print(f"Orientation change: ({orient_change[0]:.1f}°, {orient_change[1]:.1f}°, {orient_change[2]:.1f}°)")
        
        # Check if ball moved/rotated
        pos_moved = any(abs(change) > 0.001 for change in pos_change)
        orient_changed = any(abs(change) > 0.1 for change in orient_change)
        
        print(f"\n🎯 RESULTS:")
        print("-"*40)
        if pos_moved:
            print("✅ Ball POSITION changed - ball is rotating around model center!")
        else:
            print("❌ Ball position did NOT change - ball is not following model rotation")
            
        if orient_changed:
            print("✅ Ball ORIENTATION changed - ball is rotating with model!")
        else:
            print("❌ Ball orientation did NOT change - ball is not rotating")
            
        # Overall result
        if pos_moved or orient_changed:
            print("\n🎉 SUCCESS: Magenta ball is rotating with the model!")
            return True
        else:
            print("\n❌ FAILURE: Magenta ball is NOT rotating with the model")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_magenta_ball_rotation()
