#!/usr/bin/env python3
"""
Simple test to verify STEP file loading works
Focus: Just load a STEP file and verify it displays
"""

import sys
import os

def test_step_loading():
    """Test basic STEP file loading"""
    print("🔍 TESTING STEP FILE LOADING")
    print("=" * 40)
    
    # Check if STEP file exists
    step_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if not os.path.exists(step_file):
        print(f"❌ STEP file not found: {step_file}")
        return False
    
    print(f"✅ Found STEP file: {step_file}")
    
    try:
        # Import PyQt5 first
        from PyQt5.QtWidgets import QApplication
        
        # Create QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            print("✅ Created QApplication")
        
        # Import the viewer
        from step_viewer import StepViewerTDK
        
        # Create viewer
        print("Creating viewer...")
        viewer = StepViewerTDK()
        print("✅ Viewer created")
        
        # Set active viewer to top
        viewer.active_viewer = "top"
        print("✅ Set active viewer to TOP")
        
        # Try to load the STEP file
        print(f"\n🔄 Loading STEP file: {step_file}")
        result = viewer.load_step_file_direct(step_file)
        
        if result:
            print("✅ STEP file loaded successfully!")
            
            # Check if we have polydata
            if hasattr(viewer.step_loader_left, 'current_polydata') and viewer.step_loader_left.current_polydata:
                polydata = viewer.step_loader_left.current_polydata
                points = polydata.GetNumberOfPoints()
                cells = polydata.GetNumberOfCells()
                print(f"✅ Polydata created: {points} points, {cells} cells")
                
                # Check if VTK actors were created (multi-actor system)
                if hasattr(viewer.vtk_renderer_left, 'step_actors') and viewer.vtk_renderer_left.step_actors:
                    print("✅ VTK multi-actors created")
                    total_visible = 0
                    for i, actor in enumerate(viewer.vtk_renderer_left.step_actors):
                        visibility = actor.GetVisibility()
                        print(f"✅ Actor {i+1} visibility: {visibility}")
                        if visibility:
                            total_visible += 1

                    if total_visible > 0:
                        print(f"\n🎉 SUCCESS: STEP file loaded with {total_visible} visible actors!")
                        return True
                    else:
                        print("❌ No actors are visible")
                        return False
                elif hasattr(viewer.vtk_renderer_left, 'step_actor') and viewer.vtk_renderer_left.step_actor:
                    print("✅ VTK single actor created")
                    visibility = viewer.vtk_renderer_left.step_actor.GetVisibility()
                    print(f"✅ Actor visibility: {visibility}")

                    if visibility:
                        print("\n🎉 SUCCESS: STEP file loaded and should be visible!")
                        return True
                    else:
                        print("❌ Actor is not visible")
                        return False
                else:
                    print("❌ No VTK actor created")
                    return False
            else:
                print("❌ No polydata created")
                return False
        else:
            print("❌ STEP file loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test"""
    success = test_step_loading()
    
    if success:
        print("\n✅ STEP FILE LOADING WORKS!")
        print("The unified system is properly loading and displaying STEP files.")
        print("\nNext steps:")
        print("1. Run the actual GUI: python step_viewer.py")
        print("2. Click 'Open STEP File' and select the STEP file")
        print("3. Verify the 3D model appears in the viewer")
    else:
        print("\n❌ STEP FILE LOADING FAILED")
        print("There are still issues with the loading system.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
