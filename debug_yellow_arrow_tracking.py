#!/usr/bin/env python3
"""
Debug script to track exactly which yellow arrows are being created and by whom.
This will help identify the source of extra yellow arrows that don't move correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK as StepViewer
import time

class YellowArrowTracker:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewer()
        self.yellow_arrows_before = []
        self.yellow_arrows_after = []
        
    def find_yellow_arrows(self, renderer, stage_name):
        """Find all yellow arrows in the renderer"""
        yellow_arrows = []
        
        if not renderer or not hasattr(renderer, 'renderer'):
            print(f"   ❌ No renderer available")
            return yellow_arrows
            
        # Get all actors from VTK renderer
        actor_collection = renderer.renderer.GetActors()
        if not actor_collection:
            print(f"   ❌ No actor collection")
            return yellow_arrows
            
        actor_collection.InitTraversal()
        actor_count = 0
        
        while True:
            actor = actor_collection.GetNextActor()
            if not actor:
                break
                
            actor_count += 1
            color = actor.GetProperty().GetColor()
            pos = actor.GetPosition()
            
            # Check if this is a yellow arrow (yellow = red + green, no blue)
            if (color[0] > 0.8 and color[1] > 0.8 and color[2] < 0.2):
                # This is a yellow actor
                actor_info = {
                    'actor_number': actor_count,
                    'position': pos,
                    'color': color,
                    'actor_object': actor,
                    'stage': stage_name
                }
                
                # Try to identify the source
                source = self.identify_yellow_arrow_source(actor, renderer)
                actor_info['source'] = source
                
                yellow_arrows.append(actor_info)
                
        return yellow_arrows
        
    def identify_yellow_arrow_source(self, actor, renderer):
        """Try to identify which code created this yellow arrow"""
        # Check if it's a known part origin arrow
        if hasattr(renderer, 'part_origin_x_arrow') and actor == renderer.part_origin_x_arrow:
            return "part_origin_x_arrow (EXPECTED)"
        if hasattr(renderer, 'part_origin_y_arrow') and actor == renderer.part_origin_y_arrow:
            return "part_origin_y_arrow (EXPECTED)"
        if hasattr(renderer, 'part_origin_z_arrow') and actor == renderer.part_origin_z_arrow:
            return "part_origin_z_arrow (EXPECTED)"
            
        # Check if it's in the origin_actors list (world origin - should NOT be yellow)
        if hasattr(renderer, 'origin_actors') and actor in renderer.origin_actors:
            return "origin_actors (UNEXPECTED - world origin should be red/green/blue)"
            
        # Check if it's in step_actors list
        if hasattr(renderer, 'step_actors') and actor in renderer.step_actors:
            return "step_actors (UNEXPECTED - model should not be yellow)"
            
        # Check if it's the main step_actor
        if hasattr(renderer, 'step_actor') and actor == renderer.step_actor:
            return "step_actor (UNEXPECTED - model should not be yellow)"
            
        return "UNKNOWN SOURCE (EXTRA ARROW!)"
        
    def debug_yellow_arrows(self, stage_name):
        """Debug all yellow arrows in both viewers"""
        print(f"\n{'='*60}")
        print(f"🟡 YELLOW ARROW TRACKING - {stage_name}")
        print(f"{'='*60}")
        
        # Debug top viewer
        if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
            print(f"\n📊 TOP VIEWER YELLOW ARROWS:")
            yellow_arrows = self.find_yellow_arrows(self.viewer.vtk_renderer_left, stage_name)
            
            if yellow_arrows:
                for i, arrow in enumerate(yellow_arrows):
                    print(f"   🟡 Yellow Arrow {i+1}:")
                    print(f"      Actor Number: {arrow['actor_number']}")
                    print(f"      Position: ({arrow['position'][0]:.3f}, {arrow['position'][1]:.3f}, {arrow['position'][2]:.3f})")
                    print(f"      Color: ({arrow['color'][0]:.3f}, {arrow['color'][1]:.3f}, {arrow['color'][2]:.3f})")
                    print(f"      Source: {arrow['source']}")
                    
                    # Check if this is an extra arrow
                    if "UNEXPECTED" in arrow['source'] or "UNKNOWN" in arrow['source']:
                        print(f"      🚨 THIS IS AN EXTRA ARROW!")
                        
            else:
                print(f"   ✅ No yellow arrows found")
                
            # Store for comparison
            if stage_name == "INITIAL_STATE":
                self.yellow_arrows_before = yellow_arrows
            elif stage_name == "AFTER_X_MOVEMENT":
                self.yellow_arrows_after = yellow_arrows
                
        # Debug bottom viewer  
        if hasattr(self.viewer, 'vtk_renderer_right') and self.viewer.vtk_renderer_right:
            print(f"\n📊 BOTTOM VIEWER YELLOW ARROWS:")
            yellow_arrows = self.find_yellow_arrows(self.viewer.vtk_renderer_right, stage_name)
            
            if yellow_arrows:
                for i, arrow in enumerate(yellow_arrows):
                    print(f"   🟡 Yellow Arrow {i+1}:")
                    print(f"      Actor Number: {arrow['actor_number']}")
                    print(f"      Position: ({arrow['position'][0]:.3f}, {arrow['position'][1]:.3f}, {arrow['position'][2]:.3f})")
                    print(f"      Color: ({arrow['color'][0]:.3f}, {arrow['color'][1]:.3f}, {arrow['color'][2]:.3f})")
                    print(f"      Source: {arrow['source']}")
            else:
                print(f"   ✅ No yellow arrows found")
                
    def run_yellow_arrow_test(self):
        """Run the yellow arrow tracking test"""
        print("🟡 STARTING YELLOW ARROW TRACKING")
        print("="*60)
        
        # Show the viewer
        self.viewer.show()
        
        # Wait for GUI to initialize
        QTimer.singleShot(1000, self.load_step_file)
        
        # Start the event loop
        self.app.exec_()
        
    def load_step_file(self):
        """Load a STEP file for testing"""
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            self.app.quit()
            return
            
        print(f"📂 Loading STEP file: {step_file}")
        
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        
        # Load the file
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print(f"❌ Failed to load STEP file")
            self.app.quit()
            return
            
        print(f"✅ STEP file loaded successfully")
        
        # Debug initial state
        QTimer.singleShot(2000, self.debug_initial_state)
        
    def debug_initial_state(self):
        """Debug the initial state after loading"""
        self.debug_yellow_arrows("INITIAL_STATE")
        
        # Perform X+ movement
        QTimer.singleShot(1000, self.perform_x_movement)
        
    def perform_x_movement(self):
        """Perform X+ movement and debug the result"""
        print(f"\n🔄 PERFORMING X+ MOVEMENT (+2.0mm)")
        
        # Ensure top viewer is active
        self.viewer.active_viewer = "top"
        
        # Perform X+ movement
        self.viewer.move_shape('x', 2.0)
        
        # Debug after movement
        QTimer.singleShot(1000, self.debug_after_movement)
        
    def debug_after_movement(self):
        """Debug state after X+ movement"""
        self.debug_yellow_arrows("AFTER_X_MOVEMENT")
        
        # Compare and analyze
        QTimer.singleShot(1000, self.analyze_yellow_arrows)
        
    def analyze_yellow_arrows(self):
        """Analyze the yellow arrow changes"""
        print(f"\n🎯 YELLOW ARROW ANALYSIS")
        print(f"="*60)
        
        print(f"📊 BEFORE X+ MOVEMENT: {len(self.yellow_arrows_before)} yellow arrows")
        print(f"📊 AFTER X+ MOVEMENT: {len(self.yellow_arrows_after)} yellow arrows")
        
        if len(self.yellow_arrows_after) > len(self.yellow_arrows_before):
            extra_count = len(self.yellow_arrows_after) - len(self.yellow_arrows_before)
            print(f"🚨 EXTRA ARROWS DETECTED: {extra_count} new yellow arrows created during movement!")
            
            # Find the new arrows
            print(f"\n🔍 NEW YELLOW ARROWS:")
            for arrow in self.yellow_arrows_after:
                # Check if this arrow existed before
                found_before = False
                for before_arrow in self.yellow_arrows_before:
                    if arrow['actor_object'] == before_arrow['actor_object']:
                        found_before = True
                        break
                        
                if not found_before:
                    print(f"   🆕 NEW: Actor {arrow['actor_number']} at ({arrow['position'][0]:.3f}, {arrow['position'][1]:.3f}, {arrow['position'][2]:.3f})")
                    print(f"       Source: {arrow['source']}")
                    
        # Check for arrows that didn't move correctly
        print(f"\n🔍 MOVEMENT ANALYSIS:")
        for after_arrow in self.yellow_arrows_after:
            for before_arrow in self.yellow_arrows_before:
                if after_arrow['actor_object'] == before_arrow['actor_object']:
                    # Same arrow, check if it moved
                    pos_before = before_arrow['position']
                    pos_after = after_arrow['position']
                    
                    x_moved = abs(pos_after[0] - pos_before[0])
                    
                    if x_moved < 0.1:  # Should have moved ~2mm in X
                        print(f"   🚨 ARROW DIDN'T MOVE: {after_arrow['source']}")
                        print(f"       Before: ({pos_before[0]:.3f}, {pos_before[1]:.3f}, {pos_before[2]:.3f})")
                        print(f"       After:  ({pos_after[0]:.3f}, {pos_after[1]:.3f}, {pos_after[2]:.3f})")
                    else:
                        print(f"   ✅ ARROW MOVED CORRECTLY: {after_arrow['source']}")
                        print(f"       Moved {x_moved:.3f}mm in X direction")
                    break
        
        # Close the application
        QTimer.singleShot(2000, self.app.quit)

if __name__ == "__main__":
    tracker = YellowArrowTracker()
    tracker.run_yellow_arrow_test()
