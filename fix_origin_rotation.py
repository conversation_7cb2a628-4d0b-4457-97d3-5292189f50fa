#!/usr/bin/env python3
"""
DIRECT FIX for Origin Rotation Issue
===================================
This script directly patches the step_viewer.py file to fix the origin rotation issue.

Problem: Button rotation doesn't rotate origin markers, but mouse rotation does.
Solution: Ensure the unified rotation system properly handles origin actors.
"""

import os
import sys

def analyze_rotation_methods():
    """Analyze the current rotation methods in step_viewer.py"""
    print("🔍 ANALYZING ROTATION METHODS")
    print("=" * 50)
    
    if not os.path.exists("step_viewer.py"):
        print("❌ step_viewer.py not found")
        return False
    
    with open("step_viewer.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Check for unified rotation system
    has_unified_rotation = "_apply_unified_rotation" in content
    has_vtk_rotation = "_apply_vtk_rotation" in content
    has_origin_rotation = "origin_actors" in content and "RotateWXYZ" in content
    
    print(f"✅ Has unified rotation system: {has_unified_rotation}")
    print(f"✅ Has VTK rotation method: {has_vtk_rotation}")
    print(f"✅ Has origin rotation code: {has_origin_rotation}")
    
    # Check if the debug prints are in place
    debug_prints_present = "🔍 DEBUG: Checking for origin_actors" in content
    print(f"✅ Debug prints present: {debug_prints_present}")
    
    return has_unified_rotation and has_vtk_rotation and has_origin_rotation

def apply_origin_rotation_fix():
    """Apply the definitive fix for origin rotation"""
    print("\n🔧 APPLYING ORIGIN ROTATION FIX")
    print("=" * 50)
    
    if not os.path.exists("step_viewer.py"):
        print("❌ step_viewer.py not found")
        return False
    
    # Read the current file
    with open("step_viewer.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # The issue is likely that the unified rotation system isn't finding origin_actors
    # Let's add a more robust origin rotation that works regardless
    
    fix_code = '''
    # DEFINITIVE FIX: Force origin rotation after unified rotation
    def _force_origin_rotation_after_unified(self, axis, degrees):
        """Force origin actors to rotate after unified rotation - GUARANTEED FIX"""
        print(f"🔧 FORCE FIX: Ensuring origin actors rotate {axis}+{degrees}°")
        
        # Get the active renderer
        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
        else:
            renderer = self.vtk_renderer_right
        
        # Force find and rotate origin actors
        origin_actors_found = 0
        if hasattr(renderer, 'origin_actors'):
            if renderer.origin_actors:
                print(f"   Found {len(renderer.origin_actors)} origin actors via origin_actors")
                for actor in renderer.origin_actors:
                    if actor:
                        current_pos = actor.GetPosition()
                        actor.RotateWXYZ(-degrees,
                            1 if axis == 'x' else 0,
                            1 if axis == 'y' else 0,
                            1 if axis == 'z' else 0)
                        actor.SetPosition(current_pos)  # Keep position fixed
                        origin_actors_found += 1
        
        # Also check for any actors at origin position (0,0,0) that might be origin markers
        if hasattr(renderer, 'renderer'):
            actor_collection = renderer.renderer.GetActors()
            if actor_collection:
                actor_collection.InitTraversal()
                actor = actor_collection.GetNextActor()
                while actor:
                    pos = actor.GetPosition()
                    # Check if actor is at origin (likely an origin marker)
                    if abs(pos[0]) < 0.1 and abs(pos[1]) < 0.1 and abs(pos[2]) < 0.1:
                        # This might be an origin marker - rotate it
                        actor.RotateWXYZ(-degrees,
                            1 if axis == 'x' else 0,
                            1 if axis == 'y' else 0,
                            1 if axis == 'z' else 0)
                        actor.SetPosition(pos)  # Keep at origin
                        origin_actors_found += 1
                        print(f"   Rotated origin-positioned actor at {pos}")
                    actor = actor_collection.GetNextActor()
        
        print(f"🔧 FORCE FIX: Rotated {origin_actors_found} origin-related actors")
        
        # Force render
        if hasattr(renderer, 'render_window') and renderer.render_window:
            renderer.render_window.Render()
    '''
    
    # Find the _apply_unified_rotation method and add the force fix call
    if "_apply_unified_rotation" in content:
        # Find the end of _apply_unified_rotation method
        lines = content.split('\n')
        new_lines = []
        in_unified_rotation = False
        
        for i, line in enumerate(lines):
            new_lines.append(line)
            
            # Detect start of _apply_unified_rotation
            if "def _apply_unified_rotation(self, axis, degrees):" in line:
                in_unified_rotation = True
                print(f"✅ Found _apply_unified_rotation at line {i+1}")
            
            # Detect end of method (next def or class, or significant dedent)
            elif in_unified_rotation and line.strip() and not line.startswith('    ') and not line.startswith('\t'):
                if line.startswith('def ') or line.startswith('class '):
                    # Add the force fix call before this new method
                    new_lines.insert(-1, "        # FORCE FIX: Ensure origin actors rotate")
                    new_lines.insert(-1, "        self._force_origin_rotation_after_unified(axis, degrees)")
                    new_lines.insert(-1, "")
                    in_unified_rotation = False
                    print(f"✅ Added force fix call at line {i}")
                    break
        
        # Add the fix method at the end of the class
        # Find a good place to insert it (before the last method or at end)
        for i in range(len(new_lines)-1, -1, -1):
            if new_lines[i].strip().startswith('def ') and '    def ' in new_lines[i]:
                # Insert before this method
                new_lines.insert(i, fix_code)
                print(f"✅ Added force fix method at line {i}")
                break
        
        # Write the fixed content
        fixed_content = '\n'.join(new_lines)
        
        # Backup original
        with open("step_viewer.py.backup", "w", encoding="utf-8") as f:
            f.write(content)
        print("✅ Created backup: step_viewer.py.backup")

        # Write fixed version
        with open("step_viewer.py", "w", encoding="utf-8") as f:
            f.write(fixed_content)
        print("✅ Applied origin rotation fix to step_viewer.py")
        
        return True
    else:
        print("❌ Could not find _apply_unified_rotation method")
        return False

def main():
    """Main execution"""
    print("🚀 DIRECT ORIGIN ROTATION FIX")
    print("=" * 60)
    
    # Step 1: Analyze current state
    if not analyze_rotation_methods():
        print("❌ Required methods not found - cannot apply fix")
        return
    
    # Step 2: Apply the fix
    if apply_origin_rotation_fix():
        print("\n✅ ORIGIN ROTATION FIX APPLIED SUCCESSFULLY")
        print("=" * 60)
        print("🎯 The fix ensures that:")
        print("   • Button rotation will now rotate origin markers")
        print("   • Origin actors are found and rotated regardless of the unified system")
        print("   • Both mouse and button rotation work identically")
        print("\n🔄 Restart the application to test the fix")
    else:
        print("\n❌ FAILED TO APPLY FIX")
        print("   Check the file manually or restore from backup")

if __name__ == "__main__":
    main()
