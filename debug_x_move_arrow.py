#!/usr/bin/env python3
"""
DEBUG X+ MOVE ARROW ISSUE: Find the duplicate yellow arrow problem
This will test X+ movement and identify where duplicate arrows are created
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

# Add current directory to path
sys.path.append('.')

def debug_x_move_arrow_issue():
    """Debug the X+ move button to find duplicate arrow issue"""

    print("🐛 DEBUG X+ MOVE ARROW ISSUE WITH LOADED MODEL")
    print("=" * 70)

    # Import and create the application
    from step_viewer import StepViewerTDK

    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()

    # Wait for GUI to initialize
    QTest.qWait(3000)
    print("✅ GUI initialized")

    # CRITICAL: Load a STEP file first to create origin markers
    print("\n🔍 LOADING STEP FILE TO CREATE ORIGIN MARKERS...")
    print("-" * 50)

    # Look for any STEP files in the current directory
    import glob
    step_files = glob.glob("*.step") + glob.glob("*.stp") + glob.glob("*.STEP") + glob.glob("*.STP")

    if step_files:
        step_file = step_files[0]
        print(f"📁 Found STEP file: {step_file}")

        try:
            # Set active viewer to top
            viewer.active_viewer = "top"

            # Load the STEP file using the correct method
            success = viewer.load_step_file_direct(step_file)
            QTest.qWait(5000)  # Wait for loading to complete

            if not success:
                print(f"❌ STEP file loading returned False")
                print("⚠️  Continuing test without loaded model...")
            else:
                print("✅ STEP file loaded successfully")

                # Create origin overlays to ensure we have arrows to test
                viewer.create_origin_overlay()
                QTest.qWait(2000)

                print("✅ Origin overlays created")

        except Exception as e:
            print(f"❌ STEP file loading FAILED: {e}")
            print("⚠️  Continuing test without loaded model...")
    else:
        print("⚠️  No STEP files found in current directory")
        print("⚠️  Continuing test without loaded model...")
    
    # Function to count arrows in top viewer
    def count_arrows_in_top_viewer():
        """Count all arrow-like actors in top viewer"""
        if not hasattr(viewer, 'vtk_renderer_left') or not viewer.vtk_renderer_left:
            return 0, []
        
        renderer = viewer.vtk_renderer_left.renderer
        if not renderer:
            return 0, []
        
        arrow_count = 0
        arrow_details = []
        
        # Get all actors
        actors = renderer.GetActors()
        actors.InitTraversal()
        
        for i in range(actors.GetNumberOfItems()):
            actor = actors.GetNextActor()
            if actor:
                # Check if this looks like an arrow (has specific properties)
                pos = actor.GetPosition()
                orientation = actor.GetOrientation()
                
                # Get actor properties for identification
                bounds = actor.GetBounds()
                size = max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4]) if bounds else 0

                # Look for actors that might be arrows (positioned away from origin OR small size)
                if abs(pos[0]) > 0.1 or abs(pos[1]) > 0.1 or abs(pos[2]) > 0.1 or (size > 1.5 and size < 3.0):
                    arrow_count += 1

                    # Try to identify actor type
                    actor_type = "Unknown"
                    vtk_renderer = viewer.vtk_renderer_left
                    if hasattr(vtk_renderer, 'origin_actors') and actor in vtk_renderer.origin_actors:
                        actor_type = "World Origin"
                    elif hasattr(vtk_renderer, 'part_origin_sphere') and actor == vtk_renderer.part_origin_sphere:
                        actor_type = "Part Origin Sphere"
                    elif hasattr(vtk_renderer, 'part_origin_x_arrow') and actor == vtk_renderer.part_origin_x_arrow:
                        actor_type = "Part Origin X Arrow"
                    elif hasattr(vtk_renderer, 'part_origin_y_arrow') and actor == vtk_renderer.part_origin_y_arrow:
                        actor_type = "Part Origin Y Arrow"
                    elif hasattr(vtk_renderer, 'part_origin_z_arrow') and actor == vtk_renderer.part_origin_z_arrow:
                        actor_type = "Part Origin Z Arrow"
                    elif hasattr(vtk_renderer, 'step_actors') and actor in vtk_renderer.step_actors:
                        actor_type = "Model Actor"
                    elif hasattr(vtk_renderer, 'bbox_actor') and actor == vtk_renderer.bbox_actor:
                        actor_type = "Bounding Box"

                    arrow_details.append({
                        'index': i,
                        'position': pos,
                        'orientation': orientation,
                        'size': size,
                        'type': actor_type,
                        'actor': actor
                    })
        
        return arrow_count, arrow_details
    
    # STEP 1: Count arrows BEFORE X+ move
    print("\n🔍 STEP 1: Counting arrows BEFORE X+ move...")
    print("-" * 50)
    
    before_count, before_arrows = count_arrows_in_top_viewer()
    print(f"📊 BEFORE X+ move: Found {before_count} arrow-like actors in top viewer")
    
    for i, arrow in enumerate(before_arrows):
        print(f"   Arrow {i+1}: {arrow['type']} - pos=({arrow['position'][0]:.3f}, {arrow['position'][1]:.3f}, {arrow['position'][2]:.3f}) size={arrow['size']:.1f}")
    
    # STEP 2: Perform X+ move and capture debug output
    print("\n🔍 STEP 2: Performing X+ move (2.0mm)...")
    print("-" * 50)
    
    try:
        # Set active viewer to top
        viewer.active_viewer = "top"
        
        # Perform X+ move
        viewer.move_shape("x", 2.0)
        QTest.qWait(3000)  # Wait for move to complete
        
        print("✅ X+ move completed")
        
    except Exception as e:
        print(f"❌ X+ move FAILED: {e}")
        import traceback
        traceback.print_exc()
    
    # STEP 3: Count arrows AFTER X+ move
    print("\n🔍 STEP 3: Counting arrows AFTER X+ move...")
    print("-" * 50)
    
    after_count, after_arrows = count_arrows_in_top_viewer()
    print(f"📊 AFTER X+ move: Found {after_count} arrow-like actors in top viewer")
    
    for i, arrow in enumerate(after_arrows):
        print(f"   Arrow {i+1}: {arrow['type']} - pos=({arrow['position'][0]:.3f}, {arrow['position'][1]:.3f}, {arrow['position'][2]:.3f}) size={arrow['size']:.1f}")
    
    # STEP 4: World Origin Actor Analysis
    print("\n🔍 STEP 4: WORLD ORIGIN ACTOR ANALYSIS...")
    print("-" * 50)

    # Check world origin actors specifically
    renderer = viewer.vtk_renderer_left
    if renderer and hasattr(renderer, 'origin_actors') and renderer.origin_actors:
        print(f"🔴 WORLD ORIGIN ACTORS: {len(renderer.origin_actors)} actors")
        for i, actor in enumerate(renderer.origin_actors):
            if actor:
                pos = actor.GetPosition()
                orient = actor.GetOrientation()
                print(f"   World Origin Actor {i}: pos={pos}, orient={orient}")
    else:
        print("🔴 WORLD ORIGIN ACTORS: NONE FOUND")

    # Check part origin actors specifically
    if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
        pos = renderer.part_origin_sphere.GetPosition()
        print(f"🟢 PART ORIGIN SPHERE: pos={pos}")

        if hasattr(renderer, 'part_origin_x_arrow') and renderer.part_origin_x_arrow:
            pos = renderer.part_origin_x_arrow.GetPosition()
            print(f"🟡 PART ORIGIN X ARROW: pos={pos}")
        if hasattr(renderer, 'part_origin_y_arrow') and renderer.part_origin_y_arrow:
            pos = renderer.part_origin_y_arrow.GetPosition()
            print(f"🟡 PART ORIGIN Y ARROW: pos={pos}")
        if hasattr(renderer, 'part_origin_z_arrow') and renderer.part_origin_z_arrow:
            pos = renderer.part_origin_z_arrow.GetPosition()
            print(f"🟡 PART ORIGIN Z ARROW: pos={pos}")
    else:
        print("🟢 PART ORIGIN ACTORS: NONE FOUND")

    # STEP 5: Arrow Count Analysis
    print("\n🔍 STEP 5: ARROW COUNT ANALYSIS...")
    print("-" * 50)
    
    if after_count > before_count:
        print(f"🚨 DUPLICATE ARROW DETECTED!")
        print(f"   Before: {before_count} arrows")
        print(f"   After:  {after_count} arrows")
        print(f"   Extra:  {after_count - before_count} arrows created")
        
        # Find the new arrows
        print("\n🔍 NEW ARROWS CREATED:")
        for i, after_arrow in enumerate(after_arrows):
            is_new = True
            for before_arrow in before_arrows:
                # Check if positions are similar (within 0.1mm)
                pos_diff = sum(abs(a - b) for a, b in zip(after_arrow['position'], before_arrow['position']))
                if pos_diff < 0.1:
                    is_new = False
                    break
            
            if is_new:
                print(f"   🆕 NEW Arrow: {after_arrow['type']} - pos=({after_arrow['position'][0]:.3f}, {after_arrow['position'][1]:.3f}, {after_arrow['position'][2]:.3f}) size={after_arrow['size']:.1f}")
        
    elif after_count == before_count:
        print(f"✅ NO DUPLICATE ARROWS - Count stayed the same ({after_count})")
        
        # Check if arrows moved correctly
        print("\n🔍 ARROW MOVEMENT ANALYSIS:")
        for i, (before, after) in enumerate(zip(before_arrows, after_arrows)):
            x_moved = after['position'][0] - before['position'][0]
            y_moved = after['position'][1] - before['position'][1] 
            z_moved = after['position'][2] - before['position'][2]
            
            print(f"   Arrow {i+1}: moved X={x_moved:.3f} Y={y_moved:.3f} Z={z_moved:.3f}")
            
            if abs(x_moved - 2.0) < 0.1:
                print(f"      ✅ Correctly moved +2.0mm in X direction")
            else:
                print(f"      ❌ Did NOT move +2.0mm in X direction (expected 2.0, got {x_moved:.3f})")
    
    else:
        print(f"⚠️  ARROWS DISAPPEARED - Count decreased from {before_count} to {after_count}")
    
    # Final summary
    print("\n" + "=" * 70)
    print("🏁 DEBUG X+ MOVE ARROW ISSUE COMPLETED")
    print("=" * 70)
    
    if after_count > before_count:
        print("🚨 PROBLEM CONFIRMED: Duplicate arrows are being created during X+ move")
        print("🔧 NEXT STEP: Investigate arrow creation code in movement functions")
    else:
        print("✅ NO DUPLICATE ARROW ISSUE DETECTED")
    
    print("=" * 70)
    
    # CRITICAL: Properly close the application to prevent hanging
    print("\n🔚 CLOSING APPLICATION...")
    try:
        if hasattr(viewer, 'close'):
            viewer.close()
        if hasattr(app, 'quit'):
            app.quit()
        if hasattr(app, 'exit'):
            app.exit(0)
    except Exception as e:
        print(f"⚠️  Error during cleanup: {e}")

    print("✅ Debug program completed and closed properly")
    return after_count > before_count

if __name__ == "__main__":
    has_duplicate = debug_x_move_arrow_issue()
    print(f"\n🎯 Debug completed. Duplicate arrow issue: {'CONFIRMED' if has_duplicate else 'NOT FOUND'}")
    sys.exit(1 if has_duplicate else 0)
