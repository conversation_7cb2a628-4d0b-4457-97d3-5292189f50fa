#!/usr/bin/env python3
"""
COMPREHENSIVE TEST: ALL 12 BUTTONS (6 rotation + 6 movement)
Tests that ALL actors move correctly for EVERY button
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK
import time

def check_all_actors(viewer, test_name, expected_behavior):
    """Check positions of ALL actors (world origin, local origin, model)"""
    print(f"\n🔍 {test_name} - CHECKING ALL ACTORS:")
    
    renderer = viewer.vtk_renderer_left
    if not hasattr(renderer, 'renderer'):
        print("   ❌ No renderer found")
        return False
        
    red_actors = []  # World origin
    green_actors = []  # Local origin  
    model_actors = []  # Model
    
    actor_collection = renderer.renderer.GetActors()
    if actor_collection:
        actor_collection.InitTraversal()
        actor = actor_collection.GetNextActor()
        while actor:
            color = actor.GetProperty().GetColor()
            pos = actor.GetPosition()
            
            # Red actors (world origin markers)
            if color[0] > 0.8 and color[1] < 0.3 and color[2] < 0.3:
                red_actors.append(pos)
            # Green actors (local origin markers)  
            elif color[0] < 0.3 and color[1] > 0.8 and color[2] < 0.3:
                green_actors.append(pos)
            # Model actors (large actors that aren't markers)
            else:
                bounds = actor.GetBounds()
                if bounds:
                    size = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])
                    if size > 5.0:  # Large actors are likely model parts
                        model_actors.append(pos)
            
            actor = actor_collection.GetNextActor()
    
    # Check results based on expected behavior
    success = True
    
    print(f"   📊 Found: {len(red_actors)} red, {len(green_actors)} green, {len(model_actors)} model actors")
    
    # Check world origin actors
    if expected_behavior == "ROTATION":
        # During rotation: world origin should stay at (0,0,0)
        world_at_origin = all(abs(pos[0]) < 0.001 and abs(pos[1]) < 0.001 and abs(pos[2]) < 0.001 for pos in red_actors)
        if world_at_origin:
            print(f"   ✅ World origin: All {len(red_actors)} actors at (0,0,0) - CORRECT for rotation")
        else:
            print(f"   ❌ World origin: Some actors moved from (0,0,0) - WRONG for rotation")
            success = False
            
        # During rotation: local origin should move with model (not at 0,0,0)
        local_moved = any(abs(pos[0]) > 0.001 or abs(pos[1]) > 0.001 or abs(pos[2]) > 0.001 for pos in green_actors)
        if local_moved:
            print(f"   ✅ Local origin: Moved with model - CORRECT for rotation")
        else:
            print(f"   ❌ Local origin: Stayed at (0,0,0) - WRONG for rotation")
            success = False
            
    elif expected_behavior == "MOVEMENT":
        # FIXED: During movement, check if world origin position changed correctly
        # The world origin should reflect the actual movement, even if result is (0,0,0)

        # Check if world origin text shows the correct moved position
        world_correct = False
        if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
            # Get the world origin display text to see if it shows moved position
            display_data = viewer._calculate_unified_display_numbers("top")
            world_text = display_data.get('world_origin', '')

            # Extract position from world origin text
            import re
            origin_match = re.search(r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', world_text)
            if origin_match:
                world_x, world_y, world_z = map(float, origin_match.groups())
                # Check if the position reflects actual red actor position
                if red_actors:
                    red_pos = red_actors[0]
                    tolerance = 0.1
                    if (abs(world_x - red_pos[0]) < tolerance and
                        abs(world_y - red_pos[1]) < tolerance and
                        abs(world_z - red_pos[2]) < tolerance):
                        world_correct = True
                        print(f"   ✅ World origin: Shows correct position ({world_x:.1f}, {world_y:.1f}, {world_z:.1f}) matching red actor")
                    else:
                        print(f"   ❌ World origin: Text shows ({world_x:.1f}, {world_y:.1f}, {world_z:.1f}) but red actor at ({red_pos[0]:.1f}, {red_pos[1]:.1f}, {red_pos[2]:.1f})")
                        success = False
                else:
                    print(f"   ❌ World origin: No red actors found")
                    success = False
            else:
                print(f"   ❌ World origin: Could not parse position from text: {world_text}")
                success = False
        else:
            print(f"   ❌ World origin: No renderer found")
            success = False

        # Check local origin (should always move with model, never at original 0,0,0)
        local_moved = any(abs(pos[0]) > 0.001 or abs(pos[1]) > 0.001 or abs(pos[2]) > 0.001 for pos in green_actors)

        if local_moved:
            print(f"   ✅ Local origin: Moved with model - CORRECT for movement")
        else:
            print(f"   ❌ Local origin: Stayed at (0,0,0) - WRONG for movement")
            success = False
    
    return success

def test_all_12_buttons():
    """Test ALL 12 buttons (6 rotation + 6 movement) with comprehensive actor checking"""
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load STEP file
    step_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if os.path.exists(step_file):
        print(f"📁 Loading STEP file: {step_file}")
        viewer.load_step_file_direct(step_file)
        time.sleep(3)
        print("✅ STEP file loaded")
    else:
        print("❌ No STEP file found")
        app.quit()
        return
    
    print("\n" + "="*80)
    print("🎯 TESTING ALL 12 BUTTONS - COMPREHENSIVE ACTOR VERIFICATION")
    print("="*80)
    
    # Test all 6 rotation buttons
    rotation_tests = [
        ("X+ Rotation", lambda: viewer.rotate_shape('x', 15.0), "ROTATION"),
        ("X- Rotation", lambda: viewer.rotate_shape('x', -15.0), "ROTATION"),
        ("Y+ Rotation", lambda: viewer.rotate_shape('y', 15.0), "ROTATION"),
        ("Y- Rotation", lambda: viewer.rotate_shape('y', -15.0), "ROTATION"),
        ("Z+ Rotation", lambda: viewer.rotate_shape('z', 15.0), "ROTATION"),
        ("Z- Rotation", lambda: viewer.rotate_shape('z', -15.0), "ROTATION"),
    ]
    
    # Test all 6 movement buttons  
    movement_tests = [
        ("X+ Movement", lambda: viewer.move_shape('x', 2.0), "MOVEMENT"),
        ("X- Movement", lambda: viewer.move_shape('x', -2.0), "MOVEMENT"),
        ("Y+ Movement", lambda: viewer.move_shape('y', 2.0), "MOVEMENT"),
        ("Y- Movement", lambda: viewer.move_shape('y', -2.0), "MOVEMENT"),
        ("Z+ Movement", lambda: viewer.move_shape('z', 2.0), "MOVEMENT"),
        ("Z- Movement", lambda: viewer.move_shape('z', -2.0), "MOVEMENT"),
    ]
    
    all_tests = rotation_tests + movement_tests
    results = []
    
    for test_name, test_func, expected_behavior in all_tests:
        print(f"\n🔄 PERFORMING: {test_name}")
        
        # Perform the operation
        test_func()
        time.sleep(1.5)  # Allow operation to complete
        
        # Check all actors
        result = check_all_actors(viewer, f"AFTER {test_name}", expected_behavior)
        results.append((test_name, result, expected_behavior))
        
        if result:
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    # Final summary
    print("\n" + "="*80)
    print("🎯 FINAL RESULTS - ALL 12 BUTTONS")
    print("="*80)
    
    rotation_passed = 0
    rotation_failed = 0
    movement_passed = 0
    movement_failed = 0
    
    for test_name, result, expected_behavior in results:
        if result:
            print(f"   ✅ PASSED: {test_name}")
            if expected_behavior == "ROTATION":
                rotation_passed += 1
            else:
                movement_passed += 1
        else:
            print(f"   ❌ FAILED: {test_name}")
            if expected_behavior == "ROTATION":
                rotation_failed += 1
            else:
                movement_failed += 1
    
    print(f"\n📊 ROTATION BUTTONS: {rotation_passed} PASSED, {rotation_failed} FAILED out of 6")
    print(f"📊 MOVEMENT BUTTONS: {movement_passed} PASSED, {movement_failed} FAILED out of 6")
    print(f"📊 TOTAL: {rotation_passed + movement_passed} PASSED, {rotation_failed + movement_failed} FAILED out of 12")
    
    if rotation_failed == 0 and movement_failed == 0:
        print("\n🎉 ALL 12 BUTTONS WORKING CORRECTLY!")
        print("   ✅ All rotation buttons: World origin at (0,0,0), Local origin moves")
        print("   ✅ All movement buttons: Both origins move with model")
    else:
        print(f"\n❌ {rotation_failed + movement_failed} BUTTONS FAILED!")
        print("   🔧 FIXES NEEDED!")
    
    print("="*80)
    
    # Keep app running briefly
    QTimer.singleShot(5000, app.quit)
    app.exec_()

if __name__ == "__main__":
    test_all_12_buttons()
