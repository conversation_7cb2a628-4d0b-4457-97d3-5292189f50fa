#!/usr/bin/env python3

# Create a simple STEP file with a cube at position (5, 3, 2) for testing

from OCC.Core import gp_Pnt, gp_Vec, gp_Ax2, gp_Dir
from OCC.Core import BRep<PERSON><PERSON><PERSON>I_MakeBox
from OCC.Core import ST<PERSON><PERSON><PERSON><PERSON>_Writer, STEPControl_AsIs
from OCC.Core import Interface_Static

def create_test_cube():
    """Create a simple cube at position (5, 3, 2)"""
    
    # Create a cube with dimensions 2x2x2 at position (5, 3, 2)
    corner_point = gp_Pnt(5.0, 3.0, 2.0)
    
    # Create the cube
    cube_maker = BRepPrimAPI_MakeBox(corner_point, 2.0, 2.0, 2.0)
    cube_shape = cube_maker.Shape()
    
    # Write to STEP file
    step_writer = STEPControl_Writer()
    Interface_Static.SetCVal("write.step.schema", "AP203")
    
    # Transfer the shape
    step_writer.Transfer(cube_shape, STEPControl_AsIs)
    
    # Write the file
    status = step_writer.Write("test_cube.step")
    
    if status == 1:  # IFSelect_RetDone
        print("✅ Created test_cube.step with cube at position (5, 3, 2)")
        print("   Cube dimensions: 2x2x2")
        print("   Cube center should be at: (6, 4, 3)")
        return True
    else:
        print("❌ Failed to create test_cube.step")
        return False

if __name__ == "__main__":
    create_test_cube()
