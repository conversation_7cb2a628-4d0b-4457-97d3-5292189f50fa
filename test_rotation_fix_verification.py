#!/usr/bin/env python3
"""
TEST: Verify rotation fix - buttons now use same method as mouse
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK
import time

def test_rotation_fix():
    """Test that button rotation now uses same method as mouse rotation"""
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load STEP file
    step_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if os.path.exists(step_file):
        print(f"📁 Loading STEP file: {step_file}")
        viewer.load_step_file_direct(step_file)
        time.sleep(3)
        print("✅ STEP file loaded")
    else:
        print("❌ No STEP file found")
        app.quit()
        return
    
    print("\n" + "="*80)
    print("🎯 TESTING ROTATION FIX")
    print("="*80)
    
    # Test one rotation to verify the fix
    print("\n🔄 Testing X+ rotation (15°)")
    viewer.rotate_shape('x', 15.0)
    time.sleep(2)
    
    # Check world origin positions
    renderer = viewer.vtk_renderer_left
    if hasattr(renderer, 'renderer'):
        red_actors_found = 0
        all_at_origin = True
        
        actor_collection = renderer.renderer.GetActors()
        if actor_collection:
            actor_collection.InitTraversal()
            actor = actor_collection.GetNextActor()
            while actor:
                color = actor.GetProperty().GetColor()
                # Check if this is a red actor (world origin marker)
                if color[0] > 0.8 and color[1] < 0.3 and color[2] < 0.3:
                    pos = actor.GetPosition()
                    red_actors_found += 1
                    if abs(pos[0]) < 0.001 and abs(pos[1]) < 0.001 and abs(pos[2]) < 0.001:
                        print(f"   ✅ Red actor {red_actors_found}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) - AT ORIGIN")
                    else:
                        print(f"   ❌ Red actor {red_actors_found}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) - MOVED!")
                        all_at_origin = False
                actor = actor_collection.GetNextActor()
        
        if red_actors_found == 0:
            print("   ❌ No red actors found!")
        elif all_at_origin:
            print(f"   ✅ SUCCESS: All {red_actors_found} world origin actors stayed at (0,0,0)")
        else:
            print(f"   ❌ FAILED: Some world origin actors moved away from (0,0,0)")
    
    print("="*80)
    
    # Keep app running briefly
    QTimer.singleShot(3000, app.quit)
    app.exec_()

if __name__ == "__main__":
    test_rotation_fix()
