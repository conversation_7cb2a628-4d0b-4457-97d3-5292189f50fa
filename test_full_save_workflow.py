#!/usr/bin/env python3
"""
Test the complete save workflow with <PERSON><PERSON>
"""

import sys
import os
import time
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

print("=== FULL SAVE WORKFLOW TEST ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()
viewer.show()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Testing with: {test_file}")
    
    def run_test():
        try:
            print("\n1. Loading STEP file...")
            viewer.active_viewer = "top"
            success = viewer.load_step_file_direct(test_file)
            
            if success:
                print("✅ File loaded successfully")
                
                # Get original bounds and rotation
                renderer = viewer.vtk_renderer_left
                if hasattr(renderer, 'step_actors') and renderer.step_actors:
                    original_actor = renderer.step_actors[0]
                    original_bounds = original_actor.GetBounds()
                    print(f"Original bounds: {original_bounds}")
                    
                    # Get original rotation values
                    orig_rot = viewer._extract_rotation_from_vtk_actor("top")
                    print(f"Original rotation: {orig_rot}")
                    
                    print("\n2. Applying rotation...")
                    # Apply 90 degree X rotation
                    viewer._apply_model_rotation("top", "x", 90.0)
                    
                    # Get rotated bounds and rotation
                    rotated_bounds = original_actor.GetBounds()
                    current_rot = viewer._extract_rotation_from_vtk_actor("top")
                    print(f"Rotated bounds: {rotated_bounds}")
                    print(f"Current rotation: {current_rot}")
                    
                    # Check if bounds changed
                    bounds_changed = any(abs(a - b) > 0.01 for a, b in zip(original_bounds, rotated_bounds))
                    print(f"Bounds changed: {bounds_changed}")
                    
                    if bounds_changed:
                        print("✅ Visual rotation working")
                        
                        print("\n3. Testing save...")
                        test_save_file = "test_full_workflow.step"
                        
                        if os.path.exists(test_save_file):
                            os.remove(test_save_file)
                        
                        # Get values for save
                        loader = viewer.step_loader_left
                        orig_rot_save = viewer.orig_rot_left if hasattr(viewer, 'orig_rot_left') else {'x': 0, 'y': 0, 'z': 0}
                        
                        print(f"Save parameters:")
                        print(f"  Current rotation: {current_rot}")
                        print(f"  Original rotation: {orig_rot_save}")
                        print(f"  Delta: X={current_rot['x'] - orig_rot_save['x']:.1f}°")
                        
                        # Call the simple save method
                        print("Calling _save_step_simple...")
                        success = viewer._save_step_simple(test_save_file, loader, current_rot, orig_rot_save)
                        
                        if success and os.path.exists(test_save_file):
                            file_size = os.path.getsize(test_save_file)
                            print(f"✅ Save successful: {file_size} bytes")
                            
                            print("\n4. Loading saved file...")
                            viewer.active_viewer = "bottom"
                            load_success = viewer.load_step_file_direct(test_save_file)
                            
                            if load_success:
                                print("✅ Saved file loaded")
                                
                                # Get saved file properties
                                bottom_renderer = viewer.vtk_renderer_right
                                if hasattr(bottom_renderer, 'step_actors') and bottom_renderer.step_actors:
                                    saved_actor = bottom_renderer.step_actors[0]
                                    saved_bounds = saved_actor.GetBounds()
                                    saved_rot = viewer._extract_rotation_from_vtk_actor("bottom")
                                    
                                    print(f"Saved file bounds: {saved_bounds}")
                                    print(f"Saved file rotation: {saved_rot}")
                                    
                                    # Compare bounds
                                    bounds_match = all(abs(a - b) < 0.1 for a, b in zip(rotated_bounds, saved_bounds))
                                    print(f"Saved bounds match rotated bounds: {bounds_match}")
                                    
                                    if bounds_match:
                                        print("\n🎉 SUCCESS: Rotation save system is working!")
                                        print("✅ Geometry is correctly rotated in saved file")
                                        print("✅ Visual appearance is preserved")
                                        print("✅ Bounds match the rotated model")
                                    else:
                                        print("\n❌ FAILURE: Saved geometry doesn't match rotated geometry")
                                        print("The transformation is not being applied correctly")
                                else:
                                    print("❌ No actors in saved file")
                            else:
                                print("❌ Failed to load saved file")
                            
                            # Clean up
                            os.remove(test_save_file)
                        else:
                            print("❌ Save failed")
                    else:
                        print("❌ Visual rotation not working")
                else:
                    print("❌ No actors found")
            else:
                print("❌ Failed to load file")
                
        except Exception as e:
            print(f"❌ Error during test: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n=== TEST FINISHED ===")
        app.quit()
    
    # Run test after a short delay to let GUI initialize
    QTimer.singleShot(1000, run_test)
    
    app.exec_()
else:
    print("❌ No STEP files found")
    app.quit()
