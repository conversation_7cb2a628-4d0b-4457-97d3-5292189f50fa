#!/usr/bin/env python3
"""
Test script to verify the duplicate arrow fix.
This script will:
1. Load the STEP viewer
2. Load a STEP file
3. Perform X+ movement
4. Check that no duplicate arrows are created
5. Verify model actors are positioned correctly
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK as <PERSON><PERSON><PERSON><PERSON>

def test_duplicate_arrow_fix():
    """Test the duplicate arrow fix"""
    print("🧪 TESTING DUPLICATE ARROW FIX")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    viewer = StepViewer()
    viewer.show()
    
    # Load a STEP file
    step_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if os.path.exists(step_file):
        print(f"📁 Loading STEP file: {step_file}")
        viewer.load_step_file_direct(step_file, "top")
        
        # Wait for loading to complete
        app.processEvents()
        time.sleep(1)
        
        # Get initial actor positions
        renderer = viewer.vtk_renderer_left
        print("\n🔍 BEFORE X+ MOVEMENT:")
        print("-" * 30)
        
        # Count and position model actors
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            print(f"📊 Model actors: {len(renderer.step_actors)}")
            for i, actor in enumerate(renderer.step_actors):
                pos = actor.GetPosition()
                print(f"   Model Actor {i}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # Count and position world origin actors
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            print(f"📊 World origin actors: {len(renderer.origin_actors)}")
            for i, actor in enumerate(renderer.origin_actors):
                pos = actor.GetPosition()
                print(f"   World Origin Actor {i}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # Count and position part origin actors
        if hasattr(renderer, 'part_origin_actors') and renderer.part_origin_actors:
            print(f"📊 Part origin actors: {len(renderer.part_origin_actors)}")
            for i, actor in enumerate(renderer.part_origin_actors):
                pos = actor.GetPosition()
                print(f"   Part Origin Actor {i}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # Perform X+ movement
        print("\n🔄 PERFORMING X+ MOVEMENT (+2.0mm)")
        print("-" * 40)
        viewer.move_x_plus()
        
        # Wait for movement to complete
        app.processEvents()
        time.sleep(0.5)
        
        print("\n🔍 AFTER X+ MOVEMENT:")
        print("-" * 30)
        
        # Check final actor positions
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            print(f"📊 Model actors: {len(renderer.step_actors)}")
            for i, actor in enumerate(renderer.step_actors):
                pos = actor.GetPosition()
                print(f"   Model Actor {i}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # Check world origin actors
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            print(f"📊 World origin actors: {len(renderer.origin_actors)}")
            for i, actor in enumerate(renderer.origin_actors):
                pos = actor.GetPosition()
                print(f"   World Origin Actor {i}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # Check part origin actors
        if hasattr(renderer, 'part_origin_actors') and renderer.part_origin_actors:
            print(f"📊 Part origin actors: {len(renderer.part_origin_actors)}")
            for i, actor in enumerate(renderer.part_origin_actors):
                pos = actor.GetPosition()
                print(f"   Part Origin Actor {i}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # Verify no duplicates at same position
        print("\n✅ DUPLICATE CHECK:")
        print("-" * 20)
        
        all_positions = []
        
        # Collect all actor positions
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            for actor in renderer.step_actors:
                pos = actor.GetPosition()
                all_positions.append(("Model", pos))
        
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            for actor in renderer.origin_actors:
                pos = actor.GetPosition()
                all_positions.append(("World Origin", pos))
        
        if hasattr(renderer, 'part_origin_actors') and renderer.part_origin_actors:
            for actor in renderer.part_origin_actors:
                pos = actor.GetPosition()
                all_positions.append(("Part Origin", pos))
        
        # Check for duplicates at same position
        duplicates_found = False
        for i, (type1, pos1) in enumerate(all_positions):
            for j, (type2, pos2) in enumerate(all_positions[i+1:], i+1):
                # Check if positions are very close (within 0.001mm)
                if (abs(pos1[0] - pos2[0]) < 0.001 and 
                    abs(pos1[1] - pos2[1]) < 0.001 and 
                    abs(pos1[2] - pos2[2]) < 0.001):
                    print(f"🚨 DUPLICATE FOUND: {type1} and {type2} at position ({pos1[0]:.3f}, {pos1[1]:.3f}, {pos1[2]:.3f})")
                    duplicates_found = True
        
        if not duplicates_found:
            print("✅ NO DUPLICATES FOUND - FIX SUCCESSFUL!")
        else:
            print("❌ DUPLICATES STILL EXIST - FIX FAILED!")
        
        print("\n🏁 TEST COMPLETED")
        print("=" * 50)
        
    else:
        print(f"❌ STEP file not found: {step_file}")
    
    # Close after 3 seconds
    QTimer.singleShot(3000, app.quit)
    app.exec_()

if __name__ == "__main__":
    test_duplicate_arrow_fix()
