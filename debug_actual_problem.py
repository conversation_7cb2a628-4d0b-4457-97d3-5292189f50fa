#!/usr/bin/env python3
"""
Debug the actual problem - no BS, just facts
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== ACTUAL PROBLEM DEBUG ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Testing with: {test_file}")
    
    # Load file
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ File loaded")
        
        # Get original bounds
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            original_actor = renderer.step_actors[0]
            original_bounds = original_actor.GetBounds()
            print(f"Original bounds: {original_bounds}")
            
            # Apply rotation
            print("Applying X+90° rotation...")
            viewer._apply_model_rotation("top", "x", 90.0)
            
            # Get rotated bounds
            rotated_bounds = original_actor.GetBounds()
            print(f"Rotated bounds: {rotated_bounds}")
            
            # Check if bounds actually changed
            bounds_changed = any(abs(a - b) > 0.01 for a, b in zip(original_bounds, rotated_bounds))
            print(f"Bounds changed after rotation: {bounds_changed}")
            print(f"Original bounds: {original_bounds}")
            print(f"Rotated bounds:  {rotated_bounds}")

            if bounds_changed:
                print("✅ Visual rotation working")
            else:
                print("❌ Visual rotation not working - bounds didn't change")
                print("THE ACTUAL PROBLEM:")
                print("- Visual rotation works (bounds change)")
                print("- Save process runs (file created)")
                print("- But saved geometry doesn't match rotated geometry")
                print("- This means the transformation is not being applied to the OpenCASCADE shape correctly")
                app.quit()
                print("=== DEBUG FINISHED ===")
                exit()

            if bounds_changed:
                
                # Now test save
                print("\nTesting save...")
                test_save_file = "debug_actual.step"
                
                if os.path.exists(test_save_file):
                    os.remove(test_save_file)
                
                # Get current values
                current_pos = viewer._extract_position_from_display("top")
                current_rot = viewer._extract_rotation_from_vtk_actor("top")
                orig_pos = viewer.orig_pos_left
                orig_rot = viewer.orig_rot_left
                
                print(f"Current rot: {current_rot}")
                print(f"Original rot: {orig_rot}")
                print(f"Delta rot: X={current_rot['x'] - orig_rot['x']:.1f}°")

                # Call SIMPLE save method
                print("DEBUG: About to call _save_step_simple")
                loader = viewer.step_loader_left
                print(f"DEBUG: Loader = {loader}")
                print(f"DEBUG: Method exists = {hasattr(viewer, '_save_step_simple')}")

                success = viewer._save_step_simple(
                    test_save_file, loader, current_rot, orig_rot
                )
                print(f"DEBUG: Save returned: {success}")
                
                if success and os.path.exists(test_save_file):
                    file_size = os.path.getsize(test_save_file)
                    print(f"✅ Save successful: {file_size} bytes")
                    
                    # Load saved file
                    viewer.active_viewer = "bottom"
                    load_success = viewer.load_step_file_direct(test_save_file)
                    
                    if load_success:
                        print("✅ Saved file loaded")
                        
                        # Get saved file bounds
                        bottom_renderer = viewer.vtk_renderer_right
                        if hasattr(bottom_renderer, 'step_actors') and bottom_renderer.step_actors:
                            saved_actor = bottom_renderer.step_actors[0]
                            saved_bounds = saved_actor.GetBounds()
                            print(f"Saved file bounds: {saved_bounds}")
                            
                            # Compare with rotated bounds
                            bounds_match = all(abs(a - b) < 0.1 for a, b in zip(rotated_bounds, saved_bounds))
                            print(f"Saved bounds match rotated bounds: {bounds_match}")
                            
                            if bounds_match:
                                print("✅ SUCCESS: Rotation preserved!")
                            else:
                                print("❌ FAILURE: Rotation NOT preserved!")
                                print("THE ACTUAL PROBLEM:")
                                print("- Visual rotation works (bounds change)")
                                print("- Save process runs (file created)")
                                print("- But saved geometry doesn't match rotated geometry")
                                print("- This means the transformation is not being applied to the OpenCASCADE shape correctly")
                        else:
                            print("❌ No actors in saved file")
                    else:
                        print("❌ Failed to load saved file")
                        
                    # Clean up
                    os.remove(test_save_file)
                    
                else:
                    print("❌ Save failed")
            else:
                print("❌ Visual rotation not working - bounds didn't change")
        else:
            print("❌ No actors found")
    else:
        print("❌ Failed to load file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== DEBUG FINISHED ===")
