#!/usr/bin/env python3
"""
Show actual BEFORE and AFTER numbers to prove if the fix works
"""

import sys
import os
import re
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import time

def extract_origin_numbers(text):
    """Extract origin numbers from yellow text"""
    match = re.search(r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', text)
    if match:
        return (float(match.group(1)), float(match.group(2)), float(match.group(3)))
    return None

def test_mouse_rotation_numbers():
    """Test mouse rotation and show actual BEFORE/AFTER numbers"""
    print("🧪 TESTING: Mouse rotation yellow text numbers")
    print("=" * 50)
    
    # Create application
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Load SOIC file (has non-zero origin)
    soic_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if not os.path.exists(soic_file):
        print(f"❌ {soic_file} not found")
        return False

    print(f"📁 Loading {soic_file}...")
    viewer.load_step_file_direct(soic_file)
    app.processEvents()
    time.sleep(1)
    
    # Get BEFORE numbers
    print("\n🔍 BEFORE MOUSE ROTATION:")
    before_display = viewer._calculate_unified_display_numbers("top")
    before_model_origin = extract_origin_numbers(before_display['model'])
    before_local_origin = extract_origin_numbers(before_display['local_origin'])
    
    print(f"📊 BEFORE Model Origin:  {before_model_origin}")
    print(f"📊 BEFORE Local Origin:  {before_local_origin}")
    
    # Apply mouse rotation (same method mouse uses)
    print("\n🖱️ APPLYING MOUSE ROTATION: X+15°")
    viewer._apply_model_rotation("top", "x", 15.0)
    app.processEvents()
    time.sleep(0.5)
    
    # Get AFTER numbers
    print("\n🔍 AFTER MOUSE ROTATION:")
    after_display = viewer._calculate_unified_display_numbers("top")
    after_model_origin = extract_origin_numbers(after_display['model'])
    after_local_origin = extract_origin_numbers(after_display['local_origin'])
    
    print(f"📊 AFTER Model Origin:   {after_model_origin}")
    print(f"📊 AFTER Local Origin:   {after_local_origin}")
    
    # Compare numbers
    print("\n📈 COMPARISON:")
    model_changed = before_model_origin != after_model_origin if before_model_origin and after_model_origin else False
    local_changed = before_local_origin != after_local_origin if before_local_origin and after_local_origin else False
    
    if model_changed:
        print(f"✅ Model Origin CHANGED: {before_model_origin} → {after_model_origin}")
    else:
        print(f"❌ Model Origin UNCHANGED: {before_model_origin}")
    
    if local_changed:
        print(f"✅ Local Origin CHANGED: {before_local_origin} → {after_local_origin}")
    else:
        print(f"❌ Local Origin UNCHANGED: {before_local_origin}")
    
    # Final result
    success = model_changed and local_changed
    print("\n" + "=" * 50)
    if success:
        print("🎉 SUCCESS: Mouse rotation DOES update yellow text numbers!")
        print("   The fix is working correctly.")
    else:
        print("❌ FAILURE: Mouse rotation does NOT update yellow text numbers!")
        print("   The fix is not working.")
    
    app.quit()
    return success

def test_button_rotation_numbers():
    """Test button rotation and show actual BEFORE/AFTER numbers"""
    print("\n\n🧪 TESTING: Button rotation yellow text numbers")
    print("=" * 50)
    
    # Create application
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Load SOIC file (has non-zero origin)
    soic_file = "SOIC16P127_1270X940X610L89X51.STEP"
    print(f"📁 Loading {soic_file}...")
    viewer.load_step_file_direct(soic_file)
    app.processEvents()
    time.sleep(1)
    
    # Get BEFORE numbers
    print("\n🔍 BEFORE BUTTON ROTATION:")
    before_display = viewer._calculate_unified_display_numbers("top")
    before_model_origin = extract_origin_numbers(before_display['model'])
    before_local_origin = extract_origin_numbers(before_display['local_origin'])
    
    print(f"📊 BEFORE Model Origin:  {before_model_origin}")
    print(f"📊 BEFORE Local Origin:  {before_local_origin}")
    
    # Apply button rotation (same method buttons use)
    print("\n🔘 APPLYING BUTTON ROTATION: X+15°")
    viewer.rotate_shape("x", 15.0)
    app.processEvents()
    time.sleep(0.5)
    
    # Get AFTER numbers
    print("\n🔍 AFTER BUTTON ROTATION:")
    after_display = viewer._calculate_unified_display_numbers("top")
    after_model_origin = extract_origin_numbers(after_display['model'])
    after_local_origin = extract_origin_numbers(after_display['local_origin'])
    
    print(f"📊 AFTER Model Origin:   {after_model_origin}")
    print(f"📊 AFTER Local Origin:   {after_local_origin}")
    
    # Compare numbers
    print("\n📈 COMPARISON:")
    model_changed = before_model_origin != after_model_origin if before_model_origin and after_model_origin else False
    local_changed = before_local_origin != after_local_origin if before_local_origin and after_local_origin else False
    
    if model_changed:
        print(f"✅ Model Origin CHANGED: {before_model_origin} → {after_model_origin}")
    else:
        print(f"❌ Model Origin UNCHANGED: {before_model_origin}")
    
    if local_changed:
        print(f"✅ Local Origin CHANGED: {before_local_origin} → {after_local_origin}")
    else:
        print(f"❌ Local Origin UNCHANGED: {before_local_origin}")
    
    # Final result
    success = model_changed and local_changed
    print("\n" + "=" * 50)
    if success:
        print("🎉 SUCCESS: Button rotation DOES update yellow text numbers!")
        print("   Buttons work correctly.")
    else:
        print("❌ FAILURE: Button rotation does NOT update yellow text numbers!")
        print("   Buttons are broken too.")
    
    app.quit()
    return success

if __name__ == "__main__":
    print("🧪 ACTUAL NUMBERS TEST")
    print("Testing both mouse rotation and button rotation")
    print("=" * 60)
    
    # Test mouse rotation
    mouse_works = test_mouse_rotation_numbers()
    
    # Test button rotation  
    button_works = test_button_rotation_numbers()
    
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS:")
    
    if mouse_works:
        print("✅ Mouse rotation: WORKS - Yellow text updates")
    else:
        print("❌ Mouse rotation: BROKEN - Yellow text doesn't update")
    
    if button_works:
        print("✅ Button rotation: WORKS - Yellow text updates")
    else:
        print("❌ Button rotation: BROKEN - Yellow text doesn't update")
    
    if mouse_works and button_works:
        print("\n🎉 BOTH WORK: The fix is successful!")
    elif button_works and not mouse_works:
        print("\n🔧 BUTTONS WORK, MOUSE BROKEN: Need to copy button approach to mouse")
    elif mouse_works and not button_works:
        print("\n🔧 MOUSE WORKS, BUTTONS BROKEN: Something is wrong")
    else:
        print("\n❌ BOTH BROKEN: Major issue with the system")
    
    sys.exit(0)
