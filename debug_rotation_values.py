#!/usr/bin/env python3
"""
Debug the rotation values being passed to the save method
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

def debug_rotation_values():
    print("=== DEBUG ROTATION VALUES ===")
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    try:
        # Create viewer
        viewer = StepViewerTDK()
        print("✅ Created viewer")
        
        # Find a STEP file to test with
        step_files = [f for f in os.listdir('.') if f.endswith('.step') or f.endswith('.STEP')]
        if not step_files:
            print("❌ No STEP files found in current directory")
            return False
            
        original_file = step_files[0]
        print(f"📁 Using file: {original_file}")
        
        # Step 1: Load the file
        print(f"\n1. LOADING: {original_file}")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(original_file)
        
        if not success:
            print("❌ Failed to load file")
            return False
            
        print("✅ File loaded successfully")
        
        # Get the loader
        loader = viewer.step_loader_left
        loader.current_filename = original_file
        
        # Step 2: Check initial rotation state
        print(f"\n2. INITIAL STATE:")
        print(f"   current_rot_left: {getattr(viewer, 'current_rot_left', 'NOT SET')}")
        print(f"   orig_rot_left: {getattr(viewer, 'orig_rot_left', 'NOT SET')}")
        
        # Initialize if needed
        if not hasattr(viewer, 'orig_rot_left'):
            viewer.orig_rot_left = {'x': 0, 'y': 0, 'z': 0}
            print("   Initialized orig_rot_left to zero")
        
        if not hasattr(viewer, 'current_rot_left'):
            viewer.current_rot_left = {'x': 0, 'y': 0, 'z': 0}
            print("   Initialized current_rot_left to zero")
        
        # Step 3: Apply rotation
        print(f"\n3. APPLYING ROTATION:")
        print("   Applying 90° X rotation...")
        viewer._apply_model_rotation('top', 'x', 90.0)
        
        # Check rotation states after rotation
        current_rot = viewer.current_rot_left.copy()
        orig_rot = viewer.orig_rot_left.copy()
        
        print(f"   After rotation:")
        print(f"   current_rot_left: {current_rot}")
        print(f"   orig_rot_left: {orig_rot}")
        
        # Calculate delta
        delta_x = current_rot['x'] - orig_rot['x']
        delta_y = current_rot['y'] - orig_rot['y']
        delta_z = current_rot['z'] - orig_rot['z']
        
        print(f"   Delta rotation: X={delta_x:.6f}° Y={delta_y:.6f}° Z={delta_z:.6f}°")
        
        # Step 4: Test the save method with debug
        print(f"\n4. TESTING SAVE METHOD:")
        output_file = "debug_rotation_test.step"
        
        # Remove existing file
        if os.path.exists(output_file):
            os.remove(output_file)
        
        # Call save method
        print("   Calling save method...")
        save_success = viewer._save_step_with_current_rotation(output_file, loader, current_rot)
        
        print(f"   Save method returned: {save_success}")
        
        return save_success
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_rotation_values()
    
    if success:
        print("\n✅ DEBUG COMPLETED")
    else:
        print("\n❌ DEBUG FAILED")
    
    sys.exit(0 if success else 1)
