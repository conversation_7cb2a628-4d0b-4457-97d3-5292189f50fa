#!/usr/bin/env python3
"""
Real Test - Actually test with loaded STEP file
===============================================
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QFileDialog
from PyQt5.QtCore import QTimer

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def real_test():
    """Real test with actual STEP file loading"""
    print("🧪 REAL TEST - WITH ACTUAL STEP FILE")
    print("=" * 60)
    
    # Import after path setup
    try:
        from step_viewer import StepViewerTDK
    except Exception as e:
        print(f"❌ Failed to import step_viewer: {e}")
        return False
    
    # Create Qt application
    app = QApplication([])
    
    # Create step viewer
    viewer = StepViewerTDK()
    viewer.show()
    viewer.active_viewer = "top"
    
    # Wait for initialization
    time.sleep(3)
    
    print("\n📁 LOADING STEP FILE VIA GUI")
    print("-" * 40)
    
    # Find STEP files
    step_files = [f for f in os.listdir('.') if f.endswith(('.step', '.stp', '.STEP', '.STP'))]
    if step_files:
        step_file = step_files[0]
        print(f"Found STEP file: {step_file}")
        
        # Try to load via the actual GUI method
        try:
            # Simulate clicking "Load STEP File" button
            if hasattr(viewer, 'load_step_file'):
                print("Calling load_step_file method...")
                viewer.load_step_file()
                time.sleep(2)
            else:
                print("No load_step_file method found")
                
                # Try direct loading
                if hasattr(viewer, 'vtk_renderer_left'):
                    renderer = viewer.vtk_renderer_left
                    if hasattr(renderer, 'load_step_file'):
                        print("Calling renderer load_step_file...")
                        success = renderer.load_step_file(step_file)
                        print(f"Load result: {success}")
                        time.sleep(2)
                        
                        # Create origin overlay
                        if hasattr(renderer, 'create_origin_overlay'):
                            print("Creating origin overlay...")
                            renderer.create_origin_overlay()
                            time.sleep(1)
        except Exception as e:
            print(f"Load error: {e}")
    
    print("\n📊 CHECKING CURRENT STATE")
    print("-" * 40)
    check_current_state(viewer)
    
    print("\n🔄 TESTING BUTTON ROTATION")
    print("-" * 40)
    test_actual_button_rotation(viewer)
    
    print("\n📊 CHECKING AFTER ROTATION")
    print("-" * 40)
    check_current_state(viewer)
    
    # Keep running for manual testing
    print("\n⏳ Application running - test manually now...")
    print("   Click rotation buttons and check if:")
    print("   1. Yellow world origin numbers change")
    print("   2. Origin markers (red arrows) rotate with model")
    
    # Don't auto-close - let user test manually
    app.exec_()

def check_current_state(viewer):
    """Check the current state of everything"""
    print("🔍 Current state:")
    
    # Check world origin text
    if hasattr(viewer, 'world_origin_text_actor_left'):
        text = viewer.world_origin_text_actor_left.GetInput()
        print(f"   📊 World origin text: {text}")
    else:
        print("   ❌ No world origin text actor")
    
    # Check origin actors
    if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            print(f"   ✅ Found {len(renderer.origin_actors)} origin actors")
            for i, actor in enumerate(renderer.origin_actors):
                if actor:
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    print(f"      Actor {i}: pos={pos}, orient={orient}")
        else:
            print("   ❌ No origin actors found")
    
    # Check rotation tracking
    if hasattr(viewer, 'current_rot_left'):
        print(f"   📊 Rotation tracking: {viewer.current_rot_left}")
    else:
        print("   ❌ No rotation tracking")

def test_actual_button_rotation(viewer):
    """Test actual button rotation"""
    print("🔘 Testing button rotation...")
    
    try:
        print("   Applying X+15° rotation...")
        viewer.rotate_shape('x', 15.0)
        time.sleep(1)
        
        print("   Applying Y+15° rotation...")
        viewer.rotate_shape('y', 15.0)
        time.sleep(1)
        
        print("   Applying Z+15° rotation...")
        viewer.rotate_shape('z', 15.0)
        time.sleep(1)
        
        print("   ✅ Button rotations applied")
        
    except Exception as e:
        print(f"   ❌ Button rotation error: {e}")

if __name__ == "__main__":
    real_test()
