#!/usr/bin/env python3
"""
Comprehensive Test and Fix System
Tests all functionality and automatically fixes any issues found
"""

import os
import sys
import subprocess
import traceback
import time
from pathlib import Path

class ComprehensiveTestFixer:
    def __init__(self):
        self.test_results = []
        self.fixes_applied = []
        self.errors_found = []
        
    def log(self, message, level="INFO"):
        """Log messages with timestamp"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def test_imports(self):
        """Test all module imports and fix any issues"""
        self.log("=== TESTING IMPORTS ===")
        
        modules = ['step_loader', 'vtk_renderer', 'gui_components', 'step_viewer']
        
        for module in modules:
            try:
                self.log(f"Testing {module} import...")
                exec(f"import {module}")
                self.log(f"✅ {module} imported successfully")
                self.test_results.append(f"PASS: {module} import")
            except Exception as e:
                self.log(f"❌ {module} import failed: {e}", "ERROR")
                self.errors_found.append(f"Import error in {module}: {e}")
                self.test_results.append(f"FAIL: {module} import - {e}")
                
                # Try to fix the import error
                if "IndentationError" in str(e) or "unexpected indent" in str(e):
                    self.fix_indentation_error(module, str(e))
                    
    def fix_indentation_error(self, module, error_msg):
        """Fix indentation errors in Python files"""
        self.log(f"Attempting to fix indentation error in {module}")
        
        # Extract line number from error message
        import re
        line_match = re.search(r'line (\d+)', error_msg)
        if line_match:
            line_num = int(line_match.group(1))
            self.log(f"Found error at line {line_num}")
            
            # Read the file and fix indentation issues
            filename = f"{module}.py"
            if os.path.exists(filename):
                self.fix_file_indentation(filename, line_num)
                
    def fix_file_indentation(self, filename, error_line):
        """Fix indentation issues in a specific file"""
        self.log(f"Fixing indentation in {filename} around line {error_line}")
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # Look for common indentation issues around the error line
            fixed = False
            for i in range(max(0, error_line-10), min(len(lines), error_line+10)):
                line = lines[i]
                
                # Remove lines that are clearly dead code after return statements
                if i > 0 and 'return ' in lines[i-1] and line.strip() and not line.startswith('    def ') and not line.startswith('class '):
                    if line.startswith('        ') or line.startswith('            '):
                        self.log(f"Removing dead code at line {i+1}: {line.strip()}")
                        lines[i] = ''
                        fixed = True
                        
            if fixed:
                # Write the fixed file
                with open(filename, 'w', encoding='utf-8') as f:
                    f.writelines(lines)
                self.log(f"✅ Fixed indentation issues in {filename}")
                self.fixes_applied.append(f"Fixed indentation in {filename}")
                
        except Exception as e:
            self.log(f"❌ Failed to fix {filename}: {e}", "ERROR")
            
    def test_step_loader(self):
        """Test STEP file loading functionality"""
        self.log("=== TESTING STEP LOADER ===")
        
        try:
            from step_loader import STEPLoader
            loader = STEPLoader()
            
            # Test with the SOIC STEP file
            step_file = "SOIC16P127_1270X940X610L89X51.STEP"
            if os.path.exists(step_file):
                self.log(f"Testing STEP file loading: {step_file}")
                success, message = loader.load_step_file(step_file)
                
                if success:
                    self.log(f"✅ STEP file loaded: {message}")
                    self.test_results.append("PASS: STEP file loading")
                    
                    # Check if it's real geometry or fake
                    if hasattr(loader, 'current_polydata') and loader.current_polydata:
                        points = loader.current_polydata.GetNumberOfPoints()
                        cells = loader.current_polydata.GetNumberOfCells()
                        self.log(f"Geometry: {points} points, {cells} cells")
                        
                        if points == 8 and cells == 6:
                            self.log("⚠️  WARNING: This appears to be fake cube geometry")
                            self.log("OpenCASCADE is not available - using fallback geometry")
                            self.test_results.append("WARN: Using fake geometry (OpenCASCADE missing)")
                        else:
                            self.log("✅ Real STEP geometry loaded")
                            self.test_results.append("PASS: Real STEP geometry")
                    else:
                        self.log("❌ No polydata generated")
                        self.test_results.append("FAIL: No polydata")
                else:
                    self.log(f"❌ STEP file loading failed: {message}")
                    self.test_results.append(f"FAIL: STEP loading - {message}")
            else:
                self.log(f"❌ STEP file not found: {step_file}")
                self.test_results.append("FAIL: STEP file not found")
                
        except Exception as e:
            self.log(f"❌ STEP loader test failed: {e}", "ERROR")
            self.test_results.append(f"FAIL: STEP loader - {e}")
            traceback.print_exc()
            
    def test_unified_system(self):
        """Test the unified transformation system"""
        self.log("=== TESTING UNIFIED SYSTEM ===")
        
        try:
            from step_viewer import StepViewerTDK
            
            # Create a minimal test instance (without GUI)
            self.log("Creating test viewer instance...")
            
            # Mock the GUI components for testing
            import sys
            from unittest.mock import Mock
            
            # Mock PyQt5 components
            sys.modules['PyQt5.QtWidgets'] = Mock()
            sys.modules['PyQt5.QtCore'] = Mock()
            sys.modules['PyQt5.QtGui'] = Mock()
            
            # Test unified_transform method exists and works
            viewer = StepViewerTDK()
            
            if hasattr(viewer, 'unified_transform'):
                self.log("✅ unified_transform method found")
                
                # Test different operations
                operations = ['load', 'rotate', 'move']
                for op in operations:
                    try:
                        if op == 'load':
                            result = viewer.unified_transform('load', step_data=None, filename='test.step')
                        elif op == 'rotate':
                            result = viewer.unified_transform('rotate', axis='x', degrees=15)
                        elif op == 'move':
                            result = viewer.unified_transform('move', axis='y', amount=5.0)
                            
                        self.log(f"✅ unified_transform('{op}') executed")
                        self.test_results.append(f"PASS: unified_transform {op}")
                        
                    except Exception as e:
                        self.log(f"❌ unified_transform('{op}') failed: {e}")
                        self.test_results.append(f"FAIL: unified_transform {op} - {e}")
                        
            else:
                self.log("❌ unified_transform method not found")
                self.test_results.append("FAIL: unified_transform method missing")
                
        except Exception as e:
            self.log(f"❌ Unified system test failed: {e}", "ERROR")
            self.test_results.append(f"FAIL: Unified system - {e}")
            traceback.print_exc()
            
    def test_vtk_functionality(self):
        """Test VTK rendering functionality"""
        self.log("=== TESTING VTK FUNCTIONALITY ===")
        
        try:
            from vtk_renderer import VTKRenderer
            
            # Test VTK renderer creation
            renderer = VTKRenderer(None)  # No parent for testing
            
            self.log("✅ VTK renderer created successfully")
            self.test_results.append("PASS: VTK renderer creation")
            
            # Test basic VTK operations
            if hasattr(renderer, 'renderer'):
                self.log("✅ VTK renderer has renderer attribute")
                self.test_results.append("PASS: VTK renderer structure")
            else:
                self.log("❌ VTK renderer missing renderer attribute")
                self.test_results.append("FAIL: VTK renderer structure")
                
        except Exception as e:
            self.log(f"❌ VTK functionality test failed: {e}", "ERROR")
            self.test_results.append(f"FAIL: VTK functionality - {e}")
            traceback.print_exc()

    def test_gui_components(self):
        """Test GUI components"""
        self.log("=== TESTING GUI COMPONENTS ===")

        try:
            from gui_components import create_tool_dock

            # Mock parent for testing
            from unittest.mock import Mock
            mock_parent = Mock()

            dock = create_tool_dock(mock_parent)

            self.log("✅ GUI components loaded successfully")
            self.test_results.append("PASS: GUI components")

        except Exception as e:
            self.log(f"❌ GUI components test failed: {e}", "ERROR")
            self.test_results.append(f"FAIL: GUI components - {e}")

    def test_file_operations(self):
        """Test file operations and integration"""
        self.log("=== TESTING FILE OPERATIONS ===")

        try:
            # Test if STEP file exists
            step_file = "SOIC16P127_1270X940X610L89X51.STEP"
            if os.path.exists(step_file):
                self.log(f"✅ STEP file found: {step_file}")

                # Check file size
                size = os.path.getsize(step_file)
                self.log(f"File size: {size} bytes")

                if size > 1000:  # Reasonable STEP file size
                    self.test_results.append("PASS: STEP file valid")
                else:
                    self.test_results.append("WARN: STEP file too small")

            else:
                self.log(f"❌ STEP file not found: {step_file}")
                self.test_results.append("FAIL: STEP file missing")

        except Exception as e:
            self.log(f"❌ File operations test failed: {e}", "ERROR")
            self.test_results.append(f"FAIL: File operations - {e}")

    def run_integration_test(self):
        """Run a complete integration test"""
        self.log("=== INTEGRATION TEST ===")

        try:
            # Test the complete workflow
            from step_loader import STEPLoader
            from vtk_renderer import VTKRenderer

            # 1. Load STEP file
            loader = STEPLoader()
            step_file = "SOIC16P127_1270X940X610L89X51.STEP"

            if os.path.exists(step_file):
                success, message = loader.load_step_file(step_file)

                if success and hasattr(loader, 'current_polydata') and loader.current_polydata:
                    self.log("✅ STEP file loaded for integration test")

                    # 2. Test VTK rendering
                    renderer = VTKRenderer(None)

                    # 3. Test unified system integration
                    self.log("✅ Integration test components working")
                    self.test_results.append("PASS: Integration test")

                else:
                    self.log("❌ Integration test failed - no STEP data")
                    self.test_results.append("FAIL: Integration test - no data")
            else:
                self.log("❌ Integration test failed - no STEP file")
                self.test_results.append("FAIL: Integration test - no file")

        except Exception as e:
            self.log(f"❌ Integration test failed: {e}", "ERROR")
            self.test_results.append(f"FAIL: Integration test - {e}")

    def fix_critical_issues(self):
        """Fix any critical issues found during testing"""
        self.log("=== FIXING CRITICAL ISSUES ===")

        # Fix step_viewer.py indentation issues
        if any("step_viewer" in error for error in self.errors_found):
            self.log("Fixing step_viewer.py issues...")
            self.fix_step_viewer_file()

        # Check for missing dependencies
        self.check_dependencies()

    def fix_step_viewer_file(self):
        """Fix critical issues in step_viewer.py"""
        try:
            filename = "step_viewer.py"
            if not os.path.exists(filename):
                self.log(f"❌ {filename} not found")
                return

            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()

            # Remove dead code patterns
            lines = content.split('\n')
            fixed_lines = []
            skip_dead_code = False

            for i, line in enumerate(lines):
                # Detect return statements
                if line.strip().startswith('return ') and not line.strip().startswith('return self.unified_transform'):
                    fixed_lines.append(line)
                    skip_dead_code = True
                    continue

                # Skip dead code after return until next method
                if skip_dead_code:
                    if line.strip().startswith('def ') or line.strip().startswith('class '):
                        skip_dead_code = False
                        fixed_lines.append(line)
                    # Skip this line (it's dead code)
                    continue

                fixed_lines.append(line)

            # Write fixed content
            with open(filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(fixed_lines))

            self.log(f"✅ Fixed {filename}")
            self.fixes_applied.append(f"Fixed dead code in {filename}")

        except Exception as e:
            self.log(f"❌ Failed to fix step_viewer.py: {e}", "ERROR")

    def check_dependencies(self):
        """Check and report on dependencies"""
        self.log("=== CHECKING DEPENDENCIES ===")

        dependencies = {
            'vtk': 'VTK visualization',
            'PyQt5': 'GUI framework',
            'numpy': 'Numerical operations'
        }

        for dep, desc in dependencies.items():
            try:
                __import__(dep)
                self.log(f"✅ {dep} available ({desc})")
            except ImportError:
                self.log(f"❌ {dep} missing ({desc})")
                self.test_results.append(f"MISSING: {dep}")

        # Check for OpenCASCADE
        try:
            import OCC
            self.log("✅ OpenCASCADE available (real STEP loading)")
        except ImportError:
            self.log("⚠️  OpenCASCADE not available (will use fake geometry)")
            self.test_results.append("WARN: OpenCASCADE missing")

    def generate_report(self):
        """Generate comprehensive test report"""
        self.log("=== GENERATING REPORT ===")

        report = []
        report.append("=" * 60)
        report.append("COMPREHENSIVE TEST REPORT")
        report.append("=" * 60)
        report.append("")

        # Summary
        passes = len([r for r in self.test_results if r.startswith("PASS")])
        fails = len([r for r in self.test_results if r.startswith("FAIL")])
        warns = len([r for r in self.test_results if r.startswith("WARN")])

        report.append(f"SUMMARY: {passes} PASS, {fails} FAIL, {warns} WARN")
        report.append("")

        # Test results
        report.append("TEST RESULTS:")
        for result in self.test_results:
            report.append(f"  {result}")
        report.append("")

        # Fixes applied
        if self.fixes_applied:
            report.append("FIXES APPLIED:")
            for fix in self.fixes_applied:
                report.append(f"  ✅ {fix}")
            report.append("")

        # Errors found
        if self.errors_found:
            report.append("ERRORS FOUND:")
            for error in self.errors_found:
                report.append(f"  ❌ {error}")
            report.append("")

        report.append("=" * 60)

        # Print report
        for line in report:
            print(line)

        # Save report to file
        with open("test_report.txt", "w") as f:
            f.write("\n".join(report))

        self.log("✅ Report saved to test_report.txt")

    def run_all_tests(self):
        """Run all tests and fixes"""
        self.log("🚀 STARTING COMPREHENSIVE TEST AND FIX SYSTEM")
        self.log("=" * 60)

        # Run tests in order
        self.test_imports()
        self.fix_critical_issues()  # Fix issues after import test
        self.test_imports()  # Re-test imports after fixes
        self.test_step_loader()
        self.test_vtk_functionality()
        self.test_gui_components()
        self.test_unified_system()
        self.test_file_operations()
        self.run_integration_test()
        self.check_dependencies()

        # Generate final report
        self.generate_report()

        self.log("🎉 COMPREHENSIVE TESTING COMPLETE")

        return len([r for r in self.test_results if r.startswith("FAIL")]) == 0

def main():
    """Main test runner"""
    tester = ComprehensiveTestFixer()
    success = tester.run_all_tests()

    if success:
        print("\n🎉 ALL TESTS PASSED! System is ready to use.")
    else:
        print("\n⚠️  Some tests failed. Check the report for details.")

    return success

if __name__ == "__main__":
    main()
