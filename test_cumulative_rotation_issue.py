#!/usr/bin/env python3
"""
Test the cumulative rotation issue: X+ 3 times, then Y+ once.
This should reproduce the problem where local origin doesn't follow model.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK as <PERSON><PERSON><PERSON><PERSON>

def test_cumulative_rotations():
    """Test the specific sequence: X+ 3 times, then Y+ once"""
    print("🔍 TESTING CUMULATIVE ROTATION ISSUE")
    print("=" * 60)
    print("Sequence: X+ 3 times (45°), then Y+ once (15°)")
    print("Expected: Local origin should follow model throughout")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    viewer = StepViewer()
    
    # Load a STEP file
    test_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    print(f"📂 Loading STEP file: {test_file}")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    print("✅ STEP file loaded successfully")
    
    def capture_state(step_name):
        """Capture current state for debugging"""
        print(f"\n📊 {step_name}:")
        print("=" * 40)
        
        # Get tracking variables
        current_rot = getattr(viewer, 'current_rot_left', {'x': 0, 'y': 0, 'z': 0})
        current_pos = getattr(viewer, 'current_pos_left', {'x': 0, 'y': 0, 'z': 0})
        
        print(f"🔢 Tracking: current_rot = {current_rot}")
        print(f"🔢 Tracking: current_pos = {current_pos}")
        
        # Get display numbers
        display_data = viewer._calculate_unified_display_numbers("top")
        print(f"🔢 Model: {display_data['model']}")
        print(f"🔢 Local Origin: {display_data['local_origin']}")
        
        # Check if they match
        model_match = display_data['model'] == display_data['local_origin'].replace("Local Origin", "Model")
        print(f"🔢 Model/Local Origin Match: {'✅ YES' if model_match else '❌ NO'}")
        
        # Get actual actor positions if available
        if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
            renderer = viewer.vtk_renderer_left
            
            # Check green ball position
            if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
                green_pos = renderer.part_origin_sphere.GetPosition()
                green_orient = renderer.part_origin_sphere.GetOrientation()
                print(f"🟢 Green Ball: pos={green_pos}, orient={green_orient}")
            
            # Check model actors
            if hasattr(renderer, 'step_actors') and renderer.step_actors:
                for i, actor in enumerate(renderer.step_actors[:2]):  # Show first 2
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    print(f"🔧 Model Actor {i}: pos={pos}, orient={orient}")
        
        return current_rot, current_pos, model_match
    
    # Initial state
    initial_rot, initial_pos, initial_match = capture_state("INITIAL STATE")
    
    # Step 1: X+ rotation (15°)
    print(f"\n🔄 STEP 1: X+ 15°")
    viewer.rotate_shape('x', 15.0)
    step1_rot, step1_pos, step1_match = capture_state("AFTER X+ 15°")
    
    # Step 2: X+ rotation (30° total)
    print(f"\n🔄 STEP 2: X+ 15° (30° total)")
    viewer.rotate_shape('x', 15.0)
    step2_rot, step2_pos, step2_match = capture_state("AFTER X+ 30° TOTAL")
    
    # Step 3: X+ rotation (45° total)
    print(f"\n🔄 STEP 3: X+ 15° (45° total)")
    viewer.rotate_shape('x', 15.0)
    step3_rot, step3_pos, step3_match = capture_state("AFTER X+ 45° TOTAL")
    
    # Step 4: Y+ rotation (this is where the problem should occur)
    print(f"\n🔄 STEP 4: Y+ 15° (PROBLEM STEP)")
    viewer.rotate_shape('y', 15.0)
    step4_rot, step4_pos, step4_match = capture_state("AFTER Y+ 15° (PROBLEM EXPECTED)")
    
    # Summary
    print(f"\n🎯 SUMMARY:")
    print("=" * 60)
    print(f"Initial:     Match = {'✅' if initial_match else '❌'}")
    print(f"After X+15°: Match = {'✅' if step1_match else '❌'}")
    print(f"After X+30°: Match = {'✅' if step2_match else '❌'}")
    print(f"After X+45°: Match = {'✅' if step3_match else '❌'}")
    print(f"After Y+15°: Match = {'✅' if step4_match else '❌'} ← PROBLEM HERE")
    
    if not step4_match:
        print(f"\n❌ PROBLEM CONFIRMED: Local origin doesn't follow model after cumulative rotations")
        print(f"   Expected: Model and Local Origin should show identical values")
        print(f"   Actual: They show different values")
    else:
        print(f"\n✅ NO PROBLEM: Local origin correctly follows model")
    
    # Keep app running for inspection
    print(f"\n🔍 App will stay open for 10 seconds for visual inspection...")
    QTimer.singleShot(10000, app.quit)
    app.exec_()
    
    return True

if __name__ == "__main__":
    test_cumulative_rotations()
