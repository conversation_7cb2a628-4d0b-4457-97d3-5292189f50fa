#!/usr/bin/env python3
"""
Test the new actor-based save approach
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== TEST ACTOR SAVE APPROACH ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Testing with: {test_file}")
    
    # Load file
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ File loaded")
        
        # Get original bounds
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            original_actor = renderer.step_actors[0]
            original_polydata = original_actor.GetMapper().GetInput()
            original_bounds = original_polydata.GetBounds()
            print(f"Original bounds: {original_bounds}")
            
            # Apply rotation
            print("\nApplying 90° X rotation...")
            viewer._apply_model_rotation("top", "x", 90.0)
            
            # Get the actor after rotation
            rotated_actor = renderer.step_actors[0]
            
            # Test the new save method
            print("\nTesting new actor save method...")
            loader = viewer.step_loader_left
            test_save_file = "test_actor_save.step"
            
            if os.path.exists(test_save_file):
                os.remove(test_save_file)
            
            # Use the new method
            success = loader.save_transformed_actor_as_step(test_save_file, rotated_actor)
            print(f"Save result: {success}")
            
            if os.path.exists(test_save_file):
                file_size = os.path.getsize(test_save_file)
                print(f"File created: {file_size} bytes")
                
                # Test loading the saved file
                print("\nTesting load of saved file...")
                viewer.active_viewer = "bottom"
                load_success = viewer.load_step_file_direct(test_save_file)
                
                if load_success:
                    print("✅ Saved file loaded successfully")
                    
                    # Check bounds of loaded file
                    bottom_renderer = viewer.vtk_renderer_right
                    if hasattr(bottom_renderer, 'step_actors') and bottom_renderer.step_actors:
                        saved_actor = bottom_renderer.step_actors[0]
                        saved_polydata = saved_actor.GetMapper().GetInput()
                        saved_bounds = saved_polydata.GetBounds()
                        print(f"Saved file bounds: {saved_bounds}")
                        
                        # Compare with original bounds (should be different)
                        bounds_different = any(abs(a - b) > 0.1 for a, b in zip(original_bounds, saved_bounds))
                        print(f"Saved bounds different from original: {bounds_different}")
                        
                        if bounds_different:
                            print("🎉 SUCCESS: Rotation preserved in saved file!")
                            
                            # Check the yellow text to see if rotation values are correct
                            print("\nChecking yellow text display...")
                            viewer.update_text_overlays()
                            
                            # Get the text from bottom viewer
                            if hasattr(viewer, 'combined_text_actor_right'):
                                text_content = viewer.combined_text_actor_right.GetInput()
                                print(f"Bottom viewer text:\n{text_content}")
                        else:
                            print("❌ FAILURE: Rotation not preserved")
                else:
                    print("❌ Failed to load saved file")
                
                os.remove(test_save_file)
            else:
                print("❌ No file created")
        else:
            print("❌ No actors found")
    else:
        print("❌ Failed to load file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== TEST FINISHED ===")
