#!/usr/bin/env python3
"""
Debug script to test the green ball position issue during cumulative rotations.
This will help us understand exactly what's happening with X+, X+, X+, Y+ sequence.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import <PERSON><PERSON>iewerTDK
from PyQt5.QtWidgets import QApplication
import time

def debug_cumulative_rotations():
    """Test the problematic X+, X+, X+, Y+ rotation sequence with detailed debugging"""
    
    print("🔥🔥🔥 DEBUG: Starting cumulative rotation test")
    print("🔥🔥🔥 DEBUG: This will test X+, X+, X+, Y+ sequence")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for UI to initialize
    app.processEvents()
    time.sleep(1)
    
    # Load a test file (you'll need to load one manually or provide path)
    print("🔥 DEBUG: Please load a STEP file manually, then press Enter to continue...")
    input("Press Enter when STEP file is loaded...")
    
    def debug_state(step_name):
        """Print detailed state information"""
        print(f"\n{'='*60}")
        print(f"🔍 DEBUG STATE: {step_name}")
        print(f"{'='*60}")
        
        # Print tracking variables
        if hasattr(viewer, 'current_rot_left'):
            print(f"📊 current_rot_left: {viewer.current_rot_left}")
        if hasattr(viewer, 'current_pos_left'):
            print(f"📊 current_pos_left: {viewer.current_pos_left}")
        if hasattr(viewer, 'orig_pos_left'):
            print(f"📊 orig_pos_left: {viewer.orig_pos_left}")
            
        # Check green ball position
        if (hasattr(viewer, 'vtk_renderer_left') and 
            hasattr(viewer.vtk_renderer_left, 'part_origin_sphere') and 
            viewer.vtk_renderer_left.part_origin_sphere):
            
            green_ball = viewer.vtk_renderer_left.part_origin_sphere
            ball_pos = green_ball.GetPosition()
            ball_orient = green_ball.GetOrientation()
            print(f"🟢 Green Ball Position: {ball_pos}")
            print(f"🟢 Green Ball Orientation: {ball_orient}")
        else:
            print("🟢 Green Ball: NOT FOUND")
            
        # Check model actor positions
        if (hasattr(viewer, 'vtk_renderer_left') and 
            hasattr(viewer.vtk_renderer_left, 'step_actors')):
            
            actors = viewer.vtk_renderer_left.step_actors
            if actors:
                for i, actor in enumerate(actors[:2]):  # Show first 2 actors
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    print(f"🔧 Model Actor {i}: pos={pos}, orient={orient}")
        
        # Calculate what the green ball position SHOULD be
        if (hasattr(viewer, 'orig_pos_left') and hasattr(viewer, 'current_rot_left')):
            import vtk
            transform = vtk.vtkTransform()
            transform.PostMultiply()
            
            # Apply cumulative rotations
            transform.RotateX(viewer.current_rot_left['x'])
            transform.RotateY(viewer.current_rot_left['y'])
            transform.RotateZ(viewer.current_rot_left['z'])
            
            # Transform original position
            orig_pos = (viewer.orig_pos_left['x'], viewer.orig_pos_left['y'], viewer.orig_pos_left['z'])
            expected_pos = transform.TransformPoint(orig_pos)
            print(f"🎯 EXPECTED Green Ball Position: {expected_pos}")
            
            # Compare with actual
            if (hasattr(viewer, 'vtk_renderer_left') and 
                hasattr(viewer.vtk_renderer_left, 'part_origin_sphere') and 
                viewer.vtk_renderer_left.part_origin_sphere):
                
                actual_pos = viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
                diff = [expected_pos[i] - actual_pos[i] for i in range(3)]
                print(f"🚨 DIFFERENCE: {diff}")
                
                if abs(diff[0]) > 0.001 or abs(diff[1]) > 0.001 or abs(diff[2]) > 0.001:
                    print("❌ GREEN BALL POSITION IS WRONG!")
                else:
                    print("✅ Green ball position is correct")
    
    # Initial state
    debug_state("INITIAL STATE")
    
    # Step 1: X+ 15°
    print(f"\n🔄 STEP 1: X+ 15°")
    viewer.rotate_shape('x', 15.0)
    app.processEvents()
    time.sleep(0.5)
    debug_state("AFTER X+ 15°")
    
    # Step 2: X+ 15° (total 30°)
    print(f"\n🔄 STEP 2: X+ 15° (total 30°)")
    viewer.rotate_shape('x', 15.0)
    app.processEvents()
    time.sleep(0.5)
    debug_state("AFTER X+ 30° TOTAL")
    
    # Step 3: X+ 15° (total 45°)
    print(f"\n🔄 STEP 3: X+ 15° (total 45°)")
    viewer.rotate_shape('x', 15.0)
    app.processEvents()
    time.sleep(0.5)
    debug_state("AFTER X+ 45° TOTAL")
    
    # Step 4: Y+ 15° (PROBLEM STEP)
    print(f"\n🔄 STEP 4: Y+ 15° (PROBLEM STEP)")
    viewer.rotate_shape('y', 15.0)
    app.processEvents()
    time.sleep(0.5)
    debug_state("AFTER Y+ 15° (PROBLEM EXPECTED)")
    
    print(f"\n🎯 SUMMARY:")
    print(f"{'='*60}")
    print(f"If green ball position matches expected position in all steps,")
    print(f"then the fix is working. If not, we found the bug!")
    
    # Keep app running for visual inspection
    print(f"\n🔍 App will stay open for visual inspection...")
    print(f"Press Ctrl+C to exit")
    
    try:
        app.exec_()
    except KeyboardInterrupt:
        print("Exiting...")

if __name__ == "__main__":
    debug_cumulative_rotations()
