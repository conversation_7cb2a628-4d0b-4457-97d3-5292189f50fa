#!/usr/bin/env python3
"""
Load SOIC footprint and show origin numbers
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from step_viewer import <PERSON><PERSON>iewer<PERSON>D<PERSON>

def load_soic_test():
    """Load SOIC footprint and show the GUI"""
    
    print("🔍 LOADING SOIC FOOTPRINT TEST")
    print("=" * 50)
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create the viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    print("1. Opening GUI window...")
    
    # Wait a moment for GUI to initialize, then load SOIC
    QTimer.singleShot(1000, lambda: load_soic(viewer))
    
    def load_soic(viewer):
        print("2. Looking for SOIC footprint...")
        
        # Look for SOIC files in the save directory
        save_dir = "e:/python/viewer/save"
        soic_files = []
        
        if os.path.exists(save_dir):
            all_files = os.listdir(save_dir)
            # Look for SOIC files
            soic_files = [f for f in all_files if 'soic' in f.lower() and f.endswith('.step')]
            
            if soic_files:
                print(f"   Found {len(soic_files)} SOIC files:")
                for f in soic_files:
                    print(f"     - {f}")
                
                # Load the first SOIC file
                soic_file = os.path.join(save_dir, soic_files[0])
                print(f"   Loading: {soic_files[0]}")
                
                # Set active viewer to TOP and load
                viewer.active_viewer = "top"
                success = viewer.load_step_file_direct(soic_file)
                
                if success:
                    print("✅ SOIC footprint loaded successfully!")
                    print("\n📋 WHAT TO LOOK FOR IN THE GUI:")
                    print("   - Check the yellow text in the TOP viewer")
                    print("   - Look for 'Origin (X = ... Y = ... Z = ...)' values")
                    print("   - The numbers should show actual STEP file origin position")
                    print("   - NOT all zeros!")
                    
                    # Print the current values for reference
                    if hasattr(viewer, 'current_pos_left'):
                        print(f"\n📊 CURRENT VALUES:")
                        print(f"   current_pos_left: {viewer.current_pos_left}")
                        if hasattr(viewer, 'orig_pos_left'):
                            print(f"   orig_pos_left: {viewer.orig_pos_left}")
                    
                    print("\n🎯 EXPECTED BEHAVIOR:")
                    print("   - Main origin display: Shows actual STEP file values")
                    print("   - Local origin display: Shows actual STEP file values") 
                    print("   - World origin display: Shows (0.000, 0.000, 0.000)")
                    print("\n💡 The GUI window is now open - you can see the numbers!")
                    print("   Try clicking the rotation buttons to see the numbers update!")
                    
                else:
                    print("❌ Failed to load SOIC footprint")
            else:
                print("   No SOIC files found, looking for any STEP files...")
                
                # Look for any STEP files
                step_files = [f for f in all_files if f.endswith('.step')]
                if step_files:
                    print(f"   Found {len(step_files)} STEP files:")
                    for f in step_files[:5]:  # Show first 5
                        print(f"     - {f}")
                    
                    # Load the first one
                    first_file = os.path.join(save_dir, step_files[0])
                    print(f"   Loading first available file: {step_files[0]}")
                    
                    viewer.active_viewer = "top"
                    success = viewer.load_step_file_direct(first_file)
                    
                    if success:
                        print("✅ First available STEP file loaded!")
                        print("\n📊 CURRENT VALUES:")
                        if hasattr(viewer, 'current_pos_left'):
                            print(f"   current_pos_left: {viewer.current_pos_left}")
                            if hasattr(viewer, 'orig_pos_left'):
                                print(f"   orig_pos_left: {viewer.orig_pos_left}")
                    else:
                        print("❌ Failed to load any STEP file")
                else:
                    print("   No STEP files found in save directory")
        else:
            print(f"   Save directory not found: {save_dir}")
    
    print("\n🖥️  GUI window should be opening...")
    print("   Close the window when you're done verifying the numbers")
    
    # Run the application
    app.exec_()
    
    print("\n✅ SOIC test completed")

if __name__ == "__main__":
    load_soic_test()
