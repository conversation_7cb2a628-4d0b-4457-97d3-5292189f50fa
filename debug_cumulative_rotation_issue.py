#!/usr/bin/env python3
"""
Debug the exact cumulative rotation issue: X+, X+, X+, Y+
Track what happens to the green ball position vs model position.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import time

def debug_cumulative_rotation():
    """Debug the cumulative rotation issue"""
    
    print("🔍 DEBUGGING: Cumulative rotation X+, X+, X+, Y+ issue")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for UI to initialize
    app.processEvents()
    time.sleep(1)
    
    print("🔍 Please load a STEP file, then press Enter...")
    input("Press Enter when STEP file is loaded...")
    
    def get_green_ball_actual_position():
        """Get the actual green ball position from the renderer"""
        if (hasattr(viewer, 'vtk_renderer_left') and 
            hasattr(viewer.vtk_renderer_left, 'part_origin_sphere') and 
            viewer.vtk_renderer_left.part_origin_sphere):
            
            green_ball = viewer.vtk_renderer_left.part_origin_sphere
            return green_ball.GetPosition()
        return None
    
    def get_model_center_position():
        """Calculate actual model center from model actors"""
        if not hasattr(viewer, 'vtk_renderer_left'):
            return None
            
        renderer = viewer.vtk_renderer_left.renderer
        actors = renderer.GetActors()
        actors.InitTraversal()
        
        # Find large actors (model parts)
        model_bounds = None
        for i in range(actors.GetNumberOfItems()):
            actor = actors.GetNextActor()
            if actor:
                bounds = actor.GetBounds()
                size = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])
                
                # Large actors are likely model parts
                if size > 5.0:
                    if model_bounds is None:
                        model_bounds = list(bounds)
                    else:
                        # Expand bounds to include this actor
                        model_bounds[0] = min(model_bounds[0], bounds[0])
                        model_bounds[1] = max(model_bounds[1], bounds[1])
                        model_bounds[2] = min(model_bounds[2], bounds[2])
                        model_bounds[3] = max(model_bounds[3], bounds[3])
                        model_bounds[4] = min(model_bounds[4], bounds[4])
                        model_bounds[5] = max(model_bounds[5], bounds[5])
        
        if model_bounds:
            center_x = (model_bounds[0] + model_bounds[1]) / 2
            center_y = (model_bounds[2] + model_bounds[3]) / 2
            center_z = (model_bounds[4] + model_bounds[5]) / 2
            return (center_x, center_y, center_z)
        
        return None
    
    def debug_step(step_name):
        """Debug a single step"""
        print(f"\n{'='*60}")
        print(f"🔍 {step_name}")
        print(f"{'='*60}")
        
        # Get tracking variables
        if hasattr(viewer, 'current_pos_left'):
            print(f"📊 current_pos_left: {viewer.current_pos_left}")
        if hasattr(viewer, 'current_rot_left'):
            print(f"📊 current_rot_left: {viewer.current_rot_left}")
        if hasattr(viewer, 'orig_pos_left'):
            print(f"📊 orig_pos_left: {viewer.orig_pos_left}")
            
        # Get actual green ball position
        green_ball_pos = get_green_ball_actual_position()
        if green_ball_pos:
            print(f"🟢 Green ball actual position: {green_ball_pos}")
        else:
            print(f"🟢 Green ball: NOT FOUND")
            
        # Get actual model center
        model_center = get_model_center_position()
        if model_center:
            print(f"📦 Model center (calculated): {model_center}")
        else:
            print(f"📦 Model center: NOT FOUND")
            
        # Compare positions
        if green_ball_pos and model_center and hasattr(viewer, 'current_pos_left'):
            expected_pos = (viewer.current_pos_left['x'], viewer.current_pos_left['y'], viewer.current_pos_left['z'])
            
            print(f"\n📏 POSITION COMPARISON:")
            print(f"   Expected (current_pos_left): {expected_pos}")
            print(f"   Green ball actual:           {green_ball_pos}")
            print(f"   Model center actual:         {model_center}")
            
            # Check if green ball matches expected
            diff_gb = ((green_ball_pos[0] - expected_pos[0])**2 + 
                      (green_ball_pos[1] - expected_pos[1])**2 + 
                      (green_ball_pos[2] - expected_pos[2])**2)**0.5
            
            # Check if green ball matches model center
            diff_mc = ((green_ball_pos[0] - model_center[0])**2 + 
                      (green_ball_pos[1] - model_center[1])**2 + 
                      (green_ball_pos[2] - model_center[2])**2)**0.5
            
            print(f"   Green ball vs Expected:      {diff_gb:.6f}")
            print(f"   Green ball vs Model center:  {diff_mc:.6f}")
            
            if diff_gb < 0.01:
                print(f"   ✅ Green ball matches expected position")
            else:
                print(f"   ❌ Green ball does NOT match expected position")
                
            if diff_mc < 0.01:
                print(f"   ✅ Green ball matches model center")
            else:
                print(f"   ❌ Green ball does NOT match model center")
    
    # Initial state
    debug_step("INITIAL STATE")
    
    # Perform the problematic sequence
    rotations = [
        ("X+ 15°", 'x', 15.0),
        ("X+ 15° (2nd)", 'x', 15.0), 
        ("X+ 15° (3rd)", 'x', 15.0),
        ("Y+ 15°", 'y', 15.0)
    ]
    
    for step_name, axis, degrees in rotations:
        print(f"\n🔄 Performing {step_name}...")
        viewer.rotate_shape(axis, degrees)
        app.processEvents()
        time.sleep(1)
        
        debug_step(f"AFTER {step_name}")
    
    print(f"\n🔍 CUMULATIVE ROTATION TEST COMPLETE")
    print(f"The above output shows exactly where the green ball is vs where it should be.")
    print(f"Press Ctrl+C to exit")
    
    try:
        app.exec_()
    except KeyboardInterrupt:
        print("Exiting...")

if __name__ == "__main__":
    debug_cumulative_rotation()
