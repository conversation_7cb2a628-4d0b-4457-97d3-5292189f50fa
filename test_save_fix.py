#!/usr/bin/env python3
"""
Test the fixed save system
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== SAVE FIX TEST ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"🔧 Testing with: {test_file}")
    
    print("\n=== STEP 1: LOAD AND ROTATE ===")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ STEP file loaded")
        
        # Apply rotation
        print("🔄 Applying X+90° rotation")
        viewer._apply_model_rotation("top", "x", 90.0)
        
        # Get rotation values
        current_rot = viewer._extract_rotation_from_vtk_actor("top")
        orig_rot = viewer.orig_rot_left
        
        delta_x = current_rot['x'] - orig_rot['x']
        print(f"   Current rotation: {current_rot}")
        print(f"   Original rotation: {orig_rot}")
        print(f"   Delta X rotation: {delta_x:.1f}°")
        
        print("\n=== STEP 2: SAVE WITH FIXED METHOD ===")
        test_save_file = "test_save_fix.step"
        
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
        
        # Call save method
        loader = viewer.step_loader_left
        current_pos = viewer._extract_position_from_display("top")
        
        success = viewer._save_step_with_transformations(
            test_save_file, loader, current_pos, current_rot, viewer.orig_pos_left, orig_rot
        )
        
        if success and os.path.exists(test_save_file):
            file_size = os.path.getsize(test_save_file)
            print(f"✅ Save successful: {test_save_file} ({file_size} bytes)")
            
            print("\n=== STEP 3: LOAD SAVED FILE ===")
            viewer.active_viewer = "bottom"
            load_success = viewer.load_step_file_direct(test_save_file)
            
            if load_success:
                print("✅ Saved file loaded in bottom viewer")
                
                # Get geometry bounds to check if rotation was preserved
                bottom_renderer = viewer.vtk_renderer_right
                if hasattr(bottom_renderer, 'step_actors') and bottom_renderer.step_actors:
                    saved_actor = bottom_renderer.step_actors[0]
                    saved_bounds = saved_actor.GetBounds()
                    saved_center = [
                        (saved_bounds[0] + saved_bounds[1]) / 2,
                        (saved_bounds[2] + saved_bounds[3]) / 2,
                        (saved_bounds[4] + saved_bounds[5]) / 2
                    ]
                    print(f"   Saved file geometry center: {saved_center}")
                    
                    # Compare with original file geometry
                    top_renderer = viewer.vtk_renderer_left
                    if hasattr(top_renderer, 'step_actors') and top_renderer.step_actors:
                        rotated_actor = top_renderer.step_actors[0]
                        rotated_bounds = rotated_actor.GetBounds()
                        rotated_center = [
                            (rotated_bounds[0] + rotated_bounds[1]) / 2,
                            (rotated_bounds[2] + rotated_bounds[3]) / 2,
                            (rotated_bounds[4] + rotated_bounds[5]) / 2
                        ]
                        print(f"   Rotated geometry center: {rotated_center}")
                        
                        # Check if they match (indicating rotation was preserved)
                        import numpy as np
                        diff = np.array(saved_center) - np.array(rotated_center)
                        matches = np.linalg.norm(diff) < 0.5
                        
                        print(f"   Centers match: {matches} (diff: {diff})")
                        
                        if matches:
                            print("✅ SUCCESS: Rotation preserved in saved file!")
                        else:
                            print("❌ FAILURE: Rotation not preserved in saved file!")
                else:
                    print("❌ No geometry found in saved file")
            else:
                print("❌ Failed to load saved file")
                
            # Clean up
            os.remove(test_save_file)
            
        else:
            print("❌ Save failed")
            
    else:
        print("❌ Failed to load STEP file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== SAVE FIX TEST FINISHED ===")
