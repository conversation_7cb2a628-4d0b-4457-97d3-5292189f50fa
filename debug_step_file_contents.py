#!/usr/bin/env python3
"""
Debug the actual STEP file contents to verify rotation is really saved
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== DEBUG STEP FILE CONTENTS ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Testing with: {test_file}")
    
    # Load file
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ File loaded")
        
        # Apply rotation
        print("\nApplying 90° X rotation...")
        viewer._apply_model_rotation("top", "x", 90.0)
        
        # Save the rotated file
        print("\nSaving rotated file...")
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            actor = renderer.step_actors[0]
            loader = viewer.step_loader_left
            
            test_save_file = "debug_rotated.step"
            if os.path.exists(test_save_file):
                os.remove(test_save_file)
            
            # Save using the new method
            success = loader.save_transformed_actor_as_step(test_save_file, actor)
            print(f"Save result: {success}")
            
            if os.path.exists(test_save_file):
                file_size = os.path.getsize(test_save_file)
                print(f"File created: {file_size} bytes")
                
                # Read and compare STEP file contents
                print("\n=== COMPARING STEP FILE CONTENTS ===")
                
                # Read original file
                print(f"\nReading original file: {test_file}")
                with open(test_file, 'r') as f:
                    original_lines = f.readlines()
                
                # Read saved file
                print(f"Reading saved file: {test_save_file}")
                with open(test_save_file, 'r') as f:
                    saved_lines = f.readlines()
                
                print(f"Original file: {len(original_lines)} lines")
                print(f"Saved file: {len(saved_lines)} lines")
                
                # Look for AXIS2_PLACEMENT_3D entries
                print("\n=== SEARCHING FOR AXIS2_PLACEMENT_3D ===")
                
                def find_axis_placements(lines, filename):
                    axis_placements = []
                    for i, line in enumerate(lines):
                        if 'AXIS2_PLACEMENT_3D' in line:
                            axis_placements.append((i+1, line.strip()))
                    return axis_placements
                
                original_axis = find_axis_placements(original_lines, test_file)
                saved_axis = find_axis_placements(saved_lines, test_save_file)
                
                print(f"\nOriginal file AXIS2_PLACEMENT_3D entries: {len(original_axis)}")
                for line_num, content in original_axis[:3]:  # Show first 3
                    print(f"  Line {line_num}: {content}")
                
                print(f"\nSaved file AXIS2_PLACEMENT_3D entries: {len(saved_axis)}")
                for line_num, content in saved_axis[:3]:  # Show first 3
                    print(f"  Line {line_num}: {content}")
                
                # Compare the coordinate system entries
                if original_axis and saved_axis:
                    print(f"\n=== COMPARING COORDINATE SYSTEMS ===")
                    
                    # Check if they're different
                    axis_different = False
                    for i, ((orig_line, orig_content), (saved_line, saved_content)) in enumerate(zip(original_axis, saved_axis)):
                        if orig_content != saved_content:
                            axis_different = True
                            print(f"DIFFERENCE found in entry {i+1}:")
                            print(f"  Original: {orig_content}")
                            print(f"  Saved:    {saved_content}")
                            break
                    
                    if not axis_different:
                        print("❌ PROBLEM: AXIS2_PLACEMENT_3D entries are IDENTICAL!")
                        print("This means the coordinate system was NOT updated in the saved file")
                        print("The geometry might be transformed but the coordinate system metadata is unchanged")
                    else:
                        print("✅ GOOD: AXIS2_PLACEMENT_3D entries are DIFFERENT!")
                        print("The coordinate system was updated in the saved file")
                
                # Look for direction vectors
                print("\n=== SEARCHING FOR DIRECTION VECTORS ===")
                
                def find_directions(lines, filename):
                    directions = []
                    for i, line in enumerate(lines):
                        if 'DIRECTION' in line and ('(' in line and ')' in line):
                            directions.append((i+1, line.strip()))
                    return directions
                
                original_dirs = find_directions(original_lines, test_file)
                saved_dirs = find_directions(saved_lines, test_save_file)
                
                print(f"\nOriginal file DIRECTION entries: {len(original_dirs)}")
                for line_num, content in original_dirs[:5]:  # Show first 5
                    print(f"  Line {line_num}: {content}")
                
                print(f"\nSaved file DIRECTION entries: {len(saved_dirs)}")
                for line_num, content in saved_dirs[:5]:  # Show first 5
                    print(f"  Line {line_num}: {content}")
                
                # Test loading the saved file to see what the unified system reads
                print("\n=== TESTING LOAD OF SAVED FILE ===")
                viewer.active_viewer = "bottom"
                load_success = viewer.load_step_file_direct(test_save_file)
                
                if load_success:
                    print("✅ Saved file loaded successfully")
                    
                    # Check what the unified system reads from the saved file
                    print("Checking unified system analysis of saved file...")
                    bottom_loader = viewer.step_loader_right
                    if hasattr(bottom_loader, 'axis_data') and bottom_loader.axis_data:
                        print(f"Saved file axis_data: {bottom_loader.axis_data}")
                        
                        # Compare with original
                        top_loader = viewer.step_loader_left
                        if hasattr(top_loader, 'axis_data') and top_loader.axis_data:
                            print(f"Original file axis_data: {top_loader.axis_data}")
                            
                            if bottom_loader.axis_data == top_loader.axis_data:
                                print("❌ CRITICAL PROBLEM: axis_data is IDENTICAL!")
                                print("The STEP file coordinate system was NOT updated")
                                print("This is why the yellow text shows (0,0,0) rotation")
                            else:
                                print("✅ GOOD: axis_data is DIFFERENT!")
                                print("The STEP file coordinate system was updated")
                        else:
                            print("❌ No axis_data in original loader")
                    else:
                        print("❌ No axis_data in saved file loader")
                else:
                    print("❌ Failed to load saved file")
                
                # Clean up
                os.remove(test_save_file)
            else:
                print("❌ No file created")
        else:
            print("❌ No actors found")
    else:
        print("❌ Failed to load file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== DEBUG FINISHED ===")
