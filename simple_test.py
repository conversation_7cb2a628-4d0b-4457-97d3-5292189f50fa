#!/usr/bin/env python3
"""
Simple test: load, rotate, save
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

def simple_test():
    print("=== SIMPLE TEST: Load -> Rotate -> Save ===")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Find STEP file
    step_files = [f for f in os.listdir('.') if f.endswith('.STEP')]
    if not step_files:
        print("❌ No STEP files found")
        return False
    
    original_file = step_files[0]
    output_file = "simple_rotated.step"
    
    print(f"📁 Original: {original_file}")
    print(f"📁 Output: {output_file}")
    
    # Load file
    viewer.active_viewer = "top"
    if not viewer.load_step_file_direct(original_file):
        print("❌ Failed to load")
        return False
    
    print("✅ Loaded")
    
    # Apply 90° X rotation
    viewer._apply_model_rotation('top', 'x', 90.0)
    print("✅ Rotated 90° around X")
    
    # Save with current rotation
    loader = viewer.step_loader_left
    current_rot = viewer.current_rot_left
    
    # Make sure we have original rotation set
    if not hasattr(viewer, 'orig_rot_left'):
        viewer.orig_rot_left = {'x': 0, 'y': 0, 'z': 0}
    
    success = viewer._save_step_with_current_rotation(output_file, loader, current_rot)
    
    if not success:
        print("❌ Save failed")
        return False
    
    print("✅ Saved")
    
    # Check if files are different
    with open(original_file, 'r', encoding='utf-8', errors='ignore') as f:
        orig_content = f.read()
    
    with open(output_file, 'r', encoding='utf-8', errors='ignore') as f:
        new_content = f.read()
    
    if orig_content == new_content:
        print("❌ Files are identical - rotation not applied")
        return False
    else:
        print("✅ Files are different - rotation applied")
        return True

if __name__ == "__main__":
    if simple_test():
        print("\n🎉 SUCCESS: Load-rotate-save works!")
    else:
        print("\n💥 FAILED: Load-rotate-save doesn't work")
