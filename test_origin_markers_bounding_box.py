#!/usr/bin/env python3
"""
TARGETED TEST: Origin Markers and Bounding Box Movement
Specifically tests that origin markers and bounding box move with the model
"""

import sys
import os
import time
import numpy as np
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

# Add current directory to path
sys.path.append('.')

def find_all_actors_in_renderer(renderer):
    """Find all actors in a VTK renderer and categorize them"""
    actors = []
    if renderer:
        actor_collection = renderer.GetActors()
        actor_collection.InitTraversal()
        
        while True:
            actor = actor_collection.GetNextActor()
            if not actor:
                break
            
            # Get actor properties
            actor_info = {
                'actor': actor,
                'position': actor.GetPosition(),
                'orientation': actor.GetOrientation(),
                'visibility': actor.GetVisibility(),
                'bounds': actor.GetBounds(),
                'type': type(actor).__name__
            }
            
            # Try to identify actor type by properties
            bounds = actor.GetBounds()
            if bounds:
                size = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4])
                actor_info['size'] = size
                
                # Categorize by size and properties
                if size < 1.0:
                    actor_info['category'] = 'marker'  # Small objects likely markers
                elif size > 100.0:
                    actor_info['category'] = 'model'   # Large objects likely model parts
                else:
                    actor_info['category'] = 'unknown'
            
            actors.append(actor_info)
    
    return actors

def test_origin_markers_and_bounding_box():
    """Test origin markers and bounding box movement"""
    
    print("🎯 TARGETED TEST: Origin Markers and Bounding Box Movement")
    print("=" * 70)
    
    # Import and create the application
    from step_viewer import StepViewerTDK
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for GUI to initialize
    QTest.qWait(2000)
    print("✅ GUI initialized")
    
    # Load a test STEP file to ensure all visual elements are present
    test_files = [
        r"e:\python\viewer\save\test_part.step",
        r"e:\python\viewer\save\sample.step",
        r"e:\python\3d-view\test.step",
        r"e:\python\3d-view\sample.step"
    ]
    
    test_file = None
    for f in test_files:
        if os.path.exists(f):
            test_file = f
            break
    
    if test_file:
        print(f"📁 Loading test file: {test_file}")
        try:
            viewer.load_step_file_direct(test_file)
            QTest.qWait(3000)  # Wait for file to load
            print("✅ STEP file loaded successfully")
        except Exception as e:
            print(f"❌ Error loading STEP file: {e}")
            print("⚠️  Continuing test without STEP file...")
    else:
        print("⚠️  No test STEP file found - testing with default geometry")
    
    print("\n🔍 ANALYZING ALL ACTORS IN TOP RENDERER...")
    print("-" * 60)
    
    # Get TOP renderer
    top_renderer = None
    if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
        top_renderer = viewer.vtk_renderer_left.renderer
    
    if not top_renderer:
        print("❌ Could not access TOP renderer")
        return False
    
    # Find all actors
    all_actors = find_all_actors_in_renderer(top_renderer)
    print(f"✅ Found {len(all_actors)} total actors in TOP renderer")
    
    # Categorize actors
    markers = [a for a in all_actors if a['category'] == 'marker']
    model_parts = [a for a in all_actors if a['category'] == 'model']
    unknown = [a for a in all_actors if a['category'] == 'unknown']
    
    print(f"   📊 Markers (small): {len(markers)}")
    print(f"   📊 Model parts (large): {len(model_parts)}")
    print(f"   📊 Unknown size: {len(unknown)}")
    
    # Show details of each category
    print(f"\n📋 DETAILED ACTOR ANALYSIS:")
    for i, actor_info in enumerate(all_actors):
        pos = actor_info['position']
        orient = actor_info['orientation']
        bounds = actor_info['bounds']
        size = actor_info.get('size', 0)
        visible = actor_info['visibility']
        
        print(f"   Actor {i+1}: {actor_info['category'].upper()}")
        print(f"     Type: {actor_info['type']}")
        print(f"     Position: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        print(f"     Orientation: ({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
        print(f"     Size: {size:.3f}")
        print(f"     Visible: {visible}")
        print(f"     Bounds: {bounds}")
        print()
    
    if len(all_actors) == 0:
        print("❌ No actors found to test!")
        return False
    
    print(f"\n🧪 TESTING MOVEMENT OF ALL {len(all_actors)} ACTORS...")
    print("-" * 60)
    
    # Capture BEFORE state
    before_states = []
    for i, actor_info in enumerate(all_actors):
        actor = actor_info['actor']
        before_state = {
            'index': i,
            'position': actor.GetPosition(),
            'orientation': actor.GetOrientation(),
            'category': actor_info['category']
        }
        before_states.append(before_state)
    
    print(f"📊 Captured BEFORE state for {len(before_states)} actors")
    
    # Apply X+90 rotation
    print(f"\n🔄 Applying X+90° rotation...")
    try:
        viewer.rotate_shape('x', 90)
        QTest.qWait(2000)  # Wait for rotation and rendering
        
        # Force update
        if hasattr(viewer, 'update_text_overlays'):
            viewer.update_text_overlays()
        QTest.qWait(500)
        
        print("✅ Rotation applied")
        
    except Exception as e:
        print(f"❌ Rotation failed: {e}")
        return False
    
    # Capture AFTER state
    after_states = []
    for i, actor_info in enumerate(all_actors):
        actor = actor_info['actor']
        after_state = {
            'index': i,
            'position': actor.GetPosition(),
            'orientation': actor.GetOrientation(),
            'category': actor_info['category']
        }
        after_states.append(after_state)
    
    print(f"📊 Captured AFTER state for {len(after_states)} actors")
    
    # Compare states
    print(f"\n📈 MOVEMENT ANALYSIS:")
    print("-" * 40)
    
    moved_actors = 0
    stationary_actors = 0
    
    for before, after in zip(before_states, after_states):
        # Calculate position difference
        pos_diff = sum(abs(a - b) for a, b in zip(after['position'], before['position']))
        
        # Calculate orientation difference
        orient_diff = sum(abs(a - b) for a, b in zip(after['orientation'], before['orientation']))
        
        # Check for significant movement (threshold for detecting change)
        pos_moved = pos_diff > 0.001
        orient_moved = orient_diff > 0.1  # degrees
        
        category = before['category']
        index = before['index']
        
        if pos_moved or orient_moved:
            print(f"   ✅ Actor {index+1} ({category.upper()}): MOVED")
            print(f"      Position: {before['position']} → {after['position']}")
            print(f"      Orientation: {before['orientation']} → {after['orientation']}")
            print(f"      Δ Position: {pos_diff:.6f}")
            print(f"      Δ Orientation: {orient_diff:.1f}°")
            moved_actors += 1
        else:
            print(f"   ⚠️  Actor {index+1} ({category.upper()}): NO MOVEMENT")
            print(f"      Position: {before['position']} (unchanged)")
            print(f"      Orientation: {before['orientation']} (unchanged)")
            stationary_actors += 1
        print()
    
    # Check for specific origin marker patterns
    print(f"\n🎯 ORIGIN MARKER ANALYSIS:")
    print("-" * 40)
    
    # Look for actors that might be origin markers
    potential_origin_markers = []
    for i, actor_info in enumerate(all_actors):
        pos = actor_info['position']
        size = actor_info.get('size', 0)
        
        # Origin markers are typically small and at or near origin
        if size < 10.0 and (abs(pos[0]) < 1.0 and abs(pos[1]) < 1.0 and abs(pos[2]) < 1.0):
            potential_origin_markers.append((i, actor_info))
    
    if potential_origin_markers:
        print(f"✅ Found {len(potential_origin_markers)} potential origin markers:")
        for i, (actor_idx, actor_info) in enumerate(potential_origin_markers):
            print(f"   Marker {i+1}: Actor {actor_idx+1}")
            print(f"     Position: {actor_info['position']}")
            print(f"     Size: {actor_info.get('size', 0):.3f}")
            
            # Check if this marker moved
            before = before_states[actor_idx]
            after = after_states[actor_idx]
            pos_diff = sum(abs(a - b) for a, b in zip(after['position'], before['position']))
            orient_diff = sum(abs(a - b) for a, b in zip(after['orientation'], before['orientation']))
            
            if pos_diff > 0.001 or orient_diff > 0.1:
                print(f"     Status: ✅ MOVED with model")
            else:
                print(f"     Status: ❌ DID NOT MOVE")
            print()
    else:
        print("⚠️  No obvious origin markers found (small objects near origin)")
    
    # Final summary
    print("=" * 70)
    print("🏁 ORIGIN MARKERS & BOUNDING BOX TEST RESULTS:")
    print(f"📊 Total actors analyzed: {len(all_actors)}")
    print(f"✅ Actors that moved: {moved_actors}")
    print(f"⚠️  Actors that didn't move: {stationary_actors}")
    print(f"🎯 Potential origin markers: {len(potential_origin_markers)}")
    
    if moved_actors > 0:
        print("✅ SOME visual elements are moving with the model")
    else:
        print("❌ NO visual elements moved - this indicates a problem")
    
    if stationary_actors > 0:
        print("⚠️  SOME visual elements are NOT moving - need investigation")
    
    success_rate = moved_actors / len(all_actors) * 100 if all_actors else 0
    print(f"📈 Movement success rate: {success_rate:.1f}%")
    
    return moved_actors > 0

if __name__ == "__main__":
    success = test_origin_markers_and_bounding_box()
    sys.exit(0 if success else 1)
