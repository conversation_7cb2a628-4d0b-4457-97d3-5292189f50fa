#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

from step_viewer import Step<PERSON>iewerTD<PERSON>

def test_save():
    print("=== TESTING SAVE PROCESS ===")
    
    # Create viewer
    viewer = StepViewerTDK()
    
    # Load file
    print("1. Loading demo.step...")
    success = viewer.load_step_file_direct('demo.step')
    if not success:
        print("❌ Failed to load demo.step")
        return
    print("✅ File loaded")
    
    # Apply rotation
    print("2. Applying rotations...")
    viewer._apply_model_rotation('top', 'x', 30.0)
    viewer._apply_model_rotation('top', 'y', 45.0) 
    viewer._apply_model_rotation('top', 'z', 60.0)
    print("✅ Rotations applied")
    
    # Get current rotation values
    rotated_rot = viewer.current_rot_left.copy()
    print(f"Current rotation: {rotated_rot}")
    
    # Save with debug
    print("3. Starting save process...")
    loader = viewer.step_loader_left
    
    # Check if loader has original shape
    if hasattr(loader, 'original_shape') and loader.original_shape:
        print("✅ Loader has original shape")
    else:
        print("❌ Loader missing original shape")
        return
    
    # Check if we have actors
    if hasattr(viewer.vtk_renderer_left, 'step_actors') and viewer.vtk_renderer_left.step_actors:
        print("✅ VTK actors available")
        actor = viewer.vtk_renderer_left.step_actors[0]
        matrix = actor.GetMatrix()
        print(f"Actor matrix available: {matrix is not None}")
    else:
        print("❌ No VTK actors available")
        return
    
    # Try save
    success = viewer._save_step_with_current_rotation('test_debug.step', loader, rotated_rot)
    print(f"Save result: {success}")
    
    if success and os.path.exists('test_debug.step'):
        file_size = os.path.getsize('test_debug.step')
        print(f"✅ File saved: test_debug.step ({file_size} bytes)")
    else:
        print("❌ Save failed or file not created")

if __name__ == "__main__":
    test_save()
