#!/usr/bin/env python3
"""
Complete test to verify visual rotation is preserved in saved files
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import numpy as np

print("=== COMPLETE VISUAL ROTATION TEST ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"🔧 Testing with: {test_file}")
    
    print("\n=== STEP 1: LOAD ORIGINAL FILE ===")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ Original STEP file loaded in TOP viewer")
        
        # Get original geometry bounds and center
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            original_actor = renderer.step_actors[0]
            original_bounds = original_actor.GetBounds()
            original_center = [
                (original_bounds[0] + original_bounds[1]) / 2,
                (original_bounds[2] + original_bounds[3]) / 2,
                (original_bounds[4] + original_bounds[5]) / 2
            ]
            print(f"   Original geometry center: {original_center}")
            print(f"   Original geometry bounds: {original_bounds}")
        
        print("\n=== STEP 2: APPLY ROTATION ===")
        print("🔄 Applying X+90° rotation (large rotation for clear visual difference)")
        viewer._apply_model_rotation("top", "x", 90.0)
        
        # Get rotated geometry bounds and center
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            rotated_actor = renderer.step_actors[0]
            rotated_bounds = rotated_actor.GetBounds()
            rotated_center = [
                (rotated_bounds[0] + rotated_bounds[1]) / 2,
                (rotated_bounds[2] + rotated_bounds[3]) / 2,
                (rotated_bounds[4] + rotated_bounds[5]) / 2
            ]
            print(f"   Rotated geometry center: {rotated_center}")
            print(f"   Rotated geometry bounds: {rotated_bounds}")
            
            # Check if geometry actually moved (visual rotation)
            center_diff = np.array(rotated_center) - np.array(original_center)
            bounds_diff = np.array(rotated_bounds) - np.array(original_bounds)
            
            center_moved = np.linalg.norm(center_diff) > 0.1
            bounds_changed = np.linalg.norm(bounds_diff) > 0.1
            
            print(f"   Geometry center moved: {center_moved} (diff: {center_diff})")
            print(f"   Geometry bounds changed: {bounds_changed}")
            
            if center_moved or bounds_changed:
                print("✅ Visual rotation applied - geometry moved")
            else:
                print("❌ Visual rotation NOT applied - geometry unchanged")
                
        print("\n=== STEP 3: SAVE ROTATED FILE ===")
        test_save_file = "test_visual_rotation.step"
        
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
        
        # Save using the actual save method
        loader = viewer.step_loader_left
        current_pos = viewer._extract_position_from_display("top")
        current_rot = viewer._extract_rotation_from_vtk_actor("top")
        orig_pos = viewer.orig_pos_left
        orig_rot = viewer.orig_rot_left
        
        print(f"   Saving with rotation delta: {current_rot['x'] - orig_rot['x']:.1f}° X")
        
        success = viewer._save_step_with_transformations(
            test_save_file, loader, current_pos, current_rot, orig_pos, orig_rot
        )
        
        if success and os.path.exists(test_save_file):
            file_size = os.path.getsize(test_save_file)
            print(f"✅ Rotated file saved: {test_save_file} ({file_size} bytes)")
            
            print("\n=== STEP 4: LOAD SAVED FILE IN BOTTOM VIEWER ===")
            viewer.active_viewer = "bottom"
            load_success = viewer.load_step_file_direct(test_save_file)
            
            if load_success:
                print("✅ Saved file loaded in BOTTOM viewer")
                
                # Get saved file geometry bounds and center
                bottom_renderer = viewer.vtk_renderer_right
                if hasattr(bottom_renderer, 'step_actors') and bottom_renderer.step_actors:
                    saved_actor = bottom_renderer.step_actors[0]
                    saved_bounds = saved_actor.GetBounds()
                    saved_center = [
                        (saved_bounds[0] + saved_bounds[1]) / 2,
                        (saved_bounds[2] + saved_bounds[3]) / 2,
                        (saved_bounds[4] + saved_bounds[5]) / 2
                    ]
                    print(f"   Saved file geometry center: {saved_center}")
                    print(f"   Saved file geometry bounds: {saved_bounds}")
                    
                    print("\n=== STEP 5: COMPARE VISUAL GEOMETRY ===")
                    print(f"   Original center: {original_center}")
                    print(f"   Rotated center:  {rotated_center}")
                    print(f"   Saved center:    {saved_center}")
                    
                    # Compare saved geometry with rotated geometry
                    saved_vs_rotated_diff = np.array(saved_center) - np.array(rotated_center)
                    saved_vs_original_diff = np.array(saved_center) - np.array(original_center)
                    
                    saved_matches_rotated = np.linalg.norm(saved_vs_rotated_diff) < 0.1
                    saved_matches_original = np.linalg.norm(saved_vs_original_diff) < 0.1
                    
                    print(f"   Saved matches rotated: {saved_matches_rotated} (diff: {saved_vs_rotated_diff})")
                    print(f"   Saved matches original: {saved_matches_original} (diff: {saved_vs_original_diff})")
                    
                    print("\n=== FINAL RESULT ===")
                    if saved_matches_rotated and not saved_matches_original:
                        print("✅ SUCCESS: Saved file preserves the rotated geometry!")
                        print("   The visual rotation has been correctly saved and loaded.")
                    elif saved_matches_original and not saved_matches_rotated:
                        print("❌ FAILURE: Saved file shows original geometry, rotation lost!")
                        print("   The rotation was not preserved in the saved file.")
                    else:
                        print("❓ UNCLEAR: Geometry comparison inconclusive")
                        print("   Manual visual inspection required.")
                        
                else:
                    print("❌ No geometry found in saved file")
            else:
                print("❌ Failed to load saved file")
                
            # Clean up
            os.remove(test_save_file)
            
        else:
            print("❌ Save failed or file not created")
            
    else:
        print("❌ Failed to load original STEP file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== COMPLETE VISUAL ROTATION TEST FINISHED ===")
