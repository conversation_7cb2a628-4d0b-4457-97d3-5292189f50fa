#!/usr/bin/env python3
"""
Test to verify the initial values issue after STEP file loading.
This will load a STEP file and check what the initial display numbers show.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from step_viewer import <PERSON><PERSON><PERSON><PERSON>

def test_initial_values():
    """Test the initial values after loading a STEP file"""
    print("🔍 TESTING INITIAL VALUES AFTER STEP FILE LOADING")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    viewer = StepViewer()
    
    # Load a STEP file
    test_file = "SOIC16P127_1270X940X610L89X51.STEP"
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    print(f"📂 Loading STEP file: {test_file}")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if not success:
        print("❌ Failed to load STEP file")
        return False
    
    print("✅ STEP file loaded successfully")
    print()
    
    # Check the tracking variables after loading
    print("🔍 TRACKING VARIABLES AFTER LOADING:")
    print(f"   orig_pos_left: {getattr(viewer, 'orig_pos_left', 'NOT SET')}")
    print(f"   orig_rot_left: {getattr(viewer, 'orig_rot_left', 'NOT SET')}")
    print(f"   current_pos_left: {getattr(viewer, 'current_pos_left', 'NOT SET')}")
    print(f"   current_rot_left: {getattr(viewer, 'current_rot_left', 'NOT SET')}")
    print()
    
    # Calculate what the display numbers should show
    print("🔢 CALCULATING DISPLAY NUMBERS:")
    display_data = viewer._calculate_unified_display_numbers("top")
    
    print(f"   Model: {display_data['model']}")
    print(f"   Local Origin: {display_data['local_origin']}")
    print()
    
    # Check if Model and Local Origin match
    if display_data['model'] == display_data['local_origin'].replace("Local Origin", "Model"):
        print("✅ Model and Local Origin numbers MATCH")
    else:
        print("❌ Model and Local Origin numbers DO NOT MATCH")
    
    # Check what the user_only_rot calculation gives us
    current_rot = getattr(viewer, 'current_rot_left', {'x': 0, 'y': 0, 'z': 0})
    initial_rot = getattr(viewer, 'orig_rot_left', {'x': 0, 'y': 0, 'z': 0})
    
    user_only_rot = {
        'x': current_rot['x'] - initial_rot['x'],
        'y': current_rot['y'] - initial_rot['y'],
        'z': current_rot['z'] - initial_rot['z']
    }
    
    print("🔍 USER ROTATION CALCULATION:")
    print(f"   current_rot: {current_rot}")
    print(f"   initial_rot (orig_rot): {initial_rot}")
    print(f"   user_only_rot (current - initial): {user_only_rot}")
    print()
    
    # Check if user_only_rot is near zero (should be for initial load)
    user_has_rotated = any(abs(user_only_rot[axis]) > 0.001 for axis in ['x', 'y', 'z'])
    print(f"🔍 USER HAS ROTATED: {user_has_rotated}")
    
    if user_has_rotated:
        print("❌ PROBLEM: User rotation detected on initial load!")
        print("   This means current_rot != orig_rot, which is wrong for initial state")
    else:
        print("✅ CORRECT: No user rotation detected on initial load")
    
    print()
    print("🎯 EXPECTED BEHAVIOR:")
    print("   1. After loading STEP file, current_rot should equal orig_rot")
    print("   2. user_only_rot should be (0,0,0)")
    print("   3. Display should show raw STEP file direction vectors")
    print("   4. Model and Local Origin should show identical values")
    
    return True

if __name__ == "__main__":
    test_initial_values()
