#!/usr/bin/env python3
"""
REAL DEBUG PROGRAM: Test with actual STEP file loading and rotation
This program will actually load a STEP file and test real rotations
"""

import sys
import os
import time

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_real_step_loading():
    """Test with real STEP file loading"""
    print("🔧 REAL DEBUG: Testing actual STEP file loading and rotation")
    print("=" * 70)
    
    try:
        # Import Qt first
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # Create QApplication
        app = QApplication(sys.argv)
        
        # Import the main class
        from step_viewer import StepViewerTDK
        
        # Create the viewer
        viewer = StepViewerTDK()
        viewer.show()
        
        print("✅ Viewer created and shown")
        
        # Set up a timer to run tests after the GUI is ready
        def run_tests():
            print("\n🔍 TESTING PHASE 1: Check initial state")
            
            # Check if we have any STEP files to test with
            step_files = []
            for ext in ['*.step', '*.stp']:
                import glob
                step_files.extend(glob.glob(ext))
            
            if not step_files:
                print("❌ No STEP files found in current directory")
                print("   Please put a .step or .stp file in the current directory")
                app.quit()
                return
            
            test_file = step_files[0]
            print(f"📁 Found STEP file: {test_file}")
            
            # Try to load the file
            try:
                print(f"🔄 Loading {test_file}...")
                # Simulate file loading (we'd need to trigger the actual load mechanism)
                # For now, let's check what happens when we call the calculation function
                
                # Check initial tracking values
                print("\n🔍 CHECKING INITIAL TRACKING VALUES:")
                current_rot_left = getattr(viewer, 'current_rot_left', None)
                current_pos_left = getattr(viewer, 'current_pos_left', None)
                
                print(f"   current_rot_left = {current_rot_left}")
                print(f"   current_pos_left = {current_pos_left}")
                
                if current_rot_left is None:
                    print("❌ current_rot_left is None - tracking variables not initialized")
                if current_pos_left is None:
                    print("❌ current_pos_left is None - tracking variables not initialized")
                
                # Try to call the calculation function
                print("\n🔍 TESTING CALCULATION FUNCTION:")
                try:
                    if hasattr(viewer, '_calculate_unified_display_numbers'):
                        results = viewer._calculate_unified_display_numbers('top')
                        print("✅ Calculation function exists and runs")
                        print(f"   Model: {results.get('model', 'ERROR')}")
                        print(f"   Local Origin: {results.get('local_origin', 'ERROR')}")
                    else:
                        print("❌ _calculate_unified_display_numbers method not found")
                except Exception as e:
                    print(f"❌ Error calling calculation function: {e}")
                    import traceback
                    traceback.print_exc()
                
                # Test rotation function
                print("\n🔍 TESTING ROTATION FUNCTION:")
                try:
                    if hasattr(viewer, '_apply_unified_rotation'):
                        print("🔄 Applying test rotation: X-axis, 45 degrees")
                        viewer._apply_unified_rotation('x', 45)
                        
                        # Check if tracking values changed
                        new_rot = getattr(viewer, 'current_rot_left', None)
                        print(f"   After rotation: current_rot_left = {new_rot}")
                        
                        # Test calculation again
                        if hasattr(viewer, '_calculate_unified_display_numbers'):
                            results = viewer._calculate_unified_display_numbers('top')
                            print("   After rotation calculation:")
                            print(f"     Model: {results.get('model', 'ERROR')}")
                            print(f"     Local Origin: {results.get('local_origin', 'ERROR')}")
                    else:
                        print("❌ _apply_unified_rotation method not found")
                except Exception as e:
                    print(f"❌ Error testing rotation: {e}")
                    import traceback
                    traceback.print_exc()
                
            except Exception as e:
                print(f"❌ Error in test: {e}")
                import traceback
                traceback.print_exc()
            
            print("\n🏁 REAL DEBUG COMPLETE")
            
            # Close after a delay
            QTimer.singleShot(2000, app.quit)
        
        # Run tests after 1 second to let GUI initialize
        QTimer.singleShot(1000, run_tests)
        
        # Start the event loop
        app.exec_()
        
    except Exception as e:
        print(f"❌ CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_real_step_loading()
