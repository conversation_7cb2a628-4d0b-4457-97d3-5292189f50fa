#!/usr/bin/env python3
"""
Debug script to trace rotation saving issues
This will help us understand exactly what's happening during rotation and save operations
"""

import sys
import os
sys.path.append('.')

def debug_rotation_tracking():
    """Debug the rotation tracking system"""
    print("=" * 60)
    print("DEBUGGING ROTATION TRACKING SYSTEM")
    print("=" * 60)
    
    try:
        # Import the main application
        from step_viewer import StepViewerTDK
        from PyQt5.QtWidgets import QApplication
        
        # Create minimal app instance
        app = QApplication(sys.argv)
        viewer = StepViewerTDK()
        
        # Check initial rotation state
        print("\n1. INITIAL ROTATION STATE:")
        print(f"   current_rot_left exists: {hasattr(viewer, 'current_rot_left')}")
        print(f"   current_rot_right exists: {hasattr(viewer, 'current_rot_right')}")
        
        if hasattr(viewer, 'current_rot_left'):
            print(f"   current_rot_left: {viewer.current_rot_left}")
        if hasattr(viewer, 'current_rot_right'):
            print(f"   current_rot_right: {viewer.current_rot_right}")
            
        # Check original rotation state
        print(f"   orig_rot_left exists: {hasattr(viewer, 'orig_rot_left')}")
        print(f"   orig_rot_right exists: {hasattr(viewer, 'orig_rot_right')}")
        
        if hasattr(viewer, 'orig_rot_left'):
            print(f"   orig_rot_left: {viewer.orig_rot_left}")
        if hasattr(viewer, 'orig_rot_right'):
            print(f"   orig_rot_right: {viewer.orig_rot_right}")
            
        # Test rotation extraction method
        print("\n2. TESTING ROTATION EXTRACTION:")
        try:
            top_rot = viewer._extract_rotation_from_vtk_actor("top")
            print(f"   Top viewer rotation: {top_rot}")
        except Exception as e:
            print(f"   ERROR extracting top rotation: {e}")
            
        try:
            bottom_rot = viewer._extract_rotation_from_vtk_actor("bottom")
            print(f"   Bottom viewer rotation: {bottom_rot}")
        except Exception as e:
            print(f"   ERROR extracting bottom rotation: {e}")
            
        # Test position extraction method
        print("\n3. TESTING POSITION EXTRACTION:")
        try:
            top_pos = viewer._extract_position_from_display("top")
            print(f"   Top viewer position: {top_pos}")
        except Exception as e:
            print(f"   ERROR extracting top position: {e}")
            
        try:
            bottom_pos = viewer._extract_position_from_display("bottom")
            print(f"   Bottom viewer position: {bottom_pos}")
        except Exception as e:
            print(f"   ERROR extracting bottom position: {e}")
            
        # Check STEP loaders
        print("\n4. CHECKING STEP LOADERS:")
        print(f"   step_loader_left exists: {hasattr(viewer, 'step_loader_left')}")
        print(f"   step_loader_right exists: {hasattr(viewer, 'step_loader_right')}")
        
        if hasattr(viewer, 'step_loader_left') and viewer.step_loader_left:
            print(f"   step_loader_left.current_filename: {getattr(viewer.step_loader_left, 'current_filename', 'None')}")
        if hasattr(viewer, 'step_loader_right') and viewer.step_loader_right:
            print(f"   step_loader_right.current_filename: {getattr(viewer.step_loader_right, 'current_filename', 'None')}")
            
        app.quit()
        return True
        
    except Exception as e:
        print(f"ERROR in debug_rotation_tracking: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_step_file_analysis():
    """Debug STEP file coordinate system analysis"""
    print("\n" + "=" * 60)
    print("DEBUGGING STEP FILE ANALYSIS")
    print("=" * 60)
    
    # Look for STEP files in current directory
    step_files = [f for f in os.listdir('.') if f.endswith('.step')]
    print(f"\nFound STEP files: {step_files}")
    
    if not step_files:
        print("No STEP files found for analysis")
        return False
        
    # Analyze the first STEP file
    step_file = step_files[0]
    print(f"\nAnalyzing: {step_file}")
    
    try:
        with open(step_file, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            
        print(f"Total lines: {len(lines)}")
        
        # Look for AXIS2_PLACEMENT_3D entries
        axis_lines = []
        for i, line in enumerate(lines):
            if 'AXIS2_PLACEMENT_3D' in line:
                axis_lines.append((i+1, line.strip()))
                
        print(f"\nFound {len(axis_lines)} AXIS2_PLACEMENT_3D entries:")
        for line_num, line in axis_lines[:5]:  # Show first 5
            print(f"   Line {line_num}: {line}")
            
        # Look for coordinate system entries
        coord_lines = []
        for i, line in enumerate(lines):
            if 'CARTESIAN_POINT' in line and any(x in line for x in ['0.', '1.', '-']):
                coord_lines.append((i+1, line.strip()))
                
        print(f"\nFound {len(coord_lines)} coordinate entries (showing first 10):")
        for line_num, line in coord_lines[:10]:
            print(f"   Line {line_num}: {line}")
            
        return True
        
    except Exception as e:
        print(f"ERROR analyzing STEP file: {e}")
        return False

def debug_save_transformation_thresholds():
    """Debug the transformation threshold logic"""
    print("\n" + "=" * 60)
    print("DEBUGGING SAVE TRANSFORMATION THRESHOLDS")
    print("=" * 60)
    
    # Test different rotation values against thresholds
    test_rotations = [
        {'x': 0.05, 'y': 0.0, 'z': 0.0},   # Below threshold
        {'x': 0.2, 'y': 0.0, 'z': 0.0},    # Above threshold
        {'x': 5.0, 'y': 0.0, 'z': 0.0},    # Well above threshold
        {'x': 45.0, 'y': 0.0, 'z': 0.0},   # Old threshold
        {'x': 0.0, 'y': 15.0, 'z': 0.0},   # Y rotation
        {'x': 0.0, 'y': 0.0, 'z': 30.0},   # Z rotation
    ]
    
    orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
    
    print("\nTesting rotation thresholds:")
    for i, current_rot in enumerate(test_rotations):
        delta_rot = {
            'x': current_rot['x'] - orig_rot['x'],
            'y': current_rot['y'] - orig_rot['y'],
            'z': current_rot['z'] - orig_rot['z']
        }
        
        # Current threshold logic (should be 0.1)
        rot_changed = (abs(delta_rot['x']) > 0.1 or abs(delta_rot['y']) > 0.1 or abs(delta_rot['z']) > 0.1)
        
        print(f"   Test {i+1}: current={current_rot}, delta={delta_rot}, changed={rot_changed}")
        
    return True

def main():
    """Main debugging function"""
    print("STEP VIEWER ROTATION SAVE DEBUGGING")
    print("=" * 60)
    
    success = True
    
    # Run all debug tests
    success &= debug_rotation_tracking()
    success &= debug_step_file_analysis()
    success &= debug_save_transformation_thresholds()
    
    print("\n" + "=" * 60)
    if success:
        print("DEBUGGING COMPLETED - Check output above for issues")
    else:
        print("DEBUGGING COMPLETED WITH ERRORS - Check error messages above")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    main()
