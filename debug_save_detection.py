#!/usr/bin/env python3
"""
Debug why the save method is not detecting transformations
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== SAVE DETECTION DEBUG TEST ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"🔧 Testing with: {test_file}")
    
    print("\n=== STEP 1: LOAD STEP FILE ===")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ STEP file loaded")
        
        print("\n=== STEP 2: APPLY ROTATION ===")
        print("🔄 Applying X+15° rotation")
        viewer._apply_model_rotation("top", "x", 15.0)
        
        print("\n=== STEP 3: CHECK VTK ACTOR TRANSFORMATIONS ===")
        renderer = viewer.vtk_renderer_left
        
        # Check step_actors
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            print(f"   Found {len(renderer.step_actors)} step_actors")
            for i, actor in enumerate(renderer.step_actors):
                print(f"   Actor {i}:")
                print(f"      UserTransform: {actor.GetUserTransform()}")
                print(f"      Orientation: {actor.GetOrientation()}")
                print(f"      Position: {actor.GetPosition()}")
                print(f"      Matrix: {actor.GetMatrix()}")
                
                # Check if orientation is significant
                orientation = actor.GetOrientation()
                has_rotation = (abs(orientation[0]) > 0.1 or abs(orientation[1]) > 0.1 or abs(orientation[2]) > 0.1)
                print(f"      Has significant rotation: {has_rotation}")
        
        # Check step_actor (single)
        if hasattr(renderer, 'step_actor') and renderer.step_actor:
            print(f"   Found single step_actor")
            actor = renderer.step_actor
            print(f"      UserTransform: {actor.GetUserTransform()}")
            print(f"      Orientation: {actor.GetOrientation()}")
            print(f"      Position: {actor.GetPosition()}")
            print(f"      Matrix: {actor.GetMatrix()}")
            
            # Check if orientation is significant
            orientation = actor.GetOrientation()
            has_rotation = (abs(orientation[0]) > 0.1 or abs(orientation[1]) > 0.1 or abs(orientation[2]) > 0.1)
            print(f"      Has significant rotation: {has_rotation}")
        
        print("\n=== STEP 4: TEST SAVE DETECTION LOGIC ===")
        # Get the values that the save method would use
        current_pos = viewer._extract_position_from_display("top")
        current_rot = viewer._extract_rotation_from_vtk_actor("top")
        orig_pos = viewer.orig_pos_left
        orig_rot = viewer.orig_rot_left
        
        print(f"   current_pos: {current_pos}")
        print(f"   current_rot: {current_rot}")
        print(f"   orig_pos: {orig_pos}")
        print(f"   orig_rot: {orig_rot}")
        
        # Calculate deltas
        delta_rot = {
            'x': current_rot['x'] - orig_rot['x'],
            'y': current_rot['y'] - orig_rot['y'],
            'z': current_rot['z'] - orig_rot['z']
        }
        print(f"   delta_rot: {delta_rot}")
        
        # Check if save method would detect changes
        rot_changed = (abs(delta_rot['x']) > 0.1 or abs(delta_rot['y']) > 0.1 or abs(delta_rot['z']) > 0.1)
        print(f"   Save method would detect rotation change: {rot_changed}")
        
        print("\n=== STEP 5: CALL SAVE METHOD TO SEE WHAT HAPPENS ===")
        test_save_file = "debug_save_detection.step"
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
            
        loader = viewer.step_loader_left
        try:
            success = viewer._save_step_with_transformations(
                test_save_file, loader, current_pos, current_rot, orig_pos, orig_rot
            )
            print(f"   Save method returned: {success}")
            
            if os.path.exists(test_save_file):
                file_size = os.path.getsize(test_save_file)
                print(f"   File created: {test_save_file} ({file_size} bytes)")
                os.remove(test_save_file)
            else:
                print("   No file created")
                
        except Exception as e:
            print(f"   Exception in save method: {e}")
            import traceback
            traceback.print_exc()
            
    else:
        print("❌ Failed to load STEP file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== SAVE DETECTION DEBUG TEST FINISHED ===")
