#!/usr/bin/env python3
"""
Load SOIC-16 footprint and show origin numbers
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK

def load_soic16_test():
    """Load SOIC-16 footprint and show the GUI with origin numbers"""
    
    print("🔍 LOADING SOIC-16 FOOTPRINT TEST")
    print("=" * 50)
    
    # The SOIC file we found
    soic_file = "SOIC16P127_1270X940X610L89X51.STEP"
    
    print(f"📋 PREDICTED NUMBERS FOR {soic_file}:")
    print("   Based on typical SOIC-16 footprint:")
    print("   - Origin Position: Likely (0.000, 0.000, 0.000) or geometric center")
    print("   - Direction: Likely (0.000, 0.000, 0.000) for standard orientation")
    print("   - Package dimensions from filename: 12.70 x 9.40 x 6.10mm")
    print()
    
    if not os.path.exists(soic_file):
        print(f"❌ File not found: {soic_file}")
        return
    
    # Create Qt application
    app = QApplication(sys.argv)
    
    # Create the viewer
    viewer = StepViewerTDK()
    viewer.show()
    
    print("1. Opening GUI window...")
    
    # Wait a moment for GUI to initialize, then load SOIC
    QTimer.singleShot(1000, lambda: load_soic(viewer, soic_file))
    
    def load_soic(viewer, filename):
        print(f"2. Loading SOIC-16 footprint: {filename}")
        
        # Set active viewer to TOP and load
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(filename)
        
        if success:
            print("✅ SOIC-16 footprint loaded successfully!")
            
            # Print the actual values we can read from the code
            if hasattr(viewer, 'current_pos_left'):
                print(f"\n📊 ACTUAL VALUES FROM CODE:")
                print(f"   current_pos_left: {viewer.current_pos_left}")
                if hasattr(viewer, 'orig_pos_left'):
                    print(f"   orig_pos_left: {viewer.orig_pos_left}")
                if hasattr(viewer, 'current_rot_left'):
                    print(f"   current_rot_left: {viewer.current_rot_left}")
                if hasattr(viewer, 'orig_rot_left'):
                    print(f"   orig_rot_left: {viewer.orig_rot_left}")
            
            print(f"\n📋 WHAT YOU SHOULD SEE IN THE GUI:")
            print("   Look at the yellow text in the TOP viewer:")
            print("   - Main display: 'Origin (X = ??? Y = ??? Z = ???)'")
            print("   - Local origin display: 'Local Origin ... Origin (X = ??? Y = ??? Z = ???)'")
            print("   - World origin display: 'World Origin ... Origin (X = 0.000 Y = 0.000 Z = 0.000)'")
            print()
            print("🔍 VERIFICATION:")
            print("   - The Origin numbers should match the values printed above")
            print("   - They should NOT be all zeros (unless that's the actual STEP file origin)")
            print("   - Local origin should show the same as main origin")
            print("   - World origin should always be (0.000, 0.000, 0.000)")
            
        else:
            print("❌ Failed to load SOIC-16 footprint")
    
    print("\n🖥️  GUI window should be opening...")
    print("   You can now verify the numbers match what I printed above!")
    print("   Close the window when you're done verifying")
    
    # Run the application
    app.exec_()
    
    print("\n✅ SOIC-16 test completed")

if __name__ == "__main__":
    load_soic16_test()
