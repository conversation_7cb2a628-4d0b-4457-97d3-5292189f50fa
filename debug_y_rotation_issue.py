#!/usr/bin/env python3
"""
Debug Y rotation button issue and world origin arrow rotation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK as StepViewer
import time

class YRotationDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_step = 0
        
    def run_debug(self):
        """Run the Y rotation debug test"""
        print("🧪 Y ROTATION BUTTON DEBUG TEST")
        print("=" * 50)
        
        # Create viewer
        self.viewer = StepViewer()
        self.viewer.show()
        
        # Wait for viewer to initialize, then start test
        QTimer.singleShot(2000, self.start_test)
        
        # Run the application
        self.app.exec_()
        
    def start_test(self):
        """Start the debug test sequence"""
        print("\n🎯 STARTING Y ROTATION DEBUG TEST")
        
        # Load a STEP file first
        step_files = [
            "e:/python/viewer/save/sample.step",
            "sample.step",
            "test.step"
        ]
        
        loaded = False
        for step_file in step_files:
            if os.path.exists(step_file):
                print(f"📁 Loading STEP file: {step_file}")
                if hasattr(self.viewer, 'load_step_file'):
                    self.viewer.load_step_file(step_file)
                    loaded = True
                    break
        
        if not loaded:
            print("❌ No STEP file found - creating test without file")
        
        # Wait for load, then test Y rotation
        QTimer.singleShot(1000, self.test_y_rotation)
        
    def test_y_rotation(self):
        """Test Y rotation button specifically"""
        print("\n🧪 TESTING Y+ ROTATION BUTTON")
        
        # Capture BEFORE state
        self.capture_actor_states("BEFORE Y+")
        
        # Execute Y+ rotation
        print("🔄 Executing Y+ rotation (15°)...")
        self.viewer.rotate_shape('y', 15.0)
        
        # Wait a moment, then capture AFTER state
        QTimer.singleShot(500, self.capture_after_y_plus)
        
    def capture_after_y_plus(self):
        """Capture state after Y+ rotation"""
        self.capture_actor_states("AFTER Y+")
        
        # Now test Y- rotation
        print("\n🧪 TESTING Y- ROTATION BUTTON")
        
        # Execute Y- rotation
        print("🔄 Executing Y- rotation (-15°)...")
        self.viewer.rotate_shape('y', -15.0)
        
        # Wait a moment, then capture final state
        QTimer.singleShot(500, self.capture_after_y_minus)
        
    def capture_after_y_minus(self):
        """Capture state after Y- rotation"""
        self.capture_actor_states("AFTER Y-")
        
        # Test world origin arrow rotation
        print("\n🧪 TESTING WORLD ORIGIN ARROW ROTATION")
        self.test_world_origin_arrows()
        
    def test_world_origin_arrows(self):
        """Test if world origin arrows rotate correctly"""
        print("🔍 Checking world origin arrow orientations...")
        
        if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
            renderer = self.viewer.vtk_renderer_left
            
            # Check for world origin actors
            if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
                print(f"✅ Found {len(renderer.origin_actors)} world origin actors")
                
                for i, actor in enumerate(renderer.origin_actors):
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    print(f"   World Origin Actor {i}: Pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), Orient=({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
            else:
                print("❌ No world origin actors found")
        
        # Complete test
        print("\n✅ Y ROTATION DEBUG TEST COMPLETE")
        QTimer.singleShot(1000, self.app.quit)
        
    def capture_actor_states(self, label):
        """Capture and display current actor states"""
        print(f"\n📊 {label} - ACTOR STATES:")
        print("-" * 40)
        
        if not hasattr(self.viewer, 'vtk_renderer_left') or not self.viewer.vtk_renderer_left:
            print("❌ No renderer available")
            return
            
        renderer = self.viewer.vtk_renderer_left
        
        # Check for model actors
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            print(f"🎯 MODEL ACTORS ({len(renderer.step_actors)}):")
            for i, actor in enumerate(renderer.step_actors):
                pos = actor.GetPosition()
                orient = actor.GetOrientation()
                print(f"   Model {i}: Pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), Orient=({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
        
        # Check for world origin actors (red)
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            print(f"🔴 WORLD ORIGIN ACTORS ({len(renderer.origin_actors)}):")
            for i, actor in enumerate(renderer.origin_actors):
                pos = actor.GetPosition()
                orient = actor.GetOrientation()
                print(f"   World {i}: Pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), Orient=({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
        
        # Check for part origin actors (green)
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            actor = renderer.part_origin_sphere
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            print(f"🟢 PART ORIGIN SPHERE: Pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), Orient=({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
        
        # Check for part origin arrows
        arrow_names = ['part_origin_x_arrow', 'part_origin_y_arrow', 'part_origin_z_arrow']
        for arrow_name in arrow_names:
            if hasattr(renderer, arrow_name):
                actor = getattr(renderer, arrow_name)
                if actor:
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    print(f"🟡 {arrow_name.upper()}: Pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), Orient=({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")

if __name__ == "__main__":
    debugger = YRotationDebugger()
    debugger.run_debug()
