#!/usr/bin/env python3
"""
Test script to verify that the reset_to_original function properly cleans up extra yellow arrows.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK as StepViewer

class ResetFixTester:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewer()
        
    def count_yellow_arrows(self, stage_name):
        """Count yellow arrows in the top viewer"""
        if not hasattr(self.viewer, 'vtk_renderer_left') or not self.viewer.vtk_renderer_left:
            print(f"   ❌ No top renderer available")
            return 0
            
        renderer = self.viewer.vtk_renderer_left.renderer
        if not renderer:
            print(f"   ❌ No VTK renderer available")
            return 0
            
        # Count yellow arrows
        yellow_count = 0
        actors = renderer.GetActors()
        actors.InitTraversal()
        
        while True:
            actor = actors.GetNextActor()
            if not actor:
                break
                
            color = actor.GetProperty().GetColor()
            # Check if this is a yellow arrow (yellow = red + green, no blue)
            if (color[0] > 0.8 and color[1] > 0.8 and color[2] < 0.2):
                yellow_count += 1
                pos = actor.GetPosition()
                print(f"   🟡 Yellow arrow at ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
                
        print(f"📊 {stage_name}: {yellow_count} yellow arrows found")
        return yellow_count
        
    def run_reset_test(self):
        """Run the reset fix test"""
        print("🔧 STARTING RESET FIX TEST")
        print("="*60)
        
        # Show the viewer
        self.viewer.show()
        
        # Wait for GUI to initialize
        QTimer.singleShot(1000, self.load_step_file)
        
        # Start the event loop
        self.app.exec_()
        
    def load_step_file(self):
        """Load a STEP file for testing"""
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            self.app.quit()
            return
            
        print(f"📂 Loading STEP file: {step_file}")
        
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        
        # Load the file
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print(f"❌ Failed to load STEP file")
            self.app.quit()
            return
            
        print(f"✅ STEP file loaded successfully")
        
        # Count initial yellow arrows
        QTimer.singleShot(2000, self.count_initial_arrows)
        
    def count_initial_arrows(self):
        """Count arrows after initial load"""
        initial_count = self.count_yellow_arrows("INITIAL STATE")
        
        # Perform X+ movement
        QTimer.singleShot(1000, self.perform_movement)
        
    def perform_movement(self):
        """Perform X+ movement"""
        print(f"\n🔄 PERFORMING X+ MOVEMENT (+2.0mm)")
        
        # Ensure top viewer is active
        self.viewer.active_viewer = "top"
        
        # Perform X+ movement
        self.viewer.move_shape('x', 2.0)
        
        # Count arrows after movement
        QTimer.singleShot(1000, self.count_after_movement)
        
    def count_after_movement(self):
        """Count arrows after movement"""
        after_movement_count = self.count_yellow_arrows("AFTER X+ MOVEMENT")
        
        # Perform reset
        QTimer.singleShot(1000, self.perform_reset)
        
    def perform_reset(self):
        """Perform reset to original"""
        print(f"\n🔄 PERFORMING RESET TO ORIGINAL")
        
        # Call reset function
        self.viewer.reset_to_original()
        
        # Count arrows after reset
        QTimer.singleShot(2000, self.count_after_reset)
        
    def count_after_reset(self):
        """Count arrows after reset and analyze results"""
        after_reset_count = self.count_yellow_arrows("AFTER RESET")
        
        print(f"\n🎯 RESET FIX TEST RESULTS")
        print(f"="*60)
        
        if after_reset_count == 3:  # Should have exactly 3 yellow arrows (X, Y, Z)
            print(f"✅ RESET FIX SUCCESSFUL: Exactly 3 yellow arrows remain (X, Y, Z)")
        elif after_reset_count > 3:
            print(f"❌ RESET FIX FAILED: {after_reset_count} yellow arrows found (should be 3)")
            print(f"   Extra arrows were not cleaned up properly!")
        elif after_reset_count < 3:
            print(f"⚠️  RESET FIX ISSUE: Only {after_reset_count} yellow arrows found (should be 3)")
            print(f"   Some arrows may have been over-cleaned!")
        else:
            print(f"❓ UNEXPECTED RESULT: {after_reset_count} yellow arrows")
            
        # Close the application
        QTimer.singleShot(2000, self.app.quit)

if __name__ == "__main__":
    tester = ResetFixTester()
    tester.run_reset_test()
