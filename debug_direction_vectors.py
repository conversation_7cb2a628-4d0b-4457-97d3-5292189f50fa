#!/usr/bin/env python3

"""
DEBUG: Direction Vectors Investigation
Check if direction vectors are being stored and preserved on the green sphere actor
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import Step<PERSON>iewerTDK as Step<PERSON>iewer
from PyQt5.QtWidgets import QApplication
import time

def debug_direction_vectors():
    """Debug the direction vectors on green sphere actor"""
    
    print("=" * 80)
    print("DEBUG: DIRECTION VECTORS INVESTIGATION")
    print("=" * 80)
    
    app = QApplication(sys.argv)
    
    try:
        # Create the main viewer
        viewer = StepViewer()
        viewer.show()
        
        # Wait for GUI to initialize
        app.processEvents()
        time.sleep(2)
        
        print("\n1. Loading SOIC-16 footprint...")
        
        # Load the SOIC-16 file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        # Set to TOP viewer and load file
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        print("✅ STEP file loaded successfully")
        
        # Wait for loading to complete
        app.processEvents()
        time.sleep(2)
        
        print("\n2. CHECKING GREEN SPHERE ACTOR...")
        
        # Check if green sphere exists
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
            print("✅ Green sphere actor found")
            
            # Check direction vectors
            if hasattr(renderer.part_origin_sphere, 'z_direction'):
                print(f"✅ Z Direction stored: {renderer.part_origin_sphere.z_direction}")
            else:
                print("❌ Z Direction NOT stored")
                
            if hasattr(renderer.part_origin_sphere, 'x_direction'):
                print(f"✅ X Direction stored: {renderer.part_origin_sphere.x_direction}")
            else:
                print("❌ X Direction NOT stored")
                
            # Check UserTransform
            user_transform = renderer.part_origin_sphere.GetUserTransform()
            if user_transform:
                matrix = user_transform.GetMatrix()
                print(f"✅ UserTransform exists: Position ({matrix.GetElement(0,3):.3f}, {matrix.GetElement(1,3):.3f}, {matrix.GetElement(2,3):.3f})")
            else:
                print("❌ UserTransform is None")
                
            # Check Position
            position = renderer.part_origin_sphere.GetPosition()
            print(f"   Actor Position: ({position[0]:.3f}, {position[1]:.3f}, {position[2]:.3f})")
            
        else:
            print("❌ Green sphere actor NOT found")
            
        print("\n3. CHECKING ORIGINAL STEP FILE DATA...")
        
        # Check if original STEP file data is stored
        if hasattr(viewer, 'orig_z_direction_left'):
            print(f"✅ Original Z Direction: {viewer.orig_z_direction_left}")
        else:
            print("❌ Original Z Direction NOT stored")
            
        if hasattr(viewer, 'orig_x_direction_left'):
            print(f"✅ Original X Direction: {viewer.orig_x_direction_left}")
        else:
            print("❌ Original X Direction NOT stored")
            
        if hasattr(viewer, 'orig_pos_left'):
            print(f"✅ Original Position: {viewer.orig_pos_left}")
        else:
            print("❌ Original Position NOT stored")
        
        print("\n4. TESTING LOCAL ORIGIN DISPLAY LOGIC...")
        
        # Test the exact logic used in update_text_overlays
        if hasattr(viewer.vtk_renderer_left, 'part_origin_sphere') and viewer.vtk_renderer_left.part_origin_sphere:
            print("✅ Part origin sphere exists for display logic")
            
            if hasattr(viewer.vtk_renderer_left.part_origin_sphere, 'z_direction') and hasattr(viewer.vtk_renderer_left.part_origin_sphere, 'x_direction'):
                z_dir = viewer.vtk_renderer_left.part_origin_sphere.z_direction
                x_dir = viewer.vtk_renderer_left.part_origin_sphere.x_direction
                print(f"✅ Direction vectors found: Z={z_dir}, X={x_dir}")
                
                dir_x, dir_y, dir_z = z_dir[0], z_dir[1], z_dir[2]
                ref_x, ref_y, ref_z = x_dir[0], x_dir[1], x_dir[2]
                pos = viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
                
                local_origin_display = f"Local Origin Direction (X = {dir_x:.3f} Y = {dir_y:.3f} Z = {dir_z:.3f}) REF. Direction (X = {ref_x:.3f} Y = {ref_y:.3f} Z = {ref_z:.3f}) Origin (X = {pos[0]:.3f} Y = {pos[1]:.3f} Z = {pos[2]:.3f})"
                print(f"✅ Would show: {local_origin_display}")
            else:
                print("❌ Direction vectors missing - would use fallback")
                if hasattr(viewer, 'current_pos_left'):
                    print(f"   Fallback would show: Origin (X = {viewer.current_pos_left['x']:.3f} Y = {viewer.current_pos_left['y']:.3f} Z = {viewer.current_pos_left['z']:.3f})")
                else:
                    print("   Fallback would show all zeros")
        else:
            print("❌ Part origin sphere missing for display logic")
        
        print("\n" + "=" * 80)
        print("DIRECTION VECTORS DEBUG COMPLETE")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ Debug test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_direction_vectors()
