#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

print("=== SIMPLE ROTATION SAVE TEST ===")

# Test different rotation combinations by running the demo with different values
test_cases = [
    {"name": "Simple 90° X rotation", "x": 90.0, "y": 0.0, "z": 0.0},
    {"name": "Simple 90° Y rotation", "x": 0.0, "y": 90.0, "z": 0.0}, 
    {"name": "Simple 90° Z rotation", "x": 0.0, "y": 0.0, "z": 90.0},
    {"name": "45° XYZ rotation", "x": 45.0, "y": 45.0, "z": 45.0},
]

for i, test_case in enumerate(test_cases):
    print(f"\n--- TEST {i+1}: {test_case['name']} ---")
    
    # Create a modified demo script for this test
    demo_content = f'''#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

# Create QApplication
app = QApplication(sys.argv)

print("=== ROTATION SAVE TEST: {test_case['name']} ===")

try:
    # Create viewer
    viewer = StepViewerTDK()
    
    # Load original file
    print("1. Loading demo.step...")
    success = viewer.load_step_file_direct('demo.step')
    if not success:
        print("❌ Failed to load demo.step")
        sys.exit(1)
    
    # Apply rotations
    print("2. Applying rotations: X={test_case['x']}° Y={test_case['y']}° Z={test_case['z']}°")
    if {test_case['x']} != 0:
        viewer._apply_model_rotation('top', 'x', {test_case['x']})
    if {test_case['y']} != 0:
        viewer._apply_model_rotation('top', 'y', {test_case['y']})
    if {test_case['z']} != 0:
        viewer._apply_model_rotation('top', 'z', {test_case['z']})
    
    # Get applied rotation
    applied_rot = viewer.current_rot_left.copy()
    print(f"Applied rotation: {{applied_rot}}")
    
    # Update text to get direction vectors
    viewer.update_text_overlays()
    
    # Get unified results for top viewer
    unified_results = viewer._calculate_unified_display_numbers("top")
    model_data = unified_results['model']
    print(f"Applied direction vectors: {{model_data}}")
    
    # Save file
    test_filename = 'test_{i+1}.step'
    print(f"3. Saving to {{test_filename}}...")
    
    loader = viewer.step_loader_left
    save_success = viewer._save_step_with_current_rotation(test_filename, loader, applied_rot)
    
    if not save_success:
        print("❌ Save failed")
        sys.exit(1)
    
    if not os.path.exists(test_filename):
        print("❌ File not created")
        sys.exit(1)
    
    file_size = os.path.getsize(test_filename)
    print(f"✅ File created: {{file_size}} bytes")
    
    # Load saved file
    print("4. Loading saved file...")
    load_success = viewer.load_step_file_direct(test_filename, viewer="bottom")
    
    if not load_success:
        print("❌ Failed to load saved file")
        sys.exit(1)
    
    # Get saved rotation
    saved_rot = viewer.current_rot_right.copy()
    print(f"Saved rotation: {{saved_rot}}")
    
    # Update text for bottom viewer
    viewer.update_text_overlays()
    
    # Get unified results for bottom viewer
    unified_results_bottom = viewer._calculate_unified_display_numbers("bottom")
    model_data_bottom = unified_results_bottom['model']
    print(f"Saved direction vectors: {{model_data_bottom}}")
    
    # Compare
    tolerance = 0.1
    x_match = abs(applied_rot['x'] - saved_rot['x']) < tolerance
    y_match = abs(applied_rot['y'] - saved_rot['y']) < tolerance  
    z_match = abs(applied_rot['z'] - saved_rot['z']) < tolerance
    
    if x_match and y_match and z_match:
        print("✅ SUCCESS: Rotation values preserved!")
        print(f"   Applied: X={{applied_rot['x']:.1f}}° Y={{applied_rot['y']:.1f}}° Z={{applied_rot['z']:.1f}}°")
        print(f"   Saved:   X={{saved_rot['x']:.1f}}° Y={{saved_rot['y']:.1f}}° Z={{saved_rot['z']:.1f}}°")
    else:
        print("❌ FAILURE: Rotation values not preserved")
        print(f"   Applied: X={{applied_rot['x']:.1f}}° Y={{applied_rot['y']:.1f}}° Z={{applied_rot['z']:.1f}}°")
        print(f"   Saved:   X={{saved_rot['x']:.1f}}° Y={{saved_rot['y']:.1f}}° Z={{saved_rot['z']:.1f}}°")
    
    # Check direction vectors match
    if model_data == model_data_bottom:
        print("✅ SUCCESS: Direction vectors preserved!")
    else:
        print("❌ FAILURE: Direction vectors not preserved")
        print(f"   Applied: {{model_data}}")
        print(f"   Saved:   {{model_data_bottom}}")
    
    # Clean up
    try:
        os.remove(test_filename)
    except:
        pass
        
except Exception as e:
    print(f"❌ Exception: {{e}}")
    import traceback
    traceback.print_exc()

print("=== TEST FINISHED ===")
'''
    
    # Write test script
    test_script = f"test_demo_{i+1}.py"
    with open(test_script, 'w') as f:
        f.write(demo_content)
    
    # Run test
    import subprocess
    try:
        result = subprocess.run([
            f"{os.environ['USERPROFILE']}\\miniforge3\\envs\\step-viewer\\python.exe", 
            test_script
        ], capture_output=True, text=True, timeout=60, cwd=".")
        
        print("STDOUT:")
        print(result.stdout)
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ Test {i+1} completed successfully")
        else:
            print(f"❌ Test {i+1} failed with return code {result.returncode}")
            
    except subprocess.TimeoutExpired:
        print(f"❌ Test {i+1} timed out")
    except Exception as e:
        print(f"❌ Test {i+1} exception: {e}")
    
    # Clean up test script
    try:
        os.remove(test_script)
    except:
        pass

print("\n=== ALL TESTS COMPLETED ===")
