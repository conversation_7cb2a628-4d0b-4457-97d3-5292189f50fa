#!/usr/bin/env python3
"""
Test script to verify that world origin position stays fixed during translation
but orientation follows the model (just like mouse rotation).
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK as StepViewer

class WorldOriginFixTester:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewer()
        
    def track_world_origin(self, stage_name):
        """Track world origin position and orientation"""
        if not hasattr(self.viewer, 'vtk_renderer_left') or not self.viewer.vtk_renderer_left:
            print(f"   ❌ No top renderer available")
            return
            
        renderer = self.viewer.vtk_renderer_left.renderer
        if not renderer:
            print(f"   ❌ No VTK renderer available")
            return
            
        print(f"\n🔴 {stage_name} - WORLD ORIGIN TRACKING:")
        print(f"-" * 50)
        
        # Find red actors (world origin)
        red_actors = []
        actors = renderer.GetActors()
        actors.InitTraversal()
        
        while True:
            actor = actors.GetNextActor()
            if not actor:
                break
                
            color = actor.GetProperty().GetColor()
            # Check if this is a red actor (world origin)
            if color[0] > 0.8 and color[1] < 0.3 and color[2] < 0.3:
                pos = actor.GetPosition()
                orient = actor.GetOrientation()
                red_actors.append({'pos': pos, 'orient': orient})
                
        print(f"   Found {len(red_actors)} red actors (world origin markers)")
        for i, actor in enumerate(red_actors):
            print(f"   Red {i+1}: pos=({actor['pos'][0]:.3f}, {actor['pos'][1]:.3f}, {actor['pos'][2]:.3f}), orient=({actor['orient'][0]:.1f}°, {actor['orient'][1]:.1f}°, {actor['orient'][2]:.1f}°)")
            
        return red_actors
        
    def run_world_origin_test(self):
        """Run the world origin fix test"""
        print("🔴 STARTING WORLD ORIGIN FIX TEST")
        print("="*60)
        
        # Show the viewer
        self.viewer.show()
        
        # Wait for GUI to initialize
        QTimer.singleShot(1000, self.load_step_file)
        
        # Start the event loop
        self.app.exec_()
        
    def load_step_file(self):
        """Load a STEP file for testing"""
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            self.app.quit()
            return
            
        print(f"📂 Loading STEP file: {step_file}")
        
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        
        # Load the file
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print(f"❌ Failed to load STEP file")
            self.app.quit()
            return
            
        print(f"✅ STEP file loaded successfully")
        
        # Track initial state
        QTimer.singleShot(2000, self.track_initial_state)
        
    def track_initial_state(self):
        """Track initial state after loading"""
        self.initial_red_actors = self.track_world_origin("INITIAL STATE")
        
        # Perform X rotation
        QTimer.singleShot(1000, self.perform_x_rotation)
        
    def perform_x_rotation(self):
        """Perform X+ rotation"""
        print(f"\n🔄 PERFORMING X+ ROTATION (+15°)")
        
        # Ensure top viewer is active
        self.viewer.active_viewer = "top"
        
        # Perform X+ rotation
        self.viewer.rotate_shape('x', 15.0)
        
        # Track after rotation
        QTimer.singleShot(1000, self.track_after_rotation)
        
    def track_after_rotation(self):
        """Track state after rotation"""
        self.after_rotation_red_actors = self.track_world_origin("AFTER X+ ROTATION")
        
        # Perform translation
        QTimer.singleShot(1000, self.perform_x_translation)
        
    def perform_x_translation(self):
        """Perform X+ translation"""
        print(f"\n🔄 PERFORMING X+ TRANSLATION (+2.0mm)")
        
        # Perform X+ translation
        self.viewer.move_shape('x', 2.0)
        
        # Track after translation
        QTimer.singleShot(1000, self.track_after_translation)
        
    def track_after_translation(self):
        """Track state after translation and analyze"""
        self.after_translation_red_actors = self.track_world_origin("AFTER X+ TRANSLATION")
        
        print(f"\n🎯 WORLD ORIGIN FIX ANALYSIS")
        print(f"="*60)
        
        # Check rotation behavior
        print(f"📊 ROTATION ANALYSIS:")
        if len(self.initial_red_actors) > 0 and len(self.after_rotation_red_actors) > 0:
            initial_pos = self.initial_red_actors[0]['pos']
            initial_orient = self.initial_red_actors[0]['orient']
            after_rot_pos = self.after_rotation_red_actors[0]['pos']
            after_rot_orient = self.after_rotation_red_actors[0]['orient']
            
            pos_moved = abs(after_rot_pos[0] - initial_pos[0]) + abs(after_rot_pos[1] - initial_pos[1]) + abs(after_rot_pos[2] - initial_pos[2])
            orient_changed = abs(after_rot_orient[0] - initial_orient[0]) + abs(after_rot_orient[1] - initial_orient[1]) + abs(after_rot_orient[2] - initial_orient[2])
            
            if pos_moved < 0.1:
                print(f"   ✅ ROTATION: Position stayed fixed (moved {pos_moved:.3f}mm)")
            else:
                print(f"   ❌ ROTATION: Position moved {pos_moved:.3f}mm (should stay fixed)")
                
            if orient_changed > 1.0:
                print(f"   ✅ ROTATION: Orientation changed {orient_changed:.1f}° (should rotate with model)")
            else:
                print(f"   ❌ ROTATION: Orientation barely changed {orient_changed:.1f}° (should rotate with model)")
        
        # Check translation behavior  
        print(f"\n📊 TRANSLATION ANALYSIS:")
        if len(self.after_rotation_red_actors) > 0 and len(self.after_translation_red_actors) > 0:
            before_trans_pos = self.after_rotation_red_actors[0]['pos']
            before_trans_orient = self.after_rotation_red_actors[0]['orient']
            after_trans_pos = self.after_translation_red_actors[0]['pos']
            after_trans_orient = self.after_translation_red_actors[0]['orient']
            
            pos_moved = abs(after_trans_pos[0] - before_trans_pos[0]) + abs(after_trans_pos[1] - before_trans_pos[1]) + abs(after_trans_pos[2] - before_trans_pos[2])
            orient_changed = abs(after_trans_orient[0] - before_trans_orient[0]) + abs(after_trans_orient[1] - before_trans_orient[1]) + abs(after_trans_orient[2] - before_trans_orient[2])
            
            if pos_moved < 0.1:
                print(f"   ✅ TRANSLATION: Position stayed fixed (moved {pos_moved:.3f}mm) - CORRECT!")
            else:
                print(f"   ❌ TRANSLATION: Position moved {pos_moved:.3f}mm (should stay fixed) - WRONG!")
                
            if orient_changed < 1.0:
                print(f"   ✅ TRANSLATION: Orientation stayed same {orient_changed:.1f}° (correct for translation)")
            else:
                print(f"   ⚠️  TRANSLATION: Orientation changed {orient_changed:.1f}° (unexpected for translation)")
        
        # Overall result
        print(f"\n🏁 FINAL RESULT:")
        if len(self.after_translation_red_actors) > 0:
            final_pos = self.after_translation_red_actors[0]['pos']
            if abs(final_pos[0]) < 0.1 and abs(final_pos[1]) < 0.1 and abs(final_pos[2]) < 0.1:
                print(f"   ✅ SUCCESS: World origin stayed at (0,0,0) during translation!")
            else:
                print(f"   ❌ FAILED: World origin moved to ({final_pos[0]:.3f}, {final_pos[1]:.3f}, {final_pos[2]:.3f})")
        
        # Close the application
        QTimer.singleShot(2000, self.app.quit)

if __name__ == "__main__":
    tester = WorldOriginFixTester()
    tester.run_world_origin_test()
