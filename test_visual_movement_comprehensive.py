#!/usr/bin/env python3
"""
COMPREHENSIVE VISUAL MOVEMENT TEST
Tests that ALL visual elements move together correctly:
- Model geometry
- Origin markers (local and world)
- Green ball
- Bounding box
- Text overlays
"""

import sys
import os
import time
import numpy as np
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

# Add current directory to path
sys.path.append('.')

def get_actor_position(actor):
    """Get the position of a VTK actor"""
    if actor:
        return actor.GetPosition()
    return None

def get_actor_orientation(actor):
    """Get the orientation of a VTK actor"""
    if actor:
        return actor.GetOrientation()
    return None

def get_transform_matrix(actor):
    """Get the transformation matrix of a VTK actor"""
    if actor:
        transform = actor.GetUserTransform()
        if transform:
            matrix = transform.GetMatrix()
            return [[matrix.GetElement(i, j) for j in range(4)] for i in range(4)]
    return None

def test_all_visual_elements():
    """Test that ALL visual elements move together correctly"""
    
    print("🔧 COMPREHENSIVE VISUAL MOVEMENT TEST STARTING...")
    print("=" * 70)
    
    # Import and create the application
    from step_viewer import StepViewerTDK
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for GUI to initialize
    QTest.qWait(2000)
    print("✅ GUI initialized")
    
    # Load a test STEP file to ensure all visual elements are present
    test_files = [
        r"e:\python\viewer\save\test_part.step",
        r"e:\python\viewer\save\sample.step",
        r"e:\python\3d-view\test.step",
        r"e:\python\3d-view\sample.step"
    ]
    
    test_file = None
    for f in test_files:
        if os.path.exists(f):
            test_file = f
            break
    
    if test_file:
        print(f"📁 Loading test file: {test_file}")
        try:
            viewer.load_step_file_direct(test_file)
            QTest.qWait(3000)  # Wait for file to load
            print("✅ STEP file loaded successfully")
        except Exception as e:
            print(f"❌ Error loading STEP file: {e}")
            print("⚠️  Continuing test without STEP file...")
    else:
        print("⚠️  No test STEP file found - testing with default geometry")
    
    print("\n🔍 IDENTIFYING ALL VISUAL ELEMENTS...")
    print("-" * 50)
    
    # Get all visual elements from TOP viewer
    visual_elements = {}
    
    # Check for model actors
    if hasattr(viewer, 'step_loader_left') and viewer.step_loader_left:
        if hasattr(viewer.step_loader_left, 'current_polydata'):
            print("✅ Found model geometry (step_loader_left)")
            visual_elements['model'] = viewer.step_loader_left
    
    # Check for origin markers
    if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
        renderer = viewer.vtk_renderer_left.renderer
        if renderer:
            actors = renderer.GetActors()
            actors.InitTraversal()
            actor_count = 0
            while True:
                actor = actors.GetNextActor()
                if not actor:
                    break
                actor_count += 1
                print(f"   Found actor {actor_count}: {type(actor).__name__}")
            
            print(f"✅ Found {actor_count} actors in TOP renderer")
    
    # Check for green ball (coordinate system marker)
    ball_found = False
    if hasattr(viewer, 'coordinate_system_actor_left'):
        print("✅ Found coordinate system actor (green ball)")
        visual_elements['green_ball'] = viewer.coordinate_system_actor_left
        ball_found = True
    
    # Check for bounding box
    bbox_found = False
    if hasattr(viewer, 'bounding_box_actor_left'):
        print("✅ Found bounding box actor")
        visual_elements['bounding_box'] = viewer.bounding_box_actor_left
        bbox_found = True
    
    # Check for origin markers
    origin_markers = []
    if hasattr(viewer, 'local_origin_marker_left'):
        print("✅ Found local origin marker")
        visual_elements['local_origin'] = viewer.local_origin_marker_left
        origin_markers.append('local')
    
    if hasattr(viewer, 'world_origin_marker_left'):
        print("✅ Found world origin marker")
        visual_elements['world_origin'] = viewer.world_origin_marker_left
        origin_markers.append('world')
    
    print(f"\n📊 VISUAL ELEMENTS SUMMARY:")
    print(f"   Model geometry: {'✅' if 'model' in visual_elements else '❌'}")
    print(f"   Green ball: {'✅' if ball_found else '❌'}")
    print(f"   Bounding box: {'✅' if bbox_found else '❌'}")
    print(f"   Origin markers: {len(origin_markers)} found {origin_markers}")
    
    if len(visual_elements) == 0:
        print("❌ No visual elements found to test!")
        return False
    
    print(f"\n🧪 TESTING ROTATION MOVEMENTS...")
    print("-" * 50)
    
    # Test rotations and capture positions/orientations
    rotation_tests = [
        ("X+90", "x", 90),
        ("Y+90", "y", 90),
        ("Z+90", "z", 90),
    ]
    
    successful_tests = 0
    failed_tests = 0
    
    for test_name, axis, degrees in rotation_tests:
        print(f"\n🔄 Testing {test_name} rotation...")
        
        # Capture BEFORE state
        before_state = {}
        for name, element in visual_elements.items():
            if hasattr(element, 'GetPosition'):
                before_state[f"{name}_pos"] = element.GetPosition()
            if hasattr(element, 'GetOrientation'):
                before_state[f"{name}_orient"] = element.GetOrientation()
        
        print(f"   📊 Captured BEFORE state for {len(before_state)} properties")
        
        try:
            # Perform rotation
            print(f"   🔄 Applying {test_name} rotation...")
            viewer.rotate_shape(axis, degrees)
            QTest.qWait(1500)  # Wait for rotation to complete and render
            
            # Force update of all visual elements
            if hasattr(viewer, 'update_text_overlays'):
                viewer.update_text_overlays()
            
            # Force render
            if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
                viewer.vtk_renderer_left.GetRenderWindow().Render()
            
            QTest.qWait(500)  # Wait for render to complete
            
            # Capture AFTER state
            after_state = {}
            for name, element in visual_elements.items():
                if hasattr(element, 'GetPosition'):
                    after_state[f"{name}_pos"] = element.GetPosition()
                if hasattr(element, 'GetOrientation'):
                    after_state[f"{name}_orient"] = element.GetOrientation()
            
            print(f"   📊 Captured AFTER state for {len(after_state)} properties")
            
            # Compare states to detect movement
            movements_detected = 0
            no_movement = 0
            
            for prop_name in before_state:
                if prop_name in after_state:
                    before_val = before_state[prop_name]
                    after_val = after_state[prop_name]
                    
                    # Check if values changed (allowing for small floating point differences)
                    if before_val and after_val:
                        if len(before_val) == 3 and len(after_val) == 3:  # Position/orientation tuple
                            diff = sum(abs(a - b) for a, b in zip(before_val, after_val))
                            if diff > 0.001:  # Threshold for detecting change
                                print(f"     ✅ {prop_name}: MOVED {before_val} → {after_val}")
                                movements_detected += 1
                            else:
                                print(f"     ⚠️  {prop_name}: NO MOVEMENT {before_val}")
                                no_movement += 1
                        else:
                            print(f"     ❓ {prop_name}: Unexpected format")
                    else:
                        print(f"     ❌ {prop_name}: Missing data")
            
            # Evaluate test result
            if movements_detected > 0:
                print(f"   ✅ {test_name} PASSED - {movements_detected} elements moved, {no_movement} didn't move")
                successful_tests += 1
            else:
                print(f"   ❌ {test_name} FAILED - No movement detected in any visual elements!")
                failed_tests += 1
                
        except Exception as e:
            print(f"   ❌ {test_name} FAILED with exception: {e}")
            failed_tests += 1
    
    # Test translation if methods exist
    print(f"\n🧪 TESTING TRANSLATION MOVEMENTS...")
    print("-" * 50)
    
    # Try to find translation methods
    translation_methods = []
    for method_name in dir(viewer):
        if 'translate' in method_name.lower() or 'move' in method_name.lower():
            if callable(getattr(viewer, method_name)):
                translation_methods.append(method_name)
    
    print(f"Found {len(translation_methods)} potential translation methods:")
    for method in translation_methods[:5]:  # Show first 5
        print(f"   - {method}")
    
    if translation_methods:
        # Test one translation method
        test_method = translation_methods[0]
        print(f"\n🔄 Testing translation method: {test_method}")
        
        try:
            # Capture before state
            before_pos = {}
            for name, element in visual_elements.items():
                if hasattr(element, 'GetPosition'):
                    before_pos[name] = element.GetPosition()
            
            # Apply translation
            method = getattr(viewer, test_method)
            method()
            QTest.qWait(1000)
            
            # Capture after state
            after_pos = {}
            for name, element in visual_elements.items():
                if hasattr(element, 'GetPosition'):
                    after_pos[name] = element.GetPosition()
            
            # Check for movement
            translation_movements = 0
            for name in before_pos:
                if name in after_pos:
                    before_val = before_pos[name]
                    after_val = after_pos[name]
                    if before_val and after_val:
                        diff = sum(abs(a - b) for a, b in zip(before_val, after_val))
                        if diff > 0.001:
                            print(f"     ✅ {name}: TRANSLATED {before_val} → {after_val}")
                            translation_movements += 1
            
            if translation_movements > 0:
                print(f"   ✅ Translation test PASSED - {translation_movements} elements moved")
                successful_tests += 1
            else:
                print(f"   ❌ Translation test FAILED - No movement detected")
                failed_tests += 1
                
        except Exception as e:
            print(f"   ❌ Translation test FAILED: {e}")
            failed_tests += 1
    
    # Final summary
    print("\n" + "=" * 70)
    print("🏁 COMPREHENSIVE VISUAL MOVEMENT TEST RESULTS:")
    print(f"✅ Successful tests: {successful_tests}")
    print(f"❌ Failed tests: {failed_tests}")
    if successful_tests + failed_tests > 0:
        print(f"📊 Success rate: {successful_tests/(successful_tests+failed_tests)*100:.1f}%")
    
    if failed_tests == 0:
        print("🎉 ALL VISUAL ELEMENTS MOVE TOGETHER CORRECTLY!")
        return True
    else:
        print("⚠️  Some visual elements are NOT moving together correctly!")
        return False

if __name__ == "__main__":
    success = test_all_visual_elements()
    sys.exit(0 if success else 1)
