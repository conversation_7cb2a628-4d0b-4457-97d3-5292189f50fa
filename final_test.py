#!/usr/bin/env python3
"""
Final Test - Verify Both Issues Are Fixed
=========================================
1. World origin numbers stay at (0,0,0) during rotation
2. Origin markers rotate with the model
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def final_test():
    """Final test to verify both issues are fixed"""
    print("🎯 FINAL TEST - VERIFYING FIXES")
    print("=" * 60)
    
    # Import after path setup
    try:
        from step_viewer import StepViewerTDK
    except Exception as e:
        print(f"❌ Failed to import step_viewer: {e}")
        return False
    
    # Create Qt application
    app = QApplication([])
    
    # Create step viewer
    viewer = StepViewerTDK()
    viewer.show()
    viewer.active_viewer = "top"
    
    # Wait for initialization
    time.sleep(3)
    
    # Create origin overlay manually to ensure we have origin actors
    if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
        print("📍 Creating origin overlay...")
        viewer.vtk_renderer_left.create_origin_overlay()
        time.sleep(1)
    
    print("\n1️⃣ TESTING WORLD ORIGIN NUMBERS STAY AT (0,0,0)")
    print("-" * 50)
    
    # Get initial world origin text
    initial_text = get_world_origin_text(viewer)
    print(f"📊 Initial: {initial_text}")
    
    # Extract initial origin numbers
    initial_origin = extract_origin_numbers(initial_text)
    print(f"📊 Initial origin: {initial_origin}")
    
    # Apply rotation
    print("🔄 Applying Y+30° rotation...")
    viewer.rotate_shape('y', 30.0)
    time.sleep(1)
    
    # Get new world origin text
    new_text = get_world_origin_text(viewer)
    print(f"📊 After rotation: {new_text}")
    
    # Extract new origin numbers
    new_origin = extract_origin_numbers(new_text)
    print(f"📊 New origin: {new_origin}")
    
    # Check if origin stayed at (0,0,0)
    if new_origin == (0.0, 0.0, 0.0):
        print("✅ SUCCESS: World origin numbers stayed at (0,0,0)")
    else:
        print(f"❌ FAILED: World origin numbers changed to {new_origin}")
    
    # Check if direction vectors changed
    initial_direction = extract_direction_numbers(initial_text)
    new_direction = extract_direction_numbers(new_text)
    print(f"📐 Initial direction: {initial_direction}")
    print(f"📐 New direction: {new_direction}")
    
    if initial_direction != new_direction:
        print("✅ SUCCESS: Direction vectors changed (as expected)")
    else:
        print("❌ FAILED: Direction vectors did not change")
    
    print("\n2️⃣ TESTING ORIGIN MARKERS ROTATE WITH MODEL")
    print("-" * 50)
    
    # Get initial origin marker orientations
    initial_orientations = get_origin_orientations(viewer)
    print(f"📊 Initial orientations: {len(initial_orientations)} markers")
    
    # Apply another rotation
    print("🔄 Applying Z+45° rotation...")
    viewer.rotate_shape('z', 45.0)
    time.sleep(1)
    
    # Get new orientations
    new_orientations = get_origin_orientations(viewer)
    print(f"📊 New orientations: {len(new_orientations)} markers")
    
    # Check if orientations changed
    if len(initial_orientations) > 0 and len(new_orientations) > 0:
        if initial_orientations != new_orientations:
            print("✅ SUCCESS: Origin markers rotated with model")
        else:
            print("❌ FAILED: Origin markers did not rotate")
    else:
        print("⚠️  Cannot test: No origin markers found")
    
    print("\n🎯 FINAL VERIFICATION")
    print("-" * 30)
    
    # Final world origin check
    final_text = get_world_origin_text(viewer)
    final_origin = extract_origin_numbers(final_text)
    print(f"📊 Final world origin: {final_origin}")
    
    if final_origin == (0.0, 0.0, 0.0):
        print("✅ CONFIRMED: World origin stays at (0,0,0) after multiple rotations")
    else:
        print(f"❌ FAILED: World origin is {final_origin}, should be (0,0,0)")
    
    # Close after test
    QTimer.singleShot(3000, app.quit)
    app.exec_()

def get_world_origin_text(viewer):
    """Get current world origin text"""
    try:
        if hasattr(viewer, 'world_origin_text_actor_left'):
            return viewer.world_origin_text_actor_left.GetInput()
    except:
        pass
    return "No world origin text found"

def extract_origin_numbers(text):
    """Extract origin (X,Y,Z) numbers from world origin text"""
    import re
    match = re.search(r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', text)
    if match:
        return (float(match.group(1)), float(match.group(2)), float(match.group(3)))
    return (0.0, 0.0, 0.0)

def extract_direction_numbers(text):
    """Extract direction (X,Y,Z) numbers from world origin text"""
    import re
    match = re.search(r'Direction \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', text)
    if match:
        return (float(match.group(1)), float(match.group(2)), float(match.group(3)))
    return (0.0, 0.0, 0.0)

def get_origin_orientations(viewer):
    """Get current orientations of origin markers"""
    orientations = []
    try:
        if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
            renderer = viewer.vtk_renderer_left
            if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
                for actor in renderer.origin_actors:
                    if actor:
                        orientations.append(actor.GetOrientation())
    except:
        pass
    return orientations

if __name__ == "__main__":
    final_test()
