#!/usr/bin/env python3
"""
COMPREHENSIVE DEBUG TEST: Test all buttons with debug output
This will show exactly what's happening with origins and unified code
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

# Add current directory to path
sys.path.append('.')

def test_all_buttons_with_debug():
    """Test all rotation and translation buttons with full debug output"""
    
    print("🎯 COMPREHENSIVE DEBUG TEST: All Buttons with Debug Output")
    print("=" * 70)
    
    # Import and create the application
    from step_viewer import StepViewerTDK
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for GUI to initialize
    QTest.qWait(3000)
    print("✅ GUI initialized")
    
    # Load a test STEP file to ensure all visual elements are present
    test_files = [
        r"e:\python\viewer\save\test_part.step",
        r"e:\python\viewer\save\sample.step",
        r"e:\python\3d-view\test.step",
        r"e:\python\3d-view\sample.step"
    ]
    
    test_file = None
    for f in test_files:
        if os.path.exists(f):
            test_file = f
            break
    
    if test_file:
        print(f"📁 Loading test file: {test_file}")
        try:
            viewer.load_step_file_direct(test_file)
            QTest.qWait(4000)  # Wait for file to load
            print("✅ STEP file loaded successfully")
        except Exception as e:
            print(f"❌ Error loading STEP file: {e}")
            print("⚠️  Continuing test without STEP file...")
    else:
        print("⚠️  No test STEP file found - testing with default geometry")
    
    # Test rotation buttons with debug output
    rotation_tests = [
        ("X+45°", "x", 45),
        ("Y+90°", "y", 90),
        ("Z-30°", "z", -30),
    ]
    
    print(f"\n🧪 TESTING ROTATION BUTTONS WITH DEBUG OUTPUT...")
    print("=" * 70)
    
    for test_name, axis, degrees in rotation_tests:
        print(f"\n🔄 TESTING {test_name}...")
        print("-" * 40)
        
        try:
            # Call the rotation method - this will trigger debug output
            viewer.rotate_shape(axis, degrees)
            QTest.qWait(3000)  # Wait for rotation and debug output
            
            print(f"✅ {test_name} completed")
            
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
            import traceback
            traceback.print_exc()
        
        print("-" * 40)
    
    # Test translation buttons with debug output
    translation_tests = [
        ("X+2.0mm", "x", 2.0),
        ("Y-1.5mm", "y", -1.5),
        ("Z+3.0mm", "z", 3.0),
    ]
    
    print(f"\n🧪 TESTING TRANSLATION BUTTONS WITH DEBUG OUTPUT...")
    print("=" * 70)
    
    for test_name, axis, amount in translation_tests:
        print(f"\n🔄 TESTING {test_name}...")
        print("-" * 40)
        
        try:
            # Call the translation method - this will trigger debug output
            viewer.move_shape(axis, amount)
            QTest.qWait(3000)  # Wait for movement and debug output
            
            print(f"✅ {test_name} completed")
            
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
            import traceback
            traceback.print_exc()
        
        print("-" * 40)
    
    # Final analysis
    print("\n" + "=" * 70)
    print("🏁 COMPREHENSIVE DEBUG TEST COMPLETED")
    print("=" * 70)
    print("📊 ANALYSIS:")
    print("   - Check the debug output above for BEFORE/AFTER states")
    print("   - Look for inconsistencies in origin marker behavior")
    print("   - Verify that unified code is being called for all operations")
    print("   - Check that world origin markers stay fixed while local origin markers move")
    print("=" * 70)
    
    return True

if __name__ == "__main__":
    success = test_all_buttons_with_debug()
    print(f"\n🎯 Test completed. Check debug output above for detailed analysis.")
    sys.exit(0 if success else 1)
