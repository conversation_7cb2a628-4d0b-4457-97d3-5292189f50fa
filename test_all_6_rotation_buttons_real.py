#!/usr/bin/env python3
"""
REAL TEST: ALL 6 ROTATION BUTTONS
Actually tests X+, X-, Y+, Y-, Z+, Z- rotation buttons properly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK
import time

def check_world_origin_positions(viewer, test_name):
    """Check if all red actors (world origin) are at (0,0,0)"""
    print(f"\n🔴 {test_name} - World Origin Check:")
    
    renderer = viewer.vtk_renderer_left
    if not hasattr(renderer, 'renderer'):
        print("   ❌ No renderer found")
        return False
        
    red_actors_found = 0
    all_at_origin = True
    
    actor_collection = renderer.renderer.GetActors()
    if actor_collection:
        actor_collection.InitTraversal()
        actor = actor_collection.GetNextActor()
        while actor:
            color = actor.GetProperty().GetColor()
            # Check if this is a red actor (world origin marker)
            if color[0] > 0.8 and color[1] < 0.3 and color[2] < 0.3:
                pos = actor.GetPosition()
                red_actors_found += 1
                if abs(pos[0]) < 0.001 and abs(pos[1]) < 0.001 and abs(pos[2]) < 0.001:
                    print(f"   ✅ Red {red_actors_found}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) - AT ORIGIN")
                else:
                    print(f"   ❌ Red {red_actors_found}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) - NOT AT ORIGIN")
                    all_at_origin = False
            actor = actor_collection.GetNextActor()
    
    if red_actors_found == 0:
        print("   ❌ No red actors found!")
        return False
    elif all_at_origin:
        print(f"   ✅ {test_name}: World origin correctly stayed at (0,0,0)")
        return True
    else:
        print(f"   ❌ {test_name}: World origin moved away from (0,0,0) - FIX FAILED!")
        return False

def test_all_6_rotation_buttons():
    """Test all 6 rotation buttons properly"""
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load a STEP file first
    step_file = "e:/python/viewer/save/sample.step"
    if os.path.exists(step_file):
        print(f"📁 Loading STEP file: {step_file}")
        viewer.load_step_file(step_file)
        time.sleep(2)  # Allow loading to complete
        print("✅ STEP file loaded")
    else:
        print("❌ No STEP file found - test may not work properly")
    
    print("\n" + "="*80)
    print("🎯 TESTING ALL 6 ROTATION BUTTONS")
    print("="*80)
    
    # Test all 6 rotation buttons - POSITIVE AND NEGATIVE
    rotation_tests = [
        ("X+ Rotation", lambda: viewer.rotate_shape('x', 15.0)),
        ("X- Rotation", lambda: viewer.rotate_shape('x', -15.0)),
        ("Y+ Rotation", lambda: viewer.rotate_shape('y', 15.0)),
        ("Y- Rotation", lambda: viewer.rotate_shape('y', -15.0)),
        ("Z+ Rotation", lambda: viewer.rotate_shape('z', 15.0)),
        ("Z- Rotation", lambda: viewer.rotate_shape('z', -15.0)),
    ]
    
    results = []
    
    for test_name, rotation_func in rotation_tests:
        print(f"\n🔄 PERFORMING: {test_name}")
        
        # Check BEFORE rotation
        before_result = check_world_origin_positions(viewer, f"BEFORE {test_name}")
        
        # Perform rotation
        print(f"🔄 EXECUTING: {test_name}")
        rotation_func()
        time.sleep(1)  # Allow rotation to complete
        
        # Check AFTER rotation
        after_result = check_world_origin_positions(viewer, f"AFTER {test_name}")
        results.append((test_name, after_result))
        
        if after_result:
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    # Final summary
    print("\n" + "="*80)
    print("🎯 FINAL RESULTS - ALL 6 ROTATION BUTTONS")
    print("="*80)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        if result:
            print(f"   ✅ PASSED: {test_name}")
            passed += 1
        else:
            print(f"   ❌ FAILED: {test_name}")
            failed += 1
    
    print(f"\n📊 SUMMARY: {passed} PASSED, {failed} FAILED out of 6 rotation buttons")
    
    if failed == 0:
        print("\n🎉 ALL 6 ROTATION BUTTONS WORKING CORRECTLY!")
        print("   ✅ World origin stays at (0,0,0) during ALL rotations")
    else:
        print(f"\n❌ {failed} ROTATION BUTTONS FAILED!")
        print("   ❌ World origin not staying at (0,0,0) during some rotations")
        print("   🔧 FIXES NEEDED!")
    
    print("="*80)
    
    # Keep app running briefly to see results
    QTimer.singleShot(3000, app.quit)
    app.exec_()

if __name__ == "__main__":
    test_all_6_rotation_buttons()
