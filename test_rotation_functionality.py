#!/usr/bin/env python3

"""
TEST: Rotation Functionality
Test that model rotation still works after my changes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK as <PERSON><PERSON><PERSON>wer
from PyQt5.QtWidgets import QApplication
import time

def test_rotation_functionality():
    """Test that rotation buttons still work and update displays"""
    
    print("=" * 80)
    print("TEST: ROTATION FUNCTIONALITY")
    print("=" * 80)
    
    app = QApplication(sys.argv)
    
    try:
        # Create the main viewer
        viewer = StepViewer()
        viewer.show()
        
        # Wait for GUI to initialize
        app.processEvents()
        time.sleep(2)
        
        print("\n1. Loading SOIC-16 footprint...")
        
        # Load the SOIC-16 file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        # Set to TOP viewer and load file
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        print("✅ STEP file loaded successfully")
        
        # Wait for loading to complete
        app.processEvents()
        time.sleep(2)
        
        print("\n2. CHECKING INITIAL STATE...")
        
        # Check initial position
        if hasattr(viewer, 'current_pos_left'):
            print(f"✅ Initial current_pos_left: {viewer.current_pos_left}")
        else:
            print("❌ current_pos_left not found")
            
        # Check green sphere position
        if hasattr(viewer.vtk_renderer_left, 'part_origin_sphere') and viewer.vtk_renderer_left.part_origin_sphere:
            user_transform = viewer.vtk_renderer_left.part_origin_sphere.GetUserTransform()
            if user_transform:
                matrix = user_transform.GetMatrix()
                pos = [matrix.GetElement(0, 3), matrix.GetElement(1, 3), matrix.GetElement(2, 3)]
                print(f"✅ Initial green sphere UserTransform position: {pos}")
            else:
                print("❌ Green sphere UserTransform not found")
                
            actor_pos = viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
            print(f"   Initial green sphere GetPosition(): {actor_pos}")
        
        print("\n3. TESTING X ROTATION BUTTON...")
        
        # Test X rotation button
        if hasattr(viewer, 'rotate_x_pos_left'):
            print("   Clicking X+ rotation button...")
            viewer.rotate_x_pos_left()
            app.processEvents()
            time.sleep(1)
            
            # Check position after rotation
            if hasattr(viewer, 'current_pos_left'):
                print(f"   After X+ rotation current_pos_left: {viewer.current_pos_left}")
            
            # Check green sphere position after rotation
            if hasattr(viewer.vtk_renderer_left, 'part_origin_sphere') and viewer.vtk_renderer_left.part_origin_sphere:
                user_transform = viewer.vtk_renderer_left.part_origin_sphere.GetUserTransform()
                if user_transform:
                    matrix = user_transform.GetMatrix()
                    pos = [matrix.GetElement(0, 3), matrix.GetElement(1, 3), matrix.GetElement(2, 3)]
                    print(f"   After X+ rotation green sphere UserTransform: {pos}")
                else:
                    print("   ❌ Green sphere UserTransform lost after rotation")
                    
                actor_pos = viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
                print(f"   After X+ rotation green sphere GetPosition(): {actor_pos}")
        else:
            print("❌ rotate_x_pos_left method not found")
        
        print("\n4. TESTING MOUSE INTERACTION...")
        
        # Test mouse interaction handler
        print("   Testing mouse interaction handler...")
        viewer.on_mouse_interaction_left(None, "TestEvent")
        
        print("\n5. CHECKING FINAL STATE...")
        
        # Check final position
        if hasattr(viewer, 'current_pos_left'):
            print(f"   Final current_pos_left: {viewer.current_pos_left}")
        
        print("\n" + "=" * 80)
        print("ROTATION FUNCTIONALITY TEST COMPLETE")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_rotation_functionality()
