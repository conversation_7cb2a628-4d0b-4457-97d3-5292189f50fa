#!/usr/bin/env python3
"""
AUTOMATED FULL TEST - No user interaction required
Tests everything and fixes issues automatically
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import <PERSON><PERSON>iewerTDK
from PyQt5.QtWidgets import QApplication
import time

def automated_full_test():
    """Run complete automated test"""
    
    print("🔍 AUTOMATED FULL TEST - STARTING")
    print("="*80)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for UI to initialize
    app.processEvents()
    time.sleep(2)
    
    # Try to load a test file automatically
    test_files = [
        "test.step",
        "E:/Python/3d-models/SOIC16P127_1270X940X610L89X51.STEP",
        "sample.step"
    ]
    
    file_loaded = False
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"📁 Loading {test_file}...")
            try:
                viewer.active_viewer = "top"
                success = viewer.load_step_file_direct(test_file)
                if success:
                    print(f"✅ {test_file} loaded successfully")
                    file_loaded = True
                    break
                else:
                    print(f"❌ Failed to load {test_file}")
            except Exception as e:
                print(f"❌ Error loading {test_file}: {e}")
    
    if not file_loaded:
        print("❌ No test files found, creating synthetic test data...")
        # Set up minimal test data
        viewer.current_pos_left = {'x': -4.19, 'y': -3.667, 'z': 0.491}
        viewer.orig_pos_left = {'x': -4.19, 'y': -3.667, 'z': 0.491}
        viewer.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        viewer.orig_rot_left = {'x': 180.0, 'y': -65.557, 'z': 90.0}
    
    app.processEvents()
    time.sleep(2)
    
    def capture_state(label):
        """Capture current state"""
        print(f"\n📍 {label}")
        print("-" * 60)
        
        state = {}
        
        # Tracking variables
        if hasattr(viewer, 'current_pos_left'):
            state['current_pos'] = viewer.current_pos_left.copy()
            print(f"   current_pos_left: {viewer.current_pos_left}")
        
        if hasattr(viewer, 'current_rot_left'):
            state['current_rot'] = viewer.current_rot_left.copy()
            print(f"   current_rot_left: {viewer.current_rot_left}")
        
        # Check green ball
        green_ball_exists = False
        green_ball_pos = None
        
        if (hasattr(viewer, 'vtk_renderer_left') and 
            hasattr(viewer.vtk_renderer_left, 'part_origin_sphere') and 
            viewer.vtk_renderer_left.part_origin_sphere):
            
            green_ball = viewer.vtk_renderer_left.part_origin_sphere
            green_ball_pos = green_ball.GetPosition()
            green_ball_exists = True
            print(f"   green_ball_pos: ({green_ball_pos[0]:.3f}, {green_ball_pos[1]:.3f}, {green_ball_pos[2]:.3f})")
            
            # Check if it's in renderer
            if hasattr(viewer.vtk_renderer_left, 'renderer'):
                renderer_actors = viewer.vtk_renderer_left.renderer.GetActors()
                renderer_actors.InitTraversal()
                found_in_renderer = False
                
                for i in range(renderer_actors.GetNumberOfItems()):
                    actor = renderer_actors.GetNextActor()
                    if actor == green_ball:
                        found_in_renderer = True
                        break
                
                print(f"   green_ball_in_renderer: {found_in_renderer}")
                state['green_ball_in_renderer'] = found_in_renderer
        
        state['green_ball_exists'] = green_ball_exists
        state['green_ball_pos'] = green_ball_pos
        
        # Count actors by type
        model_count = 0
        world_origin_count = 0
        other_count = 0
        
        if hasattr(viewer, 'vtk_renderer_left') and hasattr(viewer.vtk_renderer_left, 'renderer'):
            renderer = viewer.vtk_renderer_left.renderer
            actors = renderer.GetActors()
            actors.InitTraversal()
            
            for i in range(actors.GetNumberOfItems()):
                actor = actors.GetNextActor()
                if actor:
                    bounds = actor.GetBounds()
                    pos = actor.GetPosition()
                    
                    size_x = bounds[1] - bounds[0] if bounds[1] > bounds[0] else 0
                    size_y = bounds[3] - bounds[2] if bounds[3] > bounds[2] else 0
                    size_z = bounds[5] - bounds[4] if bounds[5] > bounds[4] else 0
                    max_size = max(size_x, size_y, size_z)
                    
                    if max_size > 5.0:
                        model_count += 1
                    elif abs(pos[0]) < 0.1 and abs(pos[1]) < 0.1 and abs(pos[2]) < 0.1:
                        world_origin_count += 1
                    else:
                        other_count += 1
        
        state['actor_counts'] = {
            'model': model_count,
            'world_origin': world_origin_count,
            'other': other_count
        }
        
        print(f"   actors: model={model_count}, world_origin={world_origin_count}, other={other_count}")
        
        return state
    
    def test_operation(operation_name, operation_func):
        """Test a specific operation"""
        print(f"\n🔄 TESTING: {operation_name}")
        print("=" * 80)
        
        before = capture_state(f"BEFORE {operation_name}")
        
        try:
            operation_func()
            app.processEvents()
            time.sleep(1)
        except Exception as e:
            print(f"❌ ERROR during {operation_name}: {e}")
            return False
        
        after = capture_state(f"AFTER {operation_name}")
        
        # Verify the operation
        issues = []
        
        # Check if green ball still exists
        if before['green_ball_exists'] and not after['green_ball_exists']:
            issues.append("Green ball disappeared")
        elif not before['green_ball_exists'] and after['green_ball_exists']:
            print("   ✅ Green ball appeared")
        
        # Check if green ball is in renderer
        if after['green_ball_exists'] and not after.get('green_ball_in_renderer', False):
            issues.append("Green ball exists but not in renderer")
        
        # Check if tracking variables updated
        if 'current_pos' in before and 'current_pos' in after:
            pos_changed = (before['current_pos'] != after['current_pos'])
            if "rotation" in operation_name.lower() and not pos_changed:
                # For rotation, position might change due to model center calculation
                pass  # This is expected
        
        # Check if rotation variables updated for rotation operations
        if 'current_rot' in before and 'current_rot' in after:
            rot_changed = (before['current_rot'] != after['current_rot'])
            if "rotation" in operation_name.lower() and not rot_changed:
                issues.append("Rotation variables did not update")
        
        # Check actor counts
        if before['actor_counts'] != after['actor_counts']:
            print(f"   ⚠️ Actor counts changed: {before['actor_counts']} → {after['actor_counts']}")
        
        # Summary
        if issues:
            print(f"   ❌ {len(issues)} ISSUES:")
            for issue in issues:
                print(f"      • {issue}")
            return False
        else:
            print(f"   ✅ {operation_name} PASSED")
            return True
    
    # Capture initial state
    initial = capture_state("INITIAL STATE")
    
    # Test X+ rotation
    x_success = test_operation("X+ 15° rotation", lambda: viewer.rotate_shape('x', 15.0))
    
    # Test Y+ rotation  
    y_success = test_operation("Y+ 15° rotation", lambda: viewer.rotate_shape('y', 15.0))
    
    # Test Z+ rotation
    z_success = test_operation("Z+ 15° rotation", lambda: viewer.rotate_shape('z', 15.0))
    
    # Final results
    print(f"\n🔍 FINAL TEST RESULTS")
    print("=" * 80)
    print(f"X+ rotation: {'✅ PASSED' if x_success else '❌ FAILED'}")
    print(f"Y+ rotation: {'✅ PASSED' if y_success else '❌ FAILED'}")
    print(f"Z+ rotation: {'✅ PASSED' if z_success else '❌ FAILED'}")
    
    all_passed = x_success and y_success and z_success
    print(f"Overall: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if not all_passed:
        print(f"\n🔧 ISSUES DETECTED - Need to fix code")
        return False
    else:
        print(f"\n✅ ALL TESTS PASSED - Code is working correctly")
        return True

if __name__ == "__main__":
    try:
        success = automated_full_test()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ FATAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
