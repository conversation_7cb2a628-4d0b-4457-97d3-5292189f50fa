#!/usr/bin/env python3
"""
Comprehensive STEP file loading diagnosis
Tests the unified system and identifies the exact failure points
"""

import sys
import os
import traceback

def test_step_loader_basic():
    """Test basic STEP loader functionality"""
    print("\n=== TESTING BASIC STEP LOADER ===")
    
    try:
        from step_loader import STEPLoader
        
        loader = STEPLoader()
        print("✅ STEPLoader imported and created successfully")
        
        # Test with the STEP file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        print(f"📁 Testing with file: {step_file}")
        
        # Try to load the file
        success, message = loader.load_step_file(step_file)
        print(f"Load result: success={success}, message='{message}'")
        
        if success:
            print("✅ STEP file loaded successfully")
            if hasattr(loader, 'current_polydata') and loader.current_polydata:
                polydata = loader.current_polydata
                print(f"✅ Polydata created: {polydata.GetNumberOfPoints()} points, {polydata.GetNumberOfCells()} cells")
                return True
            else:
                print("❌ No polydata created")
                return False
        else:
            print(f"❌ STEP file loading failed: {message}")
            return False
            
    except Exception as e:
        print(f"❌ Exception in basic STEP loader test: {e}")
        traceback.print_exc()
        return False

def test_vtk_renderer_basic():
    """Test basic VTK renderer functionality"""
    print("\n=== TESTING BASIC VTK RENDERER ===")
    
    try:
        from vtk_renderer import VTKRenderer
        
        renderer = VTKRenderer()
        print("✅ VTKRenderer imported and created successfully")
        
        # Check if it has the display_polydata method
        if hasattr(renderer, 'display_polydata'):
            print("✅ display_polydata method exists")
        else:
            print("❌ display_polydata method missing")
            return False
            
        # Check if it has the add_step_data method (this should NOT exist)
        if hasattr(renderer, 'add_step_data'):
            print("⚠️  add_step_data method exists (unexpected)")
        else:
            print("✅ add_step_data method does not exist (expected)")
            
        return True
        
    except Exception as e:
        print(f"❌ Exception in basic VTK renderer test: {e}")
        traceback.print_exc()
        return False

def test_unified_system_issue():
    """Test the unified system to identify the exact issue"""
    print("\n=== TESTING UNIFIED SYSTEM ISSUE ===")
    
    try:
        # Import without creating GUI
        import step_viewer
        
        # Create a minimal test viewer
        class TestViewer:
            def __init__(self):
                from step_loader import STEPLoader
                from vtk_renderer import VTKRenderer
                
                self.step_loader_left = STEPLoader()
                self.vtk_renderer_left = VTKRenderer()
                self.active_viewer = "top"
                
            def _convert_step_to_vtk(self, step_data, filename):
                """Copy of the method from step_viewer.py"""
                try:
                    if step_data and hasattr(step_data, 'current_polydata'):
                        return step_data.current_polydata
                    return None
                except Exception as e:
                    print(f"ERROR converting STEP to VTK: {e}")
                    return None
                    
            def _unified_load(self, step_data, filename):
                """Copy of the problematic method from step_viewer.py"""
                print(f"UNIFIED LOAD: Processing {filename}")

                try:
                    # Step 1: Convert STEP data to VTK format
                    vtk_data = self._convert_step_to_vtk(step_data, filename)
                    print(f"VTK data conversion result: {vtk_data is not None}")

                    # Step 2: Display in active viewer
                    if self.active_viewer == "top":
                        renderer = self.vtk_renderer_left
                    else:
                        renderer = None  # Not testing bottom viewer

                    print(f"Renderer selected: {renderer is not None}")
                    
                    # Step 3: Add to renderer - THIS IS THE PROBLEM
                    if vtk_data and renderer:
                        print("Attempting to call renderer.add_step_data()...")
                        if hasattr(renderer, 'add_step_data'):
                            renderer.add_step_data(vtk_data)
                            print(f"UNIFIED LOAD: Successfully loaded {filename}")
                            return True
                        else:
                            print("❌ PROBLEM FOUND: renderer.add_step_data() method does not exist!")
                            print("Available methods:")
                            methods = [m for m in dir(renderer) if not m.startswith('_')]
                            for method in methods[:10]:  # Show first 10 methods
                                print(f"  - {method}")
                            if hasattr(renderer, 'display_polydata'):
                                print("✅ SOLUTION: renderer.display_polydata() method exists!")
                            return False
                    else:
                        print(f"UNIFIED LOAD: Failed to process {filename}")
                        return False

                except Exception as e:
                    print(f"ERROR in _unified_load: {e}")
                    traceback.print_exc()
                    return False
        
        # Test the unified system
        viewer = TestViewer()
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        
        if os.path.exists(step_file):
            # Load the STEP file first
            success, message = viewer.step_loader_left.load_step_file(step_file)
            if success:
                print("✅ STEP file loaded into loader")
                # Now test the unified load
                result = viewer._unified_load(viewer.step_loader_left, step_file)
                print(f"Unified load result: {result}")
                return result
            else:
                print(f"❌ Failed to load STEP file: {message}")
                return False
        else:
            print(f"❌ STEP file not found: {step_file}")
            return False
            
    except Exception as e:
        print(f"❌ Exception in unified system test: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all diagnostic tests"""
    print("🔍 STEP FILE LOADING DIAGNOSIS")
    print("=" * 50)
    
    tests = [
        ("Basic STEP Loader", test_step_loader_basic),
        ("Basic VTK Renderer", test_vtk_renderer_basic),
        ("Unified System Issue", test_unified_system_issue),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: CRASHED - {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DIAGNOSIS SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
    
    print(f"\nResult: {passed}/{total} tests passed")
    
    if passed < total:
        print("\n🔧 IDENTIFIED ISSUES:")
        print("- The unified system calls renderer.add_step_data() which doesn't exist")
        print("- Should call renderer.display_polydata() instead")
        print("- This is why STEP files don't display in the unified system")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
