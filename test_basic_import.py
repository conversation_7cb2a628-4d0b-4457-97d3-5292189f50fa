#!/usr/bin/env python3
"""
Basic test to check if the core modules can be imported
"""

def test_imports():
    """Test if all core modules can be imported"""
    try:
        print("Testing step_loader import...")
        import step_loader
        print("✅ step_loader imported successfully")
        
        print("Testing vtk_renderer import...")
        import vtk_renderer
        print("✅ vtk_renderer imported successfully")
        
        print("Testing gui_components import...")
        import gui_components
        print("✅ gui_components imported successfully")
        
        print("Testing step_viewer import...")
        import step_viewer
        print("✅ step_viewer imported successfully")
        
        print("\n🎉 ALL IMPORTS SUCCESSFUL!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_imports()
