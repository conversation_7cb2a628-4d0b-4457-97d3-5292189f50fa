#!/usr/bin/env python3
"""
Complete debug test to find and fix the save rotation issue
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== COMPLETE SAVE DEBUG TEST ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"🔧 Testing with: {test_file}")
    
    print("\n=== STEP 1: LOAD STEP FILE ===")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ STEP file loaded")
        
        # Get initial values
        initial_pos = viewer._extract_position_from_display("top")
        initial_rot = viewer._extract_rotation_from_vtk_actor("top")
        print(f"   Initial position: {initial_pos}")
        print(f"   Initial rotation: {initial_rot}")
        
        print("\n=== STEP 2: APPLY ROTATION ===")
        print("🔄 Applying X+15° rotation")
        viewer._apply_model_rotation("top", "x", 15.0)
        
        # Get values after rotation
        after_pos = viewer._extract_position_from_display("top")
        after_rot = viewer._extract_rotation_from_vtk_actor("top")
        print(f"   After rotation position: {after_pos}")
        print(f"   After rotation rotation: {after_rot}")
        
        print("\n=== STEP 3: DEBUG SAVE PROCESS ===")
        test_save_file = "debug_save_test.step"
        
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
            
        # Get the step loader
        loader = viewer.step_loader_left
        print(f"   Loader: {loader}")
        print(f"   Loader has shape: {hasattr(loader, 'shape') and loader.shape}")

        # Calculate deltas
        delta_rot = {
            'x': after_rot['x'] - initial_rot['x'],
            'y': after_rot['y'] - initial_rot['y'],
            'z': after_rot['z'] - initial_rot['z']
        }
        print(f"   Delta rotation: {delta_rot}")

        # Check if there are significant changes
        has_rotation = (abs(delta_rot['x']) > 0.1 or abs(delta_rot['y']) > 0.1 or abs(delta_rot['z']) > 0.1)
        print(f"   Has significant rotation: {has_rotation}")

        # Test the save method directly
        print("\n=== STEP 4: CALL SAVE METHOD ===")
        print("🔧 About to call _save_step_with_transformations...")
        try:
            success = viewer._save_step_with_transformations(
                test_save_file, loader, after_pos, after_rot, initial_pos, initial_rot
            )
            print(f"   Save method returned: {success}")
            
            if os.path.exists(test_save_file):
                file_size = os.path.getsize(test_save_file)
                print(f"   File created: {test_save_file} ({file_size} bytes)")
                
                print("\n=== STEP 5: LOAD SAVED FILE ===")
                # Load the saved file in bottom viewer
                viewer.active_viewer = "bottom"
                load_success = viewer.load_step_file_direct(test_save_file)
                
                if load_success:
                    saved_pos = viewer._extract_position_from_display("bottom")
                    saved_rot = viewer._extract_rotation_from_vtk_actor("bottom")
                    print(f"   Saved file position: {saved_pos}")
                    print(f"   Saved file rotation: {saved_rot}")
                    
                    # Compare results
                    print("\n=== STEP 6: COMPARE RESULTS ===")
                    print(f"   Original after rotation: {after_rot}")
                    print(f"   Loaded from saved file:  {saved_rot}")
                    
                    # Check if rotation was preserved
                    rot_preserved = (
                        abs(saved_rot['x'] - after_rot['x']) < 1.0 and
                        abs(saved_rot['y'] - after_rot['y']) < 1.0 and
                        abs(saved_rot['z'] - after_rot['z']) < 1.0
                    )
                    
                    if rot_preserved:
                        print("✅ SUCCESS: Rotation preserved in saved file!")
                    else:
                        print("❌ FAILURE: Rotation NOT preserved in saved file!")
                        print("   This is the bug we need to fix.")
                else:
                    print("❌ Failed to load saved file")
            else:
                print("❌ Save file not created")
                
        except Exception as e:
            print(f"❌ Exception in save method: {e}")
            import traceback
            traceback.print_exc()
        
        # Clean up
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
            
    else:
        print("❌ Failed to load STEP file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== COMPLETE SAVE DEBUG TEST FINISHED ===")
