#!/usr/bin/env python3
"""
Debug the green ball properties to understand why it's not being detected
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import time

def debug_green_ball_properties():
    """Debug green ball properties"""
    
    print("🔍 DEBUG GREEN BALL PROPERTIES")
    print("="*80)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for UI to initialize
    app.processEvents()
    time.sleep(1)
    
    print("🔍 Please load a STEP file, then press Enter...")
    input("Press Enter when STEP file is loaded...")
    
    def analyze_all_actors():
        """Analyze all actors in detail"""
        print(f"\n🔍 DETAILED ACTOR ANALYSIS")
        print("="*80)
        
        if hasattr(viewer, 'vtk_renderer_left') and hasattr(viewer.vtk_renderer_left, 'renderer'):
            renderer = viewer.vtk_renderer_left.renderer
            actors = renderer.GetActors()
            actors.InitTraversal()
            
            print(f"Total actors: {actors.GetNumberOfItems()}")
            
            for i in range(actors.GetNumberOfItems()):
                actor = actors.GetNextActor()
                if actor:
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    bounds = actor.GetBounds()
                    
                    # Calculate size
                    size_x = bounds[1] - bounds[0] if bounds[1] > bounds[0] else 0
                    size_y = bounds[3] - bounds[2] if bounds[3] > bounds[2] else 0
                    size_z = bounds[5] - bounds[4] if bounds[5] > bounds[4] else 0
                    max_size = max(size_x, size_y, size_z)
                    
                    # Get color
                    try:
                        prop = actor.GetProperty()
                        color = prop.GetColor()
                        opacity = prop.GetOpacity()
                        color_str = f"RGB({color[0]:.3f}, {color[1]:.3f}, {color[2]:.3f})"
                    except:
                        color = (0, 0, 0)
                        opacity = 1.0
                        color_str = "N/A"
                    
                    # Check if this could be the green ball
                    is_green_candidate = False
                    reasons = []
                    
                    # Size check
                    if 0.05 < max_size < 5.0:  # Broader size range
                        reasons.append(f"size_ok({max_size:.3f})")
                    
                    # Color check (more lenient)
                    if color[1] > 0.5:  # Some green
                        reasons.append(f"has_green({color[1]:.3f})")
                    
                    # Position check (not at origin)
                    pos_distance = (pos[0]**2 + pos[1]**2 + pos[2]**2)**0.5
                    if pos_distance > 0.1:
                        reasons.append(f"not_at_origin({pos_distance:.3f})")
                    
                    if len(reasons) >= 2:
                        is_green_candidate = True
                    
                    print(f"\nActor {i:2d}:")
                    print(f"  Position: ({pos[0]:8.3f}, {pos[1]:8.3f}, {pos[2]:8.3f})")
                    print(f"  Orientation: ({orient[0]:6.1f}°, {orient[1]:6.1f}°, {orient[2]:6.1f}°)")
                    print(f"  Bounds: ({bounds[0]:.3f}, {bounds[1]:.3f}, {bounds[2]:.3f}, {bounds[3]:.3f}, {bounds[4]:.3f}, {bounds[5]:.3f})")
                    print(f"  Size: {max_size:.3f}")
                    print(f"  Color: {color_str}, Opacity: {opacity:.3f}")
                    
                    if is_green_candidate:
                        print(f"  🟢 GREEN BALL CANDIDATE: {', '.join(reasons)}")
                    
                    # Check if this matches the renderer's part_origin_sphere
                    if hasattr(viewer.vtk_renderer_left, 'part_origin_sphere'):
                        if actor == viewer.vtk_renderer_left.part_origin_sphere:
                            print(f"  ✅ THIS IS THE RENDERER'S part_origin_sphere!")
        
        # Also check if part_origin_sphere exists
        print(f"\n🔍 RENDERER GREEN BALL CHECK")
        print("="*40)
        
        if hasattr(viewer, 'vtk_renderer_left'):
            renderer = viewer.vtk_renderer_left
            
            if hasattr(renderer, 'part_origin_sphere'):
                if renderer.part_origin_sphere:
                    green_ball = renderer.part_origin_sphere
                    pos = green_ball.GetPosition()
                    orient = green_ball.GetOrientation()
                    bounds = green_ball.GetBounds()
                    
                    try:
                        prop = green_ball.GetProperty()
                        color = prop.GetColor()
                        opacity = prop.GetOpacity()
                        color_str = f"RGB({color[0]:.3f}, {color[1]:.3f}, {color[2]:.3f})"
                    except:
                        color_str = "N/A"
                        opacity = 1.0
                    
                    print(f"✅ part_origin_sphere EXISTS:")
                    print(f"  Position: ({pos[0]:8.3f}, {pos[1]:8.3f}, {pos[2]:8.3f})")
                    print(f"  Orientation: ({orient[0]:6.1f}°, {orient[1]:6.1f}°, {orient[2]:6.1f}°)")
                    print(f"  Bounds: {bounds}")
                    print(f"  Color: {color_str}, Opacity: {opacity:.3f}")
                    
                    # Check if it's in the renderer
                    renderer_actors = renderer.renderer.GetActors()
                    renderer_actors.InitTraversal()
                    found_in_renderer = False
                    
                    for i in range(renderer_actors.GetNumberOfItems()):
                        actor = renderer_actors.GetNextActor()
                        if actor == green_ball:
                            found_in_renderer = True
                            print(f"  ✅ Green ball IS in renderer (actor {i})")
                            break
                    
                    if not found_in_renderer:
                        print(f"  ❌ Green ball is NOT in renderer!")
                        
                else:
                    print(f"❌ part_origin_sphere is None")
            else:
                print(f"❌ part_origin_sphere attribute doesn't exist")
        else:
            print(f"❌ vtk_renderer_left doesn't exist")
    
    # Analyze initial state
    analyze_all_actors()
    
    input("\nPress Enter to test X+ rotation and see what happens to green ball...")
    
    # Test X+ rotation
    print(f"\n🔄 Testing X+ rotation...")
    viewer.rotate_shape('x', 15.0)
    app.processEvents()
    time.sleep(1)
    
    analyze_all_actors()
    
    print(f"\n🔍 GREEN BALL DEBUG COMPLETE")
    print(f"Press Ctrl+C to exit")
    
    try:
        app.exec_()
    except KeyboardInterrupt:
        print("Exiting...")

if __name__ == "__main__":
    debug_green_ball_properties()
