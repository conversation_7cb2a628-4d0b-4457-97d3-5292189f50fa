#!/usr/bin/env python3
"""
TEST LOAD TIME NUMBERS - Specifically test what numbers are shown when a file is loaded
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import <PERSON><PERSON>iewerTDK
from PyQt5.QtWidgets import QApplication
import time

def test_load_time_numbers():
    """Test the numbers shown immediately after loading a file"""
    
    print("🔍 TESTING LOAD TIME NUMBERS")
    print("="*80)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for UI to initialize
    app.processEvents()
    time.sleep(2)
    
    print("📊 INITIAL STATE (before loading):")
    if hasattr(viewer, 'current_pos_left'):
        print(f"   current_pos_left: {viewer.current_pos_left}")
    if hasattr(viewer, 'orig_pos_left'):
        print(f"   orig_pos_left: {viewer.orig_pos_left}")
    
    # Try to load a test file
    test_files = [
        "test.step",
        "E:/Python/3d-models/SOIC16P127_1270X940X610L89X51.STEP",
        "sample.step"
    ]
    
    file_loaded = False
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n📁 Loading {test_file}...")
            try:
                viewer.active_viewer = "top"
                success = viewer.load_step_file_direct(test_file)
                if success:
                    print(f"✅ {test_file} loaded successfully")
                    file_loaded = True
                    break
                else:
                    print(f"❌ Failed to load {test_file}")
            except Exception as e:
                print(f"❌ Error loading {test_file}: {e}")
    
    if not file_loaded:
        print("❌ No test files found - cannot test load time numbers")
        return False
    
    # Wait for loading to complete
    app.processEvents()
    time.sleep(3)
    
    print(f"\n📊 AFTER LOADING:")
    
    # Check tracking variables
    if hasattr(viewer, 'current_pos_left'):
        current_pos = viewer.current_pos_left
        print(f"   current_pos_left: {current_pos}")
    else:
        print(f"   current_pos_left: NOT SET")
        current_pos = None
    
    if hasattr(viewer, 'orig_pos_left'):
        orig_pos = viewer.orig_pos_left
        print(f"   orig_pos_left: {orig_pos}")
    else:
        print(f"   orig_pos_left: NOT SET")
        orig_pos = None
    
    # Force text update and check what's displayed
    print(f"\n🔢 FORCING TEXT UPDATE:")
    viewer.update_text_overlays()
    app.processEvents()
    time.sleep(1)
    
    # Check actual VTK positions
    print(f"\n🔍 ACTUAL VTK POSITIONS:")
    viewer._verify_actor_positions_vs_display("top")
    
    # Check if current_pos matches orig_pos (they should at load time)
    if current_pos and orig_pos:
        pos_match = (
            abs(current_pos['x'] - orig_pos['x']) < 0.001 and
            abs(current_pos['y'] - orig_pos['y']) < 0.001 and
            abs(current_pos['z'] - orig_pos['z']) < 0.001
        )
        print(f"\n📋 LOAD TIME ANALYSIS:")
        print(f"   current_pos matches orig_pos: {pos_match}")
        
        if not pos_match:
            print(f"   ❌ ISSUE: current_pos should equal orig_pos at load time")
            print(f"   current_pos: ({current_pos['x']:.6f}, {current_pos['y']:.6f}, {current_pos['z']:.6f})")
            print(f"   orig_pos:    ({orig_pos['x']:.6f}, {orig_pos['y']:.6f}, {orig_pos['z']:.6f})")
            return False
        else:
            print(f"   ✅ current_pos correctly matches orig_pos at load time")
    
    # Check if display shows the correct original position
    print(f"\n📊 WHAT SHOULD BE DISPLAYED:")
    if orig_pos:
        print(f"   Model Origin should show: ({orig_pos['x']:.3f}, {orig_pos['y']:.3f}, {orig_pos['z']:.3f})")
        print(f"   Local Origin should show: ({orig_pos['x']:.3f}, {orig_pos['y']:.3f}, {orig_pos['z']:.3f})")
    
    return True

if __name__ == "__main__":
    try:
        success = test_load_time_numbers()
        if success:
            print(f"\n✅ Load time numbers test completed")
        else:
            print(f"\n❌ Load time numbers test found issues")
        
        print(f"\nPress Ctrl+C to exit")
        input("Press Enter to continue...")
        
    except Exception as e:
        print(f"❌ FATAL ERROR: {e}")
        import traceback
        traceback.print_exc()
