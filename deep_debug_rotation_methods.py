#!/usr/bin/env python3
"""
Deep debug to understand the difference between mouse and button rotation.
This will help identify the root cause without user intervention.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import <PERSON><PERSON>iewerTDK
from PyQt5.QtWidgets import QApplication
import time

def deep_debug_rotation():
    """Deep debug rotation methods"""
    
    print("🔍 DEEP DEBUG: Analyzing rotation methods")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for UI to initialize
    app.processEvents()
    time.sleep(1)
    
    print("🔍 Please load a STEP file, then press Enter...")
    input("Press Enter when STEP file is loaded...")
    
    def capture_all_actor_states(label):
        """Capture complete state of all actors"""
        print(f"\n{'='*80}")
        print(f"🔍 {label}")
        print(f"{'='*80}")
        
        states = {}
        
        if hasattr(viewer, 'vtk_renderer_left') and hasattr(viewer.vtk_renderer_left, 'renderer'):
            renderer = viewer.vtk_renderer_left.renderer
            actors = renderer.GetActors()
            actors.InitTraversal()
            
            print(f"📊 TRACKING VARIABLES:")
            if hasattr(viewer, 'current_pos_left'):
                print(f"   current_pos_left: {viewer.current_pos_left}")
            if hasattr(viewer, 'current_rot_left'):
                print(f"   current_rot_left: {viewer.current_rot_left}")
            if hasattr(viewer, 'orig_pos_left'):
                print(f"   orig_pos_left: {viewer.orig_pos_left}")
            
            print(f"\n📦 ALL ACTORS ({actors.GetNumberOfItems()} total):")
            
            for i in range(actors.GetNumberOfItems()):
                actor = actors.GetNextActor()
                if actor:
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    bounds = actor.GetBounds()
                    
                    # Calculate size
                    size_x = bounds[1] - bounds[0] if bounds[1] > bounds[0] else 0
                    size_y = bounds[3] - bounds[2] if bounds[3] > bounds[2] else 0
                    size_z = bounds[5] - bounds[4] if bounds[5] > bounds[4] else 0
                    max_size = max(size_x, size_y, size_z)
                    
                    # Classify actor type
                    actor_type = "UNKNOWN"
                    if max_size > 5.0:
                        actor_type = "MODEL"
                    elif max_size < 1.0:
                        actor_type = "MARKER"
                    elif abs(pos[0]) < 0.1 and abs(pos[1]) < 0.1 and abs(pos[2]) < 0.1:
                        actor_type = "WORLD_ORIGIN"
                    else:
                        actor_type = "OTHER"
                    
                    print(f"   Actor {i:2d}: {actor_type:12s} pos=({pos[0]:8.3f}, {pos[1]:8.3f}, {pos[2]:8.3f}) "
                          f"orient=({orient[0]:6.1f}°, {orient[1]:6.1f}°, {orient[2]:6.1f}°) size={max_size:.3f}")
                    
                    states[i] = {
                        'type': actor_type,
                        'position': pos,
                        'orientation': orient,
                        'size': max_size
                    }
        
        return states
    
    def calculate_model_center_from_actors(states):
        """Calculate actual model center from model actors"""
        model_actors = [s for s in states.values() if s['type'] == 'MODEL']
        if not model_actors:
            return None
            
        # Calculate bounding box of all model actors
        min_x = min_y = min_z = float('inf')
        max_x = max_y = max_z = float('-inf')
        
        for actor_state in model_actors:
            pos = actor_state['position']
            size = actor_state['size']
            
            # Approximate bounds (this is rough, but gives us an idea)
            half_size = size / 2
            min_x = min(min_x, pos[0] - half_size)
            max_x = max(max_x, pos[0] + half_size)
            min_y = min(min_y, pos[1] - half_size)
            max_y = max(max_y, pos[1] + half_size)
            min_z = min(min_z, pos[2] - half_size)
            max_z = max(max_z, pos[2] + half_size)
        
        center_x = (min_x + max_x) / 2
        center_y = (min_y + max_y) / 2
        center_z = (min_z + max_z) / 2
        
        return (center_x, center_y, center_z)
    
    def compare_states(before_states, after_states, operation):
        """Compare actor states before and after operation"""
        print(f"\n🔍 ANALYSIS: Changes after {operation}")
        print(f"{'='*80}")
        
        # Calculate model centers
        before_center = calculate_model_center_from_actors(before_states)
        after_center = calculate_model_center_from_actors(after_states)
        
        if before_center and after_center:
            center_movement = (
                after_center[0] - before_center[0],
                after_center[1] - before_center[1], 
                after_center[2] - before_center[2]
            )
            center_distance = (center_movement[0]**2 + center_movement[1]**2 + center_movement[2]**2)**0.5
            
            print(f"📦 MODEL CENTER MOVEMENT:")
            print(f"   Before: ({before_center[0]:8.3f}, {before_center[1]:8.3f}, {before_center[2]:8.3f})")
            print(f"   After:  ({after_center[0]:8.3f}, {after_center[1]:8.3f}, {after_center[2]:8.3f})")
            print(f"   Change: ({center_movement[0]:8.3f}, {center_movement[1]:8.3f}, {center_movement[2]:8.3f})")
            print(f"   Distance: {center_distance:.6f}")
        
        # Check individual actor changes
        print(f"\n📊 INDIVIDUAL ACTOR CHANGES:")
        for actor_id in before_states:
            if actor_id in after_states:
                before = before_states[actor_id]
                after = after_states[actor_id]
                
                pos_change = (
                    after['position'][0] - before['position'][0],
                    after['position'][1] - before['position'][1],
                    after['position'][2] - before['position'][2]
                )
                pos_distance = (pos_change[0]**2 + pos_change[1]**2 + pos_change[2]**2)**0.5
                
                orient_change = (
                    after['orientation'][0] - before['orientation'][0],
                    after['orientation'][1] - before['orientation'][1],
                    after['orientation'][2] - before['orientation'][2]
                )
                
                if pos_distance > 0.001 or abs(orient_change[0]) > 0.1 or abs(orient_change[1]) > 0.1 or abs(orient_change[2]) > 0.1:
                    print(f"   Actor {actor_id:2d} ({before['type']:12s}): "
                          f"pos_change=({pos_change[0]:8.3f}, {pos_change[1]:8.3f}, {pos_change[2]:8.3f}) "
                          f"orient_change=({orient_change[0]:6.1f}°, {orient_change[1]:6.1f}°, {orient_change[2]:6.1f}°)")
        
        return before_center, after_center, center_movement
    
    # Capture initial state
    initial_states = capture_all_actor_states("INITIAL STATE")
    
    print(f"\n🔄 Testing BUTTON rotation X+ 15°...")
    before_button = capture_all_actor_states("BEFORE BUTTON X+ 15°")
    
    # Apply button rotation
    viewer.rotate_shape('x', 15.0)
    app.processEvents()
    time.sleep(1)
    
    after_button = capture_all_actor_states("AFTER BUTTON X+ 15°")
    
    # Analyze button rotation
    button_before_center, button_after_center, button_movement = compare_states(
        before_button, after_button, "BUTTON X+ 15°"
    )
    
    print(f"\n🔄 Testing MOUSE rotation simulation...")
    
    # Simulate mouse rotation by calling the same method mouse uses
    # First, let's see what method mouse actually calls
    print(f"🔍 Checking what method mouse rotation uses...")
    
    # Look for the mouse rotation method
    if hasattr(viewer, '_apply_model_rotation'):
        print(f"✅ Found _apply_model_rotation method - this is what mouse uses")
        
        before_mouse = capture_all_actor_states("BEFORE MOUSE SIMULATION")
        
        # Call the same method mouse uses
        viewer._apply_model_rotation("top", 'y', 15.0)
        app.processEvents()
        time.sleep(1)
        
        after_mouse = capture_all_actor_states("AFTER MOUSE SIMULATION")
        
        # Analyze mouse rotation
        mouse_before_center, mouse_after_center, mouse_movement = compare_states(
            before_mouse, after_mouse, "MOUSE Y+ 15°"
        )
        
        print(f"\n🔍 COMPARISON: Button vs Mouse rotation")
        print(f"{'='*80}")
        print(f"Button X+ 15° model center movement: {button_movement}")
        print(f"Mouse  Y+ 15° model center movement: {mouse_movement}")
        
        # Check if green ball position matches model center
        if hasattr(viewer, 'current_pos_left'):
            expected_green = (viewer.current_pos_left['x'], viewer.current_pos_left['y'], viewer.current_pos_left['z'])
            if mouse_after_center:
                green_vs_model = (
                    expected_green[0] - mouse_after_center[0],
                    expected_green[1] - mouse_after_center[1],
                    expected_green[2] - mouse_after_center[2]
                )
                distance = (green_vs_model[0]**2 + green_vs_model[1]**2 + green_vs_model[2]**2)**0.5
                
                print(f"\n🟢 GREEN BALL ANALYSIS:")
                print(f"   Expected green ball position: {expected_green}")
                print(f"   Actual model center:          {mouse_after_center}")
                print(f"   Difference:                   {green_vs_model}")
                print(f"   Distance:                     {distance:.6f}")
                
                if distance < 0.1:
                    print(f"   ✅ Green ball matches model center!")
                else:
                    print(f"   ❌ Green ball does NOT match model center!")
    
    print(f"\n🔍 DEEP DEBUG COMPLETE")
    print(f"Press Ctrl+C to exit")
    
    try:
        app.exec_()
    except KeyboardInterrupt:
        print("Exiting...")

if __name__ == "__main__":
    deep_debug_rotation()
