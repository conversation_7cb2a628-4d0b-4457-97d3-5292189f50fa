#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
import time

def test_rotation_save():
    print("=== TESTING ROTATION SAVE SYSTEM ===")
    
    # Test cases with different rotation combinations
    test_cases = [
        {"name": "Simple X rotation", "x": 45.0, "y": 0.0, "z": 0.0},
        {"name": "Simple Y rotation", "x": 0.0, "y": 30.0, "z": 0.0},
        {"name": "Simple Z rotation", "x": 0.0, "y": 0.0, "z": 60.0},
        {"name": "Complex rotation", "x": 30.0, "y": 45.0, "z": 60.0},
        {"name": "Negative rotations", "x": -15.0, "y": -25.0, "z": -35.0},
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases):
        print(f"\n--- TEST {i+1}: {test_case['name']} ---")
        
        try:
            # Create viewer
            viewer = StepViewerTDK()
            
            # Load original file
            print("1. Loading demo.step...")
            success = viewer.load_step_file_direct('demo.step')
            if not success:
                print("❌ Failed to load demo.step")
                results.append({"test": test_case['name'], "status": "LOAD_FAILED"})
                continue
            
            # Apply rotations
            print(f"2. Applying rotations: X={test_case['x']}° Y={test_case['y']}° Z={test_case['z']}°")
            if test_case['x'] != 0:
                viewer._apply_model_rotation('top', 'x', test_case['x'])
            if test_case['y'] != 0:
                viewer._apply_model_rotation('top', 'y', test_case['y'])
            if test_case['z'] != 0:
                viewer._apply_model_rotation('top', 'z', test_case['z'])
            
            # Get applied rotation values
            applied_rot = viewer.current_rot_left.copy()
            print(f"   Applied rotation: {applied_rot}")
            
            # Save rotated file
            test_filename = f"test_rotated_{i+1}.step"
            print(f"3. Saving to {test_filename}...")
            
            # Use green button save method
            loader = viewer.step_loader_left
            save_success = viewer._save_step_with_current_rotation(test_filename, loader, applied_rot)
            
            if not save_success:
                print("❌ Save failed")
                results.append({"test": test_case['name'], "status": "SAVE_FAILED"})
                continue
            
            # Verify file was created
            if not os.path.exists(test_filename):
                print("❌ File not created")
                results.append({"test": test_case['name'], "status": "FILE_NOT_CREATED"})
                continue
            
            file_size = os.path.getsize(test_filename)
            print(f"✅ File created: {file_size} bytes")
            
            # Load saved file in bottom viewer
            print("4. Loading saved file...")
            load_success = viewer.load_step_file_direct(test_filename, viewer="bottom")
            
            if not load_success:
                print("❌ Failed to load saved file")
                results.append({"test": test_case['name'], "status": "RELOAD_FAILED"})
                continue
            
            # Get saved rotation values
            saved_rot = viewer.current_rot_right.copy()
            print(f"   Saved rotation: {saved_rot}")
            
            # Compare rotations (allow small tolerance for floating point)
            tolerance = 0.1
            x_match = abs(applied_rot['x'] - saved_rot['x']) < tolerance
            y_match = abs(applied_rot['y'] - saved_rot['y']) < tolerance
            z_match = abs(applied_rot['z'] - saved_rot['z']) < tolerance
            
            if x_match and y_match and z_match:
                print("✅ SUCCESS: Rotation values preserved!")
                results.append({
                    "test": test_case['name'], 
                    "status": "SUCCESS",
                    "applied": applied_rot,
                    "saved": saved_rot
                })
            else:
                print("❌ FAILURE: Rotation values not preserved")
                print(f"   Applied: X={applied_rot['x']:.1f}° Y={applied_rot['y']:.1f}° Z={applied_rot['z']:.1f}°")
                print(f"   Saved:   X={saved_rot['x']:.1f}° Y={saved_rot['y']:.1f}° Z={saved_rot['z']:.1f}°")
                results.append({
                    "test": test_case['name'], 
                    "status": "ROTATION_MISMATCH",
                    "applied": applied_rot,
                    "saved": saved_rot
                })
            
            # Clean up
            try:
                os.remove(test_filename)
            except:
                pass
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            results.append({"test": test_case['name'], "status": "EXCEPTION", "error": str(e)})
    
    # Summary
    print("\n=== TEST RESULTS SUMMARY ===")
    success_count = 0
    for result in results:
        status = result['status']
        if status == "SUCCESS":
            print(f"✅ {result['test']}: SUCCESS")
            success_count += 1
        else:
            print(f"❌ {result['test']}: {status}")
    
    print(f"\nOverall: {success_count}/{len(test_cases)} tests passed")
    
    if success_count == len(test_cases):
        print("🎉 ALL TESTS PASSED! Rotation save system is working perfectly!")
    else:
        print("⚠️  Some tests failed. Rotation save system needs fixes.")
    
    return success_count == len(test_cases)

if __name__ == "__main__":
    test_rotation_save()
