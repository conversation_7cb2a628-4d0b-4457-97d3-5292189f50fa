#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
STEP Viewer TDK Modular - Dual Viewer Version
Enhanced with top/bottom viewer selection and compact transform display
BACKUP CREATED: Before FreeCAD icon improvements
CACHE BUSTER: 2025-01-07-15:30:00 - MOUSE ROTATION SAVE FIX
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QSplitter, QPushButton, QLabel,
                            QFileDialog, QComboBox, QSpinBox, QGroupBox)
from PyQt5.QtCore import Qt, QTimer, QSize
from PyQt5.QtGui import QIcon, QPixmap, QPainter, QPen

# FORCE MODULE RELOAD - Clear Python import cache
import importlib
if 'gui_components' in sys.modules:
    importlib.reload(sys.modules['gui_components'])
if 'step_loader' in sys.modules:
    importlib.reload(sys.modules['step_loader'])
if 'vtk_renderer' in sys.modules:
    importlib.reload(sys.modules['vtk_renderer'])

# Import custom modules
from step_loader import STEPLoader
from vtk_renderer import VTKRenderer
from gui_components import create_tool_dock

class StepViewerTDK(QMainWindow):
    def __init__(self):
        super().__init__()
        print("*** PROGRAM STARTING - THIS MESSAGE SHOULD BE VISIBLE! ***")
        print("CONSOLE OUTPUT TEST: If you can see this, console is working!")
        self.setWindowTitle("VTK TDK STEP Viewer (Dual View) - Enhanced")
        self.setGeometry(200, 200, 1200, 800)  # More conservative size and position

        # Initialize dual components
        self.step_loader_left = STEPLoader()
        self.step_loader_right = STEPLoader()
        self.vtk_renderer_left = VTKRenderer(self)
        self.vtk_renderer_right = VTKRenderer(self)

        # Setup text overlays for both viewers - ENABLED
        self.setup_text_overlays()

        # Setup VTK interaction observers for mouse rotation detection
        self.setup_vtk_observers()

        # Data tracking for both viewers - bounding box on by default
        self.bbox_visible_left = True
        self.bbox_visible_right = True

        # Transform data for left (top) viewer
        self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track button rotations
        self.cursor_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track cursor position
        # FIXED: Store STEP file direction vectors for green sphere orientation
        self.orig_z_direction_left = None
        self.orig_x_direction_left = None

        # Transform data for right (bottom) viewer
        self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track button rotations
        self.cursor_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}  # Track cursor position
        # FIXED: Store STEP file direction vectors for green sphere orientation
        self.orig_z_direction_right = None
        self.orig_x_direction_right = None

        # Store original actor transforms for proper reset
        self.original_actor_transforms_left = []
        self.original_actor_transforms_right = []

        # Active viewer tracking
        self.active_viewer = "top"

        # Overlay mode tracking
        self.overlay_mode = False
        self.overlay_widget = None

        # Setup UI
        self.init_ui()

        # Mouse tracking timer disabled (was causing screen jumping)
        # Previous camera positions tracking disabled
        # Only track button rotations, not mouse camera movements

        # Track previous camera positions to detect mouse rotation
        self.prev_camera_pos_left = None
        self.prev_camera_pos_right = None

        self.statusBar().showMessage("Ready - Select TOP or BOTTOM viewer, then load STEP files")

        # TEMPORARY: Auto-load disabled - test manually
        # self.auto_load_and_overlay()

    def auto_load_and_overlay(self):
        """TEMPORARY: Auto-load files and show overlay for debugging"""
        import os
        from PyQt5.QtCore import QTimer

        def delayed_load():
            print("TARGET TEMP DEBUG: Auto-loading files and showing overlay...")

            # Use specific STEP files for testing
            step_files = ['SOIC16P127_1270X940X610L89X51.STEP', 'AMPHENOL_U77-A1118-200T.STEP']
            # Check if files exist
            step_files = [f for f in step_files if os.path.exists(f)]
            if len(step_files) >= 2:
                # Load first file into TOP viewer
                self.active_viewer = "top"
                self.update_viewer_highlights()
                success1 = self.load_step_file_direct(step_files[0])
                print(f"RED TOP file loaded: {success1} - {step_files[0]}")

                # Load second file into BOTTOM viewer
                self.active_viewer = "bottom"
                self.update_viewer_highlights()
                success2 = self.load_step_file_direct(step_files[1])
                print(f"BLUE BOTTOM file loaded: {success2} - {step_files[1]}")

                if success1 and success2:
                    # Wait a bit then show overlay
                    QTimer.singleShot(2000, self.toggle_viewer_overlay)
                    print("TARGET TEMP DEBUG: Will show overlay in 2 seconds...")
                else:
                    print(f"FAIL TEMP DEBUG: File loading failed - TOP:{success1}, BOTTOM:{success2}")
            else:
                print(f"FAIL TEMP DEBUG: Need at least 2 STEP files, found {len(step_files)}")
                if step_files:
                    print(f"Available files: {step_files}")

        # Delay the auto-load to let GUI initialize
        QTimer.singleShot(1000, delayed_load)

    def setup_text_overlays(self):
        """Setup VTK text overlays for both viewers"""
        import vtk

        # Setup text overlay for TOP viewer - BACK TO SINGLE COMBINED DISPLAY
        print("DEBUG DEBUG: Setting up TOP viewer text overlays...")
        if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left:
            print("DEBUG DEBUG: vtk_renderer_left found")
            renderer = self.vtk_renderer_left.renderer
            if renderer:
                print("DEBUG DEBUG: TOP renderer found, creating cursor text actor...")
                # Create cursor text actor at TOP of screen
                self.cursor_text_actor_left = vtk.vtkTextActor()
                self.cursor_text_actor_left.SetInput("CURSOR: X=0.00 Y=0.00 Z=0.00")
                self.cursor_text_actor_left.GetTextProperty().SetFontSize(14)  # Larger font for visibility
                self.cursor_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.cursor_text_actor_left.GetTextProperty().SetBold(True)
                self.cursor_text_actor_left.SetVisibility(0)  # HIDE at startup until model is loaded

                # FIXED: Position at top of screen (normalized coordinates work better)
                self.cursor_text_actor_left.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.cursor_text_actor_left.SetPosition(0.02, 0.95)  # Top left corner (2% from left, 95% from bottom)

                renderer.AddActor2D(self.cursor_text_actor_left)

                # Enable layered rendering to support layer numbers
                renderer.SetLayer(0)  # Base layer
                if hasattr(renderer, 'GetRenderWindow') and renderer.GetRenderWindow():
                    renderer.GetRenderWindow().SetNumberOfLayers(11)  # Support layers 0-10

                print("OK DEBUG: cursor_text_actor_left created with normalized position (0.02, 0.95)")

                # FORCE TEST: Make cursor text visible with test message
                self.cursor_text_actor_left.SetInput("CURSOR TEST: VISIBLE")
                self.cursor_text_actor_left.SetVisibility(0)
                print("DEBUG DEBUG: FORCED cursor text to be visible with test message")

                # CREATE MISSING TOP TEXT OVERLAY
                self.combined_text_actor_left = vtk.vtkTextActor()
                self.combined_text_actor_left.SetInput("ANGLE: 0.0deg AXIS: (X=0.00 Y=0.00 Z=1.00) POS: X=0.000mm Y=0.000mm Z=0.000mm")
                self.combined_text_actor_left.GetTextProperty().SetFontSize(14)  # Same size as cursor
                self.combined_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.combined_text_actor_left.GetTextProperty().SetBold(True)
                self.combined_text_actor_left.SetPosition(10, 10)  # Bottom left
                self.combined_text_actor_left.SetVisibility(0)  # HIDE at startup until model is loaded
                renderer.AddActor2D(self.combined_text_actor_left)
                print("OK DEBUG: TOP combined text overlay created and hidden")
            else:
                print("FAIL DEBUG: TOP renderer not found!")
        else:
            print("FAIL DEBUG: vtk_renderer_left not found!")

        # Setup text overlay for BOTTOM viewer - BACK TO SINGLE COMBINED DISPLAY
        if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
            renderer = self.vtk_renderer_right.renderer
            if renderer:
                # Create cursor text actor for BOTTOM viewer (same as TOP)
                self.cursor_text_actor_right = vtk.vtkTextActor()
                self.cursor_text_actor_right.SetInput("CURSOR: X=0.00 Y=0.00 Z=0.00")
                self.cursor_text_actor_right.GetTextProperty().SetFontSize(14)  # Same size as TOP cursor
                self.cursor_text_actor_right.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.cursor_text_actor_right.GetTextProperty().SetBold(True)
                self.cursor_text_actor_right.SetVisibility(0)  # HIDE at startup until model is loaded

                # Position at same location as TOP cursor (top left corner)
                self.cursor_text_actor_right.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                self.cursor_text_actor_right.SetPosition(0.02, 0.95)  # Same as TOP cursor

                renderer.AddActor2D(self.cursor_text_actor_right)

                # Enable layered rendering for BOTTOM viewer too
                renderer.SetLayer(0)  # Base layer
                if hasattr(renderer, 'GetRenderWindow') and renderer.GetRenderWindow():
                    renderer.GetRenderWindow().SetNumberOfLayers(11)  # Support layers 0-10

                print("OK DEBUG: BOTTOM cursor_text_actor_right created at same position as TOP")

                # Create combined text actor for position and rotation display
                self.combined_text_actor_right = vtk.vtkTextActor()
                self.combined_text_actor_right.SetInput("ANGLE: 0.0deg AXIS: (X=0.00 Y=0.00 Z=1.00) POS: X=0.000mm Y=0.000mm Z=0.000mm")
                self.combined_text_actor_right.GetTextProperty().SetFontSize(14)  # Same size as TOP cursor
                self.combined_text_actor_right.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                self.combined_text_actor_right.GetTextProperty().SetBold(True)
                self.combined_text_actor_right.SetPosition(10, 10)  # Bottom left
                self.combined_text_actor_right.SetVisibility(0)  # HIDE at startup until model is loaded
                renderer.AddActor2D(self.combined_text_actor_right)
                print("DEBUG: OK BOTTOM viewer text overlay created with font size 14")

    def setup_text_overlay_for_viewer(self, viewer):
        """Setup VTK text overlay for a specific viewer"""
        import vtk

        if viewer == "top":
            if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left:
                renderer = self.vtk_renderer_left.renderer
                if renderer:
                    # Remove existing text actor if it exists
                    if hasattr(self, 'combined_text_actor_left'):
                        renderer.RemoveActor2D(self.combined_text_actor_left)

                    # Create new text actor
                    self.combined_text_actor_left = vtk.vtkTextActor()
                    self.combined_text_actor_left.SetInput("ANGLE ROT: X=0.0deg Y=0.0deg Z=0.0deg POS: X=0.000mm Y=0.000mm Z=0.000mm")
                    self.combined_text_actor_left.GetTextProperty().SetFontSize(14)  # Same size as cursor
                    self.combined_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                    self.combined_text_actor_left.GetTextProperty().SetBold(True)
                    self.combined_text_actor_left.SetPosition(10, 10)  # Bottom left
                    self.combined_text_actor_left.SetVisibility(0)  # HIDE until explicitly shown
                    renderer.AddActor2D(self.combined_text_actor_left)
                    print("DEBUG: OK TOP viewer text overlay re-created after model load - VISIBLE")

        elif viewer == "bottom":
            if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
                renderer = self.vtk_renderer_right.renderer
                if renderer:
                    # Remove existing text actor if it exists
                    if hasattr(self, 'combined_text_actor_right'):
                        renderer.RemoveActor2D(self.combined_text_actor_right)

                    # Create new text actor
                    self.combined_text_actor_right = vtk.vtkTextActor()
                    self.combined_text_actor_right.SetInput("ANGLE ROT: X=0.0deg Y=0.0deg Z=0.0deg POS: X=0.000mm Y=0.000mm Z=0.000mm")
                    self.combined_text_actor_right.GetTextProperty().SetFontSize(14)  # Same size as cursor
                    self.combined_text_actor_right.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                    self.combined_text_actor_right.GetTextProperty().SetBold(True)
                    self.combined_text_actor_right.SetPosition(10, 10)  # Bottom left
                    self.combined_text_actor_right.SetVisibility(0)  # HIDE until explicitly shown
                    renderer.AddActor2D(self.combined_text_actor_right)
                    print("DEBUG: OK BOTTOM viewer text overlay re-created after model load - VISIBLE")

    def setup_vtk_observers(self):
        """Setup VTK observers to detect mouse rotation events"""
        # Setup observer for TOP viewer
        if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left:
            if hasattr(self.vtk_renderer_left, 'interactor') and self.vtk_renderer_left.interactor:
                # Add multiple events that should trigger during mouse rotation
                self.vtk_renderer_left.interactor.AddObserver('InteractionEvent', self.on_mouse_interaction_left)
                self.vtk_renderer_left.interactor.AddObserver('LeftButtonReleaseEvent', self.on_mouse_interaction_left)
                self.vtk_renderer_left.interactor.AddObserver('MiddleButtonReleaseEvent', self.on_mouse_interaction_left)
                self.vtk_renderer_left.interactor.AddObserver('RightButtonReleaseEvent', self.on_mouse_interaction_left)
                self.vtk_renderer_left.interactor.AddObserver('MouseMoveEvent', self.on_mouse_move_left)  # Continuous cursor tracking
                print("DEBUG: OK TOP viewer mouse interaction and move observers added (multiple events)")

        # Setup observer for BOTTOM viewer
        if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
            if hasattr(self.vtk_renderer_right, 'interactor') and self.vtk_renderer_right.interactor:
                self.vtk_renderer_right.interactor.AddObserver('InteractionEvent', self.on_mouse_interaction_right)
                self.vtk_renderer_right.interactor.AddObserver('MouseMoveEvent', self.on_mouse_move_right)  # Continuous cursor tracking
                print("DEBUG: OK BOTTOM viewer mouse interaction and move observers added")

    # ========================================================================
    # UNIFIED MODEL MOVEMENT AND DISPLAY SYSTEM
    # All input methods (mouse, buttons, movement) use these core functions
    # ========================================================================

    def _apply_model_transform(self, viewer, transform_matrix):
        """
        CORE FUNCTION: Apply transformation to model and update all displays
        This is called by ALL input methods (mouse, buttons, movement)
        """
        print(f"🔧 CORE: Applying transform to {viewer} viewer")

        # Get the correct renderer
        if viewer == "top":
            vtk_renderer = self.vtk_renderer_left
        else:
            vtk_renderer = self.vtk_renderer_right

        if not vtk_renderer:
            print(f"❌ No renderer for {viewer}")
            return False

        try:
            # 1. Apply transform to all model actors
            self._transform_model_actors(vtk_renderer, transform_matrix)

            # 2. Update origin markers to match model
            self._transform_origin_markers(vtk_renderer, transform_matrix)

            # 3. Update position tracking variables
            self._update_position_tracking(viewer, vtk_renderer)

            # 4. Update ALL displays (yellow text, cursor, etc.)
            self._update_all_displays()

            # 5. Render the changes
            vtk_renderer.render_window.Render()

            print(f"✅ CORE: Transform applied successfully to {viewer}")
            return True

        except Exception as e:
            print(f"❌ CORE: Error applying transform: {e}")
            return False

    def _transform_model_actors(self, vtk_renderer, transform_matrix):
        """Apply transformation matrix to all model actors"""
        print("🔧 CORE: Transforming model actors")

        # Transform main model actors
        if hasattr(vtk_renderer, 'step_actors') and vtk_renderer.step_actors:
            for i, actor in enumerate(vtk_renderer.step_actors):
                if actor:
                    # Create or update UserTransform
                    import vtk
                    if not actor.GetUserTransform():
                        user_transform = vtk.vtkTransform()
                        actor.SetUserTransform(user_transform)
                    else:
                        user_transform = actor.GetUserTransform()

                    # Apply the transformation
                    user_transform.Concatenate(transform_matrix)
                    user_transform.Modified()
                    print(f"🔧 CORE: Updated UserTransform for actor {i}")

    def _transform_origin_markers(self, vtk_renderer, transform_matrix):
        """Apply transformation to origin markers (green sphere, arrows)"""
        print("🔧 CORE: Transforming origin markers")

        # Transform green sphere (part origin)
        if hasattr(vtk_renderer, 'part_origin_sphere') and vtk_renderer.part_origin_sphere:
            import vtk
            if not vtk_renderer.part_origin_sphere.GetUserTransform():
                user_transform = vtk.vtkTransform()
                vtk_renderer.part_origin_sphere.SetUserTransform(user_transform)
            else:
                user_transform = vtk_renderer.part_origin_sphere.GetUserTransform()

            # Apply the transformation
            user_transform.Concatenate(transform_matrix)
            user_transform.Modified()
            print("🔧 CORE: Updated green sphere UserTransform")

        # Transform arrows and other origin markers
        origin_actors = []
        if hasattr(vtk_renderer, 'part_origin_actors'):
            origin_actors.extend(vtk_renderer.part_origin_actors)
        if hasattr(vtk_renderer, 'origin_actors'):
            origin_actors.extend(vtk_renderer.origin_actors)

        for actor in origin_actors:
            if actor:
                import vtk
                if not actor.GetUserTransform():
                    user_transform = vtk.vtkTransform()
                    actor.SetUserTransform(user_transform)
                else:
                    user_transform = actor.GetUserTransform()

                user_transform.Concatenate(transform_matrix)
                user_transform.Modified()

    def _update_position_tracking(self, viewer, vtk_renderer):
        """Update position tracking variables from current actor transforms"""
        print(f"🔧 CORE: Updating position tracking for {viewer}")

        # Read current position from green sphere UserTransform
        if hasattr(vtk_renderer, 'part_origin_sphere') and vtk_renderer.part_origin_sphere:
            user_transform = vtk_renderer.part_origin_sphere.GetUserTransform()
            if user_transform:
                matrix = user_transform.GetMatrix()
                pos = [matrix.GetElement(0, 3), matrix.GetElement(1, 3), matrix.GetElement(2, 3)]

                if viewer == "top":
                    self.current_pos_left = {'x': pos[0], 'y': pos[1], 'z': pos[2]}
                    print(f"🔧 CORE: Updated current_pos_left: {self.current_pos_left}")
                else:
                    self.current_pos_right = {'x': pos[0], 'y': pos[1], 'z': pos[2]}
                    print(f"🔧 CORE: Updated current_pos_right: {self.current_pos_right}")

    def _update_all_displays(self):
        """CORE FUNCTION: Update ALL displays (yellow text, cursor, etc.)"""
        print("🔧 CORE: Updating all displays")

        # Update text overlays (this updates yellow text displays)
        self.update_text_overlays()

        # Update transform display
        self.update_transform_display()

        print("✅ CORE: All displays updated")

    def _ensure_text_actors_exist(self):
        """Ensure all text actors exist and are properly added to renderers"""
        print("DEBUG: Ensuring text actors exist and are properly added to renderers")

        # For TOP viewer
        if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left and self.vtk_renderer_left.renderer:
            # Force recreation of local and world origin text actors
            if hasattr(self, 'local_origin_text_actor_left'):
                try:
                    self.vtk_renderer_left.renderer.RemoveActor2D(self.local_origin_text_actor_left)
                except:
                    pass
                self.local_origin_text_actor_left = None

            if hasattr(self, 'world_origin_text_actor_left'):
                try:
                    self.vtk_renderer_left.renderer.RemoveActor2D(self.world_origin_text_actor_left)
                except:
                    pass
                self.world_origin_text_actor_left = None

            print("DEBUG: Cleared existing TOP text actors for recreation")

        # For BOTTOM viewer
        if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right and self.vtk_renderer_right.renderer:
            # Force recreation of local and world origin text actors
            if hasattr(self, 'local_origin_text_actor_right'):
                try:
                    self.vtk_renderer_right.renderer.RemoveActor2D(self.local_origin_text_actor_right)
                except:
                    pass
                self.local_origin_text_actor_right = None

            if hasattr(self, 'world_origin_text_actor_right'):
                try:
                    self.vtk_renderer_right.renderer.RemoveActor2D(self.world_origin_text_actor_right)
                except:
                    pass
                self.world_origin_text_actor_right = None

            print("DEBUG: Cleared existing BOTTOM text actors for recreation")

    def on_mouse_interaction_left(self, obj, event):
        """Handle mouse interaction in TOP viewer - UNIFIED MOUSE ROTATION"""
        print(f"🖱️ UNIFIED MOUSE: {event} detected in TOP viewer")

        # Set active viewer to top and handle mouse rotation
        self.active_viewer = "top"
        self._handle_unified_mouse_rotation()

    def on_mouse_interaction_right(self, obj, event):
        """Handle mouse interaction in BOTTOM viewer - UNIFIED MOUSE ROTATION"""
        print(f"🖱️ UNIFIED MOUSE: {event} detected in BOTTOM viewer")

        # Set active viewer to bottom and handle mouse rotation
        self.active_viewer = "bottom"
        self._handle_unified_mouse_rotation()

    def _handle_unified_mouse_rotation(self):
        """UNIFIED METHOD: Handle mouse rotation for active viewer"""
        print(f"🖱️ UNIFIED: Processing mouse rotation for {self.active_viewer} viewer")

        # Get the active renderer
        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
        else:
            renderer = self.vtk_renderer_right

        if not renderer or not renderer.renderer:
            return

        # Get camera orientation (mouse rotation affects camera)
        camera = renderer.renderer.GetActiveCamera()
        if camera:
            orientation = camera.GetOrientation()

            # Update rotation values directly (mouse controls camera, not actors)
            if self.active_viewer == "top":
                self.current_rot_left = {
                    'x': orientation[0],
                    'y': orientation[1],
                    'z': orientation[2]
                }
                print(f"   Updated TOP rotation: {self.current_rot_left}")
            else:
                self.current_rot_right = {
                    'x': orientation[0],
                    'y': orientation[1],
                    'z': orientation[2]
                }
                print(f"   Updated BOTTOM rotation: {self.current_rot_right}")

            # Update cursor position
            if self.active_viewer == "top":
                self._update_cursor_position_left()
            else:
                self._update_cursor_position_right()

            # Update display using unified method
            self._update_unified_display()

    def on_mouse_move_left(self, obj, event):
        """Handle continuous mouse movement in TOP viewer for cursor tracking"""
        # Update cursor position during mouse movement (not just interaction)
        self._update_cursor_position_left()
        # Update text overlay to show new cursor position
        self.update_text_overlays()

    def _update_cursor_position_left(self):
        """Update cursor position for TOP viewer using VTK renderer interactor"""
        try:
            # Use the renderer's interactor instead of widget's interactor
            if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left:
                if hasattr(self.vtk_renderer_left, 'interactor') and self.vtk_renderer_left.interactor:
                    interactor = self.vtk_renderer_left.interactor

                    # Get mouse position
                    x, y = interactor.GetEventPosition()

                    # Convert to world coordinates using VTK picker
                    import vtk
                    picker = vtk.vtkWorldPointPicker()
                    renderer = self.vtk_renderer_left.renderer
                    if renderer:
                        picker.Pick(x, y, 0, renderer)
                        world_pos = picker.GetPickPosition()

                        # Update cursor position
                        self.cursor_pos_left = {'x': world_pos[0], 'y': world_pos[1], 'z': world_pos[2]}
                        print(f"TARGET TOP cursor updated: X={world_pos[0]:.3f}, Y={world_pos[1]:.3f}, Z={world_pos[2]:.3f}")

        except Exception as e:
            print(f"DEBUG Error updating TOP cursor position: {e}")



    def on_mouse_interaction_right(self, obj, event):
        """Handle mouse interaction in BOTTOM viewer"""
        print(f"?? BOTTOM MOUSE INTERACTION EVENT FIRED! Event: {event}")

        # Calculate rotation from camera transformation (TrackballCamera style)
        if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right.renderer:
            camera = self.vtk_renderer_right.renderer.GetActiveCamera()
            if camera:
                # Get camera orientation as rotation values
                orientation = camera.GetOrientation()
                # Update current rotation to show camera rotation
                self.current_rot_right = {
                    'x': orientation[0],
                    'y': orientation[1],
                    'z': orientation[2]
                }
                print(f"DEBUG BOTTOM camera rotation captured - X={orientation[0]:.1f}deg Y={orientation[1]:.1f}deg Z={orientation[2]:.1f}deg")
                print(f"DEBUG Updated current_rot_right: {self.current_rot_right}")

                # Update origin position based on camera rotation
                self._update_origin_from_camera_rotation("bottom", camera)

        # Update cursor position during mouse interaction
        self._update_cursor_position_right()

        # Update text overlay when mouse interaction occurs
        self.update_text_overlays()
        print("DEBUG: BOTTOM mouse interaction - text updated")

    def _update_origin_from_camera_rotation(self, viewer, camera):
        """Update origin position AND direction vectors from rotated green sphere actor (for mouse interactions)"""
        try:
            print(f"DEBUG MOUSE ORIGIN UPDATE: Updating ALL 9 numbers for {viewer} viewer from green sphere actor")

            # Get the green sphere actor transformation (the model rotates, not camera)
            if viewer == "top":
                if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left:
                    # Find the green sphere actor
                    green_sphere_actor = None
                    actors = self.vtk_renderer_left.renderer.GetActors()
                    actors.InitTraversal()
                    for i in range(actors.GetNumberOfItems()):
                        actor = actors.GetNextActor()
                        if hasattr(actor, 'GetProperty'):
                            prop = actor.GetProperty()
                            color = prop.GetColor()
                            # Look for green sphere (green color)
                            if abs(color[1] - 1.0) < 0.1 and color[0] < 0.1 and color[2] < 0.1:
                                green_sphere_actor = actor
                                break

                    if green_sphere_actor:
                        print(f"DEBUG MOUSE ORIGIN UPDATE: Found TOP green sphere actor")
                        # Get the actor's transformation matrix
                        transform = green_sphere_actor.GetUserTransform()
                        if transform:
                            # Update position from actor transform
                            self._update_origin_position_from_actor_transform("top", transform)
                            # Update direction vectors from actor transform
                            self._update_direction_vectors_from_actor_transform("top", transform)
                        else:
                            print("DEBUG MOUSE ORIGIN UPDATE: No transform found on TOP green sphere actor")
                    else:
                        print("DEBUG MOUSE ORIGIN UPDATE: Green sphere actor not found for TOP viewer")
            else:
                if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
                    # Find the green sphere actor
                    green_sphere_actor = None
                    actors = self.vtk_renderer_right.renderer.GetActors()
                    actors.InitTraversal()
                    for i in range(actors.GetNumberOfItems()):
                        actor = actors.GetNextActor()
                        if hasattr(actor, 'GetProperty'):
                            prop = actor.GetProperty()
                            color = prop.GetColor()
                            # Look for green sphere (green color)
                            if abs(color[1] - 1.0) < 0.1 and color[0] < 0.1 and color[2] < 0.1:
                                green_sphere_actor = actor
                                break

                    if green_sphere_actor:
                        print(f"DEBUG MOUSE ORIGIN UPDATE: Found BOTTOM green sphere actor")
                        # Get the actor's transformation matrix
                        transform = green_sphere_actor.GetUserTransform()
                        if transform:
                            # Update position from actor transform
                            self._update_origin_position_from_actor_transform("bottom", transform)
                            # Update direction vectors from actor transform
                            self._update_direction_vectors_from_actor_transform("bottom", transform)
                        else:
                            print("DEBUG MOUSE ORIGIN UPDATE: No transform found on BOTTOM green sphere actor")
                    else:
                        print("DEBUG MOUSE ORIGIN UPDATE: Green sphere actor not found for BOTTOM viewer")

        except Exception as e:
            print(f"DEBUG MOUSE ORIGIN UPDATE ERROR: {e}")
            # Don't let origin update errors break mouse interaction
            pass

    def _update_origin_position_from_actor_transform(self, viewer, transform):
        """Update origin position from green sphere actor transform"""
        try:
            # Get the transformation matrix
            matrix = transform.GetMatrix()

            # Get the translation (position) from the matrix
            position = [matrix.GetElement(0, 3), matrix.GetElement(1, 3), matrix.GetElement(2, 3)]

            if viewer == "top":
                # FIXED: Store as dictionary, not list, to match expected format
                self.current_pos_left = {'x': position[0], 'y': position[1], 'z': position[2]}
                print(f"DEBUG ACTOR POSITION UPDATE: TOP position updated to {self.current_pos_left}")
            else:
                # FIXED: Store as dictionary, not list, to match expected format
                self.current_pos_right = {'x': position[0], 'y': position[1], 'z': position[2]}
                print(f"DEBUG ACTOR POSITION UPDATE: BOTTOM position updated to {self.current_pos_right}")

        except Exception as e:
            print(f"DEBUG ACTOR POSITION UPDATE ERROR: {e}")

    def _update_direction_vectors_from_actor_transform(self, viewer, transform):
        """Update Direction and REF. Direction vectors from green sphere actor transform"""
        try:
            print(f"DEBUG ACTOR DIRECTION UPDATE: Updating direction vectors for {viewer} from actor transform")

            # Get the transformation matrix
            matrix = transform.GetMatrix()

            # Original STEP file direction vectors (Z direction and X direction)
            if viewer == "top":
                if hasattr(self, 'orig_z_direction_left') and hasattr(self, 'orig_x_direction_left'):
                    orig_z = self.orig_z_direction_left
                    orig_x = self.orig_x_direction_left

                    # Transform the original direction vectors
                    z_transformed = [0, 0, 0, 0]
                    x_transformed = [0, 0, 0, 0]

                    # Apply transformation to Z direction
                    matrix.MultiplyPoint([orig_z[0], orig_z[1], orig_z[2], 1.0], z_transformed)
                    # Apply transformation to X direction
                    matrix.MultiplyPoint([orig_x[0], orig_x[1], orig_x[2], 1.0], x_transformed)

                    # Update current direction vectors
                    self.current_z_direction_left = z_transformed[:3]
                    self.current_x_direction_left = x_transformed[:3]

                    print(f"DEBUG ACTOR DIRECTION UPDATE: TOP Z direction: {orig_z} -> {self.current_z_direction_left}")
                    print(f"DEBUG ACTOR DIRECTION UPDATE: TOP X direction: {orig_x} -> {self.current_x_direction_left}")
            else:
                if hasattr(self, 'orig_z_direction_right') and hasattr(self, 'orig_x_direction_right'):
                    orig_z = self.orig_z_direction_right
                    orig_x = self.orig_x_direction_right

                    # Transform the original direction vectors
                    z_transformed = [0, 0, 0, 0]
                    x_transformed = [0, 0, 0, 0]

                    # Apply transformation to Z direction
                    matrix.MultiplyPoint([orig_z[0], orig_z[1], orig_z[2], 1.0], z_transformed)
                    # Apply transformation to X direction
                    matrix.MultiplyPoint([orig_x[0], orig_x[1], orig_x[2], 1.0], x_transformed)

                    # Update current direction vectors
                    self.current_z_direction_right = z_transformed[:3]
                    self.current_x_direction_right = x_transformed[:3]

                    print(f"DEBUG ACTOR DIRECTION UPDATE: BOTTOM Z direction: {orig_z} -> {self.current_z_direction_right}")
                    print(f"DEBUG ACTOR DIRECTION UPDATE: BOTTOM X direction: {orig_x} -> {self.current_x_direction_right}")

        except Exception as e:
            print(f"DEBUG ACTOR DIRECTION UPDATE ERROR: {e}")
            import traceback
            traceback.print_exc()

    def on_mouse_move_right(self, obj, event):
        """Handle continuous mouse movement in BOTTOM viewer for cursor tracking"""
        # Update cursor position during mouse movement (not just interaction)
        self._update_cursor_position_right()
        # Update text overlay to show new cursor position
        self.update_text_overlays()

    def _update_cursor_position_right(self):
        """Update cursor position for BOTTOM viewer using VTK renderer interactor"""
        try:
            # Use the renderer's interactor instead of widget's interactor
            if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
                if hasattr(self.vtk_renderer_right, 'interactor') and self.vtk_renderer_right.interactor:
                    interactor = self.vtk_renderer_right.interactor

                    # Get mouse position
                    x, y = interactor.GetEventPosition()

                    # Convert to world coordinates using VTK picker
                    import vtk
                    picker = vtk.vtkWorldPointPicker()
                    renderer = self.vtk_renderer_right.renderer
                    if renderer:
                        picker.Pick(x, y, 0, renderer)
                        world_pos = picker.GetPickPosition()

                        # Update cursor position
                        self.cursor_pos_right = {'x': world_pos[0], 'y': world_pos[1], 'z': world_pos[2]}
                        print(f"TARGET BOTTOM cursor updated: X={world_pos[0]:.3f}, Y={world_pos[1]:.3f}, Z={world_pos[2]:.3f}")

        except Exception as e:
            print(f"DEBUG Error updating BOTTOM cursor position: {e}")

    def init_ui(self):
        # Central widget with splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QHBoxLayout(central_widget)

        # Create splitter for dual view
        splitter = QSplitter(Qt.Vertical)

        # Top viewer container
        top_container = QWidget()
        self.top_container = top_container  # Store reference for overlay functionality
        top_layout = QVBoxLayout(top_container)

        # Camera controls above TOP viewer
        camera_widget = QWidget()
        camera_layout = QHBoxLayout(camera_widget)
        camera_layout.setContentsMargins(5, 2, 5, 2)

        # Camera view buttons - FreeCAD style with custom 3D wireframe icons

        def create_iso_icon(view_type):
            """Create FreeCAD-style view icons - exactly 5 buttons like the image"""
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QPixmap, QPainter, QPen, QIcon, QColor

            # Create 24x24 icons to match FreeCAD style
            pixmap = QPixmap(24, 24)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # Use dark teal/blue-green color like FreeCAD
            pen = QPen(QColor(0, 128, 128), 1.2)  # Dark teal, clean lines
            painter.setPen(pen)

            if view_type == "axonometric":  # Isometric/Axonometric view (first icon)
                # Draw 3D cube in isometric projection - FreeCAD style
                # Front face
                painter.drawLine(4, 18, 12, 18)   # bottom
                painter.drawLine(4, 12, 12, 12)   # top
                painter.drawLine(4, 12, 4, 18)    # left
                painter.drawLine(12, 12, 12, 18)  # right
                # Back face (offset)
                painter.drawLine(8, 14, 16, 14)   # bottom
                painter.drawLine(8, 8, 16, 8)     # top
                painter.drawLine(8, 8, 8, 14)     # left
                painter.drawLine(16, 8, 16, 14)   # right
                # Connecting lines
                painter.drawLine(4, 12, 8, 8)     # top-left
                painter.drawLine(12, 12, 16, 8)   # top-right
                painter.drawLine(4, 18, 8, 14)    # bottom-left
                painter.drawLine(12, 18, 16, 14)  # bottom-right

            elif view_type == "front":  # Front view (second icon)
                # Rectangle with center cross
                painter.drawRect(6, 4, 12, 16)
                painter.drawLine(6, 12, 18, 12)   # horizontal center
                painter.drawLine(12, 4, 12, 20)   # vertical center

            elif view_type == "top":  # Top view (third icon)
                # Square with diagonal cross
                painter.drawRect(6, 6, 12, 12)
                painter.drawLine(6, 6, 18, 18)    # diagonal 1
                painter.drawLine(18, 6, 6, 18)    # diagonal 2

            elif view_type == "right":  # Right/Side view (fourth icon)
                # Tall rectangle with center line
                painter.drawRect(8, 4, 8, 16)
                painter.drawLine(8, 12, 16, 12)   # horizontal center

            elif view_type == "left":  # Left/Side view (fifth icon)
                # Tall rectangle with center line (similar to right but mirrored)
                painter.drawRect(8, 4, 8, 16)
                painter.drawLine(8, 12, 16, 12)   # horizontal center

            elif view_type == "bottom":  # Bottom view (sixth icon)
                # Square with different cross pattern than top
                painter.drawRect(6, 6, 12, 12)
                painter.drawLine(6, 12, 18, 12)   # horizontal center
                painter.drawLine(12, 6, 12, 18)   # vertical center
                # Add corner dots to distinguish from top view
                painter.drawEllipse(7, 7, 2, 2)   # top-left dot
                painter.drawEllipse(15, 7, 2, 2)  # top-right dot
                painter.drawEllipse(7, 15, 2, 2)  # bottom-left dot
                painter.drawEllipse(15, 15, 2, 2) # bottom-right dot

            elif view_type == "perspective":  # Perspective view (seventh icon)
                # 3D cube with perspective lines
                # Front face (smaller)
                painter.drawLine(6, 16, 10, 16)   # bottom
                painter.drawLine(6, 12, 10, 12)   # top
                painter.drawLine(6, 12, 6, 16)    # left
                painter.drawLine(10, 12, 10, 16)  # right
                # Back face (larger, offset)
                painter.drawLine(10, 18, 18, 18)  # bottom
                painter.drawLine(10, 8, 18, 8)    # top
                painter.drawLine(10, 8, 10, 18)   # left
                painter.drawLine(18, 8, 18, 18)   # right
                # Perspective connecting lines
                painter.drawLine(6, 12, 10, 8)    # top-left
                painter.drawLine(10, 12, 18, 8)   # top-right
                painter.drawLine(6, 16, 10, 18)   # bottom-left
                painter.drawLine(10, 16, 18, 18)  # bottom-right

            painter.end()
            return QIcon(pixmap)

        def create_freecad_view_icon(view_type):
            """Download ACTUAL FreeCAD view icons from the GitHub repository"""
            try:
                import urllib.request
                from PyQt5.QtCore import Qt
                from PyQt5.QtGui import QPixmap, QIcon

                # Map view types to ACTUAL FreeCAD view icons from GitHub (PNG files)
                base_url = "https://raw.githubusercontent.com/MisterMakerNL/Linkstage_icons/main/MM_Freecad_original_colors/view/"
                icon_urls = {
                    "axonometric": f"{base_url}view-axonometric.png",
                    "front": f"{base_url}view-front.png",
                    "rear": f"{base_url}view-rear.png",
                    "top": f"{base_url}view-top.png",
                    "bottom": f"{base_url}view-bottom.png",
                    "left": f"{base_url}view-left.png",
                    "right": f"{base_url}view-right.png"
                }

                url = icon_urls.get(view_type, icon_urls["axonometric"])
                print(f"TARGET Downloading REAL FreeCAD icon: {view_type} from {url}")

                # Download the PNG icon
                with urllib.request.urlopen(url) as response:
                    image_data = response.read()

                # Create QPixmap from PNG data
                pixmap = QPixmap()
                pixmap.loadFromData(image_data)

                # Scale to proper size while maintaining quality
                if not pixmap.isNull():
                    pixmap = pixmap.scaled(32, 32, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    print(f"OK Successfully loaded REAL FreeCAD icon: {view_type}")
                    return QIcon(pixmap)
                else:
                    raise Exception("Failed to load PNG data")

            except Exception as e:
                print(f"FAIL Failed to download FreeCAD icon for {view_type}: {e}")
                # Fallback to simple text
                from PyQt5.QtCore import Qt
                from PyQt5.QtGui import QPixmap, QPainter, QFont, QIcon, QColor

                pixmap = QPixmap(32, 32)
                pixmap.fill(Qt.transparent)
                painter = QPainter(pixmap)
                painter.setPen(QColor(60, 60, 60))
                font = QFont()
                font.setPointSize(12)
                painter.setFont(font)

                # Simple text fallback
                text_map = {
                    "axonometric": "ISO",
                    "front": "F",
                    "top": "T",
                    "right": "R",
                    "left": "L",
                    "bottom": "B",
                    "perspective": "P"
                }

                painter.drawText(pixmap.rect(), Qt.AlignCenter, text_map.get(view_type, "?"))
                painter.end()
                return QIcon(pixmap)

        # EXACT FreeCAD button style - larger for better icon visibility
        freecad_style = """
            QPushButton {
                background: #f0f0f0;
                border: 1px solid #c0c0c0;
                padding: 4px;
                margin: 1px;
                min-width: 32px;
                min-height: 32px;
                max-width: 32px;
                max-height: 32px;
            }
            QPushButton:hover {
                background: #e0e0e0;
                border: 1px solid #a0a0a0;
            }
            QPushButton:pressed {
                background: #d0d0d0;
                border: 1px solid #808080;
            }
        """

        # Create 7 FreeCAD view buttons in correct order: Front, Rear, Top, Bottom, Left, Right, Axonometric
        btn_view_front = QPushButton()
        btn_view_front.setIcon(create_freecad_view_icon("front"))
        btn_view_front.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_front.clicked.connect(lambda: self.set_camera_view("front"))
        btn_view_front.setStyleSheet(freecad_style)
        btn_view_front.setToolTip("Front View")
        camera_layout.addWidget(btn_view_front)

        btn_view_rear = QPushButton()
        btn_view_rear.setIcon(create_freecad_view_icon("rear"))
        btn_view_rear.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_rear.clicked.connect(lambda: self.set_camera_view("rear"))
        btn_view_rear.setStyleSheet(freecad_style)
        btn_view_rear.setToolTip("Rear View")
        camera_layout.addWidget(btn_view_rear)

        btn_view_top = QPushButton()
        btn_view_top.setIcon(create_freecad_view_icon("top"))
        btn_view_top.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_top.clicked.connect(lambda: self.set_camera_view("top"))
        btn_view_top.setStyleSheet(freecad_style)
        btn_view_top.setToolTip("Top View")
        camera_layout.addWidget(btn_view_top)

        btn_view_bottom = QPushButton()
        btn_view_bottom.setIcon(create_freecad_view_icon("bottom"))
        btn_view_bottom.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_bottom.clicked.connect(lambda: self.set_camera_view("bottom"))
        btn_view_bottom.setStyleSheet(freecad_style)
        btn_view_bottom.setToolTip("Bottom View")
        camera_layout.addWidget(btn_view_bottom)

        btn_view_left = QPushButton()
        btn_view_left.setIcon(create_freecad_view_icon("left"))
        btn_view_left.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_left.clicked.connect(lambda: self.set_camera_view("left"))
        btn_view_left.setStyleSheet(freecad_style)
        btn_view_left.setToolTip("Left View")
        camera_layout.addWidget(btn_view_left)

        btn_view_right = QPushButton()
        btn_view_right.setIcon(create_freecad_view_icon("right"))
        btn_view_right.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_right.clicked.connect(lambda: self.set_camera_view("right"))
        btn_view_right.setStyleSheet(freecad_style)
        btn_view_right.setToolTip("Right View")
        camera_layout.addWidget(btn_view_right)

        btn_view_axonometric = QPushButton()
        btn_view_axonometric.setIcon(create_freecad_view_icon("axonometric"))
        btn_view_axonometric.setIconSize(QSize(28, 28))  # Larger icon size
        btn_view_axonometric.clicked.connect(lambda: self.set_camera_view("iso_front_right_top"))
        btn_view_axonometric.setStyleSheet(freecad_style)
        btn_view_axonometric.setToolTip("Axonometric View")
        camera_layout.addWidget(btn_view_axonometric)

        camera_layout.addStretch()  # Push buttons to left
        top_layout.addWidget(camera_widget)

        # Top file label
        self.top_file_label = QLabel("TOP VIEWER - No file loaded")
        self.top_file_label.setStyleSheet("color: blue; font-size: 12px; padding: 5px; background-color: lightgray;")
        top_layout.addWidget(self.top_file_label)

        # Top VTK widget
        self.vtk_widget_left = self.vtk_renderer_left.vtk_widget
        if self.vtk_widget_left:
            print(f"Adding TOP VTK widget: {type(self.vtk_widget_left)}")
            # Set minimum size and size policy for proper resizing
            self.vtk_widget_left.setMinimumSize(400, 300)
            from PyQt5.QtWidgets import QSizePolicy
            self.vtk_widget_left.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            top_layout.addWidget(self.vtk_widget_left)

            # Connect cursor position callback with multiple attempts
            self._connect_cursor_callback_left()
        else:
            print("ERROR: TOP VTK widget is None")

        # Bottom viewer container
        bottom_container = QWidget()
        self.bottom_container = bottom_container  # Store reference for overlay functionality
        bottom_layout = QVBoxLayout(bottom_container)

        # Bottom file label
        self.bottom_file_label = QLabel("BOTTOM VIEWER - No file loaded")
        self.bottom_file_label.setStyleSheet("color: blue; font-size: 12px; padding: 5px; background-color: lightgray;")
        bottom_layout.addWidget(self.bottom_file_label)

        # Bottom VTK widget
        self.vtk_widget_right = self.vtk_renderer_right.vtk_widget
        if self.vtk_widget_right:
            print(f"Adding BOTTOM VTK widget: {type(self.vtk_widget_right)}")
            # Set minimum size and size policy for proper resizing
            self.vtk_widget_right.setMinimumSize(400, 300)
            self.vtk_widget_right.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            bottom_layout.addWidget(self.vtk_widget_right)

            # Connect cursor position callback for bottom viewer
            self._connect_cursor_callback_right()
        else:
            print("ERROR: BOTTOM VTK widget is None")

        # Add containers to splitter
        splitter.addWidget(top_container)
        splitter.addWidget(bottom_container)
        splitter.setSizes([400, 400])

        # Set splitter to expand properly
        from PyQt5.QtWidgets import QSizePolicy
        splitter.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        main_layout.addWidget(splitter)

        # Create tool dock
        dock = create_tool_dock(self)
        self.addDockWidget(Qt.LeftDockWidgetArea, dock)

        # Set initial active viewer
        self.update_viewer_highlights()

        # Hide the status bar (toolbar removal)
        self.statusBar().hide()

    def set_active_viewer(self, viewer):
        """Set the active viewer (top or bottom)"""
        self.active_viewer = viewer
        self.update_viewer_highlights()
        self.update_transform_display()
        self.statusBar().showMessage(f"Active viewer: {viewer.upper()}")

    def set_camera_view(self, view_type):
        """Set camera to standard views: top, front, side, isometric"""
        print(f"TARGET Setting camera view: {view_type} for {self.active_viewer} viewer")

        # In overlay mode, update BOTH viewers and overlay
        if hasattr(self, 'overlay_mode') and self.overlay_mode:
            print(f"TARGET Overlay mode active - updating BOTH viewers and overlay to {view_type}")

            # Update TOP viewer camera
            if self.vtk_renderer_left and self.vtk_renderer_left.renderer:
                self._set_single_camera_view(self.vtk_renderer_left, view_type)

            # Update BOTTOM viewer camera
            if self.vtk_renderer_right and self.vtk_renderer_right.renderer:
                self._set_single_camera_view(self.vtk_renderer_right, view_type)

            # Update overlay camera
            if hasattr(self, 'overlay_renderer') and self.overlay_renderer:
                overlay_camera = self.overlay_renderer.GetActiveCamera()
                if overlay_camera:
                    # Use the same camera settings as the main viewers
                    main_camera = self.vtk_renderer_left.renderer.GetActiveCamera()
                    overlay_camera.DeepCopy(main_camera)
                    print(f"OK Overlay camera also set to {view_type} view")

            # Force render all views
            if hasattr(self, 'vtk_widget_left'):
                self.vtk_widget_left.GetRenderWindow().Render()
            if hasattr(self, 'vtk_widget_right'):
                self.vtk_widget_right.GetRenderWindow().Render()

        else:
            # Normal mode - only update active viewer
            if self.active_viewer == "top":
                vtk_renderer = self.vtk_renderer_left
            else:
                vtk_renderer = self.vtk_renderer_right

            if not vtk_renderer or not vtk_renderer.renderer:
                print(f"ERROR: No renderer available for {self.active_viewer} viewer")
                return

            self._set_single_camera_view(vtk_renderer, view_type)

        print(f"OK Camera set to {view_type} view for {self.active_viewer} viewer")
        self.statusBar().showMessage(f"{self.active_viewer.upper()}: Camera set to {view_type} view")

    def _set_single_camera_view(self, vtk_renderer, view_type):
        """Set camera view for a single VTK renderer"""
        camera = vtk_renderer.renderer.GetActiveCamera()
        if not camera:
            print(f"ERROR: No camera available for renderer")
            return

        # Get model bounds for positioning camera
        bounds = vtk_renderer.renderer.ComputeVisiblePropBounds()
        if bounds[0] > bounds[1]:  # No visible props
            print("WARNING: No visible objects to focus camera on")
            bounds = [-5, 5, -5, 5, 0, 5]  # Default bounds

        # Calculate center and distance
        center_x = (bounds[0] + bounds[1]) / 2.0
        center_y = (bounds[2] + bounds[3]) / 2.0
        center_z = (bounds[4] + bounds[5]) / 2.0

        # Calculate distance based on model size
        max_dim = max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])
        distance = max_dim * 2.0

        # FIXED: Use part origin (0,0,0) as focal point, not model center
        # Standard CAD coordinate system: X=left/right, Y=front/back, Z=up/down
        focal_point = (0, 0, 0)  # Part origin, not model center

        # Set camera position and orientation based on view type
        if view_type == "top":
            # Top view: camera above, looking down Z-axis at XY plane
            camera.SetPosition(0, 0, distance)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 1, 0)  # Y-axis up (front/back)

        elif view_type == "bottom":
            # Bottom view: camera below, looking up Z-axis at XY plane
            camera.SetPosition(0, 0, -distance)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, -1, 0)  # Y-axis down (inverted)

        elif view_type == "front":
            # Front view: camera in front, looking along Y-axis at XZ plane
            camera.SetPosition(0, -distance, 0)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "rear":
            # Rear view: camera behind, looking along -Y-axis at XZ plane
            camera.SetPosition(0, distance, 0)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "right":
            # Right view: camera to the right, looking along -X-axis at YZ plane
            camera.SetPosition(distance, 0, 0)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "left":
            # Left view: camera to the left, looking along X-axis at YZ plane
            camera.SetPosition(-distance, 0, 0)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "side":
            # Legacy side view - map to right view
            camera.SetPosition(distance, 0, 0)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 1, 0)  # Y-axis up

        elif view_type == "iso_front_right_top":
            # Front-Right-Top isometric view (like FreeCAD)
            iso_distance = distance * 0.8
            camera.SetPosition(center_x + iso_distance, center_y - iso_distance, center_z + iso_distance)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "iso_front_left_top":
            # Front-Left-Top isometric view
            iso_distance = distance * 0.8
            camera.SetPosition(center_x - iso_distance, center_y - iso_distance, center_z + iso_distance)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "iso_back_right_top":
            # Back-Right-Top isometric view
            iso_distance = distance * 0.8
            camera.SetPosition(center_x + iso_distance, center_y + iso_distance, center_z + iso_distance)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "iso_back_left_top":
            # Back-Left-Top isometric view
            iso_distance = distance * 0.8
            camera.SetPosition(center_x - iso_distance, center_y + iso_distance, center_z + iso_distance)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "left":
            # Left view: camera to the left, looking at XZ plane
            camera.SetPosition(center_x, center_y - distance, center_z)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        elif view_type == "bottom":
            # Bottom view: camera below, looking up
            camera.SetPosition(center_x, center_y, center_z - distance)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 1, 0)  # Y-axis up

        elif view_type == "isometric":
            # Default isometric view (for backward compatibility)
            iso_distance = distance * 0.8
            camera.SetPosition(center_x + iso_distance, center_y - iso_distance, center_z + iso_distance)
            camera.SetFocalPoint(center_x, center_y, center_z)
            camera.SetViewUp(0, 0, 1)  # Z-axis up

        # Reset camera to fit all objects in view
        vtk_renderer.renderer.ResetCamera()

        # Render the view
        if vtk_renderer.render_window:
            vtk_renderer.render_window.Render()

        # Also update overlay camera if overlay is active
        if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_renderer'):
            try:
                if self.overlay_renderer:
                    overlay_camera = self.overlay_renderer.GetActiveCamera()
                    # Copy the camera settings to overlay
                    overlay_camera.DeepCopy(camera)
                    # Force render the overlay
                    if hasattr(self, 'vtk_widget_left'):
                        self.vtk_widget_left.GetRenderWindow().Render()
                    print(f"OK Overlay camera also set to {view_type} view")
            except Exception as e:
                print(f"DEBUG Could not update overlay camera: {e}")

        print(f"OK Camera set to {view_type} view for {self.active_viewer} viewer")
        self.statusBar().showMessage(f"{self.active_viewer.upper()}: Camera set to {view_type} view")

    def create_origin_overlay(self):
        """Create origin overlay for the active viewer"""
        print(f"TARGET Creating origin overlay for {self.active_viewer} viewer")

        # Get the active VTK renderer
        if self.active_viewer == "top":
            vtk_renderer = self.vtk_renderer_left
        else:
            vtk_renderer = self.vtk_renderer_right

        if not vtk_renderer:
            print(f"ERROR: No renderer available for {self.active_viewer} viewer")
            self.statusBar().showMessage(f"ERROR: No renderer for {self.active_viewer} viewer")
            return

        # Create the origin overlay
        success = vtk_renderer.create_origin_overlay()

        if success:
            print(f"OK Origin overlay created for {self.active_viewer} viewer")
            self.statusBar().showMessage(f"{self.active_viewer.upper()}: Origin overlay created")
        else:
            print(f"FAIL Failed to create origin overlay for {self.active_viewer} viewer")
            self.statusBar().showMessage(f"{self.active_viewer.upper()}: Origin overlay creation failed")

    def toggle_origin_overlay(self):
        """Toggle origin overlay visibility for the active viewer"""
        print(f"TARGET Toggling origin overlay for {self.active_viewer} viewer")

        # Get the active VTK renderer
        if self.active_viewer == "top":
            vtk_renderer = self.vtk_renderer_left
        else:
            vtk_renderer = self.vtk_renderer_right

        if not vtk_renderer:
            print(f"ERROR: No renderer available for {self.active_viewer} viewer")
            self.statusBar().showMessage(f"ERROR: No renderer for {self.active_viewer} viewer")
            return

        # Toggle the origin overlay
        visible = vtk_renderer.toggle_origin_overlay()

        status = "visible" if visible else "hidden"
        print(f"OK Origin overlay {status} for {self.active_viewer} viewer")
        self.statusBar().showMessage(f"{self.active_viewer.upper()}: Origin overlay {status}")

    def toggle_viewer_overlay(self):
        """Toggle overlay mode - overlay bottom viewer on top viewer with transparency"""
        try:
            print("TARGET OVERLAY BUTTON CLICKED - Starting diagnostic...")
            print(f"   Current overlay_mode: {self.overlay_mode}")
        except Exception as print_error:
            print(f"FAIL ERROR in initial diagnostic print: {print_error}")

        try:
            if not self.overlay_mode:
                # Enable overlay mode
                self._enable_overlay_mode()
            else:
                # Disable overlay mode
                self._disable_overlay_mode()

        except Exception as e:
            print(f"FAIL Error toggling viewer overlay: {e}")
            self.statusBar().showMessage(f"Error toggling overlay: {e}")

    def _enable_overlay_mode(self):
        """Enable overlay mode - show bottom viewer overlaid on top viewer"""
        try:
            print("TARGET Enabling overlay mode...")

            # Create overlay widget if it doesn't exist
            if not self.overlay_widget:
                self._create_overlay_widget()

            # Hide the bottom viewer from splitter
            if hasattr(self, 'bottom_container'):
                self.bottom_container.hide()

            # Show overlay widget
            if self.overlay_widget:
                self.overlay_widget.show()
                self.overlay_widget.raise_()  # Bring to front

            # Update state and button text
            self.overlay_mode = True
            if hasattr(self, 'overlay_toggle_btn'):
                self.overlay_toggle_btn.setText("Exit Overlay Mode")

            print("OK Overlay mode enabled")
            self.statusBar().showMessage("Overlay mode: BOTTOM viewer overlaid on TOP viewer")

        except Exception as e:
            print(f"FAIL Error enabling overlay mode: {e}")

    def _disable_overlay_mode(self):
        """Disable overlay mode - return to normal dual view"""
        try:
            print("TARGET Disabling overlay mode...")

            # Remove overlay renderer from TOP render window
            if hasattr(self, 'overlay_renderer') and hasattr(self, 'vtk_widget_left'):
                top_render_window = self.vtk_widget_left.GetRenderWindow()
                top_render_window.RemoveRenderer(self.overlay_renderer)
                top_render_window.SetNumberOfLayers(1)  # Back to single layer
                top_render_window.Render()
                print("DEBUG DEBUG: Overlay renderer removed from TOP render window")

            # Clean up overlay references
            if hasattr(self, 'overlay_renderer'):
                delattr(self, 'overlay_renderer')
            if hasattr(self, 'overlay_top_actors'):
                delattr(self, 'overlay_top_actors')
            if hasattr(self, 'overlay_bottom_actors'):
                delattr(self, 'overlay_bottom_actors')
            # IMPORTANT: Clear overlay_widget so it gets recreated next time
            self.overlay_widget = None
            print("DEBUG DEBUG: Overlay references cleaned up")

            # Show the bottom viewer in splitter
            if hasattr(self, 'bottom_container'):
                self.bottom_container.show()

            # Re-enable bounding boxes when returning to normal dual view
            if hasattr(self.vtk_renderer_left, 'toggle_bounding_box') and self.bbox_visible_left:
                self.vtk_renderer_left.toggle_bounding_box(True)
                print("DEBUG DEBUG: Re-enabled TOP bounding box after overlay mode")
            if hasattr(self.vtk_renderer_right, 'toggle_bounding_box') and self.bbox_visible_right:
                self.vtk_renderer_right.toggle_bounding_box(True)
                print("DEBUG DEBUG: Re-enabled BOTTOM bounding box after overlay mode")

            # Update state and button text
            self.overlay_mode = False
            if hasattr(self, 'overlay_toggle_btn'):
                self.overlay_toggle_btn.setText("Overlay Bottom on Top")

            print("OK Overlay mode disabled")
            self.statusBar().showMessage("Normal dual view mode restored")

        except Exception as e:
            print(f"FAIL Error disabling overlay mode: {e}")

    def _create_overlay_widget(self):
        """Create overlay using multiple renderers in the same render window"""
        try:
            print("DEBUG Creating overlay using VTK multiple renderers...")
            import vtk

            # CORRECT APPROACH: Use existing TOP render window with multiple renderers
            if not hasattr(self, 'vtk_widget_left') or not self.vtk_widget_left:
                print("FAIL No TOP VTK widget found")
                print(f"DEBUG DEBUG: vtk_widget_left exists: {hasattr(self, 'vtk_widget_left')}")
                if hasattr(self, 'vtk_widget_left'):
                    print(f"DEBUG DEBUG: vtk_widget_left value: {self.vtk_widget_left}")
                return

            top_render_window = self.vtk_widget_left.GetRenderWindow()

            # Create overlay renderer for the same render window
            self.overlay_renderer = vtk.vtkRenderer()
            top_render_window.AddRenderer(self.overlay_renderer)

            # Set viewport to cover only the TOP viewer area (not full window)
            self.overlay_renderer.SetViewport(0.0, 0.0, 1.0, 1.0)
            self.overlay_renderer.SetLayer(1)

            # Set transparent background for overlay
            self.overlay_renderer.SetBackground(0.0, 0.0, 0.0)  # Black background
            self.overlay_renderer.SetBackgroundAlpha(0.0)  # Fully transparent

            top_render_window.SetNumberOfLayers(2)
            top_render_window.SetAlphaBitPlanes(1)  # Enable alpha blending
            print("DEBUG DEBUG: Overlay renderer added to TOP render window with transparent background")

            # Set overlay widget reference for compatibility
            self.overlay_widget = self.vtk_widget_left  # Use the TOP widget as overlay reference

            # Disable bounding boxes in both viewers during overlay mode to prevent red wireframes
            if hasattr(self.vtk_renderer_left, 'toggle_bounding_box'):
                self.vtk_renderer_left.toggle_bounding_box(False)
                print("DEBUG DEBUG: Disabled TOP bounding box for overlay mode")
            if hasattr(self.vtk_renderer_right, 'toggle_bounding_box'):
                self.vtk_renderer_right.toggle_bounding_box(False)
                print("DEBUG DEBUG: Disabled BOTTOM bounding box for overlay mode")

            # Debug function for actor properties
            def debug_actor(actor, label):
                print(f"[DEBUG] {label} actor: {actor}")
                print(f"[DEBUG] {label} bounds: {actor.GetBounds() if actor else 'None'}")
                print(f"[DEBUG] {label} position: {actor.GetPosition() if actor else 'None'}")
                print(f"[DEBUG] {label} orientation: {actor.GetOrientation() if actor else 'None'}")
                print(f"[DEBUG] {label} scale: {actor.GetScale() if actor else 'None'}")
                print(f"[DEBUG] {label} visibility: {actor.GetVisibility() if actor else 'None'}")
                print(f"[DEBUG] {label} opacity: {actor.GetProperty().GetOpacity() if actor else 'None'}")
                print(f"[DEBUG] {label} color: {actor.GetProperty().GetColor() if actor else 'None'}")

            # Copy STEP actors from BOTH viewers to show both models overlaid
            # Store overlay actors for later updates
            self.overlay_top_actors = []
            self.overlay_bottom_actors = []
            total_actors_copied = 0

            # DEBUG: Check what actors are available in each viewer
            print(f"DEBUG DEBUG: Checking available actors...")
            print(f"DEBUG DEBUG: TOP vtk_renderer_left exists: {hasattr(self, 'vtk_renderer_left')}")
            if hasattr(self, 'vtk_renderer_left'):
                print(f"DEBUG DEBUG: TOP step_actors exists: {hasattr(self.vtk_renderer_left, 'step_actors')}")
                print(f"DEBUG DEBUG: TOP step_actor exists: {hasattr(self.vtk_renderer_left, 'step_actor')}")
                if hasattr(self.vtk_renderer_left, 'step_actors'):
                    print(f"DEBUG DEBUG: TOP step_actors count: {len(self.vtk_renderer_left.step_actors) if self.vtk_renderer_left.step_actors else 0}")
            print(f"DEBUG DEBUG: BOTTOM vtk_renderer_right exists: {hasattr(self, 'vtk_renderer_right')}")
            if hasattr(self, 'vtk_renderer_right'):
                print(f"DEBUG DEBUG: BOTTOM step_actors exists: {hasattr(self.vtk_renderer_right, 'step_actors')}")
                print(f"DEBUG DEBUG: BOTTOM step_actor exists: {hasattr(self.vtk_renderer_right, 'step_actor')}")
                if hasattr(self.vtk_renderer_right, 'step_actors'):
                    print(f"DEBUG DEBUG: BOTTOM step_actors count: {len(self.vtk_renderer_right.step_actors) if self.vtk_renderer_right.step_actors else 0}")
                if hasattr(self.vtk_renderer_right, 'step_actor'):
                    print(f"DEBUG DEBUG: BOTTOM step_actor exists: {self.vtk_renderer_right.step_actor is not None}")

            # DON'T COPY TOP actors - they're already visible on Layer 0 (original renderer)
            print("DEBUG SKIPPING TOP actor copying - original TOP renderer is already Layer 0 background")
            print("DEBUG Layer 0: Original 16-pin from vtk_renderer_left.renderer")
            print("DEBUG Layer 1: Only 8-pin from BOTTOM renderer will be copied")
            # Also skip TOP single actor - already on Layer 0
            print("DEBUG SKIPPING TOP single-actor copying - also already on Layer 0")

            # BOTTOM actors (BLUE)
            if hasattr(self.vtk_renderer_right, 'step_actors') and self.vtk_renderer_right.step_actors:
                print(f"DEBUG Copying {len(self.vtk_renderer_right.step_actors)} BOTTOM STEP multi-actors to overlay...")
                for i, actor in enumerate(self.vtk_renderer_right.step_actors):
                    overlay_actor = vtk.vtkActor()
                    overlay_actor.SetMapper(actor.GetMapper())
                    overlay_actor.SetUserTransform(actor.GetUserTransform())
                    overlay_actor.SetPosition(actor.GetPosition())
                    overlay_actor.SetOrientation(actor.GetOrientation())
                    overlay_actor.SetScale(actor.GetScale())
                    overlay_actor.SetVisibility(1)
                    prop = overlay_actor.GetProperty()
                    # Keep original colors but make semi-transparent
                    original_color = actor.GetProperty().GetColor()
                    prop.SetColor(original_color[0], original_color[1], original_color[2])
                    prop.SetOpacity(0.5)  # Semi-transparent
                    prop.SetAmbient(0.3)
                    prop.SetDiffuse(0.7)
                    prop.SetSpecular(0.0)
                    color = prop.GetColor()
                    print(f"BLUE DEBUG: BOTTOM multi-actor {i} color kept original: {color}")
                    debug_actor(overlay_actor, f"BOTTOM multi-actor {i}")
                    self.overlay_renderer.AddActor(overlay_actor)
                    self.overlay_top_actors.append(overlay_actor)  # Store reference - BOTTOM goes to overlay top
                    total_actors_copied += 1
                print(f"OK Copied {len(self.vtk_renderer_right.step_actors)} BOTTOM multi-actors to overlay (BLUE)")
            if hasattr(self.vtk_renderer_right, 'step_actor') and self.vtk_renderer_right.step_actor:
                # Only copy the single actor if it's visible (not hidden by multi-actor creation)
                if self.vtk_renderer_right.step_actor.GetVisibility():
                    print(f"DEBUG Copying BOTTOM single STEP actor to overlay...")
                    actor = self.vtk_renderer_right.step_actor
                else:
                    print(f"DEBUG Skipping BOTTOM single-actor (hidden by multi-actors)")
                    actor = None

                if actor:
                    overlay_actor = vtk.vtkActor()
                    overlay_actor.SetMapper(actor.GetMapper())
                    overlay_actor.SetUserTransform(actor.GetUserTransform())
                    overlay_actor.SetPosition(actor.GetPosition())
                    overlay_actor.SetOrientation(actor.GetOrientation())
                    overlay_actor.SetScale(actor.GetScale())
                    overlay_actor.SetVisibility(1)
                    prop = overlay_actor.GetProperty()
                    # Keep original colors but make semi-transparent
                    original_color = actor.GetProperty().GetColor()
                    prop.SetColor(original_color[0], original_color[1], original_color[2])
                    prop.SetOpacity(0.5)  # Semi-transparent
                    prop.SetAmbient(0.3)
                    prop.SetDiffuse(0.7)
                    prop.SetSpecular(0.0)
                    color = prop.GetColor()
                    print(f"BLUE DEBUG: BOTTOM single-actor color kept original: {color}")
                    debug_actor(overlay_actor, "BOTTOM single-actor")
                    self.overlay_renderer.AddActor(overlay_actor)
                    self.overlay_top_actors.append(overlay_actor)  # Store reference - BOTTOM goes to overlay top
                    total_actors_copied += 1
                    print(f"OK Copied BOTTOM single-actor to overlay (original color)")

            if total_actors_copied == 0:
                print("FAIL No STEP actors found to copy from either viewer")
        except Exception as e:
            print(f"FAIL Exception in overlay creation: {e}")

    # Overlay methods are defined above

    # File loading methods

    def update_overlay_content(self):
        """Update the overlay content to match the bottom viewer"""
        # Check if overlay widget exists and is visible
        if not hasattr(self, 'overlay_widget') or self.overlay_widget is None:
            print("DEBUG DEBUG: overlay_widget is None, skipping update")
            return

        # Check if overlay mode is active (instead of checking widget visibility)
        if not hasattr(self, 'overlay_mode') or not self.overlay_mode:
            print("DEBUG DEBUG: overlay_mode is not active, skipping update")
            return

        print("TARGET DEBUG: Updating overlay content")

        # Check if both viewers have models loaded
        top_has_model = hasattr(self, 'step_loader_left') and self.step_loader_left.current_polydata is not None
        bottom_has_model = hasattr(self, 'step_loader_right') and self.step_loader_right.current_polydata is not None

        print(f"RED DEBUG: TOP viewer has model: {top_has_model}")
        print(f"BLUE DEBUG: BOTTOM viewer has model: {bottom_has_model}")

        if not top_has_model:
            print("FAIL DEBUG: No model in TOP viewer!")
            return
        if not bottom_has_model:
            print("FAIL DEBUG: No model in BOTTOM viewer!")
            return

        # Update overlay VTK content if it exists
        if hasattr(self, 'overlay_vtk_widget'):
            overlay_render_window = self.overlay_vtk_widget.GetRenderWindow()
            overlay_renderer = overlay_render_window.GetRenderers().GetFirstRenderer()

            if overlay_renderer and hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
                # NOTE: Actors are already added above with forced colors
                # This duplicate code was overriding the color forcing

                # Update camera to match bottom viewer
                bottom_camera = self.vtk_renderer_right.renderer.GetActiveCamera()
                overlay_camera = overlay_renderer.GetActiveCamera()
                overlay_camera.DeepCopy(bottom_camera)

                # Render the updated scene
                overlay_render_window.Render()
                print("DEBUG: Updated overlay VTK scene")

    def _connect_cursor_callback_left(self):
        """Connect cursor position callback for TOP viewer with multiple strategies"""
        try:
            print("DEBUG Attempting to connect TOP cursor position callback...")

            # Strategy 1: Direct interactor connection (FIXED METHOD)
            if hasattr(self.vtk_widget_left, 'GetRenderWindow'):
                render_window = self.vtk_widget_left.GetRenderWindow()
                if render_window:
                    interactor = render_window.GetInteractor()
                    print(f"DEBUG DEBUG: TOP interactor found: {interactor}")
                    if interactor:
                        interactor.cursor_callback = self.on_cursor_move_left
                        print("OK DEBUG: Connected TOP cursor position callback (direct)")
                        # Test the callback immediately
                        test_pos = [0.0, 0.0, 0.0]
                        self.on_cursor_move_left(test_pos)
                        return True
                    else:
                        print("FAIL DEBUG: TOP interactor is None")
                else:
                    print("FAIL DEBUG: TOP render window is None")

            # Strategy 2: Through render window
            if hasattr(self.vtk_renderer_left, 'render_window'):
                render_window = self.vtk_renderer_left.render_window
                if render_window:
                    interactor = render_window.GetInteractor()
                    if interactor:
                        interactor.cursor_callback = self.on_cursor_move_left
                        print("OK DEBUG: Connected TOP cursor position callback (render window)")
                        return True

            # Strategy 3: Delayed connection (sometimes interactor isn't ready immediately)
            from PyQt5.QtCore import QTimer
            def delayed_connect():
                try:
                    if hasattr(self.vtk_widget_left, 'GetInteractor'):
                        interactor = self.vtk_widget_left.GetInteractor()
                        if interactor:
                            interactor.cursor_callback = self.on_cursor_move_left
                            print("OK DEBUG: Connected TOP cursor position callback (delayed)")
                            return True
                except Exception as e:
                    print(f"DEBUG Delayed cursor callback connection failed: {e}")
                return False

            QTimer.singleShot(500, delayed_connect)  # Try again after 500ms
            print("DEBUG Scheduled delayed cursor callback connection")
            return True

        except Exception as e:
            print(f"FAIL Failed to connect TOP cursor position callback: {e}")
            return False

    def on_cursor_move_left(self, world_pos):
        """Handle cursor position updates for TOP viewer"""
        try:
            self.cursor_pos_left = {'x': world_pos[0], 'y': world_pos[1], 'z': world_pos[2]}

            # CRITICAL DEBUG: Check if cursor movement is overriding rotation updates
            if hasattr(self, '_rotation_update_marker') and self._rotation_update_marker:
                print(f"? CURSOR OVERRIDE: Mouse movement after rotation update!")
                print(f"   Rotation set current_pos_left to: {self._rotation_update_values}")
                print(f"   Current current_pos_left is: {self.current_pos_left}")
                if self.current_pos_left != self._rotation_update_values:
                    print(f"? PROBLEM: current_pos_left was changed from rotation values!")
                else:
                    print(f"OK OK: current_pos_left still has rotation values")

            # Only print occasionally to avoid spam
            if not hasattr(self, '_last_cursor_print_left'):
                self._last_cursor_print_left = 0
            import time
            current_time = time.time()
            if current_time - self._last_cursor_print_left > 1.0:  # Print every 1 second
                print(f"TARGET TOP cursor position updated: {self.cursor_pos_left}")
                self._last_cursor_print_left = current_time
            self.update_text_overlays()
        except Exception as e:
            print(f"DEBUG Error updating TOP cursor position: {e}")

    def on_cursor_move_right(self, world_pos):
        """Handle cursor position updates for BOTTOM viewer"""
        try:
            self.cursor_pos_right = {'x': world_pos[0], 'y': world_pos[1], 'z': world_pos[2]}
            # Only print occasionally to avoid spam
            if not hasattr(self, '_last_cursor_print_right'):
                self._last_cursor_print_right = 0
            import time
            current_time = time.time()
            if current_time - self._last_cursor_print_right > 1.0:  # Print every 1 second
                print(f"TARGET BOTTOM cursor position updated: {self.cursor_pos_right}")
                self._last_cursor_print_right = current_time
            self.update_text_overlays()
        except Exception as e:
            print(f"DEBUG Error updating BOTTOM cursor position: {e}")

    def _connect_cursor_callback_right(self):
        """Connect cursor position callback for BOTTOM viewer with multiple strategies"""
        try:
            print("DEBUG Attempting to connect BOTTOM cursor position callback...")

            # Strategy 1: Direct interactor connection (FIXED METHOD)
            if hasattr(self.vtk_widget_right, 'GetRenderWindow'):
                render_window = self.vtk_widget_right.GetRenderWindow()
                if render_window:
                    interactor = render_window.GetInteractor()
                    if interactor:
                        interactor.cursor_callback = self.on_cursor_move_right
                        print("OK DEBUG: Connected BOTTOM cursor position callback (direct)")
                        return True

            # Strategy 2: Through render window
            if hasattr(self.vtk_renderer_right, 'render_window'):
                render_window = self.vtk_renderer_right.render_window
                if render_window:
                    interactor = render_window.GetInteractor()
                    if interactor:
                        interactor.cursor_callback = self.on_cursor_move_right
                        print("OK DEBUG: Connected BOTTOM cursor position callback (render window)")
                        return True

            # Strategy 3: Delayed connection
            from PyQt5.QtCore import QTimer
            def delayed_connect():
                try:
                    if hasattr(self.vtk_widget_right, 'GetInteractor'):
                        interactor = self.vtk_widget_right.GetInteractor()
                        if interactor:
                            interactor.cursor_callback = self.on_cursor_move_right
                            print("OK DEBUG: Connected BOTTOM cursor position callback (delayed)")
                            return True
                except Exception as e:
                    print(f"DEBUG Delayed BOTTOM cursor callback connection failed: {e}")
                return False

            QTimer.singleShot(500, delayed_connect)  # Try again after 500ms
            print("DEBUG Scheduled delayed BOTTOM cursor callback connection")
            return True

        except Exception as e:
            print(f"FAIL Failed to connect BOTTOM cursor position callback: {e}")
            return False

    def update_viewer_highlights(self):
        """Update button highlights to show active viewer"""
        if hasattr(self, 'top_btn') and hasattr(self, 'bottom_btn'):
            if self.active_viewer == "top":
                self.top_btn.setStyleSheet("QPushButton { background-color: lightgreen; font-weight: bold; padding: 8px; }")
                self.bottom_btn.setStyleSheet("QPushButton { padding: 8px; }")
            else:
                self.top_btn.setStyleSheet("QPushButton { padding: 8px; }")
                self.bottom_btn.setStyleSheet("QPushButton { background-color: lightgreen; font-weight: bold; padding: 8px; }")

    def load_step_file(self):
        """Load STEP file into active viewer using file dialog"""
        # Start in current working directory
        current_dir = os.getcwd()
        filename, _ = QFileDialog.getOpenFileName(
            self, "Open STEP File", current_dir, "STEP Files (*.step *.stp);;All Files (*)"
        )

        if filename:
            return self.load_step_file_direct(filename)
        return False

    def load_step_file_direct(self, filename):
        """Load STEP file directly without dialog"""
        if not os.path.exists(filename):
            print(f"File not found: {filename}")
            return False

        print(f"Loading STEP file: {filename}")
        if self.active_viewer == "top":
                print("DEBUG DEEP DEBUG: Loading STEP file for TOP viewer...")
                success, message = self.step_loader_left.load_step_file(filename)
                print(f"TOP load result: success={success}, message={message}")

                # Check if AXIS2_PLACEMENT_3D data was extracted
                if hasattr(self.step_loader_left, 'get_original_axis2_placement'):
                    axis_data = self.step_loader_left.get_original_axis2_placement()
                    print(f"DEBUG DEEP DEBUG: AXIS2_PLACEMENT_3D data after load: {axis_data}")
                else:
                    print("DEBUG DEEP DEBUG: get_original_axis2_placement method not found!")

                if success:
                    print(f"Polydata available: {self.step_loader_left.current_polydata is not None}")
                    if self.step_loader_left.current_polydata:
                        print(f"Polydata points: {self.step_loader_left.current_polydata.GetNumberOfPoints()}")
                        print(f"Polydata cells: {self.step_loader_left.current_polydata.GetNumberOfCells()}")

                    # Reset any previous transformations before loading new model
                    self.vtk_renderer_left.clear_view()

                    # CRITICAL FIX: Re-add cursor text actor after clear_view() removes it
                    if hasattr(self, 'cursor_text_actor_left'):
                        print("DEBUG CRITICAL FIX: Re-adding cursor text actor after clear_view()")
                        renderer = self.vtk_renderer_left.renderer
                        if renderer:
                            renderer.AddActor2D(self.cursor_text_actor_left)
                            # Ensure enhanced properties are applied
                            self.cursor_text_actor_left.GetTextProperty().SetFontSize(16)
                            self.cursor_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)
                            self.cursor_text_actor_left.GetTextProperty().SetBold(True)
                            self.cursor_text_actor_left.GetTextProperty().SetShadow(True)
                            self.cursor_text_actor_left.SetLayerNumber(10)
                            self.cursor_text_actor_left.SetVisibility(1)  # Make sure it's visible
                            print("OK Cursor text actor re-added and enhanced after clear_view()")

                    # CRITICAL FIX: Re-add combined text actor after clear_view() removes it
                    if hasattr(self, 'combined_text_actor_left'):
                        print("DEBUG CRITICAL FIX: Re-adding combined text actor after clear_view()")
                        renderer = self.vtk_renderer_left.renderer
                        if renderer:
                            renderer.AddActor2D(self.combined_text_actor_left)
                            self.combined_text_actor_left.SetVisibility(1)
                            print("OK Combined text actor re-added after clear_view()")

                    # Display the polydata
                    display_success = self.vtk_renderer_left.display_polydata(self.step_loader_left.current_polydata)
                    print(f"Display success: {display_success}")

                    self.vtk_renderer_left.fit_view()  # Auto-fit the view
                    # Show bounding box by default
                    self.vtk_renderer_left.toggle_bounding_box(True)
                    self.top_file_label.setText(f"TOP: {os.path.basename(filename)}")
                    self.extract_step_transformation_data("top")
                    # Store original actor transforms for reset functionality
                    self.store_original_actor_transforms("top")
                    # Re-setup text overlay after model loading
                    self.setup_text_overlay_for_viewer("top")
                    # DON'T RECREATE CURSOR TEXT ACTOR - it already exists and has our Original top data
                    print("DEBUG DEBUG: Skipping cursor text actor recreation to preserve Original top data")
                    if hasattr(self, 'cursor_text_actor_left'):
                        print("DEBUG DEBUG: cursor_text_actor_left already exists - preserving it")
                        self.cursor_text_actor_left.SetVisibility(1)  # Just make it visible
                    else:
                        print("DEBUG DEBUG: cursor_text_actor_left missing - creating new one")
                        import vtk
                        renderer = self.vtk_renderer_left.renderer
                        self.cursor_text_actor_left = vtk.vtkTextActor()
                        self.cursor_text_actor_left.SetInput("CURSOR: X=0.00 Y=0.00 Z=0.00")
                        self.cursor_text_actor_left.GetTextProperty().SetFontSize(14)
                        self.cursor_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)
                        self.cursor_text_actor_left.GetTextProperty().SetBold(True)
                        self.cursor_text_actor_left.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                        self.cursor_text_actor_left.SetPosition(0.02, 0.95)
                        self.cursor_text_actor_left.SetVisibility(1)
                        renderer.AddActor2D(self.cursor_text_actor_left)
                    # Enable cursor tracking
                    self._connect_cursor_callback_left()
                else:
                    print(f"Load failed: {message}")
                    self.top_file_label.setText("TOP: Load failed")
        else:
            success, message = self.step_loader_right.load_step_file(filename)
            if success:
                # Reset any previous transformations before loading new model
                self.vtk_renderer_right.clear_view()

                # CRITICAL FIX: Re-add cursor text actor after clear_view() removes it
                if hasattr(self, 'cursor_text_actor_right'):
                    print("DEBUG CRITICAL FIX: Re-adding BOTTOM cursor text actor after clear_view()")
                    renderer = self.vtk_renderer_right.renderer
                    if renderer:
                        renderer.AddActor2D(self.cursor_text_actor_right)
                        # Ensure enhanced properties are applied
                        self.cursor_text_actor_right.GetTextProperty().SetFontSize(16)
                        self.cursor_text_actor_right.GetTextProperty().SetColor(1.0, 1.0, 0.0)
                        self.cursor_text_actor_right.GetTextProperty().SetBold(True)
                        self.cursor_text_actor_right.GetTextProperty().SetShadow(True)
                        self.cursor_text_actor_right.SetLayerNumber(10)
                        self.cursor_text_actor_right.SetVisibility(1)  # Make sure it's visible
                        print("OK BOTTOM cursor text actor re-added and enhanced after clear_view()")

                # CRITICAL FIX: Re-add combined text actor after clear_view() removes it
                if hasattr(self, 'combined_text_actor_right'):
                    print("DEBUG CRITICAL FIX: Re-adding BOTTOM combined text actor after clear_view()")
                    renderer = self.vtk_renderer_right.renderer
                    if renderer:
                        renderer.AddActor2D(self.combined_text_actor_right)
                        self.combined_text_actor_right.SetVisibility(1)
                        print("OK BOTTOM combined text actor re-added after clear_view()")

                self.vtk_renderer_right.display_polydata(self.step_loader_right.current_polydata)
                self.vtk_renderer_right.fit_view()  # Auto-fit the view
                # Show bounding box by default
                self.vtk_renderer_right.toggle_bounding_box(True)
                self.bottom_file_label.setText(f"BOTTOM: {os.path.basename(filename)}")
                self.extract_step_transformation_data("bottom")
                # Store original actor transforms for reset functionality
                self.store_original_actor_transforms("bottom")
                # Re-setup text overlay after model loading
                self.setup_text_overlay_for_viewer("bottom")
                # Update overlay content if overlay mode is active and bottom viewer was loaded
                if self.active_viewer == "bottom":
                    self.update_overlay_content()
            else:
                self.bottom_file_label.setText("BOTTOM: Load failed")

        self.statusBar().showMessage(f"{self.active_viewer.title()}: {message}")
        self.update_transform_display()

        # Automatically create origin overlay after successful model load
        print("TARGET Auto-creating origin overlay after model load")
        self.create_origin_overlay()

        # Also create part origin overlay at the actual STEP file origin
        if hasattr(self, 'orig_pos_left') and self.active_viewer == "top":
            print("TARGET Auto-creating part origin overlay for TOP viewer")
            self.vtk_renderer_left.create_part_origin_overlay(
                self.orig_pos_left['x'],
                self.orig_pos_left['y'],
                self.orig_pos_left['z'],
                self.orig_z_direction_left,
                self.orig_x_direction_left
            )
        elif hasattr(self, 'orig_pos_right') and self.active_viewer == "bottom":
            print("TARGET Auto-creating part origin overlay for BOTTOM viewer")
            self.vtk_renderer_right.create_part_origin_overlay(
                self.orig_pos_right['x'],
                self.orig_pos_right['y'],
                self.orig_pos_right['z'],
                self.orig_z_direction_right,
                self.orig_x_direction_right
            )

        # CRITICAL FIX: Ensure text actors are properly recreated after new file load
        print("TARGET Ensuring text actors are properly recreated after new file load")
        self._ensure_text_actors_exist()

        # Update text overlays AFTER origin actors are created
        print("TARGET Updating text overlays after origin creation")
        self.update_text_overlays()

        return True

    def store_original_actor_transforms(self, viewer):
        """Store original actor transforms for proper reset functionality"""
        import vtk

        if viewer == "top":
            renderer = self.vtk_renderer_left
            self.original_actor_transforms_left = []

            # Store original camera position for reset
            camera = renderer.renderer.GetActiveCamera()
            self.original_camera_left = {
                'position': camera.GetPosition(),
                'focal_point': camera.GetFocalPoint(),
                'view_up': camera.GetViewUp(),
                'orientation': camera.GetOrientation()
            }
            print(f"DEBUG: Stored original TOP camera orientation: {self.original_camera_left['orientation']}")

            # Keep original rotation values as (0,0,0) for model display
            # The camera reset will handle the visual restoration

            # Store transforms for multi-actor models
            if hasattr(renderer, 'step_actors') and renderer.step_actors:
                print(f"DEBUG: Storing {len(renderer.step_actors)} original actor transforms for TOP")
                for actor in renderer.step_actors:
                    # Create a copy of the current transform
                    transform = vtk.vtkTransform()
                    if actor.GetUserTransform():
                        transform.DeepCopy(actor.GetUserTransform())
                    else:
                        transform.Identity()
                    # Store position and orientation separately for clarity
                    original_state = {
                        'transform': transform,
                        'position': actor.GetPosition(),
                        'orientation': actor.GetOrientation()
                    }
                    self.original_actor_transforms_left.append(original_state)

            # Store transform for single-actor model
            elif hasattr(renderer, 'step_actor') and renderer.step_actor:
                print(f"DEBUG: Storing original single actor transform for TOP")
                actor = renderer.step_actor
                transform = vtk.vtkTransform()
                if actor.GetUserTransform():
                    transform.DeepCopy(actor.GetUserTransform())
                else:
                    transform.Identity()
                original_state = {
                    'transform': transform,
                    'position': actor.GetPosition(),
                    'orientation': actor.GetOrientation()
                }
                self.original_actor_transforms_left.append(original_state)

        else:  # bottom viewer
            renderer = self.vtk_renderer_right
            self.original_actor_transforms_right = []

            # Store original camera position for reset
            camera = renderer.renderer.GetActiveCamera()
            self.original_camera_right = {
                'position': camera.GetPosition(),
                'focal_point': camera.GetFocalPoint(),
                'view_up': camera.GetViewUp(),
                'orientation': camera.GetOrientation()
            }
            print(f"DEBUG: Stored original BOTTOM camera orientation: {self.original_camera_right['orientation']}")

            # Store transforms for multi-actor models
            if hasattr(renderer, 'step_actors') and renderer.step_actors:
                print(f"DEBUG: Storing {len(renderer.step_actors)} original actor transforms for BOTTOM")
                for actor in renderer.step_actors:
                    transform = vtk.vtkTransform()
                    if actor.GetUserTransform():
                        transform.DeepCopy(actor.GetUserTransform())
                    else:
                        transform.Identity()
                    original_state = {
                        'transform': transform,
                        'position': actor.GetPosition(),
                        'orientation': actor.GetOrientation()
                    }
                    self.original_actor_transforms_right.append(original_state)

            # Store transform for single-actor model
            elif hasattr(renderer, 'step_actor') and renderer.step_actor:
                print(f"DEBUG: Storing original single actor transform for BOTTOM")
                actor = renderer.step_actor
                transform = vtk.vtkTransform()
                if actor.GetUserTransform():
                    transform.DeepCopy(actor.GetUserTransform())
                else:
                    transform.Identity()
                original_state = {
                    'transform': transform,
                    'position': actor.GetPosition(),
                    'orientation': actor.GetOrientation()
                }
                self.original_actor_transforms_right.append(original_state)

    def parse_step_file_coordinates(self, filename):
        """Parse STEP file to extract actual CARTESIAN_POINT and DIRECTION values"""
        print(f"DEBUG: Parsing STEP file: {filename}")
        try:
            with open(filename, 'r') as f:
                lines = f.readlines()

            print(f"DEBUG: Read {len(lines)} lines from STEP file")
            cartesian_points = []

            for i, line in enumerate(lines):
                line = line.strip()

                # Look for CARTESIAN_POINT lines - be more specific
                if 'CARTESIAN_POINT' in line and '(' in line and ')' in line:
                    print(f"DEBUG: Found CARTESIAN_POINT line {i+1}: {line}")
                    try:
                        # Find the coordinate values between the last set of parentheses
                        # Line format: #49 = CARTESIAN_POINT ( 'NONE',  ( -4.190000000000000, -3.667300000000000, 0.491400000000000 ) ) ;
                        start = line.rfind('(')
                        end = line.rfind(')')
                        if start != -1 and end != -1 and start < end:
                            coords_str = line[start+1:end].strip()
                            print(f"DEBUG: Extracting coordinates from: '{coords_str}'")
                            coords = [float(x.strip()) for x in coords_str.split(',')]
                            if len(coords) == 3:
                                cartesian_points.append(coords)
                                print(f"DEBUG: Successfully parsed CARTESIAN_POINT: {coords}")
                                # Use the first one we find
                                origin = coords
                                print(f"DEBUG: Using STEP file origin: {origin}")
                                return {'x': origin[0], 'y': origin[1], 'z': origin[2]}
                    except Exception as parse_error:
                        print(f"DEBUG: Error parsing CARTESIAN_POINT line: {parse_error}")
                        pass

            print(f"DEBUG: No valid CARTESIAN_POINT found in {len(lines)} lines")
            return None

        except Exception as e:
            print(f"DEBUG: Error reading STEP file: {e}")
            return None

    def extract_step_transformation_data(self, viewer):
        """Extract transformation data from loaded STEP file"""
        if viewer == "top":
            loader = self.step_loader_left
            polydata = loader.current_polydata
            filename = getattr(loader, 'current_filename', None)
        else:
            loader = self.step_loader_right
            polydata = loader.current_polydata
            filename = getattr(loader, 'current_filename', None)

        if polydata:
            # Get the bounds of the geometry for fallback
            bounds = polydata.GetBounds()  # [xmin, xmax, ymin, ymax, zmin, zmax]

            # FIXED: Use AXIS2_PLACEMENT_3D data from step_loader instead of parsing again
            orig_pos = None
            orig_rot = None

            # Try to get AXIS2_PLACEMENT_3D data from the step_loader
            if hasattr(loader, 'get_original_axis2_placement'):
                axis_data = loader.get_original_axis2_placement()
                if axis_data:
                    print(f"OK Using AXIS2_PLACEMENT_3D data from step_loader:")
                    print(f"   Point: {axis_data['point']}")
                    print(f"   Dir1: {axis_data['dir1']}")
                    print(f"   Dir2: {axis_data['dir2']}")

                    # Convert AXIS2_PLACEMENT_3D point to position
                    if isinstance(axis_data['point'], (tuple, list)) and len(axis_data['point']) == 3:
                        orig_pos = {'x': axis_data['point'][0], 'y': axis_data['point'][1], 'z': axis_data['point'][2]}
                    elif isinstance(axis_data['point'], str):
                        # Parse string format like "(-4.19, -3.6673, 0.4914)"
                        import re
                        coords = re.findall(r'[-+]?\d*\.?\d+', axis_data['point'])
                        if len(coords) >= 3:
                            orig_pos = {'x': float(coords[0]), 'y': float(coords[1]), 'z': float(coords[2])}

                    # Convert AXIS2_PLACEMENT_3D directions to rotation angles
                    # For now, set rotation to zero - we're focusing on displaying the correct original values
                    orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}

                    # FIXED: Store direction vectors for green sphere orientation
                    orig_z_direction = axis_data['dir1'] if 'dir1' in axis_data else None
                    orig_x_direction = axis_data['dir2'] if 'dir2' in axis_data else None

                    print(f"OK Converted AXIS2_PLACEMENT_3D to GUI format:")
                    print(f"   Position: {orig_pos}")
                    print(f"   Rotation: {orig_rot}")
                    print(f"   Z Direction: {orig_z_direction}")
                    print(f"   X Direction: {orig_x_direction}")
                else:
                    print("FAIL No AXIS2_PLACEMENT_3D data found in step_loader")
                    orig_z_direction = None
                    orig_x_direction = None
            else:
                print("FAIL step_loader does not have get_original_axis2_placement method")
                orig_z_direction = None
                orig_x_direction = None

            # Fallback if AXIS2_PLACEMENT_3D extraction failed
            if orig_pos is None:
                # Fallback to geometry bounds center if STEP parsing fails
                center_x = (bounds[0] + bounds[1]) / 2.0
                center_y = (bounds[2] + bounds[3]) / 2.0
                center_z = (bounds[4] + bounds[5]) / 2.0
                orig_pos = {'x': center_x, 'y': center_y, 'z': center_z}
                print(f"DEBUG FALLBACK: Using geometry bounds center as origin")
                print(f"  Position: X={orig_pos['x']:.6f}, Y={orig_pos['y']:.6f}, Z={orig_pos['z']:.6f}")

            if orig_rot is None:
                orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            # FIXED: Set default direction vectors if not extracted
            if orig_z_direction is None:
                orig_z_direction = [0, 0, 1]  # Default Z direction
            if orig_x_direction is None:
                orig_x_direction = [1, 0, 0]  # Default X direction

            print(f"DEBUG Model bounds: X({bounds[0]:.1f} to {bounds[1]:.1f}), Y({bounds[2]:.1f} to {bounds[3]:.1f}), Z({bounds[4]:.1f} to {bounds[5]:.1f})")

            if viewer == "top":
                self.orig_pos_left = orig_pos
                self.orig_rot_left = orig_rot
                # FIXED: Store direction vectors for green sphere orientation
                self.orig_z_direction_left = orig_z_direction
                self.orig_x_direction_left = orig_x_direction
                # Initialize current values to ACTUAL model coordinates
                self.current_pos_left = orig_pos.copy()  # Show real coordinates
                self.current_rot_left = orig_rot.copy()  # Show calculated angles from STEP file
                print(f"DEBUG DEEP DEBUG TOP: orig_pos_left = {self.orig_pos_left}")
                print(f"DEBUG DEEP DEBUG TOP: orig_rot_left = {self.orig_rot_left}")
                print(f"DEBUG DEEP DEBUG TOP: orig_z_direction_left = {self.orig_z_direction_left}")
                print(f"DEBUG DEEP DEBUG TOP: orig_x_direction_left = {self.orig_x_direction_left}")
                print(f"DEBUG DEEP DEBUG TOP: current_pos_left = {self.current_pos_left}")
                print(f"DEBUG DEEP DEBUG TOP: current_rot_left = {self.current_rot_left}")

                # Check what the GUI labels will show
                if hasattr(self, 'lbl_orig_rot_x_top'):
                    print(f"DEBUG DEEP DEBUG TOP: GUI will show orig_rot X = {self.orig_rot_left['x']:.3f}deg")
                    print(f"DEBUG DEEP DEBUG TOP: GUI will show orig_rot Y = {self.orig_rot_left['y']:.3f}deg")
                    print(f"DEBUG DEEP DEBUG TOP: GUI will show orig_rot Z = {self.orig_rot_left['z']:.3f}deg")

                # FIXED ITEM 1: Update TOP viewer GUI labels with AXIS2_PLACEMENT_3D data
                if hasattr(self, 'step_loader_left') and hasattr(self.step_loader_left, 'get_original_axis2_placement'):
                    axis_data = self.step_loader_left.get_original_axis2_placement()
                    if axis_data:
                        print("DEBUG FIXED: Updating TOP viewer labels with AXIS2_PLACEMENT_3D data")
                        self.update_original_labels(axis_data, "top")
                    else:
                        print("FAIL No AXIS2_PLACEMENT_3D data found for TOP viewer")
            else:
                print(f"DEBUG BOTTOM DEBUG: orig_rot before assignment = {orig_rot}")
                self.orig_pos_right = orig_pos
                self.orig_rot_right = orig_rot
                # FIXED: Store direction vectors for green sphere orientation
                self.orig_z_direction_right = orig_z_direction
                self.orig_x_direction_right = orig_x_direction
                # Initialize current values to ACTUAL model coordinates
                self.current_pos_right = orig_pos.copy()  # Show real coordinates
                self.current_rot_right = orig_rot.copy()  # Use ACTUAL rotation from STEP file
                print(f"DEBUG BOTTOM DEBUG: orig_rot after copy = {orig_rot}")
                print(f"DEBUG BOTTOM DEBUG: current_rot_right after copy = {self.current_rot_right}")
                print(f"DEBUG DEEP DEBUG BOTTOM: orig_pos_right = {self.orig_pos_right}")
                print(f"DEBUG DEEP DEBUG BOTTOM: orig_rot_right = {self.orig_rot_right}")
                print(f"DEBUG DEEP DEBUG BOTTOM: orig_z_direction_right = {self.orig_z_direction_right}")
                print(f"DEBUG DEEP DEBUG BOTTOM: orig_x_direction_right = {self.orig_x_direction_right}")
                print(f"DEBUG DEEP DEBUG BOTTOM: current_pos_right = {self.current_pos_right}")
                print(f"DEBUG DEEP DEBUG BOTTOM: current_rot_right = {self.current_rot_right}")

                # Check what the GUI labels will show
                if hasattr(self, 'lbl_orig_rot_x_bottom'):
                    print(f"DEBUG DEEP DEBUG BOTTOM: GUI will show orig_rot X = {self.orig_rot_right['x']:.3f}deg")
                    print(f"DEBUG DEEP DEBUG BOTTOM: GUI will show orig_rot Y = {self.orig_rot_right['y']:.3f}deg")
                    print(f"DEBUG DEEP DEBUG BOTTOM: GUI will show orig_rot Z = {self.orig_rot_right['z']:.3f}deg")

                # Update BOTTOM viewer GUI labels with AXIS2_PLACEMENT_3D data
                if hasattr(self, 'step_loader_right') and hasattr(self.step_loader_right, 'get_original_axis2_placement'):
                    axis_data = self.step_loader_right.get_original_axis2_placement()
                    if axis_data:
                        print("DEBUG Updating BOTTOM viewer labels with AXIS2_PLACEMENT_3D data")
                        self.update_original_labels(axis_data, "bottom")
        else:
            print(f"No polydata available for {viewer} viewer")

    def clear_view(self):
        """Clear the active viewer and reset numbers to zero"""
        if self.active_viewer == "top":
            self.vtk_renderer_left.clear_view()
            self.top_file_label.setText("TOP VIEWER - No file loaded")
            # Reset all transform values to zero
            self.orig_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.orig_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        else:
            self.vtk_renderer_right.clear_view()
            self.bottom_file_label.setText("BOTTOM VIEWER - No file loaded")
            # Reset all transform values to zero
            self.orig_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.orig_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

        # Update the display to show zeros
        self.update_transform_display()
        self.statusBar().showMessage(f"{self.active_viewer.title()} view cleared")

    def fit_view(self):
        """Fit view in active viewer"""
        if self.active_viewer == "top":
            if self.vtk_renderer_left:
                self.vtk_renderer_left.fit_view()
                print("Fitted TOP view")
        else:
            if self.vtk_renderer_right:
                self.vtk_renderer_right.fit_view()
                print("Fitted BOTTOM view")

    def save_transformed_step(self):
        """Save transformed STEP file from active viewer"""
        print("START SAVE_TRANSFORMED_STEP: Method called!")
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save STEP File", "", "STEP Files (*.step);;STL Files (*.stl);;All Files (*)"
        )

        if filename:
            print(f"Saving file: {filename}")
            import vtk  # Add missing import
            if self.active_viewer == "top":
                # Get the current polydata from the VTK renderer (includes transformations)
                if self.vtk_renderer_left.step_actor:
                    # Get transformed polydata
                    transform_filter = vtk.vtkTransformPolyDataFilter()
                    transform_filter.SetInputData(self.step_loader_left.current_polydata)
                    transform_filter.SetTransform(self.vtk_renderer_left.step_actor.GetUserTransform() or vtk.vtkTransform())
                    transform_filter.Update()
                    self.step_loader_left.current_polydata = transform_filter.GetOutput()
                    print("Applied TOP viewer transformations to save data")
                success = self.step_loader_left.save_step_file(filename)
            else:
                # Get the current polydata from the VTK renderer (includes transformations)
                if self.vtk_renderer_right.step_actor:
                    # Get transformed polydata
                    transform_filter = vtk.vtkTransformPolyDataFilter()
                    transform_filter.SetInputData(self.step_loader_right.current_polydata)
                    transform_filter.SetTransform(self.vtk_renderer_right.step_actor.GetUserTransform() or vtk.vtkTransform())
                    transform_filter.Update()
                    self.step_loader_right.current_polydata = transform_filter.GetOutput()
                    print("Applied BOTTOM viewer transformations to save data")
                success = self.step_loader_right.save_step_file(filename)

            if success:
                self.statusBar().showMessage(f"Saved: {filename}")
            else:
                self.statusBar().showMessage("Save failed")



    def reset_to_original(self):
        """Reset active viewer to original transform"""
        if self.active_viewer == "top":
            # Reset position tracking to original position (not 0,0,0)
            if hasattr(self, 'orig_pos_left'):
                self.current_pos_left = self.orig_pos_left.copy()
            else:
                self.current_pos_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset to original STEP file rotation (not 0,0,0) to show correct angle
            if hasattr(self, 'orig_rot_left'):
                self.current_rot_left = self.orig_rot_left.copy()
            else:
                self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset movement delta
            if hasattr(self, 'movement_delta_left'):
                self.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            # Reset the actual VTK actors - handle both single and multi-actor cases
            import vtk
            transform = vtk.vtkTransform()
            transform.Identity()

            # Reset multi-actors if they exist
            if hasattr(self.vtk_renderer_left, 'step_actors') and self.vtk_renderer_left.step_actors:
                print(f"DEBUG: Restoring {len(self.vtk_renderer_left.step_actors)} actors to original transforms")
                print(f"DEBUG: Found {len(self.original_actor_transforms_left)} stored original transforms")

                for i, actor in enumerate(self.vtk_renderer_left.step_actors):
                    if i < len(self.original_actor_transforms_left):
                        original_transform = self.original_actor_transforms_left[i]
                        print(f"DEBUG: Multi-actor {i} visibility: {actor.GetVisibility()}")

                        if actor.GetVisibility():
                            print(f"DEBUG: Restoring VISIBLE actor {i} to original state")

                            # DEBUG: Show what the stored original transform contains
                            print(f"DEBUG: Stored original transform for actor {i}: {original_transform}")
                            if original_transform:
                                print(f"DEBUG: Original transform exists - will restore to stored state")
                            else:
                                print(f"DEBUG: Original transform is None - will restore to identity")

                            # DEBUG: Show ACTUAL current values BEFORE reset
                            current_pos = actor.GetPosition()
                            current_orient = actor.GetOrientation()
                            print(f"DEBUG: Actor {i} BEFORE reset: Pos={current_pos}, Orient={current_orient}")

                            # Try to reset using stored original transform
                            if isinstance(original_transform, dict):
                                # New format: dictionary with transform, position, orientation
                                stored_transform = original_transform.get('transform')
                                stored_position = original_transform.get('position', (0, 0, 0))
                                stored_orientation = original_transform.get('orientation', (0, 0, 0))

                                actor.SetUserTransform(stored_transform)
                                actor.SetPosition(*stored_position)
                                actor.SetOrientation(*stored_orientation)
                            else:
                                # Old format: just the transform object
                                actor.SetUserTransform(original_transform)
                                actor.SetOrientation(0, 0, 0)
                                actor.SetPosition(0, 0, 0)
                            actor.Modified()

                            # DEBUG: Show ACTUAL values AFTER reset attempt
                            new_pos = actor.GetPosition()
                            new_orient = actor.GetOrientation()
                            print(f"DEBUG: Actor {i} AFTER reset: Pos={new_pos}, Orient={new_orient}")

                            # DEBUG: Show what the actor's transform is now
                            current_transform_after = actor.GetUserTransform()
                            print(f"DEBUG: Actor {i} transform AFTER reset: {current_transform_after}")

                            if new_pos == (0.0, 0.0, 0.0) and new_orient == (0.0, 0.0, 0.0):
                                print(f"DEBUG: OK VISIBLE Actor {i} reset SUCCESSFUL")
                            else:
                                print(f"DEBUG: FAIL VISIBLE Actor {i} reset FAILED - values didn't change!")
                        else:
                            print(f"DEBUG: Skipping invisible actor {i}")
                    else:
                        print(f"DEBUG: FAIL No stored transform for actor {i}")

                print("DEBUG: Actor reset using original transforms completed")

            # Reset single actor if it exists
            elif hasattr(self.vtk_renderer_left, 'step_actor') and self.vtk_renderer_left.step_actor:
                print("DEBUG: Resetting single actor")
                self.vtk_renderer_left.step_actor.SetUserTransform(transform)
                self.vtk_renderer_left.step_actor.SetOrientation(0, 0, 0)
                self.vtk_renderer_left.step_actor.SetPosition(0, 0, 0)
                self.vtk_renderer_left.step_actor.Modified()
                print("DEBUG: Single actor reset to original")

            # NOTE: Bounding box will be recreated AFTER origin fix to prevent removal

            # CRITICAL: Reset camera to stored original position
            print("DEBUG: Resetting camera to stored original position")
            camera = self.vtk_renderer_left.renderer.GetActiveCamera()
            if hasattr(self, 'original_camera_left'):
                camera.SetPosition(*self.original_camera_left['position'])
                camera.SetFocalPoint(*self.original_camera_left['focal_point'])
                camera.SetViewUp(*self.original_camera_left['view_up'])
                print(f"DEBUG: Camera restored to original orientation: {self.original_camera_left['orientation']}")
            else:
                # Fallback to default position
                camera.SetPosition(0, 0, 1)
                camera.SetFocalPoint(0, 0, 0)
                camera.SetViewUp(0, 1, 0)
                self.vtk_renderer_left.renderer.ResetCamera()
                print("DEBUG: Camera reset to default position (no stored original)")
            print("DEBUG: Camera reset completed")

            # Force render to update display
            self.vtk_renderer_left.render_window.Render()

            # ULTIMATE FIX: Completely clear ALL origin-related actors with ZERO tolerance for duplicates
            print("TARGET ULTIMATE FIX: Removing ALL origin-related actors for TOP viewer")

            # Method 1: Use existing clear function
            if hasattr(self.vtk_renderer_left, 'clear_origin_overlay'):
                self.vtk_renderer_left.clear_origin_overlay()

            # Method 2: NUCLEAR OPTION - Remove ALL colored actors (red, green, blue)
            if hasattr(self.vtk_renderer_left, 'renderer') and self.vtk_renderer_left.renderer:
                renderer = self.vtk_renderer_left.renderer
                actors_to_remove = []

                # Find ALL colored actors that could be origin markers
                actors = renderer.GetActors()
                actors.InitTraversal()
                while True:
                    actor = actors.GetNextActor()
                    if not actor:
                        break

                    # Check if this looks like ANY origin-related actor
                    prop = actor.GetProperty()
                    if prop:
                        color = prop.GetColor()
                        # Red, Green, OR Blue actors (covers all origin marker types)
                        if (color[0] > 0.7 and color[1] < 0.3 and color[2] < 0.3) or \
                           (color[0] < 0.3 and color[1] > 0.7 and color[2] < 0.3) or \
                           (color[0] < 0.3 and color[1] < 0.3 and color[2] > 0.7):
                            actors_to_remove.append(actor)

                # Remove ALL found colored actors
                for actor in actors_to_remove:
                    renderer.RemoveActor(actor)
                    print(f"OK ULTIMATE FIX: Removed colored actor")

                print(f"OK ULTIMATE FIX: Removed {len(actors_to_remove)} colored actors")

            # Method 3: Clear ALL origin-related variables and lists
            origin_vars = ['origin_actors', 'part_origin_actors', 'part_origin_sphere',
                          'part_origin_x_arrow', 'part_origin_y_arrow', 'part_origin_z_arrow']
            for var in origin_vars:
                if hasattr(self.vtk_renderer_left, var):
                    setattr(self.vtk_renderer_left, var, [] if 'actors' in var else None)
                    print(f"OK ULTIMATE FIX: Cleared {var}")

            # Method 4: Force render to clear any cached actors
            if self.vtk_renderer_left.render_window:
                self.vtk_renderer_left.render_window.Render()
                print("OK ULTIMATE FIX: Forced render to clear cache")

            # ALSO reset overlay if overlay mode is active
            if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_renderer'):
                print("TARGET Also resetting overlay actors for TOP viewer")
                # Re-create overlay to reset all transformations
                self._create_overlay_widget()
                # Force render overlay
                if hasattr(self, 'vtk_widget_left'):
                    self.vtk_widget_left.GetRenderWindow().Render()

            # CRITICAL FIX: Delete ALL existing origin overlays first, then create proper ones
            print("TARGET RESET FIX: Manually clearing ALL origin overlays for TOP viewer")

            # Step 1: Force clear ALL origin overlays (world origin)
            if hasattr(self.vtk_renderer_left, 'clear_origin_overlay'):
                self.vtk_renderer_left.clear_origin_overlay()
                print("OK RESET FIX: TOP world origin overlay cleared")

            # Step 1b: Manually clear part origin overlays (since clear_part_origin_overlay doesn't exist)
            if hasattr(self.vtk_renderer_left, 'part_origin_actors'):
                print("TARGET RESET FIX: Manually removing part origin actors")
                for actor in self.vtk_renderer_left.part_origin_actors:
                    if actor and self.vtk_renderer_left.renderer:
                        self.vtk_renderer_left.renderer.RemoveActor(actor)
                        print("OK RESET FIX: Removed part origin actor")
                self.vtk_renderer_left.part_origin_actors = []  # Clear the list
                print("OK RESET FIX: TOP part origin actors manually cleared")

            # Step 1c: Also check for individual part origin sphere
            if hasattr(self.vtk_renderer_left, 'part_origin_sphere') and self.vtk_renderer_left.part_origin_sphere:
                if self.vtk_renderer_left.renderer:
                    self.vtk_renderer_left.renderer.RemoveActor(self.vtk_renderer_left.part_origin_sphere)
                    print("OK RESET FIX: Removed part origin sphere")
                self.vtk_renderer_left.part_origin_sphere = None

            # Step 2: Create proper origin overlays at correct positions
            print("TARGET RESET FIX: Creating proper origin overlays for TOP viewer")

            # Create world origin overlay (red markers at 0,0,0) - PREVENT DUPLICATES
            if hasattr(self.vtk_renderer_left, 'create_origin_overlay'):
                # Only create if origin_actors list is empty
                if not hasattr(self.vtk_renderer_left, 'origin_actors') or len(self.vtk_renderer_left.origin_actors) == 0:
                    self.vtk_renderer_left.create_origin_overlay()
                    print("OK RESET FIX: TOP world origin overlay created at (0,0,0)")
                else:
                    print("WARN RESET FIX: Skipping world origin - already exists")

            # Create part origin overlay (green marker at STEP file origin) - PREVENT DUPLICATES
            if hasattr(self, 'orig_pos_left') and hasattr(self.vtk_renderer_left, 'create_part_origin_overlay'):
                # Only create if part origin doesn't already exist
                if not hasattr(self.vtk_renderer_left, 'part_origin_sphere') or self.vtk_renderer_left.part_origin_sphere is None:
                    self.vtk_renderer_left.create_part_origin_overlay(
                        self.orig_pos_left['x'],
                        self.orig_pos_left['y'],
                        self.orig_pos_left['z'],
                        getattr(self, 'orig_z_direction_left', None),
                        getattr(self, 'orig_x_direction_left', None)
                    )
                    print(f"OK RESET FIX: TOP part origin overlay created at ({self.orig_pos_left['x']:.3f}, {self.orig_pos_left['y']:.3f}, {self.orig_pos_left['z']:.3f})")
                else:
                    print("WARN RESET FIX: Skipping part origin - already exists")

            # Force render to ensure overlays are visible
            self.vtk_renderer_left.render_window.Render()
            print("OK RESET FIX: TOP viewer rendered with proper overlays")

            # NOW recreate bounding box AFTER origin fix (so it doesn't get removed)
            if self.bbox_visible_left:
                print("DEBUG RESET FIX: Recreating bounding box after origin fix")
                self.vtk_renderer_left.update_bounding_box()
                print("OK RESET FIX: Bounding box recreated")

            print("Reset TOP viewer to original")

        else:
            # Reset position tracking to original position (not 0,0,0)
            if hasattr(self, 'orig_pos_right'):
                self.current_pos_right = self.orig_pos_right.copy()
            else:
                self.current_pos_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset to original STEP file rotation (not 0,0,0) to show correct angle
            if hasattr(self, 'orig_rot_right'):
                self.current_rot_right = self.orig_rot_right.copy()
            else:
                self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            self.model_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
            # Reset movement delta
            if hasattr(self, 'movement_delta_right'):
                self.movement_delta_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            # Reset the actual VTK actors - handle both single and multi-actor cases
            import vtk
            transform = vtk.vtkTransform()
            transform.Identity()

            # Reset multi-actors if they exist
            if hasattr(self.vtk_renderer_right, 'step_actors') and self.vtk_renderer_right.step_actors:
                print(f"DEBUG: Restoring {len(self.vtk_renderer_right.step_actors)} BOTTOM actors to original transforms")
                print(f"DEBUG: Found {len(self.original_actor_transforms_right)} stored original transforms")

                for i, actor in enumerate(self.vtk_renderer_right.step_actors):
                    if i < len(self.original_actor_transforms_right):
                        original_transform = self.original_actor_transforms_right[i]
                        print(f"DEBUG: BOTTOM Multi-actor {i} visibility: {actor.GetVisibility()}")

                        if actor.GetVisibility():
                            print(f"DEBUG: Restoring VISIBLE BOTTOM actor {i} to original state")

                            # Try to reset using stored original transform
                            if isinstance(original_transform, dict):
                                # New format: dictionary with transform, position, orientation
                                stored_transform = original_transform.get('transform')
                                stored_position = original_transform.get('position', (0, 0, 0))
                                stored_orientation = original_transform.get('orientation', (0, 0, 0))

                                actor.SetUserTransform(stored_transform)
                                actor.SetPosition(*stored_position)
                                actor.SetOrientation(*stored_orientation)
                            else:
                                # Old format: just the transform object
                                actor.SetUserTransform(original_transform)
                                actor.SetOrientation(0, 0, 0)
                                actor.SetPosition(0, 0, 0)
                            actor.Modified()
                            print(f"DEBUG: BOTTOM Multi-actor {i} reset to original")

            # Reset single actor if it exists
            elif hasattr(self.vtk_renderer_right, 'step_actor') and self.vtk_renderer_right.step_actor:
                print("DEBUG: Resetting BOTTOM single actor")
                if hasattr(self, 'original_actor_transforms_right') and self.original_actor_transforms_right:
                    original_transform = self.original_actor_transforms_right[0]
                    if isinstance(original_transform, dict):
                        stored_transform = original_transform.get('transform')
                        stored_position = original_transform.get('position', (0, 0, 0))
                        stored_orientation = original_transform.get('orientation', (0, 0, 0))

                        self.vtk_renderer_right.step_actor.SetUserTransform(stored_transform)
                        self.vtk_renderer_right.step_actor.SetPosition(*stored_position)
                        self.vtk_renderer_right.step_actor.SetOrientation(*stored_orientation)
                    else:
                        self.vtk_renderer_right.step_actor.SetUserTransform(original_transform)
                        self.vtk_renderer_right.step_actor.SetOrientation(0, 0, 0)
                        self.vtk_renderer_right.step_actor.SetPosition(0, 0, 0)
                else:
                    # Fallback to identity transform
                    self.vtk_renderer_right.step_actor.SetUserTransform(transform)
                    self.vtk_renderer_right.step_actor.SetOrientation(0, 0, 0)
                    self.vtk_renderer_right.step_actor.SetPosition(0, 0, 0)
                self.vtk_renderer_right.step_actor.Modified()
                print("DEBUG: BOTTOM Single actor reset to original")

            # Reset camera to original position
            camera = self.vtk_renderer_right.renderer.GetActiveCamera()
            if hasattr(self, 'original_camera_right'):
                camera.SetPosition(*self.original_camera_right['position'])
                camera.SetFocalPoint(*self.original_camera_right['focal_point'])
                camera.SetViewUp(*self.original_camera_right['view_up'])
                print(f"DEBUG: BOTTOM Camera restored to original orientation: {self.original_camera_right['orientation']}")
            else:
                # Fallback to default position
                camera.SetPosition(0, 0, 1)
                camera.SetFocalPoint(0, 0, 0)
                camera.SetViewUp(0, 1, 0)
                self.vtk_renderer_right.renderer.ResetCamera()
                print("DEBUG: BOTTOM Camera reset to default position (no stored original)")
            print("DEBUG: BOTTOM Camera reset completed")

            # NOTE: Bounding box will be recreated AFTER origin fix to prevent removal

            # Force render to update display
            self.vtk_renderer_right.render_window.Render()

            # ALSO reset overlay if overlay mode is active
            if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_renderer'):
                print("TARGET Also resetting overlay actors for BOTTOM viewer")
                # Re-create overlay to reset all transformations
                self._create_overlay_widget()
                # Force render overlay
                if hasattr(self, 'vtk_widget_left'):
                    self.vtk_widget_left.GetRenderWindow().Render()

            # CRITICAL FIX: Delete ALL existing origin overlays first, then create proper ones
            print("TARGET RESET FIX: Manually clearing ALL origin overlays for BOTTOM viewer")

            # Step 1: Force clear ALL origin overlays (world origin)
            if hasattr(self.vtk_renderer_right, 'clear_origin_overlay'):
                self.vtk_renderer_right.clear_origin_overlay()
                print("OK RESET FIX: BOTTOM world origin overlay cleared")

            # Step 1b: Manually clear part origin overlays (since clear_part_origin_overlay doesn't exist)
            if hasattr(self.vtk_renderer_right, 'part_origin_actors'):
                print("TARGET RESET FIX: Manually removing part origin actors")
                for actor in self.vtk_renderer_right.part_origin_actors:
                    if actor and self.vtk_renderer_right.renderer:
                        self.vtk_renderer_right.renderer.RemoveActor(actor)
                        print("OK RESET FIX: Removed part origin actor")
                self.vtk_renderer_right.part_origin_actors = []  # Clear the list
                print("OK RESET FIX: BOTTOM part origin actors manually cleared")

            # Step 1c: Also check for individual part origin sphere
            if hasattr(self.vtk_renderer_right, 'part_origin_sphere') and self.vtk_renderer_right.part_origin_sphere:
                if self.vtk_renderer_right.renderer:
                    self.vtk_renderer_right.renderer.RemoveActor(self.vtk_renderer_right.part_origin_sphere)
                    print("OK RESET FIX: Removed part origin sphere")
                self.vtk_renderer_right.part_origin_sphere = None

            # Step 2: Create proper origin overlays at correct positions
            print("TARGET RESET FIX: Creating proper origin overlays for BOTTOM viewer")

            # Create world origin overlay (red markers at 0,0,0) - PREVENT DUPLICATES
            if hasattr(self.vtk_renderer_right, 'create_origin_overlay'):
                # Only create if origin_actors list is empty
                if not hasattr(self.vtk_renderer_right, 'origin_actors') or len(self.vtk_renderer_right.origin_actors) == 0:
                    self.vtk_renderer_right.create_origin_overlay()
                    print("OK RESET FIX: BOTTOM world origin overlay created at (0,0,0)")
                else:
                    print("WARN RESET FIX: Skipping BOTTOM world origin - already exists")

            # Create part origin overlay (green marker at STEP file origin)
            if hasattr(self, 'orig_pos_right') and hasattr(self.vtk_renderer_right, 'create_part_origin_overlay'):
                self.vtk_renderer_right.create_part_origin_overlay(
                    self.orig_pos_right['x'],
                    self.orig_pos_right['y'],
                    self.orig_pos_right['z'],
                    getattr(self, 'orig_z_direction_right', None),
                    getattr(self, 'orig_x_direction_right', None)
                )
                print(f"OK RESET FIX: BOTTOM part origin overlay created at ({self.orig_pos_right['x']:.3f}, {self.orig_pos_right['y']:.3f}, {self.orig_pos_right['z']:.3f})")

            # Force render to ensure overlays are visible
            self.vtk_renderer_right.render_window.Render()
            print("OK RESET FIX: BOTTOM viewer rendered with proper overlays")

            # NOW recreate bounding box AFTER origin fix (so it doesn't get removed)
            if self.bbox_visible_right:
                print("DEBUG RESET FIX: Recreating BOTTOM bounding box after origin fix")
                self.vtk_renderer_right.update_bounding_box()
                print("OK RESET FIX: BOTTOM bounding box recreated")

            print("Reset BOTTOM viewer to original")

        # Update the display values
        self.update_transform_display()
        self.statusBar().showMessage(f"{self.active_viewer.title()} reset to original")

    def align_bottom_center(self):
        """Align model to bottom-center: Position 0,0,0 and Rotation 0,0,0"""
        print("DEBUG: Align bottom-center called")

        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
            print("DEBUG: Aligning TOP viewer model to bottom-center")
        else:
            renderer = self.vtk_renderer_right
            print("DEBUG: Aligning BOTTOM viewer model to bottom-center")

        if not renderer:
            print("DEBUG: No renderer available")
            return

        # Reset multi-actors if they exist
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            print(f"DEBUG: Aligning {len(renderer.step_actors)} multi-actors to 0,0,0")
            for i, actor in enumerate(renderer.step_actors):
                if actor.GetVisibility():
                    print(f"DEBUG: Aligning visible actor {i} to position 0,0,0 and rotation 0,0,0")

                    # Set to exact position and rotation
                    actor.SetPosition(0, 0, 0)
                    actor.SetOrientation(0, 0, 0)
                    actor.SetUserTransform(None)  # Clear any transforms
                    actor.Modified()

                    print(f"DEBUG: Actor {i} aligned - Pos: {actor.GetPosition()}, Orient: {actor.GetOrientation()}")

        # Reset single actor if it exists
        elif hasattr(renderer, 'step_actor') and renderer.step_actor:
            print("DEBUG: Aligning single actor to 0,0,0")
            actor = renderer.step_actor
            actor.SetPosition(0, 0, 0)
            actor.SetOrientation(0, 0, 0)
            actor.SetUserTransform(None)
            actor.Modified()
            print(f"DEBUG: Single actor aligned - Pos: {actor.GetPosition()}, Orient: {actor.GetOrientation()}")

        # Update bounding box
        if self.active_viewer == "top" and self.bbox_visible_left:
            renderer.update_bounding_box()
        elif self.active_viewer == "bottom" and self.bbox_visible_right:
            renderer.update_bounding_box()

        # Update display values to show actual aligned coordinates
        if self.active_viewer == "top":
            # Keep the original position values (actual model coordinates)
            # Only reset rotation to 0,0,0 since we aligned rotation
            self.current_rot_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        else:
            # Keep the original position values (actual model coordinates)
            # Only reset rotation to 0,0,0 since we aligned rotation
            self.current_rot_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}

        # Reset camera to original position so user can see the aligned model
        print("DEBUG: Resetting camera to show aligned model")
        camera = renderer.renderer.GetActiveCamera()
        if self.active_viewer == "top" and hasattr(self, 'original_camera_left'):
            camera.SetPosition(*self.original_camera_left['position'])
            camera.SetFocalPoint(*self.original_camera_left['focal_point'])
            camera.SetViewUp(*self.original_camera_left['view_up'])
            print("DEBUG: Camera reset to original TOP position")
        elif self.active_viewer == "bottom" and hasattr(self, 'original_camera_right'):
            camera.SetPosition(*self.original_camera_right['position'])
            camera.SetFocalPoint(*self.original_camera_right['focal_point'])
            camera.SetViewUp(*self.original_camera_right['view_up'])
            print("DEBUG: Camera reset to original BOTTOM position")
        else:
            # Fallback to default camera position
            camera.SetPosition(0, 0, 10)
            camera.SetFocalPoint(0, 0, 0)
            camera.SetViewUp(0, 1, 0)
            renderer.renderer.ResetCamera()
            print("DEBUG: Camera reset to default position")

        # Force render
        renderer.render_window.Render()
        self.update_transform_display()

        print("DEBUG: Align bottom-center completed")
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Aligned to bottom-center (0,0,0)")

    def _is_actor_in_renderer(self, actor, vtk_renderer):
        """Check if an actor is actually in the VTK renderer"""
        if not actor or not vtk_renderer or not hasattr(vtk_renderer, 'renderer'):
            return False

        try:
            actor_collection = vtk_renderer.renderer.GetActors()
            actor_collection.InitTraversal()

            current_actor = actor_collection.GetNextActor()
            while current_actor:
                if current_actor == actor:
                    return True
                current_actor = actor_collection.GetNextActor()
            return False
        except:
            return False

    def rotate_shape(self, axis, degrees):
        """Rotate shape in active viewer - UNIFIED ROTATION METHOD"""
        print(f"🔄 UNIFIED ROTATION: {axis}+{degrees}° on {self.active_viewer} viewer")

        # Call the unified transformation method
        self._apply_unified_rotation(axis, degrees)

    def _apply_unified_rotation(self, axis, degrees):
        """UNIFIED METHOD: Apply rotation to active viewer and update display"""
        print(f"🔄 UNIFIED: Applying {degrees}° rotation on {axis}-axis to {self.active_viewer} viewer")

        # Step 1: Update rotation tracking values
        if self.active_viewer == "top":
            self.current_rot_left[axis] += degrees
            print(f"   Updated TOP {axis}: {self.current_rot_left[axis]:.1f}°")
        else:
            self.current_rot_right[axis] += degrees
            print(f"   Updated BOTTOM {axis}: {self.current_rot_right[axis]:.1f}°")

        # Step 2: Apply VTK rotation to 3D model
        self._apply_vtk_rotation(axis, degrees)

        # Step 3: Update yellow text display
        self._update_unified_display()

    def _apply_vtk_rotation(self, axis, degrees):
        """Apply VTK rotation to the 3D model actors"""
        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
        else:
            renderer = self.vtk_renderer_right

        # Apply rotation to ALL VTK actors (handles multi-color models)
        actors_rotated = False

        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            # Multi-color model with separate actors
            print(f"   Rotating {len(renderer.step_actors)} multi-actors by {degrees}° on {axis}-axis")
            for actor in renderer.step_actors:
                # Fix rotation direction - VTK uses opposite direction
                actor.RotateWXYZ(-degrees,  # Negate degrees to fix direction
                    1 if axis == 'x' else 0,
                    1 if axis == 'y' else 0,
                    1 if axis == 'z' else 0)
            actors_rotated = True
        elif hasattr(renderer, 'step_actor') and renderer.step_actor:
            # Single actor model
            print(f"   Rotating single actor by {degrees}° on {axis}-axis")
            renderer.step_actor.RotateWXYZ(-degrees,
                1 if axis == 'x' else 0,
                1 if axis == 'y' else 0,
                1 if axis == 'z' else 0)
            actors_rotated = True

        # Rotate origin actors to follow the model
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            print(f"   Rotating {len(renderer.origin_actors)} origin actors")
            for origin_actor in renderer.origin_actors:
                origin_actor.RotateWXYZ(-degrees,
                    1 if axis == 'x' else 0,
                    1 if axis == 'y' else 0,
                    1 if axis == 'z' else 0)

        # Update bounding box and render
        if self.active_viewer == "top" and self.bbox_visible_left:
            renderer.update_bounding_box()
        elif self.active_viewer == "bottom" and self.bbox_visible_right:
            renderer.update_bounding_box()

        renderer.render_window.Render()

    def _update_unified_display(self):
        """UNIFIED METHOD: Update yellow text display with current transformation values"""
        print(f"📊 UNIFIED: Updating display for {self.active_viewer} viewer")

        # Update the transform display labels
        self.update_transform_display()

        # Update VTK text overlays
        self.update_text_overlays()

        # Update status bar
        self.statusBar().showMessage(f"{self.active_viewer.upper()}: Display updated")

    def _apply_unified_movement(self, axis, amount):
                print(f"DEBUG: Rotating green part origin actors with model")

                # Get the model center for rotation (same as used for model actors above)
                if hasattr(self.vtk_renderer_left, 'step_actors') and self.vtk_renderer_left.step_actors:
                    first_actor = self.vtk_renderer_left.step_actors[0]
                    bounds = first_actor.GetBounds()
                    center_x = (bounds[0] + bounds[1]) / 2.0
                    center_y = (bounds[2] + bounds[3]) / 2.0
                    center_z = (bounds[4] + bounds[5]) / 2.0
                    print(f"DEBUG: Model center: ({center_x:.3f}, {center_y:.3f}, {center_z:.3f})")

                    # Get position BEFORE rotation
                    pos_before = self.vtk_renderer_left.part_origin_sphere.GetPosition()
                    print(f"DEBUG BEFORE: Green sphere at {pos_before}")

                    # MANUAL ROTATION CALCULATION: Calculate where the green sphere should be after rotation
                    import math

                    # Convert degrees to radians
                    angle_rad = math.radians(-degrees)

                    # Get current position relative to rotation center
                    rel_x = pos_before[0] - center_x
                    rel_y = pos_before[1] - center_y
                    rel_z = pos_before[2] - center_z

                    print(f"DEBUG: Relative position: ({rel_x:.3f}, {rel_y:.3f}, {rel_z:.3f})")

                    # Apply rotation matrix based on axis
                    if axis == 'x':
                        # Rotation around X-axis
                        new_rel_x = rel_x
                        new_rel_y = rel_y * math.cos(angle_rad) - rel_z * math.sin(angle_rad)
                        new_rel_z = rel_y * math.sin(angle_rad) + rel_z * math.cos(angle_rad)
                    elif axis == 'y':
                        # Rotation around Y-axis
                        new_rel_x = rel_x * math.cos(angle_rad) + rel_z * math.sin(angle_rad)
                        new_rel_y = rel_y
                        new_rel_z = -rel_x * math.sin(angle_rad) + rel_z * math.cos(angle_rad)
                    elif axis == 'z':
                        # Rotation around Z-axis
                        new_rel_x = rel_x * math.cos(angle_rad) - rel_y * math.sin(angle_rad)
                        new_rel_y = rel_x * math.sin(angle_rad) + rel_y * math.cos(angle_rad)
                        new_rel_z = rel_z

                    # Convert back to world coordinates
                    new_x = new_rel_x + center_x
                    new_y = new_rel_y + center_y
                    new_z = new_rel_z + center_z

                    print(f"DEBUG: Calculated new position: ({new_x:.3f}, {new_y:.3f}, {new_z:.3f})")

                    # CRITICAL FIX: Use ONLY UserTransform, NOT SetPosition
                    # SetPosition creates internal transform that gets concatenated with UserTransform
                    # This causes mouse interaction handler to read wrong values from UserTransform
                    user_transform = self.vtk_renderer_left.part_origin_sphere.GetUserTransform()
                    if user_transform:
                        # Update the existing UserTransform with new position
                        matrix = user_transform.GetMatrix()
                        matrix.SetElement(0, 3, new_x)  # Update X translation
                        matrix.SetElement(1, 3, new_y)  # Update Y translation
                        matrix.SetElement(2, 3, new_z)  # Update Z translation
                        user_transform.Modified()
                        print(f"DEBUG: Updated UserTransform position to ({new_x:.3f}, {new_y:.3f}, {new_z:.3f})")
                    else:
                        # Create new UserTransform if none exists (should not happen after loading)
                        import vtk
                        new_transform = vtk.vtkTransform()
                        new_transform.Translate(new_x, new_y, new_z)
                        # Re-apply stored orientation if available
                        if hasattr(self.vtk_renderer_left.part_origin_sphere, 'z_direction') and hasattr(self.vtk_renderer_left.part_origin_sphere, 'x_direction'):
                            z_direction = self.vtk_renderer_left.part_origin_sphere.z_direction
                            x_direction = self.vtk_renderer_left.part_origin_sphere.x_direction
                            if z_direction and x_direction:
                                import math
                                import numpy as np
                                z_vec = np.array(z_direction)
                                x_vec = np.array(x_direction)
                                # Calculate rotation angles from direction vectors
                                z_angle = math.degrees(math.atan2(z_vec[1], z_vec[0])) if abs(z_vec[0]) > 0.001 or abs(z_vec[1]) > 0.001 else 0
                                y_angle = math.degrees(math.asin(-z_vec[2])) if abs(z_vec[2]) < 1.0 else 0
                                x_angle = math.degrees(math.atan2(x_vec[2], x_vec[1])) if abs(x_vec[1]) > 0.001 or abs(x_vec[2]) > 0.001 else 0
                                new_transform.RotateX(x_angle)
                                new_transform.RotateY(y_angle)
                                new_transform.RotateZ(z_angle)
                        self.vtk_renderer_left.part_origin_sphere.SetUserTransform(new_transform)
                        print(f"DEBUG: Created new UserTransform with position ({new_x:.3f}, {new_y:.3f}, {new_z:.3f})")

                    # DO NOT call SetPosition - it creates internal transform that interferes with UserTransform
                    print(f"DEBUG: Updated green sphere UserTransform to ({new_x:.3f}, {new_y:.3f}, {new_z:.3f})")

                    # Do the same for arrows
                    for arrow_name in ['part_origin_x_arrow', 'part_origin_y_arrow', 'part_origin_z_arrow']:
                        if hasattr(self.vtk_renderer_left, arrow_name):
                            arrow = getattr(self.vtk_renderer_left, arrow_name)
                            if arrow:
                                arrow_pos = arrow.GetPosition()
                                # Calculate relative position for arrow
                                arrow_rel_x = arrow_pos[0] - center_x
                                arrow_rel_y = arrow_pos[1] - center_y
                                arrow_rel_z = arrow_pos[2] - center_z

                                # Apply same rotation
                                if axis == 'x':
                                    arrow_new_rel_x = arrow_rel_x
                                    arrow_new_rel_y = arrow_rel_y * math.cos(angle_rad) - arrow_rel_z * math.sin(angle_rad)
                                    arrow_new_rel_z = arrow_rel_y * math.sin(angle_rad) + arrow_rel_z * math.cos(angle_rad)
                                elif axis == 'y':
                                    arrow_new_rel_x = arrow_rel_x * math.cos(angle_rad) + arrow_rel_z * math.sin(angle_rad)
                                    arrow_new_rel_y = arrow_rel_y
                                    arrow_new_rel_z = -arrow_rel_x * math.sin(angle_rad) + arrow_rel_z * math.cos(angle_rad)
                                elif axis == 'z':
                                    arrow_new_rel_x = arrow_rel_x * math.cos(angle_rad) - arrow_rel_y * math.sin(angle_rad)
                                    arrow_new_rel_y = arrow_rel_x * math.sin(angle_rad) + arrow_rel_y * math.cos(angle_rad)
                                    arrow_new_rel_z = arrow_rel_z

                                # Set new arrow position
                                arrow.SetPosition(arrow_new_rel_x + center_x, arrow_new_rel_y + center_y, arrow_new_rel_z + center_z)

                    # Get position AFTER rotation from UserTransform (not GetPosition)
                    user_transform = self.vtk_renderer_left.part_origin_sphere.GetUserTransform()
                    if user_transform:
                        matrix = user_transform.GetMatrix()
                        pos_after = [matrix.GetElement(0, 3), matrix.GetElement(1, 3), matrix.GetElement(2, 3)]
                    else:
                        pos_after = self.vtk_renderer_left.part_origin_sphere.GetPosition()
                    print(f"DEBUG AFTER: Green sphere at {pos_after}")

                    # Calculate actual movement
                    movement = [pos_after[i] - pos_before[i] for i in range(3)]
                    total_movement = sum(abs(m) for m in movement)
                    print(f"DEBUG: Green sphere moved {total_movement:.3f} units: ({movement[0]:.3f}, {movement[1]:.3f}, {movement[2]:.3f})")

                    if total_movement > 0.001:
                        print("✅ SUCCESS: Green markers rotated with model")

                        # CRITICAL FIX: Update current_pos_left to match the new visual position
                        # This ensures the yellow text display shows the actual transformed position
                        print(f"DEBUG POSITION UPDATE: Updating current_pos_left from visual position")
                        print(f"   BEFORE: current_pos_left = {self.current_pos_left}")

                        # Update current_pos_left to match the transformed green sphere position
                        self.current_pos_left['x'] = pos_after[0]
                        self.current_pos_left['y'] = pos_after[1]
                        self.current_pos_left['z'] = pos_after[2]

                        print(f"   AFTER: current_pos_left = {self.current_pos_left}")
                        print("✅ FIXED: Origin position values now match visual display")
                    else:
                        print("❌ PROBLEM: Green markers did not move during rotation")
                else:
                    print("ERROR: No model actors found for rotation center")

            print(f"DEBUG: Green origin marker rotation completed")

            print("DEBUG: World origin actors (red) and STEP file origin actors (green) both rotate with model")

            # Update bounding box to follow rotation - FORCE RECREATION
            if self.bbox_visible_left:
                print("DEBUG: FORCE recreating bounding box after rotation")
                # Remove old bounding box completely
                if hasattr(self.vtk_renderer_left, 'bbox_actor') and self.vtk_renderer_left.bbox_actor:
                    self.vtk_renderer_left.renderer.RemoveActor(self.vtk_renderer_left.bbox_actor)
                    self.vtk_renderer_left.bbox_actor = None
                # Force recreation of bounding box to match transformed model
                self.vtk_renderer_left.toggle_bounding_box(True)
            # Don't call fit_view during rotation to prevent jumping
            self.vtk_renderer_left.render_window.Render()
            # Force update text overlays to show new rotation values
            self.update_text_overlays()
            # ALSO rotate overlay actors if overlay mode is active
            if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_renderer'):
                print(f"DEBUG: Also rotating overlay actors by {degrees}deg on {axis}-axis")
                actors = self.overlay_renderer.GetActors()
                actors.InitTraversal()
                actor = actors.GetNextActor()
                while actor:
                    actor.RotateWXYZ(-degrees,  # Same direction as main actors
                        1 if axis == 'x' else 0,
                        1 if axis == 'y' else 0,
                        1 if axis == 'z' else 0)
                    actor = actors.GetNextActor()
                # Force render overlay
                if hasattr(self, 'vtk_widget_left'):
                    self.vtk_widget_left.GetRenderWindow().Render()
            # No need to call update_combined_rotation since we're directly updating current_rot_left
        else:
            # Add to current rotation values (includes both model and camera rotation)
            if hasattr(self, 'current_rot_right'):
                self.current_rot_right[axis] += degrees
                print(f"DEBUG: Button rotation - BOTTOM {axis} += {degrees}deg = {self.current_rot_right[axis]:.1f}deg")
            else:
                # Fallback if current_rot_right doesn't exist
                self.model_rot_right[axis] += degrees

            # Apply actual rotation to ALL VTK actors (handles multi-color models)
            actors_rotated = False

            if hasattr(self.vtk_renderer_right, 'step_actors') and self.vtk_renderer_right.step_actors:
                # Multi-color model with separate actors
                print(f"DEBUG: Rotating {len(self.vtk_renderer_right.step_actors)} multi-actors by {degrees}deg on {axis}-axis")
                for actor in self.vtk_renderer_right.step_actors:
                    # Fix rotation direction - VTK uses opposite direction
                    actor.RotateWXYZ(-degrees,  # Negate degrees to fix direction
                        1 if axis == 'x' else 0,
                        1 if axis == 'y' else 0,
                        1 if axis == 'z' else 0)
                actors_rotated = True

                # ALSO rotate overlay BOTTOM actors if overlay mode is active
                if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_bottom_actors'):
                    print(f"DEBUG: Also rotating {len(self.overlay_bottom_actors)} overlay BOTTOM actors by {degrees}deg on {axis}-axis")
                    for overlay_actor in self.overlay_bottom_actors:
                        overlay_actor.RotateWXYZ(-degrees,  # Same direction as main actors
                            1 if axis == 'x' else 0,
                            1 if axis == 'y' else 0,
                            1 if axis == 'z' else 0)

            if (hasattr(self.vtk_renderer_right, 'step_actor') and
                self.vtk_renderer_right.step_actor and
                self._is_actor_in_renderer(self.vtk_renderer_right.step_actor, self.vtk_renderer_right)):
                # Single actor model - ONLY rotate if it's actually in the renderer
                print(f"DEBUG: Rotating single actor by {degrees}deg on {axis}-axis")
                # Fix rotation direction - VTK uses opposite direction
                self.vtk_renderer_right.step_actor.RotateWXYZ(-degrees,  # Negate degrees to fix direction
                    1 if axis == 'x' else 0,
                    1 if axis == 'y' else 0,
                    1 if axis == 'z' else 0)
                actors_rotated = True

                # ALSO rotate overlay BOTTOM actors if overlay mode is active (single actor case)
                if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_bottom_actors'):
                    print(f"DEBUG: Also rotating {len(self.overlay_bottom_actors)} overlay BOTTOM single actors by {degrees}deg on {axis}-axis")
                    for overlay_actor in self.overlay_bottom_actors:
                        overlay_actor.RotateWXYZ(-degrees,  # Same direction as main actors
                            1 if axis == 'x' else 0,
                            1 if axis == 'y' else 0,
                            1 if axis == 'z' else 0)

            elif hasattr(self.vtk_renderer_right, 'step_actor') and self.vtk_renderer_right.step_actor:
                print(f"DEBUG: Skipping single actor rotation - not in renderer")

            if not actors_rotated:
                print(f"DEBUG: No actors found to rotate!")

            # CREATE/UPDATE USER TRANSFORM for Option 1 save detection
            if actors_rotated:
                self._create_user_transform_for_save("bottom", axis, degrees)

            # ALSO rotate origin actors to follow the model (FIXED: use RotateWXYZ like mouse rotation)
            if hasattr(self.vtk_renderer_right, 'origin_actors') and self.vtk_renderer_right.origin_actors:
                print(f"DEBUG: Rotating {len(self.vtk_renderer_right.origin_actors)} origin actors by {degrees}deg on {axis}-axis")

                # Apply the SAME rotation to origin actors as applied to model actors
                # This matches the mouse rotation behavior from ORIGIN_BBOX_MOUSE_ROTATION_FIX_COMPLETE.md
                for origin_actor in self.vtk_renderer_right.origin_actors:
                    # Use same rotation direction as model actors (negated degrees)
                    origin_actor.RotateWXYZ(-degrees,  # Same direction as model
                        1 if axis == 'x' else 0,
                        1 if axis == 'y' else 0,
                        1 if axis == 'z' else 0)



            # ALSO rotate part origin actors (actual STEP file origin) to follow the model
            if hasattr(self.vtk_renderer_right, 'part_origin_sphere') and self.vtk_renderer_right.part_origin_sphere:
                print(f"DEBUG: Rotating STEP file origin actors (green sphere/arrows) by {degrees}deg on {axis}-axis")

                # Get position BEFORE rotation for comparison
                pos_before = self.vtk_renderer_right.part_origin_sphere.GetPosition()
                print(f"DEBUG BEFORE: BOTTOM green sphere at {pos_before}")

                part_origin_actors = [
                    self.vtk_renderer_right.part_origin_sphere,
                    self.vtk_renderer_right.part_origin_x_arrow,
                    self.vtk_renderer_right.part_origin_y_arrow,
                    self.vtk_renderer_right.part_origin_z_arrow
                ]
                for part_actor in part_origin_actors:
                    if part_actor:
                        part_actor.RotateWXYZ(-degrees,  # Same direction as model actors
                            1 if axis == 'x' else 0,
                            1 if axis == 'y' else 0,
                            1 if axis == 'z' else 0)

                # Get position AFTER rotation
                pos_after = self.vtk_renderer_right.part_origin_sphere.GetPosition()
                print(f"DEBUG AFTER: BOTTOM green sphere at {pos_after}")

                # Calculate movement
                movement = [pos_after[i] - pos_before[i] for i in range(3)]
                total_movement = sum(abs(m) for m in movement)
                print(f"DEBUG: BOTTOM green sphere moved {total_movement:.3f} units: ({movement[0]:.3f}, {movement[1]:.3f}, {movement[2]:.3f})")

                if total_movement > 0.001:
                    print("✅ SUCCESS: BOTTOM green markers rotated with model")

                    # CRITICAL FIX: Update current_pos_right to match the new visual position
                    # This ensures the yellow text display shows the actual transformed position
                    print(f"DEBUG POSITION UPDATE: Updating current_pos_right from visual position")
                    print(f"   BEFORE: current_pos_right = {self.current_pos_right}")

                    # Update current_pos_right to match the transformed green sphere position
                    self.current_pos_right['x'] = pos_after[0]
                    self.current_pos_right['y'] = pos_after[1]
                    self.current_pos_right['z'] = pos_after[2]

                    print(f"   AFTER: current_pos_right = {self.current_pos_right}")
                    print("✅ FIXED: BOTTOM origin position values now match visual display")
                else:
                    print("❌ PROBLEM: BOTTOM green markers did not move during rotation")



            print("DEBUG: World origin actors (red) and STEP file origin actors (green) both rotate with model")

            # Update bounding box to follow rotation - FORCE RECREATION
            if self.bbox_visible_right:
                print("DEBUG: FORCE recreating bounding box after rotation")
                # Remove old bounding box completely
                if hasattr(self.vtk_renderer_right, 'bbox_actor') and self.vtk_renderer_right.bbox_actor:
                    self.vtk_renderer_right.renderer.RemoveActor(self.vtk_renderer_right.bbox_actor)
                    self.vtk_renderer_right.bbox_actor = None
                # Force recreation of bounding box to match transformed model
                self.vtk_renderer_right.toggle_bounding_box(True)
            # Don't call fit_view during rotation to prevent jumping
            self.vtk_renderer_right.render_window.Render()
            # Force update text overlays to show new rotation values
            self.update_text_overlays()
            # Update overlay content if overlay mode is active
            self.update_overlay_content()
            # No need to call update_combined_rotation since we're directly updating current_rot_right

        self.update_transform_display()
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Rotated {degrees}deg around {axis}")

    def _update_unified_display(self):
        """UNIFIED METHOD: Update yellow text display with current transformation values"""
        print(f"📊 UNIFIED: Updating display for {self.active_viewer} viewer")

        # Update the transform display labels
        self.update_transform_display()

        # Update VTK text overlays
        self.update_text_overlays()

        # Update status bar
        self.statusBar().showMessage(f"{self.active_viewer.upper()}: Display updated")

    def _apply_unified_movement(self, axis, amount):
        """UNIFIED METHOD: Apply movement to active viewer and update display"""
        print(f"🔄 UNIFIED: Moving {amount}mm on {axis}-axis in {self.active_viewer} viewer")

        # Step 1: Update position tracking values
        if self.active_viewer == "top":
            self.current_pos_left[axis] += amount
            print(f"   Updated TOP {axis}: {self.current_pos_left[axis]:.3f}mm")
        else:
            self.current_pos_right[axis] += amount
            print(f"   Updated BOTTOM {axis}: {self.current_pos_right[axis]:.3f}mm")

        # Step 2: Apply VTK movement to 3D model
        self._apply_vtk_movement(axis, amount)

        # Step 3: Update yellow text display
        self._update_unified_display()

    def _apply_vtk_movement(self, axis, amount):
        """Apply VTK movement to the 3D model actors"""
        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
        else:
            renderer = self.vtk_renderer_right

        # Apply movement to ALL VTK actors
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            print(f"   Moving {len(renderer.step_actors)} multi-actors by {amount}mm on {axis}-axis")
            for actor in renderer.step_actors:
                current_pos = list(actor.GetPosition())
                if axis == 'x':
                    current_pos[0] += amount
                elif axis == 'y':
                    current_pos[1] += amount
                elif axis == 'z':
                    current_pos[2] += amount
                actor.SetPosition(current_pos)
        elif hasattr(renderer, 'step_actor') and renderer.step_actor:
            print(f"   Moving single actor by {amount}mm on {axis}-axis")
            current_pos = list(renderer.step_actor.GetPosition())
            if axis == 'x':
                current_pos[0] += amount
            elif axis == 'y':
                current_pos[1] += amount
            elif axis == 'z':
                current_pos[2] += amount
            renderer.step_actor.SetPosition(current_pos)

        # Update bounding box and render
        if self.active_viewer == "top" and self.bbox_visible_left:
            renderer.update_bounding_box()
        elif self.active_viewer == "bottom" and self.bbox_visible_right:
            renderer.update_bounding_box()

        renderer.render_window.Render()

    def toggle_bbox_overlay(self):
        """Toggle bounding box in active viewer"""
        if self.active_viewer == "top":
            self.bbox_visible_left = not self.bbox_visible_left
            if self.vtk_renderer_left:
                self.vtk_renderer_left.toggle_bounding_box(self.bbox_visible_left)
            status = "shown" if self.bbox_visible_left else "hidden"
            print(f"TOP bounding box {status}")
        else:
            self.bbox_visible_right = not self.bbox_visible_right
            if self.vtk_renderer_right:
                self.vtk_renderer_right.toggle_bounding_box(self.bbox_visible_right)
            status = "shown" if self.bbox_visible_right else "hidden"
            print(f"BOTTOM bounding box {status}")

        self.statusBar().showMessage(f"{self.active_viewer.title()}: Bounding box {status}")

    def force_view_update(self):
        """Force update of transform display - refreshes the position/rotation numbers"""
        print("DEBUG DEBUG: force_view_update() called!")
        self.update_transform_display()
        self.statusBar().showMessage("Transform display refreshed - numbers updated from current view")

    def update_transform_display(self):
        """Update the transform display labels for both viewers"""
        print("DEBUG DEEP DEBUG: update_transform_display() called!")

        # DEEP DEBUG: Check what values we have
        if hasattr(self, 'orig_rot_left'):
            print(f"DEBUG DEEP DEBUG: orig_rot_left = {self.orig_rot_left}")
        else:
            print("DEBUG DEEP DEBUG: orig_rot_left NOT SET!")

        if hasattr(self, 'orig_rot_right'):
            print(f"DEBUG DEEP DEBUG: orig_rot_right = {self.orig_rot_right}")
        else:
            print("DEBUG DEEP DEBUG: orig_rot_right NOT SET!")

        # Keep the original rotation tracking (don't read camera values for display)

        # Update TOP viewer labels with 3 decimal precision
        if hasattr(self, 'lbl_orig_pos_x'):
            self.lbl_orig_pos_x.setText(f"X: {self.orig_pos_left['x']:.3f}")
            self.lbl_orig_pos_y.setText(f"Y: {self.orig_pos_left['y']:.3f}")
            self.lbl_orig_pos_z.setText(f"Z: {self.orig_pos_left['z']:.3f}")

        # Update rotation labels using correct label names
        if hasattr(self, 'lbl_orig_angle') and hasattr(self, 'orig_rot_left'):
            # Calculate total angle magnitude
            import math
            total_angle = math.sqrt(self.orig_rot_left['x']**2 + self.orig_rot_left['y']**2 + self.orig_rot_left['z']**2)

            # Set angle label
            self.lbl_orig_angle.setText(f"{total_angle:.1f}deg")

            # Set axis labels
            if hasattr(self, 'lbl_orig_axis_x'):
                self.lbl_orig_axis_x.setText(f"X: {self.orig_rot_left['x']:.2f}")
                self.lbl_orig_axis_y.setText(f"Y: {self.orig_rot_left['y']:.2f}")
                self.lbl_orig_axis_z.setText(f"Z: {self.orig_rot_left['z']:.2f}")

        if hasattr(self, 'lbl_curr_pos_x'):
            self.lbl_curr_pos_x.setText(f"X: {self.current_pos_left['x']:.3f}")
            self.lbl_curr_pos_y.setText(f"Y: {self.current_pos_left['y']:.3f}")
            self.lbl_curr_pos_z.setText(f"Z: {self.current_pos_left['z']:.3f}")

        if hasattr(self, 'lbl_curr_rot_x'):
            self.lbl_curr_rot_x.setText(f"X: {self.current_rot_left['x']:.3f}deg")
            self.lbl_curr_rot_y.setText(f"Y: {self.current_rot_left['y']:.3f}deg")
            self.lbl_curr_rot_z.setText(f"Z: {self.current_rot_left['z']:.3f}deg")

        # Keep the original rotation tracking (don't read camera values for display)

        # Update BOTTOM viewer labels with 3 decimal precision
        if hasattr(self, 'lbl_orig_pos_x_bottom'):
            self.lbl_orig_pos_x_bottom.setText(f"X: {self.orig_pos_right['x']:.3f}")
            self.lbl_orig_pos_y_bottom.setText(f"Y: {self.orig_pos_right['y']:.3f}")
            self.lbl_orig_pos_z_bottom.setText(f"Z: {self.orig_pos_right['z']:.3f}")

        # Update BOTTOM rotation labels using correct label names
        if hasattr(self, 'lbl_orig_angle_bottom') and hasattr(self, 'orig_rot_right'):
            # Calculate total angle magnitude
            import math
            total_angle = math.sqrt(self.orig_rot_right['x']**2 + self.orig_rot_right['y']**2 + self.orig_rot_right['z']**2)

            # Set angle label
            self.lbl_orig_angle_bottom.setText(f"{total_angle:.1f}deg")

            # Set axis labels
            if hasattr(self, 'lbl_orig_axis_x_bottom'):
                self.lbl_orig_axis_x_bottom.setText(f"X: {self.orig_rot_right['x']:.2f}")
                self.lbl_orig_axis_y_bottom.setText(f"Y: {self.orig_rot_right['y']:.2f}")
                self.lbl_orig_axis_z_bottom.setText(f"Z: {self.orig_rot_right['z']:.2f}")

        if hasattr(self, 'lbl_curr_pos_x_bottom'):
            self.lbl_curr_pos_x_bottom.setText(f"X: {self.current_pos_right['x']:.3f}")
            self.lbl_curr_pos_y_bottom.setText(f"Y: {self.current_pos_right['y']:.3f}")
            self.lbl_curr_pos_z_bottom.setText(f"Z: {self.current_pos_right['z']:.3f}")

        if hasattr(self, 'lbl_curr_rot_x_bottom'):
            self.lbl_curr_rot_x_bottom.setText(f"X: {self.current_rot_right['x']:.3f}deg")
            self.lbl_curr_rot_y_bottom.setText(f"Y: {self.current_rot_right['y']:.3f}deg")
            self.lbl_curr_rot_z_bottom.setText(f"Z: {self.current_rot_right['z']:.3f}deg")

        # Update VTK text overlays
        self.update_text_overlays()

    def update_original_labels(self, axis_data, viewer="top"):
        """Update GUI labels with AXIS2_PLACEMENT_3D data from STEP file for both viewers"""
        print(f"DEBUG update_original_labels() called for {viewer} viewer with axis_data:")
        print(f"   Point: {axis_data['point']}")
        print(f"   Dir1: {axis_data['dir1']}")
        print(f"   Dir2: {axis_data['dir2']}")

        try:
            # Parse Dir1 (Direction line - line 2)
            if isinstance(axis_data['dir1'], (tuple, list)) and len(axis_data['dir1']) == 3:
                dir1_values = axis_data['dir1']
            else:
                # Parse string format like "(0.0, 0.9104, -0.4138)"
                dir1_str = str(axis_data['dir1']).strip('()')
                dir1_values = [float(x.strip()) for x in dir1_str.split(',')]

            # Parse Dir2 (REF. Direction line - line 3)
            if isinstance(axis_data['dir2'], (tuple, list)) and len(axis_data['dir2']) == 3:
                dir2_values = axis_data['dir2']
            else:
                # Parse string format like "(0.0, 0.4138, 0.9104)"
                dir2_str = str(axis_data['dir2']).strip('()')
                dir2_values = [float(x.strip()) for x in dir2_str.split(',')]

            if viewer == "top":
                # Update TOP Direction labels (Dir1)
                if hasattr(self, 'lbl_orig_axis_x'):
                    self.lbl_orig_axis_x.setText(f"X: {dir1_values[0]:.4f}")
                    self.lbl_orig_axis_y.setText(f"Y: {dir1_values[1]:.4f}")
                    self.lbl_orig_axis_z.setText(f"Z: {dir1_values[2]:.4f}")
                    print(f"OK Updated TOP Direction labels (Dir1): X={dir1_values[0]:.4f}, Y={dir1_values[1]:.4f}, Z={dir1_values[2]:.4f}")
                else:
                    print("FAIL TOP Direction labels (lbl_orig_axis_*) not found")

                # Update TOP REF. Direction labels (Dir2)
                if hasattr(self, 'lbl_orig_ref_x'):
                    self.lbl_orig_ref_x.setText(f"X: {dir2_values[0]:.4f}")
                    self.lbl_orig_ref_y.setText(f"Y: {dir2_values[1]:.4f}")
                    self.lbl_orig_ref_z.setText(f"Z: {dir2_values[2]:.4f}")
                    print(f"OK Updated TOP REF. Direction labels (Dir2): X={dir2_values[0]:.4f}, Y={dir2_values[1]:.4f}, Z={dir2_values[2]:.4f}")
                else:
                    print("FAIL TOP REF. Direction labels (lbl_orig_ref_*) not found")

            elif viewer == "bottom":
                # Update BOTTOM Direction labels (Dir1)
                if hasattr(self, 'lbl_orig_axis_x_bottom'):
                    self.lbl_orig_axis_x_bottom.setText(f"X: {dir1_values[0]:.4f}")
                    self.lbl_orig_axis_y_bottom.setText(f"Y: {dir1_values[1]:.4f}")
                    self.lbl_orig_axis_z_bottom.setText(f"Z: {dir1_values[2]:.4f}")
                    print(f"OK Updated BOTTOM Direction labels (Dir1): X={dir1_values[0]:.4f}, Y={dir1_values[1]:.4f}, Z={dir1_values[2]:.4f}")
                else:
                    print("FAIL BOTTOM Direction labels (lbl_orig_axis_*_bottom) not found")

                # Update BOTTOM REF. Direction labels (Dir2)
                if hasattr(self, 'lbl_orig_ref_x_bottom'):
                    self.lbl_orig_ref_x_bottom.setText(f"X: {dir2_values[0]:.4f}")
                    self.lbl_orig_ref_y_bottom.setText(f"Y: {dir2_values[1]:.4f}")
                    self.lbl_orig_ref_z_bottom.setText(f"Z: {dir2_values[2]:.4f}")
                    print(f"OK Updated BOTTOM REF. Direction labels (Dir2): X={dir2_values[0]:.4f}, Y={dir2_values[1]:.4f}, Z={dir2_values[2]:.4f}")
                else:
                    print("FAIL BOTTOM REF. Direction labels (lbl_orig_ref_*_bottom) not found")

        except Exception as e:
            print(f"FAIL Error updating {viewer} labels: {e}")
            import traceback
            traceback.print_exc()

    def update_original_top_labels(self, axis_data):
        """Backward compatibility wrapper"""
        self.update_original_labels(axis_data, "top")

    def _extract_step_coordinate_system(self, filename):
        """Extract coordinate system from STEP file dynamically"""
        try:
            if not filename or not os.path.exists(filename):
                print(f"DEBUG STEP file not found: {filename}")
                return None, None

            with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            import re

            # Find ANY AXIS2_PLACEMENT_3D that might contain transformations
            # Try multiple patterns to be more robust for saved files
            axis_match = None

            # Pattern 1: Try #11 (common in original files)
            axis_match = re.search(r'#11 = AXIS2_PLACEMENT_3D\(\'\',(#\d+),(#\d+),(#\d+)\);', content)

            # Pattern 2: Try any AXIS2_PLACEMENT_3D with non-standard directions (indicates transformation)
            if not axis_match:
                all_axis_matches = re.findall(r'(#\d+) = AXIS2_PLACEMENT_3D\(\'\',(#\d+),(#\d+),(#\d+)\);', content)
                for axis_id, origin_ref, z_dir_ref, x_dir_ref in all_axis_matches:
                    # Check if this axis has non-standard directions (indicating transformation)
                    z_dir_pattern = f'{z_dir_ref} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
                    z_dir_match = re.search(z_dir_pattern, content)
                    if z_dir_match:
                        z_coords = [float(x.strip()) for x in z_dir_match.group(1).split(',')]
                        # If Z direction is not (0,0,1), this axis has transformations
                        if not (abs(z_coords[0]) < 0.001 and abs(z_coords[1]) < 0.001 and abs(z_coords[2] - 1.0) < 0.001):
                            print(f"DEBUG Found transformed AXIS2_PLACEMENT_3D: {axis_id} with Z direction {z_coords}")
                            axis_match = type('Match', (), {
                                'group': lambda self, n: [axis_id, origin_ref, z_dir_ref, x_dir_ref][n-1]
                            })()
                            break

            # Pattern 3: Fallback to first AXIS2_PLACEMENT_3D found
            if not axis_match and all_axis_matches:
                axis_id, origin_ref, z_dir_ref, x_dir_ref = all_axis_matches[0]
                print(f"DEBUG Using first AXIS2_PLACEMENT_3D found: {axis_id}")
                axis_match = type('Match', (), {
                    'group': lambda self, n: [axis_id, origin_ref, z_dir_ref, x_dir_ref][n-1]
                })()

            if not axis_match:
                print(f"DEBUG Could not find any AXIS2_PLACEMENT_3D in {filename}")
                return None, None

            origin_ref = axis_match.group(1)
            z_dir_ref = axis_match.group(2)
            x_dir_ref = axis_match.group(3)

            # Extract origin point
            origin_pattern = f'{origin_ref} = CARTESIAN_POINT\\(\'\'\\,\\(([^)]+)\\)\\);'
            origin_match = re.search(origin_pattern, content)
            if origin_match:
                coords = [float(x.strip()) for x in origin_match.group(1).split(',')]
                orig_pos = {'x': coords[0], 'y': coords[1], 'z': coords[2]}
            else:
                orig_pos = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            # Extract Z direction
            z_dir_pattern = f'{z_dir_ref} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
            z_dir_match = re.search(z_dir_pattern, content)
            if z_dir_match:
                z_direction = [float(x.strip()) for x in z_dir_match.group(1).split(',')]
            else:
                z_direction = [0, 0, 1]

            # Extract X direction
            x_dir_pattern = f'{x_dir_ref} = DIRECTION\\(\'\'\\,\\(([^)]+)\\)\\);'
            x_dir_match = re.search(x_dir_pattern, content)
            if x_dir_match:
                x_direction = [float(x.strip()) for x in x_dir_match.group(1).split(',')]
            else:
                x_direction = [1, 0, 0]

            # Calculate rotation from direction vectors
            import math

            # Improved rotation calculation from direction vectors
            # Check if this is a standard orientation (no rotation)
            if (abs(z_direction[0]) < 0.001 and abs(z_direction[1]) < 0.001 and abs(z_direction[2] - 1.0) < 0.001 and
                abs(x_direction[0] - 1.0) < 0.001 and abs(x_direction[1]) < 0.001 and abs(x_direction[2]) < 0.001):
                # Standard orientation - no rotation
                orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                print(f"DEBUG Standard orientation detected - no rotation")
            else:
                # Calculate rotation angles from transformed direction vectors
                # For X rotation (rotation around X axis), look at Z direction Y and Z components
                # Use negative to match the expected sign convention
                x_rot = -math.degrees(math.atan2(z_direction[1], z_direction[2]))

                # For Y rotation (rotation around Y axis), look at Z direction X component
                y_rot = math.degrees(math.atan2(-z_direction[0], math.sqrt(z_direction[1]**2 + z_direction[2]**2)))

                # For Z rotation (rotation around Z axis), look at X direction X and Y components
                z_rot = math.degrees(math.atan2(x_direction[1], x_direction[0]))

                orig_rot = {'x': x_rot, 'y': y_rot, 'z': z_rot}
                print(f"DEBUG Transformed orientation detected:")
                print(f"   X rotation: {x_rot:.1f}deg (from Z direction Y,Z components)")
                print(f"   Y rotation: {y_rot:.1f}deg (from Z direction X component)")
                print(f"   Z rotation: {z_rot:.1f}deg (from X direction X,Y components)")

            print(f"DEBUG EXTRACTED from {filename}:")
            print(f"  Origin: {orig_pos}")
            print(f"  Z Direction: {z_direction}")
            print(f"  X Direction: {x_direction}")
            print(f"  Calculated Rotation: {orig_rot}")

            # FIXED: Return direction vectors for green sphere orientation
            return orig_pos, orig_rot, z_direction, x_direction

        except Exception as e:
            print(f"DEBUG Error extracting STEP coordinate system: {e}")
            return None, None

    def _transform_direction_vector(self, original_vector, rotation_angles):
        """Transform a direction vector by rotation angles (in degrees)"""
        import math
        import numpy as np

        # Convert degrees to radians
        rx = math.radians(rotation_angles['x'])
        ry = math.radians(rotation_angles['y'])
        rz = math.radians(rotation_angles['z'])

        # Create rotation matrices
        # Rotation around X-axis
        Rx = np.array([
            [1, 0, 0],
            [0, math.cos(rx), -math.sin(rx)],
            [0, math.sin(rx), math.cos(rx)]
        ])

        # Rotation around Y-axis
        Ry = np.array([
            [math.cos(ry), 0, math.sin(ry)],
            [0, 1, 0],
            [-math.sin(ry), 0, math.cos(ry)]
        ])

        # Rotation around Z-axis
        Rz = np.array([
            [math.cos(rz), -math.sin(rz), 0],
            [math.sin(rz), math.cos(rz), 0],
            [0, 0, 1]
        ])

        # Combined rotation matrix (order: Z * Y * X)
        R = Rz @ Ry @ Rx

        # Transform the vector
        original = np.array(original_vector)
        transformed = R @ original

        return transformed.tolist()

    def _get_actual_step_file_values(self, viewer="top"):
        """Extract actual mathematical values from VTK actor that would be written to STEP file"""
        try:
            import vtk
            import numpy as np
            import math

            # Get the appropriate VTK renderer
            if viewer == "top":
                renderer = self.vtk_renderer_left
            else:
                renderer = self.vtk_renderer_right

            # Get the first available actor (multi-actor or single actor)
            actor = None
            if hasattr(renderer, 'step_actors') and renderer.step_actors:
                actor = renderer.step_actors[0]  # Use first multi-actor
            elif hasattr(renderer, 'step_actor') and renderer.step_actor:
                actor = renderer.step_actor  # Use single actor

            if not actor:
                print(f"FAIL No VTK actor found for {viewer} viewer")
                return None

            # Get the actor's current transformation matrix
            transform = vtk.vtkTransform()
            if actor.GetUserTransform():
                transform.DeepCopy(actor.GetUserTransform())
            else:
                # Get transformation from position and orientation
                transform.Translate(*actor.GetPosition())
                transform.RotateX(actor.GetOrientation()[0])
                transform.RotateY(actor.GetOrientation()[1])
                transform.RotateZ(actor.GetOrientation()[2])

            # Get the 4x4 transformation matrix
            matrix = transform.GetMatrix()

            # Extract rotation part (3x3 upper-left submatrix)
            rotation_matrix = np.zeros((3, 3))
            for i in range(3):
                for j in range(3):
                    rotation_matrix[i, j] = matrix.GetElement(i, j)

            # Extract translation (position)
            position = [matrix.GetElement(0, 3), matrix.GetElement(1, 3), matrix.GetElement(2, 3)]

            # Convert rotation matrix to direction vectors (like STEP file AXIS2_PLACEMENT_3D)
            # Dir1 = Z-axis of the transformed coordinate system
            dir1 = [rotation_matrix[0, 2], rotation_matrix[1, 2], rotation_matrix[2, 2]]
            # Dir2 = X-axis of the transformed coordinate system
            dir2 = [rotation_matrix[0, 0], rotation_matrix[1, 0], rotation_matrix[2, 0]]

            print(f"DEBUG EXTRACTED ACTUAL VALUES from {viewer} VTK actor:")
            print(f"   Position: {position}")
            print(f"   Dir1 (Z-axis): {dir1}")
            print(f"   Dir2 (X-axis): {dir2}")

            return {
                'position': position,
                'dir1': dir1,
                'dir2': dir2
            }

        except Exception as e:
            print(f"FAIL Error extracting actual STEP values from {viewer} actor: {e}")
            return None

    def update_text_overlays(self):
        """Update VTK text overlays on both viewers"""
        import traceback
        print("DEBUG DEEP DEBUG: update_text_overlays() called!")
        print(f"DEBUG DEEP DEBUG: Called from: {traceback.format_stack()[-2].strip()}")
        if hasattr(self, 'current_pos_left'):
            print(f"DEBUG DEEP DEBUG: current_pos_left = {self.current_pos_left}")
        # Update TOP viewer text - CORRECTED FORMAT AND CALCULATION
        if hasattr(self, 'combined_text_actor_left'):
            if hasattr(self, 'orig_pos_left') and hasattr(self, 'current_rot_left'):
                # FIXED ITEM 4: Get current transformed Direction and REF. Direction values
                if hasattr(self, 'step_loader_left') and hasattr(self.step_loader_left, 'get_original_axis2_placement'):
                    axis_data = self.step_loader_left.get_original_axis2_placement()
                    if axis_data:
                        # Get original STEP file Direction values (Dir1)
                        if isinstance(axis_data['dir1'], (tuple, list)) and len(axis_data['dir1']) == 3:
                            orig_dir1_values = axis_data['dir1']
                        else:
                            dir1_str = str(axis_data['dir1']).strip('()')
                            orig_dir1_values = [float(x.strip()) for x in dir1_str.split(',')]

                        # Get original STEP file REF. Direction values (Dir2)
                        if isinstance(axis_data['dir2'], (tuple, list)) and len(axis_data['dir2']) == 3:
                            orig_dir2_values = axis_data['dir2']
                        else:
                            dir2_str = str(axis_data['dir2']).strip('()')
                            orig_dir2_values = [float(x.strip()) for x in dir2_str.split(',')]

                        # Apply current rotation transformations to get current Direction values
                        current_dir1 = self._transform_direction_vector(orig_dir1_values, self.current_rot_left)
                        current_dir2 = self._transform_direction_vector(orig_dir2_values, self.current_rot_left)

                        display_axis_x = current_dir1[0]  # Current Direction X
                        display_axis_y = current_dir1[1]  # Current Direction Y
                        display_axis_z = current_dir1[2]  # Current Direction Z
                        ref_dir_x = current_dir2[0]       # Current REF. Direction X
                        ref_dir_y = current_dir2[1]       # Current REF. Direction Y
                        ref_dir_z = current_dir2[2]       # Current REF. Direction Z
                        print(f"DEBUG FIXED: Using current transformed Direction: ({display_axis_x:.3f}, {display_axis_y:.3f}, {display_axis_z:.3f})")
                        print(f"DEBUG FIXED: Using current transformed REF. Direction: ({ref_dir_x:.3f}, {ref_dir_y:.3f}, {ref_dir_z:.3f})")
                    else:
                        # Fallback to button values if STEP data not available
                        display_axis_x = self.current_rot_left['x']
                        display_axis_y = self.current_rot_left['y']
                        display_axis_z = self.current_rot_left['z']
                        ref_dir_x = 0.0
                        ref_dir_y = 0.0
                        ref_dir_z = 0.0
                        print(f"DEBUG FALLBACK: Using button values for Direction")
                else:
                    # No STEP file loaded - use button values
                    display_axis_x = self.current_rot_left['x']
                    display_axis_y = self.current_rot_left['y']
                    display_axis_z = self.current_rot_left['z']
                    ref_dir_x = 0.0
                    ref_dir_y = 0.0
                    ref_dir_z = 0.0
                    print(f"DEBUG NO MODEL: Using button values for Direction")

                # FIXED FORMAT: Cursor at top of screen, rest on one line at bottom
                if hasattr(self, 'cursor_pos_left'):
                    cursor_text = f"CURSOR: X={self.cursor_pos_left['x']:.2f} Y={self.cursor_pos_left['y']:.2f} Z={self.cursor_pos_left['z']:.2f}"
                else:
                    cursor_text = "CURSOR: X=0.00 Y=0.00 Z=0.00"

                # Format values with equals sign: X = 0.000 or X = -0.000
                def format_value(val):
                    return f"= {val:.3f}"

                # Use current position (which now includes movement) - FIXED: Show actual values with better spacing
                if hasattr(self, 'current_pos_left'):
                    move_text = f"POS: X -{self.current_pos_left['x']:.3f} Y -{self.current_pos_left['y']:.3f} Z -{self.current_pos_left['z']:.3f}"
                else:
                    move_text = "POS: X -0.000 Y -0.000 Z -0.000"

                # Add Origin values (using current_pos_left for current position after movement) - FIXED FORMATTING
                if hasattr(self, 'current_pos_left'):
                    origin_text = f"Origin (X {format_value(self.current_pos_left['x'])} Y {format_value(self.current_pos_left['y'])} Z {format_value(self.current_pos_left['z'])})"
                else:
                    origin_text = "Origin (X = 0.000 Y = 0.000 Z = 0.000)"

                # FIXED: Cursor display should ONLY show cursor position, not STEP file data
                cursor_display = cursor_text

                # FIXED: Show current transformed Direction, REF. Direction, and Origin values with proper formatting

                if 'ref_dir_x' in locals():
                    bottom_display = f"Direction (X {format_value(display_axis_x)} Y {format_value(display_axis_y)} Z {format_value(display_axis_z)}) REF. Direction (X {format_value(ref_dir_x)} Y {format_value(ref_dir_y)} Z {format_value(ref_dir_z)}) {origin_text}"
                else:
                    bottom_display = f"Direction (X {format_value(display_axis_x)} Y {format_value(display_axis_y)} Z {format_value(display_axis_z)}) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) {origin_text}"

                print("DEBUG FIXED: Displaying actual STEP file values that get saved/loaded")
                print(f"DEBUG ROTATION VALUES: X={display_axis_x:.2f}deg, Y={display_axis_y:.2f}deg, Z={display_axis_z:.2f}deg")
                print(f"DEBUG POSITION VALUES: X={self.current_pos_left['x']:.3f}mm, Y={self.current_pos_left['y']:.3f}mm, Z={self.current_pos_left['z']:.3f}mm")
                print(f"DEBUG DEBUG: Final cursor_display text: {repr(cursor_display)}")
                print(f"DEBUG DEBUG: Final bottom_display text: {repr(bottom_display)}")
            else:
                # No STEP file loaded - HIDE text overlays instead of showing zeros
                print("DEBUG: TOP text update - No model loaded, hiding text overlays")

            # Update text actors only if model is loaded
            if hasattr(self, 'step_loader_left') and self.step_loader_left.current_polydata is not None:
                # Model is loaded - show text
                print("DEBUG DEBUG: TOP model is loaded, showing text overlays")
                if hasattr(self, 'cursor_text_actor_left'):
                    print(f"DEBUG DEEP DEBUG: Setting cursor_text_actor_left input to: {repr(cursor_display)}")
                    self.cursor_text_actor_left.SetInput(cursor_display)
                    self.cursor_text_actor_left.SetVisibility(1)

                    # FORCE cursor text to be more visible and on top
                    self.cursor_text_actor_left.GetTextProperty().SetFontSize(16)  # Larger font
                    self.cursor_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Bright yellow
                    self.cursor_text_actor_left.GetTextProperty().SetBold(True)
                    self.cursor_text_actor_left.GetTextProperty().SetShadow(True)  # Add shadow for visibility
                    self.cursor_text_actor_left.SetLayerNumber(10)  # Force to top layer

                    print(f"DEBUG DEBUG: TOP cursor text set to: {cursor_display}")
                    print(f"DEBUG DEEP DEBUG: cursor_text_actor_left visibility: {self.cursor_text_actor_left.GetVisibility()}")

                    # Debug text actor properties
                    pos = self.cursor_text_actor_left.GetPosition()
                    print(f"DEBUG DEEP DEBUG: cursor_text_actor_left position: {pos}")
                    font_size = self.cursor_text_actor_left.GetTextProperty().GetFontSize()
                    print(f"DEBUG DEEP DEBUG: cursor_text_actor_left font size: {font_size}")
                    color = self.cursor_text_actor_left.GetTextProperty().GetColor()
                    print(f"DEBUG DEEP DEBUG: cursor_text_actor_left color: {color}")
                    input_text = self.cursor_text_actor_left.GetInput()
                    print(f"DEBUG DEEP DEBUG: cursor_text_actor_left actual input: {repr(input_text)}")
                    layer = self.cursor_text_actor_left.GetLayerNumber()
                    print(f"DEBUG DEEP DEBUG: cursor_text_actor_left layer: {layer}")
                else:
                    print("DEBUG DEEP DEBUG: cursor_text_actor_left not found!")

                # Create LOCAL ORIGIN text display (first line)
                if not hasattr(self, 'local_origin_text_actor_left') or self.local_origin_text_actor_left is None:
                    import vtk
                    self.local_origin_text_actor_left = vtk.vtkTextActor()
                    self.local_origin_text_actor_left.SetTextScaleMode(vtk.vtkTextActor.TEXT_SCALE_MODE_NONE)
                    self.local_origin_text_actor_left.GetTextProperty().SetFontSize(14)
                    self.local_origin_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                    self.local_origin_text_actor_left.GetTextProperty().SetBold(True)
                    self.local_origin_text_actor_left.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                    self.local_origin_text_actor_left.SetPosition(0.02, 0.88)  # Below cursor text
                    self.local_origin_text_actor_left.SetLayerNumber(10)  # High layer for visibility
                    self.vtk_renderer_left.renderer.AddActor2D(self.local_origin_text_actor_left)
                    print("DEBUG: Added local origin text actor to top viewer")

                # Create WORLD ORIGIN text display (second line)
                if not hasattr(self, 'world_origin_text_actor_left') or self.world_origin_text_actor_left is None:
                    import vtk
                    self.world_origin_text_actor_left = vtk.vtkTextActor()
                    self.world_origin_text_actor_left.SetTextScaleMode(vtk.vtkTextActor.TEXT_SCALE_MODE_NONE)
                    self.world_origin_text_actor_left.GetTextProperty().SetFontSize(14)
                    self.world_origin_text_actor_left.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                    self.world_origin_text_actor_left.GetTextProperty().SetBold(True)
                    self.world_origin_text_actor_left.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                    self.world_origin_text_actor_left.SetPosition(0.02, 0.82)  # Below local origin text
                    self.world_origin_text_actor_left.SetLayerNumber(10)  # High layer for visibility
                    self.vtk_renderer_left.renderer.AddActor2D(self.world_origin_text_actor_left)
                    print("DEBUG: Added world origin text actor to top viewer")

                # Get LOCAL ORIGIN data (same format as bottom display: Direction + REF. Direction + Origin)
                # FIXED: Use actual STEP file direction vectors stored in the VTK actor
                if hasattr(self.vtk_renderer_left, 'part_origin_sphere') and self.vtk_renderer_left.part_origin_sphere:
                    # FIXED: Get actual transformed position from UserTransform, not GetPosition()
                    user_transform = self.vtk_renderer_left.part_origin_sphere.GetUserTransform()
                    if user_transform:
                        matrix = user_transform.GetMatrix()
                        pos = [matrix.GetElement(0, 3), matrix.GetElement(1, 3), matrix.GetElement(2, 3)]
                        print(f"DEBUG LOCAL ORIGIN: Using UserTransform position: {pos}")
                    else:
                        # Fallback to GetPosition if no UserTransform
                        pos = self.vtk_renderer_left.part_origin_sphere.GetPosition()
                        print(f"DEBUG LOCAL ORIGIN: Using GetPosition fallback: {pos}")

                    # FIXED: Get actual Direction and REF. Direction from stored STEP file vectors
                    if hasattr(self.vtk_renderer_left.part_origin_sphere, 'z_direction') and hasattr(self.vtk_renderer_left.part_origin_sphere, 'x_direction'):
                        z_dir = self.vtk_renderer_left.part_origin_sphere.z_direction
                        x_dir = self.vtk_renderer_left.part_origin_sphere.x_direction
                        dir_x, dir_y, dir_z = z_dir[0], z_dir[1], z_dir[2]
                        ref_x, ref_y, ref_z = x_dir[0], x_dir[1], x_dir[2]
                        print(f"DEBUG LOCAL ORIGIN: Using stored STEP file direction vectors")
                    else:
                        # Fallback to orientation if direction vectors not stored
                        orient = self.vtk_renderer_left.part_origin_sphere.GetOrientation()
                        dir_x, dir_y, dir_z = orient[0], orient[1], orient[2]
                        ref_x, ref_y, ref_z = orient[0], orient[1], orient[2]
                        print(f"DEBUG LOCAL ORIGIN: Using VTK orientation as fallback")

                    # Format exactly like bottom display with 9 numbers
                    local_origin_display = f"Local Origin Direction (X = {dir_x:.3f} Y = {dir_y:.3f} Z = {dir_z:.3f}) REF. Direction (X = {ref_x:.3f} Y = {ref_y:.3f} Z = {ref_z:.3f}) Origin (X = {pos[0]:.3f} Y = {pos[1]:.3f} Z = {pos[2]:.3f})"
                    print(f"DEBUG LOCAL ORIGIN: pos={pos}, dir=({dir_x:.3f},{dir_y:.3f},{dir_z:.3f}), ref=({ref_x:.3f},{ref_y:.3f},{ref_z:.3f})")
                else:
                    # Fallback: Use current_pos_left values instead of zeros
                    if hasattr(self, 'current_pos_left'):
                        local_origin_display = f"Local Origin Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = {self.current_pos_left['x']:.3f} Y = {self.current_pos_left['y']:.3f} Z = {self.current_pos_left['z']:.3f})"
                        print(f"DEBUG LOCAL ORIGIN FALLBACK: Using current_pos_left = {self.current_pos_left}")
                    else:
                        local_origin_display = "Local Origin Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)"
                        print("DEBUG LOCAL ORIGIN FALLBACK: Using zeros - no current_pos_left found")

                # Get WORLD ORIGIN data (same format as bottom display: Direction + REF. Direction + Origin)
                # FIXED: World origin should always be at (0,0,0) since it's the coordinate system origin
                if hasattr(self.vtk_renderer_left, 'origin_actors') and self.vtk_renderer_left.origin_actors and len(self.vtk_renderer_left.origin_actors) > 0:
                    # Use the first actor from origin_actors list
                    actor = self.vtk_renderer_left.origin_actors[0]
                    if actor:
                        # Get actual transformed position and rotation of world origin
                        pos = actor.GetPosition()
                        orient = actor.GetOrientation()

                        # Get actual Direction and REF. Direction from the WORLD ORIGIN actor's real transformation
                        dir_x, dir_y, dir_z = orient[0], orient[1], orient[2]
                        ref_x, ref_y, ref_z = orient[0], orient[1], orient[2]

                        # Format exactly like bottom display with 9 numbers
                        world_origin_display = f"World Origin Direction (X = {dir_x:.3f} Y = {dir_y:.3f} Z = {dir_z:.3f}) REF. Direction (X = {ref_x:.3f} Y = {ref_y:.3f} Z = {ref_z:.3f}) Origin (X = {pos[0]:.3f} Y = {pos[1]:.3f} Z = {pos[2]:.3f})"
                        print(f"DEBUG WORLD ORIGIN: pos={pos}, orient={orient}, dir=({dir_x:.3f},{dir_y:.3f},{dir_z:.3f}), ref=({ref_x:.3f},{ref_y:.3f},{ref_z:.3f})")
                else:
                    # World origin is always at coordinate system origin (0,0,0) - this is correct
                    world_origin_display = "World Origin Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)"
                    print("DEBUG: No origin_actors found - using coordinate system origin (0,0,0) for world origin")

                # Set the text for both lines
                self.local_origin_text_actor_left.SetInput(local_origin_display)
                self.local_origin_text_actor_left.SetVisibility(1)
                self.world_origin_text_actor_left.SetInput(world_origin_display)
                self.world_origin_text_actor_left.SetVisibility(1)
                print(f"DEBUG: Local origin display: {local_origin_display}")
                print(f"DEBUG: World origin display: {world_origin_display}")

                print(f"DEBUG DEEP DEBUG: Setting combined_text_actor_left input to: {repr(bottom_display)}")
                self.combined_text_actor_left.SetInput(bottom_display)
                if hasattr(self, 'combined_text_actor_left'):
                    self.combined_text_actor_left.SetVisibility(1)
                    print(f"DEBUG DEEP DEBUG: combined_text_actor_left visibility: {self.combined_text_actor_left.GetVisibility()}")
                else:
                    print("DEBUG DEEP DEBUG: combined_text_actor_left not found!")
            else:
                # No model loaded - hide text
                if hasattr(self, 'cursor_text_actor_left'):
                    self.cursor_text_actor_left.SetVisibility(0)
                if hasattr(self, 'combined_text_actor_left'):
                    self.combined_text_actor_left.SetVisibility(0)

            # Render the update
            if hasattr(self, 'vtk_renderer_left') and self.vtk_renderer_left.render_window:
                print("DEBUG DEEP DEBUG: About to render TOP viewer...")
                self.vtk_renderer_left.render_window.Render()
                print("OK DEBUG: TOP text overlays rendered (split display)")
            else:
                print("FAIL DEBUG: vtk_renderer_left or render_window not found!")

        # Update BOTTOM viewer text - CORRECTED FORMAT AND CALCULATION
        if hasattr(self, 'combined_text_actor_right'):
            if hasattr(self, 'orig_pos_right') and hasattr(self, 'current_rot_right'):
                # FIXED ITEM 4: Get current transformed Direction and REF. Direction values
                if hasattr(self, 'step_loader_right') and hasattr(self.step_loader_right, 'get_original_axis2_placement'):
                    axis_data = self.step_loader_right.get_original_axis2_placement()
                    if axis_data:
                        # Get original STEP file Direction values (Dir1)
                        if isinstance(axis_data['dir1'], (tuple, list)) and len(axis_data['dir1']) == 3:
                            orig_dir1_values = axis_data['dir1']
                        else:
                            dir1_str = str(axis_data['dir1']).strip('()')
                            orig_dir1_values = [float(x.strip()) for x in dir1_str.split(',')]

                        # Get original STEP file REF. Direction values (Dir2)
                        if isinstance(axis_data['dir2'], (tuple, list)) and len(axis_data['dir2']) == 3:
                            orig_dir2_values = axis_data['dir2']
                        else:
                            dir2_str = str(axis_data['dir2']).strip('()')
                            orig_dir2_values = [float(x.strip()) for x in dir2_str.split(',')]

                        # Apply current rotation transformations to get current Direction values
                        current_dir1 = self._transform_direction_vector(orig_dir1_values, self.current_rot_right)
                        current_dir2 = self._transform_direction_vector(orig_dir2_values, self.current_rot_right)

                        display_axis_x = current_dir1[0]  # Current Direction X
                        display_axis_y = current_dir1[1]  # Current Direction Y
                        display_axis_z = current_dir1[2]  # Current Direction Z
                        ref_dir_x = current_dir2[0]       # Current REF. Direction X
                        ref_dir_y = current_dir2[1]       # Current REF. Direction Y
                        ref_dir_z = current_dir2[2]       # Current REF. Direction Z
                        print(f"DEBUG FIXED BOTTOM: Using current transformed Direction: ({display_axis_x:.3f}, {display_axis_y:.3f}, {display_axis_z:.3f})")
                        print(f"DEBUG FIXED BOTTOM: Using current transformed REF. Direction: ({ref_dir_x:.3f}, {ref_dir_y:.3f}, {ref_dir_z:.3f})")
                    else:
                        # Fallback to button values if STEP data not available
                        display_axis_x = self.current_rot_right['x']
                        display_axis_y = self.current_rot_right['y']
                        display_axis_z = self.current_rot_right['z']
                        ref_dir_x = 0.0
                        ref_dir_y = 0.0
                        ref_dir_z = 0.0
                        print(f"DEBUG FALLBACK BOTTOM: Using button values for Direction")
                else:
                    # No STEP file loaded - use button values
                    display_axis_x = self.current_rot_right['x']
                    display_axis_y = self.current_rot_right['y']
                    display_axis_z = self.current_rot_right['z']
                    ref_dir_x = 0.0
                    ref_dir_y = 0.0
                    ref_dir_z = 0.0
                    print(f"DEBUG NO MODEL BOTTOM: Using button values for Direction")

                # Format values with equals sign: X = 0.000 or X = -0.000
                def format_value(val):
                    return f"= {val:.3f}"

                # Cursor position for BOTTOM viewer (separate actor like TOP)
                if hasattr(self, 'cursor_pos_right'):
                    cursor_display = f"CURSOR: X={self.cursor_pos_right['x']:.3f} Y={self.cursor_pos_right['y']:.3f} Z={self.cursor_pos_right['z']:.3f}"
                else:
                    cursor_display = "CURSOR: X=0.000 Y=0.000 Z=0.000"

                # Use current position (which now includes movement) - FIXED: Show actual values with better spacing
                if hasattr(self, 'current_pos_right'):
                    pos_text = f"POS: X -{self.current_pos_right['x']:.3f} Y -{self.current_pos_right['y']:.3f} Z -{self.current_pos_right['z']:.3f}"
                else:
                    pos_text = "POS: X -0.000 Y -0.000 Z -0.000"

                # Add Origin values (using current_pos_right for current position after movement) - FIXED FORMATTING
                if hasattr(self, 'current_pos_right'):
                    origin_text = f"Origin (X {format_value(self.current_pos_right['x'])} Y {format_value(self.current_pos_right['y'])} Z {format_value(self.current_pos_right['z'])})"
                else:
                    origin_text = "Origin (X = 0.000 Y = 0.000 Z = 0.000)"

                # FIXED: Show current transformed Direction, REF. Direction, and Origin values with proper formatting

                if 'ref_dir_x' in locals():
                    combined_text = f"Direction (X {format_value(display_axis_x)} Y {format_value(display_axis_y)} Z {format_value(display_axis_z)}) REF. Direction (X {format_value(ref_dir_x)} Y {format_value(ref_dir_y)} Z {format_value(ref_dir_z)}) {origin_text}"
                else:
                    combined_text = f"Direction (X {format_value(display_axis_x)} Y {format_value(display_axis_y)} Z {format_value(display_axis_z)}) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) {origin_text}"

                print("DEBUG FIXED: BOTTOM displaying actual STEP file values that get saved/loaded")
                print(f"DEBUG BOTTOM ROTATION VALUES: X={display_axis_x:.2f}deg, Y={display_axis_y:.2f}deg, Z={display_axis_z:.2f}deg")
                if hasattr(self, 'current_pos_right'):
                    print(f"DEBUG BOTTOM POSITION VALUES: X={self.current_pos_right['x']:.3f}mm, Y={self.current_pos_right['y']:.3f}mm, Z={self.current_pos_right['z']:.3f}mm")
            else:
                # No STEP file loaded - HIDE text overlay instead of showing zeros
                print("DEBUG: BOTTOM text update - No model loaded, hiding text overlay")

            # Update text actors only if model is loaded
            if hasattr(self, 'step_loader_right') and self.step_loader_right.current_polydata is not None:
                # Model is loaded - show text
                print("DEBUG DEBUG: BOTTOM model is loaded, showing text overlays")

                # Update BOTTOM cursor text (separate actor like TOP)
                if hasattr(self, 'cursor_text_actor_right'):
                    self.cursor_text_actor_right.SetInput(cursor_display)
                    self.cursor_text_actor_right.SetVisibility(1)

                    # FORCE BOTTOM cursor text to be more visible and on top
                    self.cursor_text_actor_right.GetTextProperty().SetFontSize(16)  # Larger font
                    self.cursor_text_actor_right.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Bright yellow
                    self.cursor_text_actor_right.GetTextProperty().SetBold(True)
                    self.cursor_text_actor_right.GetTextProperty().SetShadow(True)  # Add shadow for visibility
                    self.cursor_text_actor_right.SetLayerNumber(10)  # Force to top layer

                    print(f"DEBUG DEBUG: BOTTOM cursor text set to: {cursor_display}")
                else:
                    print(f"FAIL DEBUG: cursor_text_actor_right not found - creating it now")
                    # Create missing cursor text actor for BOTTOM viewer
                    import vtk
                    self.cursor_text_actor_right = vtk.vtkTextActor()
                    self.cursor_text_actor_right.SetInput(cursor_display)
                    self.cursor_text_actor_right.GetTextProperty().SetFontSize(14)
                    self.cursor_text_actor_right.GetTextProperty().SetColor(1.0, 1.0, 0.0)  # Yellow
                    self.cursor_text_actor_right.GetTextProperty().SetBold(True)
                    self.cursor_text_actor_right.GetPositionCoordinate().SetCoordinateSystemToNormalizedViewport()
                    self.cursor_text_actor_right.SetPosition(0.02, 0.95)  # Top left corner
                    self.cursor_text_actor_right.SetVisibility(1)

                    # Add to renderer
                    if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right:
                        renderer = self.vtk_renderer_right.renderer
                        if renderer:
                            renderer.AddActor2D(self.cursor_text_actor_right)
                            print(f"OK DEBUG: Created and added cursor_text_actor_right to BOTTOM renderer")

                self.combined_text_actor_right.SetInput(combined_text)
                self.combined_text_actor_right.SetVisibility(1)
            else:
                # No model loaded - hide text
                if hasattr(self, 'cursor_text_actor_right'):
                    self.cursor_text_actor_right.SetVisibility(0)
                self.combined_text_actor_right.SetVisibility(0)

            # Render the update
            if hasattr(self, 'vtk_renderer_right') and self.vtk_renderer_right.render_window:
                self.vtk_renderer_right.render_window.Render()
                print("DEBUG: BOTTOM text overlays rendered (split display)")



    def _extract_rotation_from_vtk_actor(self, viewer):
        """Extract rotation values from STEP file coordinate system analysis
        This analyzes the actual STEP file to determine applied rotations"""
        try:
            if viewer == "top":
                step_loader = self.step_loader_left
                current_rot = getattr(self, 'current_rot_left', {'x': 0, 'y': 0, 'z': 0})
            else:
                step_loader = self.step_loader_right
                current_rot = getattr(self, 'current_rot_right', {'x': 0, 'y': 0, 'z': 0})

            if not step_loader or not hasattr(step_loader, 'current_filename'):
                print(f"FAIL No STEP loader found for {viewer} viewer")
                return {'x': 0, 'y': 0, 'z': 0}

            # Method 1: Use the tracked rotation values (from button rotations)
            button_rotation = current_rot.copy()
            print(f"DEBUG METHOD 1 (tracked button rotation): {button_rotation}")

            # Method 2: Extract from STEP file coordinate system analysis
            step_rotation = self._analyze_step_coordinate_system(step_loader.current_filename)
            print(f"DEBUG METHOD 2 (STEP file coordinate system): {step_rotation}")

            # Use STEP file analysis if available, otherwise fall back to button rotation
            if step_rotation and any(abs(v) > 0.1 for v in step_rotation.values()):
                rotation = step_rotation
                print(f"DEBUG USING STEP FILE ANALYSIS: {rotation}")
            else:
                rotation = button_rotation
                print(f"DEBUG USING BUTTON ROTATION: {rotation}")

            # TODO: Add mouse rotation detection later
            if not any(abs(v) > 0.1 for v in rotation.values()):
                print("WARN No rotation detected - mouse rotations not yet captured")

            return rotation

        except Exception as e:
            print(f"FAIL Error extracting rotation: {e}")
            import traceback
            traceback.print_exc()
            return {'x': 0, 'y': 0, 'z': 0}

    def _analyze_step_coordinate_system(self, filename):
        """Analyze STEP file coordinate system to extract rotation angles"""
        try:
            if not filename or not os.path.exists(filename):
                return {'x': 0, 'y': 0, 'z': 0}

            with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            import re
            import math

            # Look for the main coordinate system (usually AXIS2_PLACEMENT_3D with ID #11)
            # This is the primary coordinate system that gets transformed
            axis_pattern = r'#11 = AXIS2_PLACEMENT_3D\(\'\',(#\d+),(#\d+),(#\d+)\);'
            axis_match = re.search(axis_pattern, content)

            if not axis_match:
                # Try alternative patterns for different STEP file formats
                axis_pattern = r'#(\d+) = AXIS2_PLACEMENT_3D\(\'\',(#\d+),(#\d+),(#\d+)\);'
                axis_matches = re.findall(axis_pattern, content)
                if axis_matches:
                    # Use the first coordinate system found
                    axis_match = axis_matches[0]
                    point_id, dir1_id, dir2_id = axis_match[1], axis_match[2], axis_match[3]
                else:
                    return {'x': 0, 'y': 0, 'z': 0}
            else:
                point_id, dir1_id, dir2_id = axis_match.groups()

            # Extract the direction vectors - handle flexible spacing and formatting
            dir1_pattern = f'{dir1_id}\\s*=\\s*DIRECTION\\(\'\'\\s*,\\s*\\(\\s*([-\\d.E+\\s]+)\\s*,\\s*([-\\d.E+\\s]+)\\s*,\\s*([-\\d.E+\\s]+)\\s*\\)\\s*\\)\\s*;'
            dir2_pattern = f'{dir2_id}\\s*=\\s*DIRECTION\\(\'\'\\s*,\\s*\\(\\s*([-\\d.E+\\s]+)\\s*,\\s*([-\\d.E+\\s]+)\\s*,\\s*([-\\d.E+\\s]+)\\s*\\)\\s*\\)\\s*;'

            dir1_match = re.search(dir1_pattern, content)
            dir2_match = re.search(dir2_pattern, content)

            if not dir1_match or not dir2_match:
                print(f"DEBUG Could not find direction vectors for coordinate system")
                print(f"DEBUG Looking for patterns:")
                print(f"   dir1_pattern: {dir1_pattern}")
                print(f"   dir2_pattern: {dir2_pattern}")
                # Try to find any DIRECTION entries for debugging
                all_directions = re.findall(r'#\d+\s*=\s*DIRECTION\([^)]+\)', content)
                print(f"DEBUG Found {len(all_directions)} DIRECTION entries in file")
                for i, direction in enumerate(all_directions[:5]):
                    print(f"   {i+1}: {direction}")
                return {'x': 0, 'y': 0, 'z': 0}

            # Parse direction vectors - clean up whitespace and convert to float
            dir1 = [float(dir1_match.group(i).strip()) for i in range(1, 4)]  # X-axis direction
            dir2 = [float(dir2_match.group(i).strip()) for i in range(1, 4)]  # Y-axis direction

            print(f"DEBUG STEP coordinate system analysis:")
            print(f"   X-axis direction: {dir1}")
            print(f"   Y-axis direction: {dir2}")

            # Calculate rotation angles from direction vectors
            # Standard coordinate system: X=(1,0,0), Y=(0,1,0), Z=(0,0,1)

            # For a 45deg X rotation, the coordinate system transforms as:
            # - X-axis stays: [1, 0, 0]
            # - Y-axis becomes: [0, cos(45deg), sin(45deg)] = [0, 0.707, 0.707]
            # - Z-axis becomes: [0, -sin(45deg), cos(45deg)] = [0, -0.707, 0.707]

            # But in our STEP file, we see:
            # - X-axis direction: [0.0, -0.707106781187, 0.707106781187]
            # - Y-axis direction: [1.0, 0.0, 0.0]

            # This suggests the coordinate system is rotated differently than expected
            # Let's analyze what we actually have:

            x_rotation = 0
            y_rotation = 0
            z_rotation = 0

            # Check if Y-axis direction shows X rotation
            if abs(dir2[1]) > 0.001 or abs(dir2[2]) > 0.001:
                # Y-axis vector after X rotation: [0, cos(x), sin(x)]
                if abs(dir2[0]) < 0.001:  # Y-axis should have X component = 0
                    x_rotation = math.degrees(math.atan2(dir2[2], dir2[1]))
                    print(f"DEBUG X rotation from Y-axis: atan2({dir2[2]:.3f}, {dir2[1]:.3f}) = {x_rotation:.1f}deg")

            # Check if X-axis direction shows rotation
            if abs(dir1[0] - 1.0) > 0.001 or abs(dir1[1]) > 0.001 or abs(dir1[2]) > 0.001:
                # X-axis is not [1,0,0], so there's some rotation
                if abs(dir1[0]) < 0.001:  # X component is 0
                    # X-axis is in YZ plane, this suggests Y or Z rotation
                    if abs(dir1[1]) > 0.001 and abs(dir1[2]) > 0.001:
                        # Could be X rotation affecting the coordinate system differently
                        x_rotation_alt = math.degrees(math.atan2(dir1[2], -dir1[1]))
                        print(f"DEBUG Alternative X rotation from X-axis: atan2({dir1[2]:.3f}, {-dir1[1]:.3f}) = {x_rotation_alt:.1f}deg")
                        if abs(x_rotation) < 0.1:  # If we didn't get rotation from Y-axis
                            x_rotation = x_rotation_alt

            print(f"DEBUG Rotation analysis:")
            print(f"   X-axis direction: {dir1}")
            print(f"   Y-axis direction: {dir2}")
            print(f"   Calculated X rotation: {x_rotation:.1f}deg")

            # Round to reasonable precision
            rotation = {
                'x': round(x_rotation, 1),
                'y': round(y_rotation, 1),
                'z': round(z_rotation, 1)
            }

            print(f"DEBUG Calculated rotations: {rotation}")
            return rotation

        except Exception as e:
            print(f"FAIL Error analyzing STEP coordinate system: {e}")
            return {'x': 0, 'y': 0, 'z': 0}

    def _get_display_text(self, viewer):
        """Get the full display text from the text overlay"""
        try:
            if viewer == "top":
                text_actor = self.combined_text_actor_left
            else:
                text_actor = self.combined_text_actor_right

            if text_actor:
                return text_actor.GetInput()
            else:
                return ""
        except Exception as e:
            print(f"FAIL Error getting display text: {e}")
            return ""

    def _extract_position_from_display(self, viewer):
        """Extract position values from the actual display text (what user sees)
        This ensures we save exactly what's displayed in the yellow text overlay"""
        try:
            if viewer == "top":
                text_actor = self.combined_text_actor_left
            else:
                text_actor = self.combined_text_actor_right

            if not text_actor:
                print(f"FAIL No text actor found for {viewer} viewer")
                return {'x': 0, 'y': 0, 'z': 0}

            # Get the display text
            display_text = text_actor.GetInput()
            print(f"DEBUG EXTRACTING POSITION FROM DISPLAY: {display_text}")

            # Parse the Origin values from the display text
            # Format: "Origin (X = -5.489 Y = -0.851 Z = -0.623)"
            import re
            origin_pattern = r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)'
            match = re.search(origin_pattern, display_text)

            if match:
                x_val = float(match.group(1))
                y_val = float(match.group(2))
                z_val = float(match.group(3))

                position = {'x': x_val, 'y': y_val, 'z': z_val}
                print(f"OK EXTRACTED POSITION FROM DISPLAY: {position}")
                return position
            else:
                print(f"FAIL Could not parse Origin values from display text")
                return {'x': 0, 'y': 0, 'z': 0}

        except Exception as e:
            print(f"FAIL Error extracting position from display: {e}")
            return {'x': 0, 'y': 0, 'z': 0}

    def _extract_axis_data_from_display(self, display_text):
        """Extract AXIS2_PLACEMENT_3D data from display text"""
        import re

        try:
            # Extract Origin (Point)
            origin_match = re.search(r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', display_text)
            if origin_match:
                point = (float(origin_match.group(1)), float(origin_match.group(2)), float(origin_match.group(3)))
            else:
                point = (0.0, 0.0, 0.0)

            # Extract Direction (Dir1)
            direction_match = re.search(r'Direction \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', display_text)
            if direction_match:
                dir1 = (float(direction_match.group(1)), float(direction_match.group(2)), float(direction_match.group(3)))
            else:
                dir1 = (0.0, 0.0, 1.0)

            # Extract REF. Direction (Dir2)
            ref_direction_match = re.search(r'REF\. Direction \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', display_text)
            if ref_direction_match:
                dir2 = (float(ref_direction_match.group(1)), float(ref_direction_match.group(2)), float(ref_direction_match.group(3)))
            else:
                dir2 = (1.0, 0.0, 0.0)

            axis_data = {
                'point': point,
                'dir1': dir1,
                'dir2': dir2
            }

            print(f"DEBUG EXTRACTED from display: Point={point}, Dir1={dir1}, Dir2={dir2}")
            return axis_data

        except Exception as e:
            print(f"FAIL Error extracting axis data from display: {e}")
            return {
                'point': (0.0, 0.0, 0.0),
                'dir1': (0.0, 0.0, 1.0),
                'dir2': (1.0, 0.0, 0.0)
            }

    def _save_step_with_current_values(self, filename, loader, current_axis_data):
        """Save STEP file using current display values (simple approach)"""
        try:
            print(f"DEBUG SIMPLE SAVE: Using current display values for coordinate system")

            # Temporarily update the loader's axis_data with current values
            original_axis_data = loader.axis_data
            loader.axis_data = current_axis_data

            # Save the file with updated coordinate system
            # Pass a dummy transform matrix to trigger coordinate system update
            import vtk
            dummy_transform = vtk.vtkMatrix4x4()  # Identity matrix to trigger update
            dummy_transform.Identity()  # Set to identity matrix
            success = loader.save_step_file(filename, dummy_transform)

            # Restore original axis_data
            loader.axis_data = original_axis_data

            if success:
                print(f"OK SIMPLE SAVE: Successfully saved with current display values")
                return True
            else:
                print(f"FAIL SIMPLE SAVE: Failed to save file")
                return False

        except Exception as e:
            print(f"FAIL SIMPLE SAVE: Exception during save: {e}")
            return False

    def save_step_file_option1(self):
        """OPTION 1: Save STEP file with transformations applied"""
        from PyQt5.QtWidgets import QFileDialog, QMessageBox
        import os

        print("*** SAVE OPTION 1: Method called!")
        print(f"*** Active viewer: {self.active_viewer}")

        # Start in current working directory
        current_dir = os.getcwd()

        # Use QFileDialog with DontConfirmOverwrite to handle overwrite ourselves
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save STEP File with Transformations", current_dir,
            "STEP Files (*.step);;All Files (*)",
            options=QFileDialog.DontConfirmOverwrite
        )

        if filename:
            # Handle overwrite confirmation ourselves for better control
            if os.path.exists(filename):
                file_info = os.stat(filename)
                import time
                mod_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_info.st_mtime))
                file_size = file_info.st_size

                reply = QMessageBox.question(
                    self,
                    "Overwrite File",
                    f"The file already exists:\n\n"
                    f"File: {os.path.basename(filename)}\n"
                    f"Size: {file_size:,} bytes\n"
                    f"Modified: {mod_time}\n\n"
                    f"Do you want to overwrite it?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    print("FILE Save cancelled by user (file exists)")
                    return
            print(f"FILE Selected filename: {filename}")
            try:
                # Get current transformation values
                if self.active_viewer == "top":
                    loader = self.step_loader_left
                    # CRITICAL FIX: Extract rotation from VTK actor (captures ALL rotations including mouse)
                    current_rot = self._extract_rotation_from_vtk_actor("top")
                    # CRITICAL FIX: Extract position from actual display text (what user sees)
                    current_pos = self._extract_position_from_display("top")
                    orig_rot = self.orig_rot_left if hasattr(self, 'orig_rot_left') else {'x': 0, 'y': 0, 'z': 0}
                    orig_pos = self.orig_pos_left if hasattr(self, 'orig_pos_left') else {'x': 0, 'y': 0, 'z': 0}
                else:
                    loader = self.step_loader_right
                    # CRITICAL FIX: Extract rotation from VTK actor (captures ALL rotations including mouse)
                    current_rot = self._extract_rotation_from_vtk_actor("bottom")
                    # CRITICAL FIX: Extract position from actual display text (what user sees)
                    current_pos = self._extract_position_from_display("bottom")
                    orig_rot = self.orig_rot_right if hasattr(self, 'orig_rot_right') else {'x': 0, 'y': 0, 'z': 0}
                    orig_pos = self.orig_pos_right if hasattr(self, 'orig_pos_right') else {'x': 0, 'y': 0, 'z': 0}

                print(f"OK OPTION 1: Saving STEP file with transformations")
                print(f"   Current Position: {current_pos}")
                print(f"   Current Rotation: {current_rot}")
                print(f"   Original Position: {orig_pos}")
                print(f"   Original Rotation: {orig_rot}")

                # Extract current display values (what user sees in yellow text)
                display_text = self._get_display_text(self.active_viewer)
                current_axis_data = self._extract_axis_data_from_display(display_text)

                print(f"OK OPTION 1: Using current display values for coordinate system:")
                print(f"   Point: {current_axis_data['point']}")
                print(f"   Dir1: {current_axis_data['dir1']}")
                print(f"   Dir2: {current_axis_data['dir2']}")

                # Save with current display values (simple approach)
                success = self._save_step_with_current_values(filename, loader, current_axis_data)

                if success:
                    # Get file info after save
                    file_size = os.path.getsize(filename) if os.path.exists(filename) else 0

                    # Force timestamp update by touching the file
                    import time
                    current_time = time.time()
                    os.utime(filename, (current_time, current_time))

                    # Get the new timestamp
                    file_info = os.stat(filename)
                    new_mod_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_info.st_mtime))

                    print(f"OK OPTION 1: STEP file saved successfully, size: {file_size} bytes")
                    print(f"? File timestamp updated to: {new_mod_time}")

                    self.statusBar().showMessage(f"OK STEP file saved: {filename}")
                    QMessageBox.information(self, "Save Successful",
                                          f"STEP file saved successfully!\n\n"
                                          f"File: {os.path.basename(filename)}\n"
                                          f"Size: {file_size:,} bytes\n"
                                          f"Modified: {new_mod_time}\n\n"
                                          f"Transformations have been applied to the geometry.")
                else:
                    raise Exception("STEP file save failed - check console for details")

            except Exception as e:
                print(f"FAIL OPTION 1: Save failed: {e}")
                self.statusBar().showMessage(f"FAIL Save failed: {e}")
                QMessageBox.critical(self, "Save Error", f"Failed to save STEP file:\n\n{str(e)}")

    def save_step_file_option2(self):
        """OPTION 2: Save original STEP file (no transformations)"""
        print("START SAVE OPTION 2: Method called! (Blue button - Transform Geometry)")
        from PyQt5.QtWidgets import QFileDialog, QMessageBox
        import shutil
        import os

        # Start in current working directory
        current_dir = os.getcwd()
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Original STEP File", current_dir, "STEP Files (*.step);;All Files (*)"
        )

        if filename:
            try:
                # Get the current loader
                if self.active_viewer == "top":
                    loader = self.step_loader_left
                else:
                    loader = self.step_loader_right

                print(f"OK OPTION 2: Saving original STEP file to: {filename}")

                # Get the original filename
                if hasattr(loader, 'current_filename') and loader.current_filename:
                    original_file = loader.current_filename
                    print(f"OK OPTION 2: Copying from original file: {original_file}")

                    # Simple file copy - preserves original exactly
                    shutil.copy2(original_file, filename)

                    # Verify the file was created and has reasonable size
                    if os.path.exists(filename) and os.path.getsize(filename) > 1000:
                        file_size = os.path.getsize(filename)
                        print(f"OK OPTION 2: Original file copied successfully, size: {file_size} bytes")

                        self.statusBar().showMessage(f"OK Original STEP file saved: {filename}")
                        QMessageBox.information(self, "Save Successful",
                                              f"Original STEP file saved successfully!\n\nFile: {filename}\nSize: {file_size:,} bytes\n\nThis is an exact copy of the original file (no transformations applied).")
                        return
                    else:
                        raise Exception("File not created or too small")
                else:
                    raise Exception("No original file available to save")

            except Exception as e:
                print(f"FAIL OPTION 2: Save failed: {e}")
                self.statusBar().showMessage(f"FAIL Save failed: {e}")
                QMessageBox.critical(self, "Save Error", f"Failed to save original STEP file:\n\n{str(e)}")

    def _save_step_with_transformations(self, filename, loader, current_pos, current_rot, orig_pos, orig_rot):
        """Save STEP file with transformations applied using multiple methods"""
        print(f"DEBUG STEP TRANSFORM SAVE: Attempting to save with transformations")

        # Calculate DELTA transformations (what actually changed)
        delta_pos = {
            'x': current_pos['x'] - orig_pos['x'],
            'y': current_pos['y'] - orig_pos['y'],
            'z': current_pos['z'] - orig_pos['z']
        }
        delta_rot = {
            'x': current_rot['x'] - orig_rot['x'],
            'y': current_rot['y'] - orig_rot['y'],
            'z': current_rot['z'] - orig_rot['z']
        }

        print(f"DEBUG STEP TRANSFORM SAVE: Delta transformations:")
        print(f"   Delta Position: {delta_pos}")
        print(f"   Delta Rotation: {delta_rot}")

        # Check if any significant transformations are needed (very high threshold for precision work)
        pos_changed = (abs(delta_pos['x']) > 10.0 or abs(delta_pos['y']) > 10.0 or abs(delta_pos['z']) > 10.0)
        rot_changed = (abs(delta_rot['x']) > 45.0 or abs(delta_rot['y']) > 45.0 or abs(delta_rot['z']) > 45.0)

        # HYBRID APPROACH: Copy original file first, then apply transformations if needed
        print(f"DEBUG STEP TRANSFORM SAVE: Using hybrid approach for colors + rotations")
        import shutil
        import os
        import vtk

        # Get the current VTK renderer and actor to check for transformations
        if self.active_viewer == "top":
            renderer = self.vtk_renderer_left
            step_actor = renderer.step_actor if hasattr(renderer, 'step_actor') else None
            step_actors = renderer.step_actors if hasattr(renderer, 'step_actors') else None
        else:
            renderer = self.vtk_renderer_right
            step_actor = renderer.step_actor if hasattr(renderer, 'step_actor') else None
            step_actors = renderer.step_actors if hasattr(renderer, 'step_actors') else None

        # Check for transformations in either single actor or multi-actors
        user_transform = None
        has_transformations = False

        if step_actors:
            # Multi-actor model - check first actor for transformations
            print(f"DEBUG STEP TRANSFORM SAVE: Checking multi-actors for transformations")
            for actor in step_actors:
                if actor.GetUserTransform():
                    user_transform = actor.GetUserTransform()
                    has_transformations = True
                    print(f"OK Found UserTransform in multi-actor")
                    break
                # Also check for rotation transformations (RotateWXYZ doesn't create UserTransform)
                orientation = actor.GetOrientation()
                if abs(orientation[0]) > 0.1 or abs(orientation[1]) > 0.1 or abs(orientation[2]) > 0.1:
                    has_transformations = True
                    print(f"OK Found rotation transformation in multi-actor: {orientation}")
                    # Create a UserTransform from the actor's current transformation
                    transform = vtk.vtkTransform()
                    transform.SetMatrix(actor.GetMatrix())
                    user_transform = transform
                    break
        elif step_actor:
            # Single actor model
            print(f"DEBUG STEP TRANSFORM SAVE: Checking single actor for transformations")
            user_transform = step_actor.GetUserTransform()
            has_transformations = user_transform is not None
        else:
            print(f"FAIL STEP TRANSFORM SAVE: No VTK actors found")
            return False

        if not has_transformations:
            # No transformations - just copy original file (preserves colors perfectly)
            print(f"DEBUG No transformations detected - copying original file for perfect color preservation")
            source_file = loader.original_filename if hasattr(loader, 'original_filename') and loader.original_filename and os.path.exists(loader.original_filename) else loader.current_filename

            if source_file and os.path.exists(source_file):
                shutil.copy2(source_file, filename)
                print(f"OK STEP TRANSFORM SAVE: Copied original file with perfect colors and coordinates")
                return True
            else:
                print(f"FAIL STEP TRANSFORM SAVE: No source file available for copying")
                return False
        else:
            # Model has been rotated/moved - save it with actual geometry transformation
            print(f"DEBUG Saving model with actual geometry transformation")
            try:
                # Get the active viewer's renderer and actors
                if self.active_viewer == "top":
                    vtk_renderer = self.vtk_renderer_left
                    step_loader = self.step_loader_left
                else:
                    vtk_renderer = self.vtk_renderer_right
                    step_loader = self.step_loader_right

                # Apply the VTK transformations to the geometry before saving
                import vtk

                # Get the original polydata
                original_polydata = step_loader.current_polydata
                if not original_polydata:
                    print(f"FAIL No polydata available for transformation")
                    return False

                # Create a transform that combines all actor transformations
                combined_transform = vtk.vtkTransform()
                combined_transform.Identity()

                # Get transformation from the first actor (they should all have the same transformation)
                if hasattr(vtk_renderer, 'step_actors') and vtk_renderer.step_actors:
                    # Multi-actor model - get transform from first actor
                    first_actor = vtk_renderer.step_actors[0]
                    if first_actor.GetUserTransform():
                        combined_transform.DeepCopy(first_actor.GetUserTransform())

                    # Also include position and orientation
                    position = first_actor.GetPosition()
                    orientation = first_actor.GetOrientation()
                    if position != (0, 0, 0):
                        combined_transform.Translate(*position)
                    if orientation != (0, 0, 0):
                        combined_transform.RotateX(orientation[0])
                        combined_transform.RotateY(orientation[1])
                        combined_transform.RotateZ(orientation[2])

                elif hasattr(vtk_renderer, 'step_actor') and vtk_renderer.step_actor:
                    # Single actor model
                    actor = vtk_renderer.step_actor
                    if actor.GetUserTransform():
                        combined_transform.DeepCopy(actor.GetUserTransform())

                    # Also include position and orientation
                    position = actor.GetPosition()
                    orientation = actor.GetOrientation()
                    if position != (0, 0, 0):
                        combined_transform.Translate(*position)
                    if orientation != (0, 0, 0):
                        combined_transform.RotateX(orientation[0])
                        combined_transform.RotateY(orientation[1])
                        combined_transform.RotateZ(orientation[2])

                # Apply the transformation to the polydata
                transform_filter = vtk.vtkTransformPolyDataFilter()
                transform_filter.SetInputData(original_polydata)
                transform_filter.SetTransform(combined_transform)
                transform_filter.Update()

                # Get the transformed polydata
                transformed_polydata = transform_filter.GetOutput()

                # Temporarily replace the current polydata with transformed version
                original_backup = step_loader.current_polydata
                step_loader.current_polydata = transformed_polydata

                print(f"TARGET GEOMETRY TRANSFORM: Applied VTK transformations to polydata")
                print(f"   Original points: {original_backup.GetNumberOfPoints()}")
                print(f"   Transformed points: {transformed_polydata.GetNumberOfPoints()}")

                # Calculate the new coordinate system based on the transformation
                print(f"DEBUG COORDINATE SYSTEM: Calculating transformed coordinate system")

                # Get the original coordinate system
                original_point = step_loader.axis_data['point'] if step_loader.axis_data else (0, 0, 0)
                original_dir1 = step_loader.axis_data['dir1'] if step_loader.axis_data else (0, 0, 1)
                original_dir2 = step_loader.axis_data['dir2'] if step_loader.axis_data else (1, 0, 0)

                print(f"DEBUG ORIGINAL coordinate system:")
                print(f"   Point: {original_point}")
                print(f"   Dir1: {original_dir1}")
                print(f"   Dir2: {original_dir2}")

                # Transform the coordinate system using the same transformation
                import numpy as np

                # Transform the point (origin)
                point_4d = [original_point[0], original_point[1], original_point[2], 1.0]
                transformed_point_4d = [0, 0, 0, 0]
                combined_transform.MultiplyPoint(point_4d, transformed_point_4d)
                transformed_point = (transformed_point_4d[0], transformed_point_4d[1], transformed_point_4d[2])

                # Transform the direction vectors (no translation, just rotation)
                dir1_4d = [original_dir1[0], original_dir1[1], original_dir1[2], 0.0]  # 0.0 for direction vector
                transformed_dir1_4d = [0, 0, 0, 0]
                combined_transform.MultiplyPoint(dir1_4d, transformed_dir1_4d)
                transformed_dir1 = (transformed_dir1_4d[0], transformed_dir1_4d[1], transformed_dir1_4d[2])

                dir2_4d = [original_dir2[0], original_dir2[1], original_dir2[2], 0.0]  # 0.0 for direction vector
                transformed_dir2_4d = [0, 0, 0, 0]
                combined_transform.MultiplyPoint(dir2_4d, transformed_dir2_4d)
                transformed_dir2 = (transformed_dir2_4d[0], transformed_dir2_4d[1], transformed_dir2_4d[2])

                print(f"DEBUG TRANSFORMED coordinate system:")
                print(f"   Point: {transformed_point}")
                print(f"   Dir1: {transformed_dir1}")
                print(f"   Dir2: {transformed_dir2}")

                # Create transformation matrix for the save method
                transform_matrix = np.eye(4)
                matrix_4x4 = vtk.vtkMatrix4x4()
                combined_transform.GetMatrix(matrix_4x4)
                for i in range(4):
                    for j in range(4):
                        transform_matrix[i, j] = matrix_4x4.GetElement(i, j)

                print(f"DEBUG COORDINATE SYSTEM: Passing transformation matrix to save method")

                # Save the transformed geometry with updated coordinate system
                success = step_loader.save_step_file(filename, transform_matrix)

                # Restore original polydata
                step_loader.current_polydata = original_backup

                if success:
                    print(f"OK GEOMETRY TRANSFORM: Successfully saved with transformed geometry")
                    self.statusBar().showMessage(f"OK Saved with transformed geometry: {filename}")
                    return True
                else:
                    print(f"FAIL GEOMETRY TRANSFORM: Save failed")
                    self.statusBar().showMessage(f"FAIL Save failed: {filename}")
                    return False

            except Exception as e:
                print(f"FAIL GEOMETRY TRANSFORM: Exception during save: {e}")
                import traceback
                traceback.print_exc()
                self.statusBar().showMessage(f"FAIL Save error: {e}")
                return False

    def _save_step_with_vtk_transform(self, filename, loader, user_transform, current_rot):
        """Fallback method using VTK transformation"""
        try:
            import vtk
            # Apply transformations to the polydata FIRST
            transform_filter = vtk.vtkTransformPolyDataFilter()
            transform_filter.SetInputData(loader.current_polydata)
            transform_filter.SetTransform(user_transform)
            transform_filter.Update()
            transformed_polydata = transform_filter.GetOutput()

            # Store original data
            original_polydata = loader.current_polydata
            original_axis_data = loader.axis_data.copy() if hasattr(loader, 'axis_data') and loader.axis_data else None

            # Update the loader's polydata with the transformed version
            loader.current_polydata = transformed_polydata

            # Transform the coordinate system data if it exists
            if original_axis_data:
                print("DEBUG Transforming coordinate system data...")

                # Transform the origin point
                point = vtk.vtkPoints()
                point.InsertNextPoint(original_axis_data['point'])
                point_polydata = vtk.vtkPolyData()
                point_polydata.SetPoints(point)

                point_transform_filter = vtk.vtkTransformPolyDataFilter()
                point_transform_filter.SetInputData(point_polydata)
                point_transform_filter.SetTransform(user_transform)
                point_transform_filter.Update()

                transformed_point = point_transform_filter.GetOutput().GetPoint(0)

                # Transform the direction vectors
                dir1 = original_axis_data['dir1']
                dir2 = original_axis_data['dir2']

                # FIXED: Use the same transformation method as the display
                # This ensures saved values match displayed values exactly
                transformed_dir1 = self._transform_direction_vector(dir1, current_rot)
                transformed_dir2 = self._transform_direction_vector(dir2, current_rot)

                # Update the axis data
                loader.axis_data = {
                    'point': transformed_point,
                    'dir1': (transformed_dir1[0], transformed_dir1[1], transformed_dir1[2]),
                    'dir2': (transformed_dir2[0], transformed_dir2[1], transformed_dir2[2])
                }

                print(f"DEBUG VTK: Using display transformation method for directions")
                print(f"DEBUG VTK: Transformed origin: {transformed_point}")
                print(f"DEBUG VTK: Transformed dir1: {transformed_dir1}")
                print(f"DEBUG VTK: Transformed dir2: {transformed_dir2}")

            # Save the transformed geometry and coordinate system to STEP file
            success = loader.save_step_file(filename)

            # Restore original data
            loader.current_polydata = original_polydata
            if original_axis_data:
                loader.axis_data = original_axis_data

            if success:
                print(f"OK VTK TRANSFORM SAVE: Applied transformations to STEP file")
                return True
            else:
                print(f"FAIL VTK TRANSFORM SAVE: Failed to save transformed STEP file")
                return False

        except Exception as e:
            print(f"FAIL Error during VTK transformation save: {e}")
            import traceback
            traceback.print_exc()
            return False

        # METHOD 3: Fallback - save as STL with clear message
        try:
            print(f"DEBUG STEP TRANSFORM SAVE: Falling back to STL export...")
            stl_filename = filename.replace('.step', '.stl').replace('.STEP', '.stl')
            success = self._save_as_stl_fallback(stl_filename, loader)
            if success:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(None, "Format Changed",
                                      f"Could not save as STEP format with transformations.\n\nSaved as STL instead: {stl_filename}\n\nThe geometry transformations have been applied.")
                return True
        except Exception as e:
            print(f"FAIL STEP TRANSFORM SAVE: STL fallback failed: {e}")

        return False

    def _save_step_opencascade_transform(self, filename, loader, delta_pos, delta_rot):
        """Save STEP file using OpenCASCADE with transformations applied"""
        try:
            # Try to import OpenCASCADE modules for STEP writing
            from OCC.Core.STEPControl_Writer import STEPControl_Writer
            from OCC.Core.Interface_Static import Interface_Static
            from OCC.Core.IFSelect_ReturnStatus import IFSelect_RetDone
            from OCC.Core.gp_Trsf import gp_Trsf
            from OCC.Core.gp_Vec import gp_Vec
            from OCC.Core.gp_Ax1 import gp_Ax1
            from OCC.Core.gp_Pnt import gp_Pnt
            from OCC.Core.gp_Dir import gp_Dir
            from OCC.Core.BRepBuilderAPI_Transform import BRepBuilderAPI_Transform
            import math
            import os

            print(f"DEBUG OPENCASCADE TRANSFORM: Creating transformation...")

            # Create transformation
            trsf = gp_Trsf()

            # Apply DELTA rotations (in degrees, convert to radians)
            if delta_rot['x'] != 0:
                axis = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(1, 0, 0))
                trsf.SetRotation(axis, math.radians(delta_rot['x']))

            if delta_rot['y'] != 0:
                trsf_y = gp_Trsf()
                axis = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 1, 0))
                trsf_y.SetRotation(axis, math.radians(delta_rot['y']))
                trsf = trsf.Multiplied(trsf_y)

            if delta_rot['z'] != 0:
                trsf_z = gp_Trsf()
                axis = gp_Ax1(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1))
                trsf_z.SetRotation(axis, math.radians(delta_rot['z']))
                trsf = trsf.Multiplied(trsf_z)

            # Apply DELTA translation
            if delta_pos['x'] != 0 or delta_pos['y'] != 0 or delta_pos['z'] != 0:
                trsf_t = gp_Trsf()
                trsf_t.SetTranslation(gp_Vec(delta_pos['x'], delta_pos['y'], delta_pos['z']))
                trsf = trsf.Multiplied(trsf_t)

            print(f"DEBUG OPENCASCADE TRANSFORM: Applying transformation to shape...")

            # Apply transformation to the shape
            transform_builder = BRepBuilderAPI_Transform(loader.shape, trsf)
            transformed_shape = transform_builder.Shape()

            print(f"DEBUG OPENCASCADE TRANSFORM: Writing transformed STEP file...")

            # Write the transformed shape
            writer = STEPControl_Writer()
            Interface_Static.SetCVal("write.step.schema", "AP214")
            Interface_Static.SetCVal("write.step.unit", "MM")

            transfer_status = writer.Transfer(transformed_shape, 1)
            if transfer_status != IFSelect_RetDone:
                print(f"FAIL OPENCASCADE TRANSFORM: Shape transfer failed")
                return False

            write_status = writer.Write(filename)
            if write_status == IFSelect_RetDone:
                if os.path.exists(filename) and os.path.getsize(filename) > 100:
                    print(f"OK OPENCASCADE TRANSFORM: Successfully saved transformed STEP file")
                    return True
                else:
                    print(f"FAIL OPENCASCADE TRANSFORM: File not created or too small")
                    return False
            else:
                print(f"FAIL OPENCASCADE TRANSFORM: Write failed")
                return False

        except ImportError as e:
            print(f"FAIL OPENCASCADE TRANSFORM: OpenCASCADE modules not available: {e}")
            return False
        except Exception as e:
            print(f"FAIL OPENCASCADE TRANSFORM: Transformation failed: {e}")
            return False

    def _save_step_text_transform(self, filename, loader, delta_pos, delta_rot, orig_pos, orig_rot):
        """Save STEP file using the STEPTransformer with DELTA transformations"""
        try:
            from step_transformer import STEPTransformer

            print(f"DEBUG STEP TEXT TRANSFORM: Using STEPTransformer with DELTA transformations...")

            # Check if we have the original filename
            if not hasattr(loader, 'current_filename') or not loader.current_filename:
                print(f"FAIL STEP TEXT TRANSFORM: No original filename available")
                return False

            # Create and use the STEP transformer
            transformer = STEPTransformer()

            # Load the original STEP file
            if not transformer.load_step_file(loader.current_filename):
                print(f"FAIL STEP TEXT TRANSFORM: Failed to load original file")
                return False

            print(f"DEBUG STEP TEXT TRANSFORM: Applying DELTA transformations...")
            print(f"   Delta Position: {delta_pos}")
            print(f"   Delta Rotation: {delta_rot}")

            # Apply the DELTA transformations (no coordinate conversion needed)
            success = transformer.apply_transformation(
                rotation_x=delta_rot['x'],
                rotation_y=delta_rot['y'],
                rotation_z=delta_rot['z'],
                translation_x=delta_pos['x'],
                translation_y=delta_pos['y'],
                translation_z=delta_pos['z']
            )

            if not success:
                print(f"FAIL STEP TEXT TRANSFORM: Transformation failed")
                return False

            # Save the transformed file
            if transformer.save_step_file(filename):
                print(f"OK STEP TEXT TRANSFORM: Successfully saved transformed STEP file")
                return True
            else:
                print(f"FAIL STEP TEXT TRANSFORM: Save failed")
                return False

        except ImportError as e:
            print(f"FAIL STEP TEXT TRANSFORM: STEPTransformer not available: {e}")
            return False
        except Exception as e:
            print(f"FAIL STEP TEXT TRANSFORM: Transformation failed: {e}")
            return False

    def _save_as_stl_fallback(self, filename, loader):
        """Fallback method: save as STL file"""
        try:
            import vtk
            import os

            print(f"DEBUG STL FALLBACK: Saving as STL file...")

            if hasattr(loader, 'current_polydata') and loader.current_polydata:
                # Create STL writer
                stl_writer = vtk.vtkSTLWriter()
                stl_writer.SetFileName(filename)
                stl_writer.SetInputData(loader.current_polydata)

                # Write the file
                stl_writer.Write()

                # Verify the file was created
                if os.path.exists(filename) and os.path.getsize(filename) > 100:
                    print(f"OK STL FALLBACK: STL file saved successfully")
                    return True
                else:
                    print(f"FAIL STL FALLBACK: File not created or too small")
                    return False
            else:
                print(f"FAIL STL FALLBACK: No polydata available")
                return False

        except Exception as e:
            print(f"FAIL STL FALLBACK: STL save failed: {e}")
            return False

    def save_original_step(self):
        """Save original STEP file without transformations - preserves colors and position"""
        print("START SAVE_ORIGINAL_STEP: Method called!")
        from PyQt5.QtWidgets import QFileDialog
        import os
        import shutil

        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Original STEP File", "", "STEP Files (*.step);;All Files (*)"
        )

        if filename:
            try:
                if self.active_viewer == "top":
                    loader = self.step_loader_left
                else:
                    loader = self.step_loader_right

                print(f"OK ORIGINAL: Saving original STEP file to preserve colors and position")

                # FIXED: Copy original STEP file to preserve colors and coordinate system
                if hasattr(loader, 'original_filename') and loader.original_filename and os.path.exists(loader.original_filename):
                    print(f"DEBUG ORIGINAL: Copying original file: {loader.original_filename}")

                    # Remove existing file if it exists
                    if os.path.exists(filename):
                        os.remove(filename)
                        print(f"DEBUG ORIGINAL: Removed existing file to ensure fresh copy")

                    # Copy original file to preserve all STEP data (colors, position, etc.)
                    shutil.copy2(loader.original_filename, filename)
                    print(f"OK ORIGINAL: Successfully copied original STEP file")

                    self.statusBar().showMessage(f"Original saved: {filename}")
                    success = True
                else:
                    print(f"FAIL ORIGINAL: No original filename available, falling back to loader save")
                    # Fallback to loader save method if original file not available
                    success = loader.save_step_file(filename)

                    if success:
                        self.statusBar().showMessage(f"Original saved: {filename}")
                        print(f"OK ORIGINAL: Saved using loader method")
                    else:
                        self.statusBar().showMessage("Original save failed")
                        print(f"FAIL ORIGINAL: Loader save failed")

            except Exception as e:
                print(f"FAIL ORIGINAL: Error: {e}")
                self.statusBar().showMessage(f"Original save error: {e}")
                success = False



    def _calculate_model_center_after_rotation(self, viewer):
        """Calculate the new model center position after rotation"""
        try:
            if viewer == 'left':
                renderer = self.vtk_renderer_left
            else:
                renderer = self.vtk_renderer_right

            # Get the first available actor to calculate center
            actor = None
            if hasattr(renderer, 'step_actors') and renderer.step_actors:
                actor = renderer.step_actors[0]
            elif hasattr(renderer, 'step_actor') and renderer.step_actor:
                actor = renderer.step_actor

            if actor:
                # Get the actor's bounds after rotation
                bounds = actor.GetBounds()  # [xmin, xmax, ymin, ymax, zmin, zmax]

                # Calculate center from bounds
                center_x = (bounds[0] + bounds[1]) / 2.0
                center_y = (bounds[2] + bounds[3]) / 2.0
                center_z = (bounds[4] + bounds[5]) / 2.0

                print(f"DEBUG: Calculated new model center: ({center_x:.3f}, {center_y:.3f}, {center_z:.3f})")
                return (center_x, center_y, center_z)
            else:
                print("DEBUG: No actor found to calculate center")
                return None

        except Exception as e:
            print(f"DEBUG: Error calculating model center: {e}")
            return None

    def move_shape(self, axis, amount):
        """Move shape along specified axis - UNIFIED MOVEMENT METHOD"""
        print(f"🔄 UNIFIED MOVEMENT: {axis}+{amount}mm on {self.active_viewer} viewer")

        # Call the unified movement method
        self._apply_unified_movement(axis, amount)
        if self.active_viewer == "top":
            # Initialize movement deltas if not exists (tracks movement from original position)
            if not hasattr(self, 'movement_delta_left'):
                self.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}

            # FIXED: Update position relative to current position, not original
            # Simply add the movement amount to the current position
            self.current_pos_left[axis] += amount

            # Update movement delta for tracking total movement from original
            if hasattr(self, 'movement_delta_left'):
                self.movement_delta_left[axis] += amount
            else:
                self.movement_delta_left = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                self.movement_delta_left[axis] = amount

            # DEBUG: Print detailed coordinate information
            print(f"DEBUG COORDINATE DEBUG: axis={axis}, amount={amount}")
            print(f"   current_pos_left[{axis}] = {self.current_pos_left[axis]:.3f}")
            if hasattr(self, 'movement_delta_left'):
                print(f"   total_movement_delta[{axis}] = {self.movement_delta_left[axis]:.3f}")

            print(f"DEBUG: Position move - TOP {axis} += {amount}mm, new_pos={self.current_pos_left[axis]:.3f}mm")

            # Apply actual translation to ALL VTK actors (handles multi-color models)
            actors_moved = False

            if hasattr(self.vtk_renderer_left, 'step_actors') and self.vtk_renderer_left.step_actors:
                # Multi-color model with separate actors
                print(f"DEBUG: Moving {len(self.vtk_renderer_left.step_actors)} multi-actors by {amount}mm on {axis}-axis")
                for i, actor in enumerate(self.vtk_renderer_left.step_actors):
                    pos_before = actor.GetPosition()
                    if axis == 'x':
                        actor.AddPosition(amount, 0, 0)
                    elif axis == 'y':
                        actor.AddPosition(0, amount, 0)
                    elif axis == 'z':
                        actor.AddPosition(0, 0, amount)
                    pos_after = actor.GetPosition()
                    print(f"   Actor {i}: {axis}-axis {pos_before[{'x':0,'y':1,'z':2}[axis]]:.3f} ? {pos_after[{'x':0,'y':1,'z':2}[axis]]:.3f}")
                actors_moved = True

                # ALSO move overlay TOP actors if overlay mode is active
                if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_top_actors'):
                    print(f"DEBUG: Also moving {len(self.overlay_top_actors)} overlay TOP actors by {amount}mm on {axis}-axis")
                    for overlay_actor in self.overlay_top_actors:
                        if axis == 'x':
                            overlay_actor.AddPosition(amount, 0, 0)
                        elif axis == 'y':
                            overlay_actor.AddPosition(0, amount, 0)
                        elif axis == 'z':
                            overlay_actor.AddPosition(0, 0, amount)

            if (hasattr(self.vtk_renderer_left, 'step_actor') and
                self.vtk_renderer_left.step_actor and
                self._is_actor_in_renderer(self.vtk_renderer_left.step_actor, self.vtk_renderer_left)):
                # Single actor model - ONLY move if it's actually in the renderer
                print(f"DEBUG: Moving single actor by {amount}mm on {axis}-axis")
                if axis == 'x':
                    self.vtk_renderer_left.step_actor.AddPosition(amount, 0, 0)
                elif axis == 'y':
                    self.vtk_renderer_left.step_actor.AddPosition(0, amount, 0)
                elif axis == 'z':
                    self.vtk_renderer_left.step_actor.AddPosition(0, 0, amount)
                actors_moved = True

                # ALSO move overlay TOP actors if overlay mode is active (single actor case)
                if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_top_actors'):
                    print(f"DEBUG: Also moving {len(self.overlay_top_actors)} overlay TOP single actors by {amount}mm on {axis}-axis")
                    for overlay_actor in self.overlay_top_actors:
                        if axis == 'x':
                            overlay_actor.AddPosition(amount, 0, 0)
                        elif axis == 'y':
                            overlay_actor.AddPosition(0, amount, 0)
                        elif axis == 'z':
                            overlay_actor.AddPosition(0, 0, amount)

            elif hasattr(self.vtk_renderer_left, 'step_actor') and self.vtk_renderer_left.step_actor:
                print(f"DEBUG: Skipping single actor movement - not in renderer")

            if not actors_moved:
                print(f"DEBUG: No actors found to move!")

            # ALSO move origin actors to follow the model
            debug_msg = f"DEBUG ORIGIN: hasattr origin_actors: {hasattr(self.vtk_renderer_left, 'origin_actors')}\n"
            if hasattr(self.vtk_renderer_left, 'origin_actors'):
                debug_msg += f"DEBUG ORIGIN: origin_actors exists, length: {len(self.vtk_renderer_left.origin_actors)}\n"
            with open("debug_origin.txt", "a") as f:
                f.write(debug_msg)

            if hasattr(self.vtk_renderer_left, 'origin_actors') and self.vtk_renderer_left.origin_actors:
                print(f"DEBUG: Moving {len(self.vtk_renderer_left.origin_actors)} origin actors by {amount}mm on {axis}-axis")
                with open("debug_origin.txt", "a") as f:
                    f.write(f"MOVING {len(self.vtk_renderer_left.origin_actors)} origin actors by {amount}mm on {axis}-axis\n")
                for origin_actor in self.vtk_renderer_left.origin_actors:
                    if axis == 'x':
                        origin_actor.AddPosition(amount, 0, 0)
                    elif axis == 'y':
                        origin_actor.AddPosition(0, amount, 0)
                    elif axis == 'z':
                        origin_actor.AddPosition(0, 0, amount)
            else:
                with open("debug_origin.txt", "a") as f:
                    f.write("ORIGIN ACTORS NOT FOUND OR EMPTY\n")

            # ALSO move part origin actors (actual STEP file origin) to follow the model
            if hasattr(self.vtk_renderer_left, 'part_origin_sphere') and self.vtk_renderer_left.part_origin_sphere:
                print(f"DEBUG: Moving part origin actors (STEP file origin) by {amount}mm on {axis}-axis")
                part_origin_actors = [
                    self.vtk_renderer_left.part_origin_sphere,
                    self.vtk_renderer_left.part_origin_x_arrow,
                    self.vtk_renderer_left.part_origin_y_arrow,
                    self.vtk_renderer_left.part_origin_z_arrow
                ]
                for part_actor in part_origin_actors:
                    if part_actor:
                        if axis == 'x':
                            part_actor.AddPosition(amount, 0, 0)
                        elif axis == 'y':
                            part_actor.AddPosition(0, amount, 0)
                        elif axis == 'z':
                            part_actor.AddPosition(0, 0, amount)

            # Update bounding box to follow movement - FORCE RECREATION
            if self.bbox_visible_left:
                print("DEBUG: FORCE recreating bounding box after movement")
                # Remove old bounding box completely
                if hasattr(self.vtk_renderer_left, 'bbox_actor') and self.vtk_renderer_left.bbox_actor:
                    self.vtk_renderer_left.renderer.RemoveActor(self.vtk_renderer_left.bbox_actor)
                    self.vtk_renderer_left.bbox_actor = None
                # Force recreation of bounding box to match transformed model
                self.vtk_renderer_left.toggle_bounding_box(True)
            # ALSO move overlay actors if overlay mode is active
            if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_renderer'):
                print(f"DEBUG: Also moving overlay actors by {amount}mm on {axis}-axis")
                actors = self.overlay_renderer.GetActors()
                actors.InitTraversal()
                actor = actors.GetNextActor()
                while actor:
                    if axis == 'x':
                        actor.AddPosition(amount, 0, 0)
                    elif axis == 'y':
                        actor.AddPosition(0, amount, 0)
                    elif axis == 'z':
                        actor.AddPosition(0, 0, amount)
                    actor = actors.GetNextActor()
                # Force render overlay
                if hasattr(self, 'vtk_widget_left'):
                    self.vtk_widget_left.GetRenderWindow().Render()

            # Render the update
            self.vtk_renderer_left.render_window.Render()

        else:
            # FIXED: Update position relative to current position, not original
            # Simply add the movement amount to the current position
            self.current_pos_right[axis] += amount

            # Update movement delta for tracking total movement from original
            if hasattr(self, 'movement_delta_right'):
                self.movement_delta_right[axis] += amount
            else:
                self.movement_delta_right = {'x': 0.0, 'y': 0.0, 'z': 0.0}
                self.movement_delta_right[axis] = amount

            print(f"DEBUG: Position move - BOTTOM {axis} += {amount}mm, new_pos={self.current_pos_right[axis]:.3f}mm")

            # Apply actual translation to ALL VTK actors (handles multi-color models)
            actors_moved = False

            if hasattr(self.vtk_renderer_right, 'step_actors') and self.vtk_renderer_right.step_actors:
                # Multi-color model with separate actors
                print(f"DEBUG: Moving {len(self.vtk_renderer_right.step_actors)} multi-actors by {amount}mm on {axis}-axis")
                for actor in self.vtk_renderer_right.step_actors:
                    if axis == 'x':
                        actor.AddPosition(amount, 0, 0)
                    elif axis == 'y':
                        actor.AddPosition(0, amount, 0)
                    elif axis == 'z':
                        actor.AddPosition(0, 0, amount)
                actors_moved = True

                # ALSO move overlay BOTTOM actors if overlay mode is active
                if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_bottom_actors'):
                    print(f"DEBUG: Also moving {len(self.overlay_bottom_actors)} overlay BOTTOM actors by {amount}mm on {axis}-axis")
                    for overlay_actor in self.overlay_bottom_actors:
                        if axis == 'x':
                            overlay_actor.AddPosition(amount, 0, 0)
                        elif axis == 'y':
                            overlay_actor.AddPosition(0, amount, 0)
                        elif axis == 'z':
                            overlay_actor.AddPosition(0, 0, amount)

            if (hasattr(self.vtk_renderer_right, 'step_actor') and
                self.vtk_renderer_right.step_actor and
                self._is_actor_in_renderer(self.vtk_renderer_right.step_actor, self.vtk_renderer_right)):
                # Single actor model - ONLY move if it's actually in the renderer
                print(f"DEBUG: Moving single actor by {amount}mm on {axis}-axis")
                if axis == 'x':
                    self.vtk_renderer_right.step_actor.AddPosition(amount, 0, 0)
                elif axis == 'y':
                    self.vtk_renderer_right.step_actor.AddPosition(0, amount, 0)
                elif axis == 'z':
                    self.vtk_renderer_right.step_actor.AddPosition(0, 0, amount)
                actors_moved = True

                # ALSO move overlay BOTTOM actors if overlay mode is active (single actor case)
                if hasattr(self, 'overlay_mode') and self.overlay_mode and hasattr(self, 'overlay_bottom_actors'):
                    print(f"DEBUG: Also moving {len(self.overlay_bottom_actors)} overlay BOTTOM single actors by {amount}mm on {axis}-axis")
                    for overlay_actor in self.overlay_bottom_actors:
                        if axis == 'x':
                            overlay_actor.AddPosition(amount, 0, 0)
                        elif axis == 'y':
                            overlay_actor.AddPosition(0, amount, 0)
                        elif axis == 'z':
                            overlay_actor.AddPosition(0, 0, amount)

            elif hasattr(self.vtk_renderer_right, 'step_actor') and self.vtk_renderer_right.step_actor:
                print(f"DEBUG: Skipping single actor movement - not in renderer")

            if not actors_moved:
                print(f"DEBUG: No actors found to move!")

            # ALSO move origin actors to follow the model
            if hasattr(self.vtk_renderer_right, 'origin_actors') and self.vtk_renderer_right.origin_actors:
                print(f"DEBUG: Moving {len(self.vtk_renderer_right.origin_actors)} origin actors by {amount}mm on {axis}-axis")
                for origin_actor in self.vtk_renderer_right.origin_actors:
                    if axis == 'x':
                        origin_actor.AddPosition(amount, 0, 0)
                    elif axis == 'y':
                        origin_actor.AddPosition(0, amount, 0)
                    elif axis == 'z':
                        origin_actor.AddPosition(0, 0, amount)

            # ALSO move part origin actors (actual STEP file origin) to follow the model
            if hasattr(self.vtk_renderer_right, 'part_origin_sphere') and self.vtk_renderer_right.part_origin_sphere:
                print(f"DEBUG: Moving part origin actors (STEP file origin) by {amount}mm on {axis}-axis")
                part_origin_actors = [
                    self.vtk_renderer_right.part_origin_sphere,
                    self.vtk_renderer_right.part_origin_x_arrow,
                    self.vtk_renderer_right.part_origin_y_arrow,
                    self.vtk_renderer_right.part_origin_z_arrow
                ]
                for part_actor in part_origin_actors:
                    if part_actor:
                        if axis == 'x':
                            part_actor.AddPosition(amount, 0, 0)
                        elif axis == 'y':
                            part_actor.AddPosition(0, amount, 0)
                        elif axis == 'z':
                            part_actor.AddPosition(0, 0, amount)

            # Update bounding box to follow movement - FORCE RECREATION
            if self.bbox_visible_right:
                print("DEBUG: FORCE recreating bounding box after movement")
                # Remove old bounding box completely
                if hasattr(self.vtk_renderer_right, 'bbox_actor') and self.vtk_renderer_right.bbox_actor:
                    self.vtk_renderer_right.renderer.RemoveActor(self.vtk_renderer_right.bbox_actor)
                    self.vtk_renderer_right.bbox_actor = None
                # Force recreation of bounding box to match transformed model
                self.vtk_renderer_right.toggle_bounding_box(True)
            # Render the update
            self.vtk_renderer_right.render_window.Render()

        # Update the text display
        self.update_transform_display()
        # Force update VTK text overlays to show new position values
        self.update_text_overlays()
        self.statusBar().showMessage(f"{self.active_viewer.title()}: Moved {amount}mm along {axis}-axis")

    def show_help(self):
        """Show comprehensive help dialog with simple explanations"""
        from PyQt5.QtWidgets import QMessageBox
        help_text = """
DEBUG STEP VIEWER TDK - Complete Button Guide

???????????????????????????????????????????????????????????????

? VIEWER SELECTION (Choose which screen to work with):
? Top Viewer - Work with the top 3D window
? Bottom Viewer - Work with the bottom 3D window

???????????????????????????????????????????????????????????????

FILE FILE OPERATIONS (Load and save your 3D models):
? Open STEP File - Browse and load a 3D model file (.step/.stp)

? Save Option 1 (Green) - Saves the ORIGINAL model geometry but
  includes the position/rotation values you set in the GUI

? Save Option 2 (Blue) - Saves the TRANSFORMED model geometry
  (actually moved/rotated) with original position/rotation values

? Save Original STEP - Saves the unmodified original file

???????????????????????????????????????????????????????????????

? ROTATION CONTROLS (Spin the model around):
? X+ / X- - Rotate around X-axis (roll left/right)
? Y+ / Y- - Rotate around Y-axis (pitch up/down)
? Z+ / Z- - Rotate around Z-axis (yaw left/right)
? Step: [15deg] - How many degrees each click rotates

???????????????????????????????????????????????????????????????

? POSITION CONTROLS (Move the model around):
? X+ / X- - Move left/right (east/west)
? Y+ / Y- - Move forward/backward (north/south)
? Z+ / Z- - Move up/down (closer/farther)
? Step: [0.10mm] - How far each click moves

???????????????????????????????????????????????????????????????

?? VIEW OPERATIONS (Control what you see):
? Clear Active View - Remove model from selected viewer
? Fit Active View - Zoom to show the whole model
? Reset to Original - Undo all changes, back to start
? Align Bottom-Center - Move model to bottom-center position

???????????????????????????????????????????????????????????????

TARGET ORIGIN & VISUAL AIDS:
? Toggle Origin Overlay - Show/hide origin markers (created automatically)
? Create Origin Overlay - Manually add origin markers (usually not needed)
? Toggle Bounding Box - Show/hide red box around model

???????????????????????????????????????????????????????????????

? OVERLAY FEATURES:
? Overlay Bottom on Top - Show both viewers on top screen

???????????????????????????????????????????????????????????????

INFO QUICK START GUIDE:
1. Click "Top Viewer" to select the top window
2. Click "Open STEP File" and choose your 3D model
3. Use X+/Y+/Z+ rotation buttons to spin until it looks right
4. Use X+/Y+/Z+ position buttons to move it where you want
5. Click "Save Option 1" or "Save Option 2" to save your work

INFO TIPS:
? Yellow text shows current rotation angles and position
? Red semicircle shows world origin (0,0,0)
? Green sphere shows the model's original origin point
? Both origin markers help you understand model positioning
        """

        msg = QMessageBox()
        msg.setWindowTitle("STEP Viewer TDK - Complete Help Guide")
        msg.setText(help_text)
        msg.setIcon(QMessageBox.Information)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()

    def _create_user_transform_for_save(self, viewer, axis, degrees):
        """Create/update UserTransform on step_actors so Option 1 can detect rotations"""
        import vtk

        # Get the appropriate renderer
        if viewer == "top":
            renderer = self.vtk_renderer_left
        else:
            renderer = self.vtk_renderer_right

        # Handle both single actor and multi-actor models
        actors_to_transform = []

        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            # Multi-actor model
            actors_to_transform = renderer.step_actors
            print(f"DEBUG: Found {len(actors_to_transform)} step_actors for UserTransform creation")
        elif hasattr(renderer, 'step_actor') and renderer.step_actor:
            # Single actor model
            actors_to_transform = [renderer.step_actor]
            print(f"DEBUG: Found single step_actor for UserTransform creation")
        else:
            print(f"DEBUG: No step_actors found for {viewer} viewer - cannot create UserTransform")
            return

        # Apply UserTransform to all actors
        first_user_transform = None
        for i, step_actor in enumerate(actors_to_transform):
            if not step_actor:
                continue

            # Get or create UserTransform
            user_transform = step_actor.GetUserTransform()
            if not user_transform:
                user_transform = vtk.vtkTransform()
                step_actor.SetUserTransform(user_transform)
                print(f"DEBUG: Created new UserTransform for {viewer} viewer step_actor[{i}]")
            else:
                print(f"DEBUG: Using existing UserTransform for {viewer} viewer step_actor[{i}]")

            # Apply the rotation to the UserTransform (same as applied to actors)
            user_transform.RotateWXYZ(-degrees,  # Same direction as actors
                1 if axis == 'x' else 0,
                1 if axis == 'y' else 0,
                1 if axis == 'z' else 0)

            print(f"DEBUG: Applied {degrees}deg rotation on {axis}-axis to UserTransform[{i}]")
            print(f"DEBUG: UserTransform[{i}] now exists: {user_transform is not None}")

            # Store the first transform for origin position calculation
            if first_user_transform is None:
                first_user_transform = user_transform

        # NOTE: Don't use UserTransform to move green origin markers for button rotations
        # Let them rotate naturally with the model using RotateWXYZ in the rotation method
        print(f"DEBUG: Skipping UserTransform positioning for green markers - they will rotate with RotateWXYZ instead")

    def _update_origin_position_after_rotation(self, viewer, user_transform):
        """Update the Origin position numbers to reflect the transformed position of the green origin marker"""
        import vtk

        print(f"DEBUG ORIGIN UPDATE: _update_origin_position_after_rotation called for {viewer}")
        print(f"DEBUG ORIGIN UPDATE: user_transform = {user_transform}")

        try:
            # Get the model center (bounding box center) instead of STEP file origin
            # This is what should be transformed to show where the origin markers are positioned
            if viewer == "top":
                renderer = getattr(self, 'vtk_renderer_left', None)
                if renderer and hasattr(renderer, 'step_actors') and renderer.step_actors:
                    # Use the first actor to get model bounds
                    bounds = renderer.step_actors[0].GetBounds()
                    model_center = [
                        (bounds[0] + bounds[1]) / 2.0,
                        (bounds[2] + bounds[3]) / 2.0,
                        (bounds[4] + bounds[5]) / 2.0
                    ]
                    print(f"DEBUG: Using model center from bounds: {model_center}")
                elif hasattr(self, 'orig_pos_left'):
                    model_center = [self.orig_pos_left['x'], self.orig_pos_left['y'], self.orig_pos_left['z']]
                    print(f"DEBUG: Using orig_pos_left as model center: {model_center}")
                else:
                    print("DEBUG: No model bounds or orig_pos_left found, using (0,0,0)")
                    model_center = [0.0, 0.0, 0.0]
            else:
                renderer = getattr(self, 'vtk_renderer_right', None)
                if renderer and hasattr(renderer, 'step_actors') and renderer.step_actors:
                    # Use the first actor to get model bounds
                    bounds = renderer.step_actors[0].GetBounds()
                    model_center = [
                        (bounds[0] + bounds[1]) / 2.0,
                        (bounds[2] + bounds[3]) / 2.0,
                        (bounds[4] + bounds[5]) / 2.0
                    ]
                    print(f"DEBUG: Using model center from bounds: {model_center}")
                elif hasattr(self, 'orig_pos_right'):
                    model_center = [self.orig_pos_right['x'], self.orig_pos_right['y'], self.orig_pos_right['z']]
                    print(f"DEBUG: Using orig_pos_right as model center: {model_center}")
                else:
                    print("DEBUG: No model bounds or orig_pos_right found, using (0,0,0)")
                    model_center = [0.0, 0.0, 0.0]

            print(f"DEBUG: Model center to transform: {model_center}")

            # Transform the model center using the UserTransform
            point = vtk.vtkPoints()
            point.InsertNextPoint(model_center)
            point_polydata = vtk.vtkPolyData()
            point_polydata.SetPoints(point)

            transform_filter = vtk.vtkTransformPolyDataFilter()
            transform_filter.SetInputData(point_polydata)
            transform_filter.SetTransform(user_transform)
            transform_filter.Update()

            transformed_point = transform_filter.GetOutput().GetPoint(0)
            print(f"DEBUG: Transformed model center: {transformed_point}")

            # Update the current_pos values to reflect the new transformed position
            if viewer == "top":
                self.current_pos_left = {
                    'x': transformed_point[0],
                    'y': transformed_point[1],
                    'z': transformed_point[2]
                }
                print(f"DEBUG: Updated current_pos_left to: {self.current_pos_left}")

                # CRITICAL DEBUG: Mark this as a rotation update to detect if it gets overridden
                self._rotation_update_marker = True
                self._rotation_update_values = self.current_pos_left.copy()
            else:
                self.current_pos_right = {
                    'x': transformed_point[0],
                    'y': transformed_point[1],
                    'z': transformed_point[2]
                }
                print(f"DEBUG: Updated current_pos_right to: {self.current_pos_right}")

            # CRITICAL: Update visual position of part origin markers (green sphere + arrows)
            print(f"DEBUG ORIGIN UPDATE: Moving part origin markers to new position: {transformed_point}")
            if viewer == "top":
                renderer = getattr(self, 'vtk_renderer_left', None)
                if renderer and hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
                    # Update position using SetPosition (this works correctly)
                    renderer.part_origin_sphere.SetPosition(transformed_point[0], transformed_point[1], transformed_point[2])
                    print(f"DEBUG: Updated TOP green sphere position to {transformed_point}")

                    # Move the green arrows to the new position
                    if hasattr(renderer, 'part_origin_x_arrow') and renderer.part_origin_x_arrow:
                        renderer.part_origin_x_arrow.SetPosition(transformed_point[0], transformed_point[1], transformed_point[2])
                    if hasattr(renderer, 'part_origin_y_arrow') and renderer.part_origin_y_arrow:
                        renderer.part_origin_y_arrow.SetPosition(transformed_point[0], transformed_point[1], transformed_point[2])
                    if hasattr(renderer, 'part_origin_z_arrow') and renderer.part_origin_z_arrow:
                        renderer.part_origin_z_arrow.SetPosition(transformed_point[0], transformed_point[1], transformed_point[2])
                    print(f"DEBUG: Moved all part origin arrows to {transformed_point}")
            else:
                renderer = getattr(self, 'vtk_renderer_right', None)
                if renderer and hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
                    # Update position using SetPosition (this works correctly)
                    renderer.part_origin_sphere.SetPosition(transformed_point[0], transformed_point[1], transformed_point[2])
                    print(f"DEBUG: Updated BOTTOM green sphere position to {transformed_point}")

                    # Move the green arrows to the new position
                    if hasattr(renderer, 'part_origin_x_arrow') and renderer.part_origin_x_arrow:
                        renderer.part_origin_x_arrow.SetPosition(transformed_point[0], transformed_point[1], transformed_point[2])
                    if hasattr(renderer, 'part_origin_y_arrow') and renderer.part_origin_y_arrow:
                        renderer.part_origin_y_arrow.SetPosition(transformed_point[0], transformed_point[1], transformed_point[2])
                    if hasattr(renderer, 'part_origin_z_arrow') and renderer.part_origin_z_arrow:
                        renderer.part_origin_z_arrow.SetPosition(transformed_point[0], transformed_point[1], transformed_point[2])
                    print(f"DEBUG: Moved all part origin arrows to {transformed_point}")

            # Force a render to show the moved origin markers
            if viewer == "top":
                renderer = getattr(self, 'vtk_renderer_left', None)
                if renderer and hasattr(renderer, 'render_window') and renderer.render_window:
                    renderer.render_window.Render()
                    print(f"DEBUG: Forced render for TOP viewer to show moved origin markers")
            else:
                renderer = getattr(self, 'vtk_renderer_right', None)
                if renderer and hasattr(renderer, 'render_window') and renderer.render_window:
                    renderer.render_window.Render()
                    print(f"DEBUG: Forced render for BOTTOM viewer to show moved origin markers")

            # CRITICAL: Update text overlays to show the new Origin values
            print(f"DEBUG ORIGIN UPDATE: Refreshing text overlays to show new Origin values")
            print(f"DEBUG ORIGIN UPDATE: About to call update_text_overlays() with current_pos_left = {self.current_pos_left if viewer == 'top' else self.current_pos_right}")
            self.update_text_overlays()
            print(f"DEBUG ORIGIN UPDATE: Finished calling update_text_overlays()")

            # Force a render to show the moved origin markers
            if viewer == "top":
                renderer = getattr(self, 'vtk_renderer_left', None)
                if renderer and hasattr(renderer, 'render_window') and renderer.render_window:
                    renderer.render_window.Render()
                    print(f"DEBUG: Forced render for TOP viewer to show moved origin markers")
            else:
                renderer = getattr(self, 'vtk_renderer_right', None)
                if renderer and hasattr(renderer, 'render_window') and renderer.render_window:
                    renderer.render_window.Render()
                    print(f"DEBUG: Forced render for BOTTOM viewer to show moved origin markers")

        except Exception as e:
            print(f"ERROR: Failed to update origin position after rotation: {e}")
            import traceback
            traceback.print_exc()

    def _update_text_display_after_button_rotation(self, viewer):
        """
        Update the yellow text display to show the current position of the rotated origin markers.
        This reads the actual position of the green origin markers after they've been rotated.
        """
        print(f"DEBUG TEXT UPDATE: Starting _update_text_display_after_button_rotation for {viewer} viewer")

        try:
            # Get the current position of the green origin sphere
            if viewer == "top":
                renderer = getattr(self, 'vtk_renderer_left', None)
            else:
                renderer = getattr(self, 'vtk_renderer_right', None)

            if not renderer or not hasattr(renderer, 'part_origin_sphere') or not renderer.part_origin_sphere:
                print(f"DEBUG TEXT UPDATE: No green origin sphere found for {viewer} viewer")
                return

            # Get the actual current position of the rotated green sphere
            current_sphere_pos = renderer.part_origin_sphere.GetPosition()
            print(f"DEBUG TEXT UPDATE: Current green sphere position = {current_sphere_pos}")

            # Update the current position values for the yellow text display
            if viewer == "top":
                self.current_pos_left['x'] = current_sphere_pos[0]
                self.current_pos_left['y'] = current_sphere_pos[1]
                self.current_pos_left['z'] = current_sphere_pos[2]
                print(f"DEBUG TEXT UPDATE: Updated current_pos_left = {self.current_pos_left}")
            else:
                self.current_pos_right['x'] = current_sphere_pos[0]
                self.current_pos_right['y'] = current_sphere_pos[1]
                self.current_pos_right['z'] = current_sphere_pos[2]
                print(f"DEBUG TEXT UPDATE: Updated current_pos_right = {self.current_pos_right}")

            # Update text overlays to show the new Origin values
            print(f"DEBUG TEXT UPDATE: Refreshing text overlays to show new Origin values")
            self.update_text_overlays()
            print(f"DEBUG TEXT UPDATE: Finished calling update_text_overlays()")

            # Force a render to update the display
            if viewer == "top":
                if renderer and hasattr(renderer, 'render_window') and renderer.render_window:
                    renderer.render_window.Render()
                    print(f"DEBUG TEXT UPDATE: Forced render for TOP viewer")
            else:
                if renderer and hasattr(renderer, 'render_window') and renderer.render_window:
                    renderer.render_window.Render()
                    print(f"DEBUG TEXT UPDATE: Forced render for BOTTOM viewer")

        except Exception as e:
            print(f"ERROR in _update_text_display_after_button_rotation: {e}")
            import traceback
            traceback.print_exc()

def main():
    app = QApplication(sys.argv)
    app.setApplicationName("TDK STEP Viewer Dual")
    app.setApplicationVersion("3.0")

    viewer = StepViewerTDK()
    viewer.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()