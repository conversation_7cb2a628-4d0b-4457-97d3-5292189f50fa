#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

print("=== SIMPLE SAVE TEST ===")

try:
    from step_viewer import StepViewerTDK
    print("✅ Imported StepViewerTDK")
    
    # Create viewer
    viewer = StepViewerTDK()
    print("✅ Created viewer")
    
    # Load file
    success = viewer.load_step_file_direct('demo.step')
    print(f"Load result: {success}")
    
    if success:
        # Apply simple rotation
        viewer._apply_model_rotation('top', 'x', 30.0)
        print("✅ Applied rotation")
        
        # Get rotation
        rotated_rot = viewer.current_rot_left.copy()
        print(f"Current rotation: {rotated_rot}")
        
        # Get loader
        loader = viewer.step_loader_left
        print(f"Loader exists: {loader is not None}")
        
        if loader and hasattr(loader, 'original_shape'):
            print(f"Original shape exists: {loader.original_shape is not None}")
        
        # Try save
        print("*** CALLING SAVE METHOD ***")
        success = viewer._save_step_with_current_rotation('simple_test.step', loader, rotated_rot)
        print(f"*** SAVE RESULT: {success} ***")
        
        if os.path.exists('simple_test.step'):
            size = os.path.getsize('simple_test.step')
            print(f"✅ File created: simple_test.step ({size} bytes)")
        else:
            print("❌ File not created")
    
except Exception as e:
    print(f"❌ Exception: {e}")
    import traceback
    traceback.print_exc()

print("=== TEST FINISHED ===")
