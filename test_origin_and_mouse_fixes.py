#!/usr/bin/env python3
"""
Test script to verify origin markers and smooth mouse movement fixes
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_origin_and_mouse_fixes():
    """Test that origin markers appear and mouse movement is smooth"""
    print("🔍 TESTING ORIGIN MARKERS AND MOUSE MOVEMENT FIXES")
    print("=" * 60)
    
    try:
        # Create QApplication
        app = QApplication(sys.argv)
        print("✅ Created QApplication")
        
        # Import and create viewer
        from step_viewer import StepViewerTDK
        print("✅ Imported StepViewerTDK")
        
        # Create viewer
        viewer = StepViewerTDK()
        print("✅ Created viewer")
        
        # Check if origin markers were automatically created
        origin_markers_found = False
        if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
            if hasattr(viewer.vtk_renderer_left, 'origin_actors'):
                origin_count = len(viewer.vtk_renderer_left.origin_actors)
                print(f"✅ TOP viewer origin actors: {origin_count}")
                if origin_count > 0:
                    origin_markers_found = True
                    
                    # Check if they're visible
                    visible_count = 0
                    for actor in viewer.vtk_renderer_left.origin_actors:
                        if actor and actor.GetVisibility():
                            visible_count += 1
                    print(f"✅ Visible origin actors: {visible_count}")
                    
        if hasattr(viewer, 'vtk_renderer_right') and viewer.vtk_renderer_right:
            if hasattr(viewer.vtk_renderer_right, 'origin_actors'):
                origin_count = len(viewer.vtk_renderer_right.origin_actors)
                print(f"✅ BOTTOM viewer origin actors: {origin_count}")
                if origin_count > 0:
                    origin_markers_found = True
        
        # Check mouse sensitivity settings
        mouse_settings_ok = False
        if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
            if hasattr(viewer.vtk_renderer_left, 'interactor') and viewer.vtk_renderer_left.interactor:
                style = viewer.vtk_renderer_left.interactor.GetInteractorStyle()
                if style:
                    motion_factor = style.GetMotionFactor()
                    print(f"✅ TOP viewer motion factor: {motion_factor}")
                    if motion_factor <= 1.0:  # Should be 1.0 or less for smooth movement
                        mouse_settings_ok = True
                        
        # Load a STEP file to test with actual geometry
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if os.path.exists(step_file):
            print(f"🔄 Loading STEP file: {step_file}")
            success = viewer._unified_load(step_file)
            if success:
                print("✅ STEP file loaded successfully")
                
                # Check if part origin markers were created
                part_origin_found = False
                if hasattr(viewer.vtk_renderer_left, 'part_origin_sphere'):
                    if viewer.vtk_renderer_left.part_origin_sphere:
                        print("✅ Part origin markers created")
                        part_origin_found = True
                        
                        # Check visibility
                        if viewer.vtk_renderer_left.part_origin_sphere.GetVisibility():
                            print("✅ Part origin markers are visible")
                        else:
                            print("❌ Part origin markers are not visible")
                
                if not part_origin_found:
                    print("❌ Part origin markers not found")
            else:
                print("❌ STEP file loading failed")
        else:
            print(f"⚠️ STEP file not found: {step_file}")
        
        # Show the viewer
        viewer.show()
        print("✅ Viewer window shown")
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 TEST RESULTS SUMMARY:")
        print("=" * 60)
        
        if origin_markers_found:
            print("✅ ORIGIN MARKERS: Working - automatically created")
        else:
            print("❌ ORIGIN MARKERS: Missing - not automatically created")
            
        if mouse_settings_ok:
            print("✅ MOUSE MOVEMENT: Fixed - smooth sensitivity settings")
        else:
            print("❌ MOUSE MOVEMENT: Still erratic - high sensitivity")
            
        print("\n🎯 INSTRUCTIONS FOR MANUAL TESTING:")
        print("1. Look for RED SEMICIRCLE and XYZ ARROWS at origin (0,0,0)")
        print("2. Try mouse rotation - should be smooth, not jerky")
        print("3. Load a STEP file - should see GREEN SPHERE at part origin")
        print("4. Mouse movement should be precise and controllable")
        
        # Keep the application running for manual testing
        print("\n⏳ Application running - test manually, then close window...")
        
        # Set up a timer to close after 30 seconds if not closed manually
        def auto_close():
            print("⏰ Auto-closing after 30 seconds...")
            app.quit()
            
        timer = QTimer()
        timer.timeout.connect(auto_close)
        timer.start(30000)  # 30 seconds
        
        # Run the application
        return app.exec_()
        
    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_origin_and_mouse_fixes()
