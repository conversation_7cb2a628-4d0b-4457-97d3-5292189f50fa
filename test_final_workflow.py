#!/usr/bin/env python3
"""
Final test of the complete load-rotate-save workflow
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

def test_complete_workflow():
    print("=== FINAL WORKFLOW TEST ===")
    print("Testing: Load -> Rotate -> Save -> Verify")
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    try:
        # Create viewer
        viewer = StepViewerTDK()
        print("✅ Created viewer")
        
        # Find a STEP file to test with
        step_files = [f for f in os.listdir('.') if f.endswith('.step') or f.endswith('.STEP')]
        if not step_files:
            print("❌ No STEP files found in current directory")
            return False
            
        original_file = step_files[0]
        output_file = "final_test_rotated.step"
        print(f"📁 Original file: {original_file}")
        print(f"📁 Output file: {output_file}")
        
        # Clean up any existing output file
        if os.path.exists(output_file):
            os.remove(output_file)
            print(f"   Removed existing {output_file}")
        
        # Step 1: Load the original file
        print(f"\n1. LOADING: {original_file}")
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(original_file)
        
        if not success:
            print("❌ Failed to load file")
            return False
            
        print("✅ File loaded successfully")
        
        # Get the loader and ensure it points to the original file
        loader = viewer.step_loader_left
        loader.current_filename = original_file
        print(f"   Loader filename: {loader.current_filename}")
        
        # Step 2: Apply rotation
        print(f"\n2. ROTATING: Applying 90° X rotation")
        
        # Initialize original rotation if not set
        if not hasattr(viewer, 'orig_rot_left'):
            viewer.orig_rot_left = {'x': 0, 'y': 0, 'z': 0}
            print("   Initialized original rotation to zero")
        
        # Apply the rotation
        viewer._apply_model_rotation('top', 'x', 90.0)
        
        # Get rotation states
        current_rot = viewer.current_rot_left.copy()
        orig_rot = viewer.orig_rot_left.copy()
        
        print(f"   Original rotation: {orig_rot}")
        print(f"   Current rotation: {current_rot}")
        
        # Calculate delta
        delta_x = current_rot['x'] - orig_rot['x']
        delta_y = current_rot['y'] - orig_rot['y']
        delta_z = current_rot['z'] - orig_rot['z']
        print(f"   Delta rotation: X={delta_x:.1f}° Y={delta_y:.1f}° Z={delta_z:.1f}°")
        
        # Step 3: Save the rotated file
        print(f"\n3. SAVING: {output_file}")
        save_success = viewer._save_step_with_current_rotation(output_file, loader, current_rot)
        
        if not save_success:
            print("❌ Save method failed")
            return False
            
        print("✅ Save method succeeded")
        
        # Check if file was created
        if not os.path.exists(output_file):
            print("❌ Output file was not created")
            return False
            
        # Get file sizes
        orig_size = os.path.getsize(original_file)
        output_size = os.path.getsize(output_file)
        print(f"   Original file size: {orig_size} bytes")
        print(f"   Output file size: {output_size} bytes")
        
        # Step 4: Compare file contents
        print(f"\n4. COMPARING: File contents")
        
        with open(original_file, 'r', encoding='utf-8', errors='ignore') as f:
            original_content = f.read()
        
        with open(output_file, 'r', encoding='utf-8', errors='ignore') as f:
            output_content = f.read()
        
        if original_content == output_content:
            print("❌ File contents are identical - no rotation was applied")
            return False
        else:
            print("✅ File contents are different - rotation was applied")
        
        # Step 5: Count direction vector changes
        import re
        
        # Extract DIRECTION vectors from both files
        direction_pattern = r'#\d+ = DIRECTION \( \'[^\']*\',\s*\(\s*([-\d\.]+)\s*,\s*([-\d\.]+)\s*,\s*([-\d\.]+)\s*\)\s*\)\s*;'
        
        orig_directions = re.findall(direction_pattern, original_content)
        output_directions = re.findall(direction_pattern, output_content)
        
        print(f"   Original file has {len(orig_directions)} DIRECTION vectors")
        print(f"   Output file has {len(output_directions)} DIRECTION vectors")
        
        # Count changes
        changes = 0
        for i, (orig, output) in enumerate(zip(orig_directions, output_directions)):
            if orig != output:
                changes += 1
                if changes <= 3:  # Show first 3 changes
                    print(f"   Changed #{i+1}: {orig} -> {output}")
        
        if changes > 3:
            print(f"   ... and {changes - 3} more changes")
        
        print(f"✅ Found {changes} changed DIRECTION vectors")
        
        # Step 6: Test loading the rotated file
        print(f"\n5. VERIFICATION: Loading rotated file")
        viewer.active_viewer = "bottom"
        verify_success = viewer.load_step_file_direct(output_file)
        
        if verify_success:
            print("✅ Rotated file loads successfully")
        else:
            print("❌ Failed to load rotated file")
            return False
        
        # Final success check
        if changes > 0:
            print(f"\n🎉 WORKFLOW TEST PASSED!")
            print(f"   ✅ File loaded successfully")
            print(f"   ✅ Rotation applied ({delta_x:.1f}° X rotation)")
            print(f"   ✅ File saved with {changes} direction vector changes")
            print(f"   ✅ Rotated file loads successfully")
            return True
        else:
            print(f"\n💥 WORKFLOW TEST FAILED!")
            print(f"   ❌ No direction vectors were changed")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_complete_workflow()
    
    if success:
        print("\n🎉 COMPLETE WORKFLOW WORKING!")
        print("The load-rotate-save functionality is now working correctly.")
    else:
        print("\n💥 WORKFLOW STILL HAS ISSUES!")
    
    sys.exit(0 if success else 1)
