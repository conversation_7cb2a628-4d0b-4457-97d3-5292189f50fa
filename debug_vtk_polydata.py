#!/usr/bin/env python3
"""
Debug what happens to VTK polydata when we rotate the model
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== DEBUG VTK POLYDATA ROTATION ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Testing with: {test_file}")
    
    # Load file
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ File loaded")
        
        # Get original polydata bounds
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'step_actors') and renderer.step_actors:
            original_actor = renderer.step_actors[0]
            original_polydata = original_actor.GetMapper().GetInput()
            original_bounds = original_polydata.GetBounds()
            print(f"Original polydata bounds: {original_bounds}")
            
            # Apply rotation
            print("\nApplying 90° X rotation...")
            viewer._apply_model_rotation("top", "x", 90.0)
            
            # Get rotated polydata bounds
            rotated_actor = renderer.step_actors[0]
            rotated_polydata = rotated_actor.GetMapper().GetInput()
            rotated_bounds = rotated_polydata.GetBounds()
            print(f"Rotated polydata bounds: {rotated_bounds}")
            
            # Check if polydata itself changed
            polydata_changed = any(abs(a - b) > 0.01 for a, b in zip(original_bounds, rotated_bounds))
            print(f"Polydata bounds changed: {polydata_changed}")
            
            if not polydata_changed:
                print("❌ PROBLEM: Polydata itself didn't change!")
                print("The rotation is applied to the ACTOR, not the polydata")
                
                # Check actor transformation
                actor_matrix = rotated_actor.GetMatrix()
                if actor_matrix:
                    print("Actor has transformation matrix:")
                    for i in range(4):
                        row = [actor_matrix.GetElement(i, j) for j in range(4)]
                        print(f"  [{row[0]:.3f}, {row[1]:.3f}, {row[2]:.3f}, {row[3]:.3f}]")
                else:
                    print("Actor has no transformation matrix")
                    
                # Get the transformed polydata
                print("\nGetting transformed polydata...")
                import vtk
                transform_filter = vtk.vtkTransformPolyDataFilter()
                transform_filter.SetInputData(original_polydata)
                
                # Create transform from actor matrix
                vtk_transform = vtk.vtkTransform()
                if actor_matrix:
                    vtk_transform.SetMatrix(actor_matrix)
                    transform_filter.SetTransform(vtk_transform)
                    transform_filter.Update()
                    
                    transformed_polydata = transform_filter.GetOutput()
                    transformed_bounds = transformed_polydata.GetBounds()
                    print(f"Transformed polydata bounds: {transformed_bounds}")
                    
                    # This should match the visual bounds
                    visual_bounds_match = all(abs(a - b) < 0.1 for a, b in zip(transformed_bounds, rotated_bounds))
                    print(f"Transformed polydata matches visual: {visual_bounds_match}")
                    
                    if not visual_bounds_match:
                        # The visual bounds come from the actor transformation
                        print("The visual bounds come from actor transformation")
                        print("We need to apply the actor transform to get the actual rotated geometry")
                        
                        # Save this transformed polydata as STEP
                        print("\nTesting save of transformed polydata...")
                        loader = viewer.step_loader_left
                        
                        # Update the loader's polydata to the transformed version
                        loader.current_polydata = transformed_polydata
                        
                        test_save_file = "debug_vtk_polydata.step"
                        if os.path.exists(test_save_file):
                            os.remove(test_save_file)
                            
                        # Try to save using VTK polydata method
                        success = loader.save_vtk_polydata_as_step(test_save_file)
                        print(f"Save result: {success}")
                        
                        if os.path.exists(test_save_file):
                            file_size = os.path.getsize(test_save_file)
                            print(f"File created: {file_size} bytes")
                            
                            # Test loading the saved file
                            print("Testing load of saved file...")
                            viewer.active_viewer = "bottom"
                            load_success = viewer.load_step_file_direct(test_save_file)
                            
                            if load_success:
                                print("✅ Saved file loaded successfully")
                                
                                # Check bounds of loaded file
                                bottom_renderer = viewer.vtk_renderer_right
                                if hasattr(bottom_renderer, 'step_actors') and bottom_renderer.step_actors:
                                    saved_actor = bottom_renderer.step_actors[0]
                                    saved_polydata = saved_actor.GetMapper().GetInput()
                                    saved_bounds = saved_polydata.GetBounds()
                                    print(f"Saved file bounds: {saved_bounds}")
                                    
                                    # Compare with transformed bounds
                                    bounds_match = all(abs(a - b) < 0.1 for a, b in zip(transformed_bounds, saved_bounds))
                                    print(f"Saved bounds match transformed: {bounds_match}")
                                    
                                    if bounds_match:
                                        print("🎉 SUCCESS: Rotation preserved in saved file!")
                                    else:
                                        print("❌ FAILURE: Rotation not preserved")
                            else:
                                print("❌ Failed to load saved file")
                            
                            os.remove(test_save_file)
                        else:
                            print("❌ No file created")
            else:
                print("✅ Polydata bounds changed directly")
        else:
            print("❌ No actors found")
    else:
        print("❌ Failed to load file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== DEBUG FINISHED ===")
