#!/usr/bin/env python3
"""
FULL DEBUG - Check Everything
=============================
This will systematically check every aspect of the rotation system.
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def full_debug():
    """Full systematic debug of rotation system"""
    print("🔍 FULL DEBUG - CHECKING EVERYTHING")
    print("=" * 60)
    
    # Import after path setup
    try:
        from step_viewer import StepViewerTDK
    except Exception as e:
        print(f"❌ Failed to import step_viewer: {e}")
        return False
    
    # Create Qt application
    app = QApplication([])
    
    # Create step viewer
    viewer = StepViewerTDK()
    viewer.show()
    viewer.active_viewer = "top"
    
    # Wait for initialization
    time.sleep(2)
    
    print("\n1️⃣ CHECKING INITIAL STATE")
    print("-" * 40)
    check_initial_state(viewer)
    
    print("\n2️⃣ LOADING STEP FILE")
    print("-" * 40)
    load_step_file(viewer)
    
    print("\n3️⃣ CHECKING AFTER LOAD")
    print("-" * 40)
    check_after_load(viewer)
    
    print("\n4️⃣ TESTING MOUSE ROTATION")
    print("-" * 40)
    test_mouse_rotation(viewer)
    
    print("\n5️⃣ TESTING BUTTON ROTATION")
    print("-" * 40)
    test_button_rotation(viewer)
    
    print("\n6️⃣ COMPARING RESULTS")
    print("-" * 40)
    compare_results(viewer)
    
    # Close after test
    QTimer.singleShot(5000, app.quit)
    app.exec_()

def check_initial_state(viewer):
    """Check what exists initially"""
    print("🔍 Checking initial state...")
    
    # Check renderers
    has_left = hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left
    has_right = hasattr(viewer, 'vtk_renderer_right') and viewer.vtk_renderer_right
    print(f"   Left renderer: {'✅' if has_left else '❌'}")
    print(f"   Right renderer: {'✅' if has_right else '❌'}")
    
    # Check text actors
    has_world_text_left = hasattr(viewer, 'world_origin_text_actor_left')
    has_world_text_right = hasattr(viewer, 'world_origin_text_actor_right')
    print(f"   World text left: {'✅' if has_world_text_left else '❌'}")
    print(f"   World text right: {'✅' if has_world_text_right else '❌'}")
    
    if has_world_text_left:
        text = viewer.world_origin_text_actor_left.GetInput()
        print(f"   Initial world text: {text}")

def load_step_file(viewer):
    """Try to load a STEP file"""
    print("📁 Looking for STEP files...")
    
    step_files = [f for f in os.listdir('.') if f.endswith(('.step', '.stp', '.STEP', '.STP'))]
    if not step_files:
        print("   ❌ No STEP files found")
        return False
    
    step_file = step_files[0]
    print(f"   📁 Found: {step_file}")
    
    # Try to load
    try:
        if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
            if hasattr(viewer.vtk_renderer_left, 'step_loader'):
                loader = viewer.vtk_renderer_left.step_loader
                success, message = loader.load_step_file(step_file)
                print(f"   {'✅' if success else '❌'} Load result: {message}")
                
                if success:
                    # Create origin overlay
                    viewer.vtk_renderer_left.create_origin_overlay()
                    print("   ✅ Origin overlay created")
                    return True
            else:
                print("   ❌ No step loader found")
        else:
            print("   ❌ No left renderer found")
    except Exception as e:
        print(f"   ❌ Load error: {e}")
    
    return False

def check_after_load(viewer):
    """Check state after loading"""
    print("🔍 Checking after load...")
    
    # Check for origin actors
    if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
        renderer = viewer.vtk_renderer_left
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            print(f"   ✅ Found {len(renderer.origin_actors)} origin actors")
            for i, actor in enumerate(renderer.origin_actors):
                if actor:
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    print(f"      Actor {i}: pos={pos}, orient={orient}")
        else:
            print("   ❌ No origin actors found")
    
    # Check world text now
    if hasattr(viewer, 'world_origin_text_actor_left'):
        text = viewer.world_origin_text_actor_left.GetInput()
        print(f"   📊 World text after load: {text}")

def test_mouse_rotation(viewer):
    """Test mouse rotation method directly"""
    print("🖱️ Testing mouse rotation...")
    
    try:
        # Get initial state
        initial_text = get_world_text(viewer)
        initial_positions = get_origin_positions(viewer)
        
        print(f"   📊 Before mouse rotation: {initial_text}")
        print(f"   📍 Initial positions: {len(initial_positions)} actors")
        
        # Apply mouse rotation
        viewer._apply_model_rotation("top", "y", 15.0)
        time.sleep(0.5)
        
        # Get new state
        new_text = get_world_text(viewer)
        new_positions = get_origin_positions(viewer)
        
        print(f"   📊 After mouse rotation: {new_text}")
        print(f"   📍 New positions: {len(new_positions)} actors")
        
        # Check if changed
        text_changed = initial_text != new_text
        positions_changed = initial_positions != new_positions
        
        print(f"   {'✅' if text_changed else '❌'} Text changed: {text_changed}")
        print(f"   {'✅' if positions_changed else '❌'} Positions changed: {positions_changed}")
        
    except Exception as e:
        print(f"   ❌ Mouse rotation error: {e}")

def test_button_rotation(viewer):
    """Test button rotation method"""
    print("🔘 Testing button rotation...")
    
    try:
        # Get initial state
        initial_text = get_world_text(viewer)
        initial_positions = get_origin_positions(viewer)
        
        print(f"   📊 Before button rotation: {initial_text}")
        print(f"   📍 Initial positions: {len(initial_positions)} actors")
        
        # Apply button rotation
        viewer.rotate_shape("z", 15.0)
        time.sleep(0.5)
        
        # Get new state
        new_text = get_world_text(viewer)
        new_positions = get_origin_positions(viewer)
        
        print(f"   📊 After button rotation: {new_text}")
        print(f"   📍 New positions: {len(new_positions)} actors")
        
        # Check if changed
        text_changed = initial_text != new_text
        positions_changed = initial_positions != new_positions
        
        print(f"   {'✅' if text_changed else '❌'} Text changed: {text_changed}")
        print(f"   {'✅' if positions_changed else '❌'} Positions changed: {positions_changed}")
        
    except Exception as e:
        print(f"   ❌ Button rotation error: {e}")

def compare_results(viewer):
    """Compare mouse vs button behavior"""
    print("⚖️ Comparing mouse vs button behavior...")
    
    # Check rotation tracking
    if hasattr(viewer, 'current_rot_left'):
        print(f"   📊 Final rotation tracking: {viewer.current_rot_left}")
    
    # Check final world text
    final_text = get_world_text(viewer)
    print(f"   📊 Final world text: {final_text}")
    
    # Summary
    print("\n🎯 SUMMARY:")
    print("   If world origin numbers are still (0,0,0) after rotation,")
    print("   then the calculation method is wrong.")
    print("   If origin actors don't move, then rotation isn't applied to them.")

def get_world_text(viewer):
    """Get current world origin text"""
    try:
        if hasattr(viewer, 'world_origin_text_actor_left'):
            return viewer.world_origin_text_actor_left.GetInput()
    except:
        pass
    return "No world text found"

def get_origin_positions(viewer):
    """Get current origin actor positions"""
    positions = []
    try:
        if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
            renderer = viewer.vtk_renderer_left
            if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
                for actor in renderer.origin_actors:
                    if actor:
                        positions.append(actor.GetPosition())
    except:
        pass
    return positions

if __name__ == "__main__":
    full_debug()
