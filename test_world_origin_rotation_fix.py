#!/usr/bin/env python3
"""
Quick test to verify that world origin stays at (0,0,0) during ALL rotations
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK as StepViewer

class WorldOriginRotationTester:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewer()
        self.test_sequence = [
            ('move', 'x', 2.0, 'X+ Translation (setup)'),
            ('move', 'y', 2.0, 'Y+ Translation (setup)'),
            ('rotate', 'x', 15.0, 'X+ Rotation'),
            ('rotate', 'y', 15.0, 'Y+ Rotation'),
            ('rotate', 'z', 15.0, 'Z+ Rotation'),
        ]
        self.current_test = 0
        
    def check_world_origin_position(self, stage_name):
        """Check if world origin is at (0,0,0)"""
        if not hasattr(self.viewer, 'vtk_renderer_left') or not self.viewer.vtk_renderer_left:
            return False
            
        renderer = self.viewer.vtk_renderer_left.renderer
        if not renderer:
            return False
            
        # Find red actors (world origin)
        red_actors = []
        actors = renderer.GetActors()
        actors.InitTraversal()
        
        while True:
            actor = actors.GetNextActor()
            if not actor:
                break
                
            color = actor.GetProperty().GetColor()
            if color[0] > 0.8 and color[1] < 0.3 and color[2] < 0.3:  # Red
                pos = actor.GetPosition()
                red_actors.append(pos)
                
        print(f"\n🔴 {stage_name} - World Origin Check:")
        all_at_origin = True
        for i, pos in enumerate(red_actors):
            distance_from_origin = sum(abs(pos[j]) for j in range(3))
            at_origin = distance_from_origin < 0.1
            status = "✅" if at_origin else "❌"
            print(f"   {status} Red {i+1}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) - {'AT ORIGIN' if at_origin else 'NOT AT ORIGIN'}")
            if not at_origin:
                all_at_origin = False
                
        return all_at_origin
        
    def run_test(self):
        """Run the test"""
        print("🔴 TESTING WORLD ORIGIN ROTATION FIX")
        print("="*60)
        
        self.viewer.show()
        QTimer.singleShot(1000, self.load_step_file)
        self.app.exec_()
        
    def load_step_file(self):
        """Load STEP file"""
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            self.app.quit()
            return
            
        self.viewer.active_viewer = "top"
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print(f"❌ Failed to load STEP file")
            self.app.quit()
            return
            
        print(f"✅ STEP file loaded successfully")
        QTimer.singleShot(2000, self.run_next_test)
        
    def run_next_test(self):
        """Run next test"""
        if self.current_test >= len(self.test_sequence):
            self.final_analysis()
            return
            
        operation_type, axis, amount, description = self.test_sequence[self.current_test]
        
        print(f"\n🔄 PERFORMING: {description}")
        
        # Check before
        if operation_type == 'rotate':
            before_ok = self.check_world_origin_position(f"BEFORE {description}")
        
        # Perform operation
        self.viewer.active_viewer = "top"
        if operation_type == 'rotate':
            self.viewer.rotate_shape(axis, amount)
        elif operation_type == 'move':
            self.viewer.move_shape(axis, amount)
            
        # Check after
        QTimer.singleShot(1000, lambda: self.check_after_operation(operation_type, description))
        
    def check_after_operation(self, operation_type, description):
        """Check after operation"""
        if operation_type == 'rotate':
            after_ok = self.check_world_origin_position(f"AFTER {description}")
            if after_ok:
                print(f"   ✅ {description}: World origin correctly stayed at (0,0,0)")
            else:
                print(f"   ❌ {description}: World origin moved away from (0,0,0) - FIX FAILED!")
        else:
            # For translations, just note the position
            self.check_world_origin_position(f"AFTER {description}")
            
        self.current_test += 1
        QTimer.singleShot(1000, self.run_next_test)
        
    def final_analysis(self):
        """Final analysis"""
        print(f"\n🎯 FINAL WORLD ORIGIN ROTATION TEST COMPLETE")
        print(f"="*60)
        print(f"Check the results above to see if world origin stays at (0,0,0) during ALL rotations")
        
        QTimer.singleShot(2000, self.app.quit)

if __name__ == "__main__":
    tester = WorldOriginRotationTester()
    tester.run_test()
