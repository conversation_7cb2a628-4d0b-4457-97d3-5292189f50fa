#!/usr/bin/env python3
"""
Test mouse rotation and capture BEFORE/AFTER yellow text numbers
"""

import sys
import os
import time
import re
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

class MouseRotationNumberTester:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.before_numbers = None
        self.after_numbers = None
        
    def extract_origin_numbers(self, text):
        """Extract origin numbers from display text"""
        match = re.search(r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', text)
        if match:
            return (float(match.group(1)), float(match.group(2)), float(match.group(3)))
        return None
    
    def capture_yellow_text_numbers(self, label):
        """Capture current yellow text numbers AND green ball position"""
        try:
            display = self.viewer._calculate_unified_display_numbers("top")
            model_origin = self.extract_origin_numbers(display['model'])
            local_origin = self.extract_origin_numbers(display['local_origin'])

            # Also capture green ball position
            green_ball_pos = None
            if hasattr(self.viewer.vtk_renderer_left, 'part_origin_sphere'):
                green_ball = self.viewer.vtk_renderer_left.part_origin_sphere
                if green_ball:
                    green_ball_pos = green_ball.GetPosition()

            # Also capture current_pos
            current_pos = getattr(self.viewer, 'current_pos_left', None)

            print(f"\n📊 {label} NUMBERS:")
            print(f"   Model Origin: {model_origin}")
            print(f"   Local Origin: {local_origin}")
            print(f"   Green Ball Position: {green_ball_pos}")
            print(f"   current_pos_left: {current_pos}")

            return {
                'model_origin': model_origin,
                'local_origin': local_origin,
                'green_ball_pos': green_ball_pos,
                'current_pos': current_pos
            }
        except Exception as e:
            print(f"❌ Error capturing {label} numbers: {e}")
            return None
    
    def start_test(self):
        """Start the mouse rotation number test"""
        print("🧪 MOUSE ROTATION NUMBER TEST")
        print("=" * 50)
        
        # Create viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Load SOIC file after GUI is ready
        QTimer.singleShot(1000, self.load_file_and_test)
        
        return self.app.exec_()
    
    def load_file_and_test(self):
        """Load file and run the test"""
        print("\n🔧 STEP 1: Loading SOIC file...")
        
        soic_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(soic_file):
            print(f"❌ File not found: {soic_file}")
            self.app.quit()
            return
        
        # Load the file
        self.viewer.load_step_file_direct(soic_file)
        self.app.processEvents()
        time.sleep(2)
        
        # Capture BEFORE numbers
        print("\n🔧 STEP 2: Capturing BEFORE numbers...")
        self.before_numbers = self.capture_yellow_text_numbers("BEFORE")
        
        if not self.before_numbers:
            print("❌ Failed to capture BEFORE numbers")
            self.app.quit()
            return
        
        # Apply mouse rotation
        QTimer.singleShot(1000, self.apply_mouse_rotation)
    
    def apply_mouse_rotation(self):
        """Apply mouse rotation and capture AFTER numbers"""
        print("\n🔧 STEP 3: Applying mouse rotation...")
        
        try:
            # Simulate what mouse rotation does - MUCH LARGER ROTATION
            print("   Calling _apply_model_rotation('top', 'x', 90.0) - BIG ROTATION")
            self.viewer._apply_model_rotation("top", "x", 90.0)
            
            self.app.processEvents()
            time.sleep(1)
            
            # Capture AFTER numbers
            print("\n🔧 STEP 4: Capturing AFTER numbers...")
            self.after_numbers = self.capture_yellow_text_numbers("AFTER")
            
            if not self.after_numbers:
                print("❌ Failed to capture AFTER numbers")
                self.app.quit()
                return
            
            # Compare and show results
            QTimer.singleShot(500, self.show_comparison)
            
        except Exception as e:
            print(f"❌ Error applying rotation: {e}")
            self.app.quit()
    
    def show_comparison(self):
        """Show BEFORE vs AFTER comparison"""
        print("\n" + "=" * 50)
        print("🎯 BEFORE vs AFTER COMPARISON:")
        print("=" * 50)
        
        if self.before_numbers and self.after_numbers:
            # Model Origin comparison
            before_model = self.before_numbers['model_origin']
            after_model = self.after_numbers['model_origin']

            print(f"\n📍 MODEL ORIGIN:")
            print(f"   BEFORE: {before_model}")
            print(f"   AFTER:  {after_model}")

            if before_model and after_model:
                model_changed = (abs(before_model[0] - after_model[0]) > 0.001 or
                               abs(before_model[1] - after_model[1]) > 0.001 or
                               abs(before_model[2] - after_model[2]) > 0.001)

                if model_changed:
                    print(f"   ✅ CHANGED: Model origin numbers DID change!")
                    print(f"   📊 Differences:")
                    print(f"      ΔX = {after_model[0] - before_model[0]:+.6f}")
                    print(f"      ΔY = {after_model[1] - before_model[1]:+.6f}")
                    print(f"      ΔZ = {after_model[2] - before_model[2]:+.6f}")
                else:
                    print(f"   ❌ NO CHANGE: Model origin numbers stayed the same!")

            # Local Origin comparison
            before_local = self.before_numbers['local_origin']
            after_local = self.after_numbers['local_origin']

            print(f"\n📍 LOCAL ORIGIN:")
            print(f"   BEFORE: {before_local}")
            print(f"   AFTER:  {after_local}")

            if before_local and after_local:
                local_changed = (abs(before_local[0] - after_local[0]) > 0.001 or
                               abs(before_local[1] - after_local[1]) > 0.001 or
                               abs(before_local[2] - after_local[2]) > 0.001)

                if local_changed:
                    print(f"   ✅ CHANGED: Local origin numbers DID change!")
                    print(f"   📊 Differences:")
                    print(f"      ΔX = {after_local[0] - before_local[0]:+.6f}")
                    print(f"      ΔY = {after_local[1] - before_local[1]:+.6f}")
                    print(f"      ΔZ = {after_local[2] - before_local[2]:+.6f}")
                else:
                    print(f"   ❌ NO CHANGE: Local origin numbers stayed the same!")

            # Green Ball Position comparison
            before_ball = self.before_numbers['green_ball_pos']
            after_ball = self.after_numbers['green_ball_pos']

            print(f"\n📍 GREEN BALL POSITION:")
            print(f"   BEFORE: {before_ball}")
            print(f"   AFTER:  {after_ball}")

            if before_ball and after_ball:
                ball_changed = (abs(before_ball[0] - after_ball[0]) > 0.001 or
                              abs(before_ball[1] - after_ball[1]) > 0.001 or
                              abs(before_ball[2] - after_ball[2]) > 0.001)

                if ball_changed:
                    print(f"   ✅ CHANGED: Green ball position DID change!")
                    print(f"   📊 Differences:")
                    print(f"      ΔX = {after_ball[0] - before_ball[0]:+.6f}")
                    print(f"      ΔY = {after_ball[1] - before_ball[1]:+.6f}")
                    print(f"      ΔZ = {after_ball[2] - before_ball[2]:+.6f}")
                else:
                    print(f"   ❌ NO CHANGE: Green ball position stayed the same!")

            # current_pos comparison
            before_current = self.before_numbers['current_pos']
            after_current = self.after_numbers['current_pos']

            print(f"\n📍 CURRENT_POS_LEFT:")
            print(f"   BEFORE: {before_current}")
            print(f"   AFTER:  {after_current}")

            if before_current and after_current:
                current_changed = (abs(before_current['x'] - after_current['x']) > 0.001 or
                                 abs(before_current['y'] - after_current['y']) > 0.001 or
                                 abs(before_current['z'] - after_current['z']) > 0.001)

                if current_changed:
                    print(f"   ✅ CHANGED: current_pos_left DID change!")
                    print(f"   📊 Differences:")
                    print(f"      ΔX = {after_current['x'] - before_current['x']:+.6f}")
                    print(f"      ΔY = {after_current['y'] - before_current['y']:+.6f}")
                    print(f"      ΔZ = {after_current['z'] - before_current['z']:+.6f}")
                else:
                    print(f"   ❌ NO CHANGE: current_pos_left stayed the same!")
            
            # Final verdict
            model_works = before_model and after_model and (abs(before_model[0] - after_model[0]) > 0.001 or
                                                          abs(before_model[1] - after_model[1]) > 0.001 or
                                                          abs(before_model[2] - after_model[2]) > 0.001)
            local_works = before_local and after_local and (abs(before_local[0] - after_local[0]) > 0.001 or
                                                          abs(before_local[1] - after_local[1]) > 0.001 or
                                                          abs(before_local[2] - after_local[2]) > 0.001)
            
            print("\n" + "=" * 50)
            if model_works and local_works:
                print("🎉 SUCCESS: Mouse rotation DOES change yellow text numbers!")
                print("✅ The fix is working correctly!")
            else:
                print("❌ FAILURE: Mouse rotation does NOT change yellow text numbers!")
                print("🔧 The fix needs more work!")
            print("=" * 50)
        
        # Exit the application
        print(f"\n🏁 TEST COMPLETE - Exiting application...")
        try:
            # Force close the viewer window
            if hasattr(self.viewer, 'close'):
                self.viewer.close()
            # Force quit the application
            if hasattr(self.app, 'quit'):
                self.app.quit()
            # Force exit
            import sys
            sys.exit(0)
        except:
            import sys
            sys.exit(0)

if __name__ == "__main__":
    tester = MouseRotationNumberTester()
    tester.start_test()
