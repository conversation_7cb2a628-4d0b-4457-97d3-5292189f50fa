#!/usr/bin/env python3
"""
Test to verify the ACTUAL green ball position vs what we think it should be.
This will help us see if there's a disconnect between tracking and rendering.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import time

def test_green_ball_actual_position():
    """Test the actual green ball position"""
    
    print("🔍 TESTING: Actual green ball position vs expected")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for UI to initialize
    app.processEvents()
    time.sleep(1)
    
    print("🔍 Please load a STEP file, then press Enter...")
    input("Press Enter when STEP file is loaded...")
    
    def find_and_check_green_ball():
        """Find the actual green ball actor and check its position"""
        print(f"\n{'='*60}")
        print(f"🔍 SEARCHING FOR GREEN BALL ACTOR")
        print(f"{'='*60}")
        
        # Get expected position from tracking
        expected_pos = None
        if hasattr(viewer, 'current_pos_left'):
            expected_pos = (viewer.current_pos_left['x'], viewer.current_pos_left['y'], viewer.current_pos_left['z'])
            print(f"📊 Expected position (from tracking): {expected_pos}")
        
        # Search through all actors in the top renderer
        green_ball_actor = None
        if (hasattr(viewer, 'vtk_renderer_left') and 
            hasattr(viewer.vtk_renderer_left, 'renderer')):
            
            renderer = viewer.vtk_renderer_left.renderer
            actors = renderer.GetActors()
            actors.InitTraversal()
            
            print(f"🔍 Searching through {actors.GetNumberOfItems()} actors...")
            
            for i in range(actors.GetNumberOfItems()):
                actor = actors.GetNextActor()
                if actor:
                    pos = actor.GetPosition()
                    bounds = actor.GetBounds()
                    
                    # Calculate size to identify small actors (potential green ball)
                    size_x = bounds[1] - bounds[0]
                    size_y = bounds[3] - bounds[2] 
                    size_z = bounds[5] - bounds[4]
                    max_size = max(size_x, size_y, size_z)
                    
                    print(f"🔍 Actor {i}: pos={pos}, size={max_size:.3f}")
                    
                    # Look for small actors that might be the green ball
                    if max_size < 1.0:  # Small actors
                        print(f"   🟢 Small actor found - checking if it's the green ball...")
                        
                        # Check if position is close to expected
                        if expected_pos:
                            diff_x = abs(pos[0] - expected_pos[0])
                            diff_y = abs(pos[1] - expected_pos[1])
                            diff_z = abs(pos[2] - expected_pos[2])
                            total_diff = (diff_x**2 + diff_y**2 + diff_z**2)**0.5
                            
                            print(f"   📏 Distance from expected: {total_diff:.6f}")
                            print(f"   📏 Individual diffs: X={diff_x:.6f}, Y={diff_y:.6f}, Z={diff_z:.6f}")
                            
                            if total_diff < 0.1:  # Close enough
                                print(f"   ✅ This is likely the GREEN BALL!")
                                green_ball_actor = actor
                                break
                            else:
                                print(f"   ❌ Too far from expected position")
                        else:
                            print(f"   ❓ No expected position to compare with")
        
        return green_ball_actor, expected_pos
    
    # Initial check
    green_ball, expected = find_and_check_green_ball()
    
    if green_ball:
        print(f"\n✅ GREEN BALL FOUND!")
        actual_pos = green_ball.GetPosition()
        print(f"📍 Actual position: {actual_pos}")
        print(f"📍 Expected position: {expected}")
        
        if expected:
            diff_x = abs(actual_pos[0] - expected[0])
            diff_y = abs(actual_pos[1] - expected[1])
            diff_z = abs(actual_pos[2] - expected[2])
            print(f"📏 Difference: X={diff_x:.6f}, Y={diff_y:.6f}, Z={diff_z:.6f}")
            
            if diff_x < 0.001 and diff_y < 0.001 and diff_z < 0.001:
                print(f"✅ GREEN BALL IS CORRECTLY POSITIONED!")
            else:
                print(f"❌ GREEN BALL IS NOT AT THE EXPECTED POSITION!")
    else:
        print(f"\n❌ GREEN BALL NOT FOUND!")
        print(f"This means either:")
        print(f"  1. The green ball actor doesn't exist")
        print(f"  2. It's not where we expect it to be")
        print(f"  3. It's not a small actor (size >= 1.0)")
    
    print(f"\n🔄 Now testing with button rotations...")
    print(f"Press Enter to perform X+ rotation...")
    input()
    
    # Perform X+ rotation
    print(f"🔄 Performing X+ 15° rotation...")
    viewer.rotate_shape('x', 15.0)
    app.processEvents()
    time.sleep(1)
    
    # Check again after rotation
    green_ball, expected = find_and_check_green_ball()
    
    if green_ball and expected:
        actual_pos = green_ball.GetPosition()
        print(f"\n📍 After X+ rotation:")
        print(f"📍 Actual position: {actual_pos}")
        print(f"📍 Expected position: {expected}")
        
        diff_x = abs(actual_pos[0] - expected[0])
        diff_y = abs(actual_pos[1] - expected[1])
        diff_z = abs(actual_pos[2] - expected[2])
        print(f"📏 Difference: X={diff_x:.6f}, Y={diff_y:.6f}, Z={diff_z:.6f}")
        
        if diff_x < 0.001 and diff_y < 0.001 and diff_z < 0.001:
            print(f"✅ GREEN BALL MOVED CORRECTLY WITH ROTATION!")
        else:
            print(f"❌ GREEN BALL DID NOT MOVE CORRECTLY!")
            print(f"❌ This confirms the visual issue you're seeing!")
    
    print(f"\n🔍 Visual inspection time...")
    print(f"Look at the application window and verify if the green ball moved with the model.")
    print(f"Press Ctrl+C to exit")
    
    try:
        app.exec_()
    except KeyboardInterrupt:
        print("Exiting...")

if __name__ == "__main__":
    test_green_ball_actual_position()
