#!/usr/bin/env python3
"""
Test script to check if mouse is moving camera or model
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QT<PERSON>r

def test_mouse_behavior():
    """Test what happens when we drag the mouse"""
    
    print("🔍 TESTING MOUSE BEHAVIOR")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # Import the main viewer
    from step_viewer import StepViewerTDK
    viewer = StepViewerTDK()
    viewer.show()
    
    print("✅ Viewer opened")
    
    # Load the SOIC STEP file specifically
    soic_file = 'SOIC16P127_1270X940X610L89X51.STEP'

    if not os.path.exists(soic_file):
        print(f"❌ SOIC file not found: {soic_file}")
        print("Please make sure this file exists in the current directory")
        return

    print(f"📁 Loading SOIC file: {soic_file}")
    viewer.active_viewer = "top"
    viewer.update_viewer_highlights()
    success = viewer.load_step_file_direct(soic_file)

    if not success:
        print(f"❌ Failed to load SOIC file: {soic_file}")
        return

    print(f"✅ SOIC file loaded successfully: {soic_file}")
    loaded_file = soic_file
    
    # Wait for file to load
    app.processEvents()
    
    def check_camera_vs_model():
        """Check camera and model positions"""
        print("\n🔍 CHECKING CAMERA VS MODEL STATE")
        
        # Check camera
        if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
            renderer = viewer.vtk_renderer_left.renderer
            if renderer:
                camera = renderer.GetActiveCamera()
                cam_pos = camera.GetPosition()
                cam_focal = camera.GetFocalPoint()
                cam_up = camera.GetViewUp()
                
                print(f"📷 CAMERA:")
                print(f"   Position: {cam_pos}")
                print(f"   Focal Point: {cam_focal}")
                print(f"   View Up: {cam_up}")
        
        # Check model actors
        if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
            renderer = viewer.vtk_renderer_left.renderer
            if renderer:
                actors = renderer.GetActors()
                actors.InitTraversal()
                
                print(f"🎭 MODEL ACTORS:")
                for i in range(actors.GetNumberOfItems()):
                    actor = actors.GetNextActor()
                    if actor:
                        pos = actor.GetPosition()
                        orient = actor.GetOrientation()
                        print(f"   Actor {i}: Position={pos}, Orientation={orient}")
        
        # Check tracking variables
        print(f"📊 TRACKING VARIABLES:")
        print(f"   current_rot_left: {getattr(viewer, 'current_rot_left', 'NOT SET')}")
        print(f"   current_pos_left: {getattr(viewer, 'current_pos_left', 'NOT SET')}")
    
    # Check initial state
    print("\n=== INITIAL STATE ===")
    check_camera_vs_model()
    
    # Set up a timer to check state periodically
    def periodic_check():
        print("\n=== PERIODIC CHECK ===")
        check_camera_vs_model()
    
    timer = QTimer()
    timer.timeout.connect(periodic_check)
    timer.start(5000)  # Check every 5 seconds
    
    print("\n" + "=" * 50)
    print("🖱️ NOW TEST MOUSE DRAGGING:")
    print("1. Try dragging with LEFT mouse button")
    print("2. Watch the console output")
    print("3. Check if camera or model is moving")
    print("4. Press Ctrl+C to exit when done")
    print("=" * 50)
    
    # Run the application
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n👋 Test completed")

if __name__ == "__main__":
    test_mouse_behavior()
