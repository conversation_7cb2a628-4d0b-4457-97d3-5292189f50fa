#!/usr/bin/env python3
"""
Simple test to verify the duplicate arrow fix without <PERSON><PERSON>.
This script will test the movement logic directly.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_movement_fix():
    """Test the movement fix logic directly"""
    print("🧪 TESTING MOVEMENT FIX LOGIC")
    print("=" * 50)
    
    try:
        from step_viewer import StepViewerTDK
        
        # Create viewer instance (no GUI)
        viewer = StepViewerTDK()
        
        # Test the _align_model_to_origin method fix
        print("\n🔧 TESTING _align_model_to_origin FIX:")
        print("-" * 40)
        
        # Set up test data
        viewer.active_viewer = "top"
        viewer.current_pos_left = {'x': -4.19, 'y': -3.67, 'z': 0.49}
        
        # Create a mock renderer with test actors
        class MockRenderer:
            def __init__(self):
                self.step_actors = [<PERSON><PERSON><PERSON><PERSON>(), MockActor()]
                
        class MockActor:
            def __init__(self):
                self.position = [0, 0, 0]
                self.orientation = [0, 0, 0]
                self.user_transform = None
                self.visibility = True
                
            def GetVisibility(self):
                return self.visibility
                
            def SetPosition(self, x, y, z):
                self.position = [x, y, z]
                print(f"   🔧 ALIGNMENT FIX: Actor position set to ({x:.3f}, {y:.3f}, {z:.3f})")
                
            def SetOrientation(self, x, y, z):
                self.orientation = [x, y, z]
                
            def SetUserTransform(self, transform):
                self.user_transform = transform
                
            def Modified(self):
                pass
                
            def GetPosition(self):
                return tuple(self.position)
                
            def GetOrientation(self):
                return tuple(self.orientation)
        
        # Create mock renderer
        mock_renderer = MockRenderer()
        viewer.vtk_renderer_left = mock_renderer
        
        print(f"✅ Test setup complete:")
        print(f"   - Active viewer: {viewer.active_viewer}")
        print(f"   - Model center position: {viewer.current_pos_left}")
        print(f"   - Mock actors: {len(mock_renderer.step_actors)}")
        
        # Test the alignment method
        print(f"\n🔄 BEFORE ALIGNMENT:")
        for i, actor in enumerate(mock_renderer.step_actors):
            pos = actor.GetPosition()
            print(f"   Actor {i}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # Call the fixed alignment method
        print(f"\n🔧 CALLING _align_model_to_origin()...")
        viewer._align_model_to_origin(mock_renderer)
        
        print(f"\n🔄 AFTER ALIGNMENT:")
        for i, actor in enumerate(mock_renderer.step_actors):
            pos = actor.GetPosition()
            print(f"   Actor {i}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # Verify the fix
        expected_pos = [-4.19, -3.67, 0.49]
        success = True
        
        for i, actor in enumerate(mock_renderer.step_actors):
            pos = actor.GetPosition()
            if (abs(pos[0] - expected_pos[0]) > 0.01 or 
                abs(pos[1] - expected_pos[1]) > 0.01 or 
                abs(pos[2] - expected_pos[2]) > 0.01):
                print(f"❌ Actor {i} position incorrect: expected {expected_pos}, got {pos}")
                success = False
        
        if success:
            print(f"\n✅ ALIGNMENT FIX SUCCESSFUL!")
            print(f"   - All actors positioned at model center: {expected_pos}")
            print(f"   - No longer positioned at (0,0,0)")
            print(f"   - This should eliminate duplicate arrows at world origin")
        else:
            print(f"\n❌ ALIGNMENT FIX FAILED!")
            
        # Test movement simulation
        print(f"\n🔄 TESTING X+ MOVEMENT SIMULATION:")
        print("-" * 40)
        
        print(f"BEFORE X+ MOVEMENT:")
        for i, actor in enumerate(mock_renderer.step_actors):
            pos = actor.GetPosition()
            print(f"   Actor {i}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # Simulate X+ movement (+2.0mm)
        print(f"\nSimulating X+ movement (+2.0mm)...")
        for actor in mock_renderer.step_actors:
            current_pos = actor.GetPosition()
            new_x = current_pos[0] + 2.0
            actor.SetPosition(new_x, current_pos[1], current_pos[2])
        
        print(f"\nAFTER X+ MOVEMENT:")
        for i, actor in enumerate(mock_renderer.step_actors):
            pos = actor.GetPosition()
            print(f"   Actor {i}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
        
        # Verify no duplicates at world origin (2,0,0)
        world_origin_after_move = [2.0, 0.0, 0.0]
        duplicates_at_world_origin = False
        
        for i, actor in enumerate(mock_renderer.step_actors):
            pos = actor.GetPosition()
            if (abs(pos[0] - world_origin_after_move[0]) < 0.01 and 
                abs(pos[1] - world_origin_after_move[1]) < 0.01 and 
                abs(pos[2] - world_origin_after_move[2]) < 0.01):
                print(f"🚨 Actor {i} is at world origin position {world_origin_after_move} - potential duplicate!")
                duplicates_at_world_origin = True
        
        if not duplicates_at_world_origin:
            print(f"\n✅ NO DUPLICATES AT WORLD ORIGIN!")
            print(f"   - Model actors are at correct position: (-2.19, -3.67, 0.49)")
            print(f"   - World origin markers will be at: (2.0, 0.0, 0.0)")
            print(f"   - No visual overlap/duplicates")
        else:
            print(f"\n❌ DUPLICATES STILL EXIST AT WORLD ORIGIN!")
        
        print(f"\n🏁 TEST COMPLETED")
        print("=" * 50)
        
        return success and not duplicates_at_world_origin
        
    except Exception as e:
        print(f"❌ TEST FAILED WITH ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_movement_fix()
    if success:
        print("\n🎉 ALL TESTS PASSED - FIX IS WORKING!")
    else:
        print("\n💥 TESTS FAILED - FIX NEEDS MORE WORK!")
    
    sys.exit(0 if success else 1)
