#!/usr/bin/env python3
"""
Test what actually works in the current system
"""

import sys
import os

def test_import():
    """Test if we can even import the module"""
    print("🧪 Testing if step_viewer.py can be imported...")
    
    try:
        # Try to import
        from step_viewer import StepViewerTDK
        print("✅ SUCCESS: step_viewer.py imports without errors")
        return True
    except SyntaxError as e:
        print(f"❌ SYNTAX ERROR: {e}")
        print(f"   Line {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ IMPORT ERROR: {e}")
        return False

if __name__ == "__main__":
    if test_import():
        print("✅ Ready to test functionality")
    else:
        print("❌ Must fix syntax errors first")
