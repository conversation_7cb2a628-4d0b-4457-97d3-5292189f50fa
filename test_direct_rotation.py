#!/usr/bin/env python3
"""
Test the direct rotation method
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== DIRECT ROTATION TEST ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Testing with: {test_file}")
    
    # Load file
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ File loaded")
        
        # Test the direct rotation method
        loader = viewer.step_loader_left
        test_save_file = "test_direct_rotation.step"
        
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
        
        print("Testing direct rotation method...")
        success = loader.save_step_with_rotation(test_save_file, 90.0, 0.0, 0.0)
        
        if success and os.path.exists(test_save_file):
            file_size = os.path.getsize(test_save_file)
            print(f"✅ Direct rotation method works: {file_size} bytes")
            
            # Load and check
            viewer.active_viewer = "bottom"
            load_success = viewer.load_step_file_direct(test_save_file)
            
            if load_success:
                print("✅ Rotated file loaded")
                
                # Get bounds
                top_renderer = viewer.vtk_renderer_left
                bottom_renderer = viewer.vtk_renderer_right
                
                if (hasattr(top_renderer, 'step_actors') and top_renderer.step_actors and
                    hasattr(bottom_renderer, 'step_actors') and bottom_renderer.step_actors):
                    
                    original_bounds = top_renderer.step_actors[0].GetBounds()
                    rotated_bounds = bottom_renderer.step_actors[0].GetBounds()
                    
                    print(f"Original bounds: {original_bounds}")
                    print(f"Rotated bounds:  {rotated_bounds}")
                    
                    # Check if bounds changed (indicating rotation)
                    bounds_changed = any(abs(a - b) > 0.1 for a, b in zip(original_bounds, rotated_bounds))
                    print(f"Bounds changed: {bounds_changed}")
                    
                    if bounds_changed:
                        print("✅ SUCCESS: Direct rotation method works!")
                    else:
                        print("❌ FAILURE: Direct rotation method not working!")
                else:
                    print("❌ No actors found")
            else:
                print("❌ Failed to load rotated file")
                
            # Clean up
            os.remove(test_save_file)
            
        else:
            print("❌ Direct rotation method failed")
    else:
        print("❌ Failed to load file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== DIRECT ROTATION TEST FINISHED ===")
