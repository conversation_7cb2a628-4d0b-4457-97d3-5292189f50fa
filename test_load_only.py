#!/usr/bin/env python3
"""
Test just the loading functionality
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

def test_load_only():
    print("=== TEST LOAD ONLY ===")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    
    # Find STEP file
    step_files = [f for f in os.listdir('.') if f.endswith('.STEP')]
    if not step_files:
        print("❌ No STEP files found")
        return False
    
    test_file = step_files[0]
    print(f"📁 Testing with: {test_file}")
    
    # Test loading
    print("Loading file...")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ Load succeeded")
        
        # Check what we got
        loader = viewer.step_loader_left
        print(f"   Loader filename: {getattr(loader, 'current_filename', 'None')}")
        print(f"   Loader has shape: {hasattr(loader, 'shape') and loader.shape is not None}")
        
        # Check VTK actors
        if hasattr(viewer, 'vtk_widget_left') and viewer.vtk_widget_left:
            renderer = viewer.vtk_widget_left.GetRenderWindow().GetRenderers().GetFirstRenderer()
            if renderer:
                actors = renderer.GetActors()
                actors.InitTraversal()
                actor_count = 0
                while actors.GetNextActor():
                    actor_count += 1
                print(f"   VTK actors in top viewer: {actor_count}")
            else:
                print("   No renderer found")
        else:
            print("   No VTK widget found")
        
        return True
    else:
        print("❌ Load failed")
        return False

if __name__ == "__main__":
    if test_load_only():
        print("\n✅ LOAD TEST PASSED")
    else:
        print("\n❌ LOAD TEST FAILED")
