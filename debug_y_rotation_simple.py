#!/usr/bin/env python3
"""
Simple debug test to see what happens during Y rotation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK
import time

class SimpleYDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def run_debug(self):
        """Run simple Y rotation debug"""
        print("🧪 SIMPLE Y ROTATION DEBUG")
        print("=" * 40)
        
        # Create viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for viewer to initialize, then start test
        QTimer.singleShot(3000, self.test_y_rotation)
        
        # Run the application
        self.app.exec_()
        
    def test_y_rotation(self):
        """Test Y rotation and see what happens"""
        print("\n🔄 TESTING Y+ ROTATION")
        
        # Get renderer
        if hasattr(self.viewer, 'vtk_renderer_left') and self.viewer.vtk_renderer_left:
            renderer = self.viewer.vtk_renderer_left
            
            # Count actors before
            if hasattr(renderer, 'renderer') and renderer.renderer:
                actors = renderer.renderer.GetActors()
                print(f"📊 Total actors before Y rotation: {actors.GetNumberOfItems()}")
                
                # Check for world origin actors
                if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
                    print(f"🔴 World origin actors: {len(renderer.origin_actors)}")
                else:
                    print("❌ No world origin actors found")
                
                # Check for part origin actors
                part_origin_count = 0
                if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
                    part_origin_count += 1
                for arrow_name in ['part_origin_x_arrow', 'part_origin_y_arrow', 'part_origin_z_arrow']:
                    if hasattr(renderer, arrow_name) and getattr(renderer, arrow_name):
                        part_origin_count += 1
                print(f"🟢 Part origin actors: {part_origin_count}")
        
        # Execute Y+ rotation
        print("\n🔄 Executing Y+ rotation (15°)...")
        self.viewer.rotate_shape('y', 15.0)
        
        # Wait and then quit
        QTimer.singleShot(2000, self.app.quit)

if __name__ == "__main__":
    debugger = SimpleYDebugger()
    debugger.run_debug()
