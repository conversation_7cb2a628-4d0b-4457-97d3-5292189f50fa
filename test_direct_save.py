#!/usr/bin/env python3
"""
Test the direct save approach - just update coordinate system in STEP file
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== TEST DIRECT SAVE APPROACH ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Testing with: {test_file}")
    
    # Load file
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ File loaded")
        
        # Apply rotation
        print("\nApplying 90° X rotation...")
        viewer._apply_model_rotation("top", "x", 90.0)
        
        # Get current rotation from the system
        current_rot = viewer.current_rot_left
        print(f"Current rotation: {current_rot}")
        
        # Test the direct save method
        print("\nTesting direct save method...")
        loader = viewer.step_loader_left
        test_save_file = "test_direct_save.step"
        
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
        
        # Use the new direct method
        success = viewer._save_step_with_current_rotation(test_save_file, loader, current_rot)
        print(f"Save result: {success}")
        
        if os.path.exists(test_save_file):
            file_size = os.path.getsize(test_save_file)
            print(f"File created: {file_size} bytes")
            
            # Test loading the saved file
            print("\nTesting load of saved file...")
            viewer.active_viewer = "bottom"
            load_success = viewer.load_step_file_direct(test_save_file)
            
            if load_success:
                print("✅ Saved file loaded successfully")
                
                # Check what the unified system reads from the saved file
                print("Checking unified system analysis of saved file...")
                bottom_loader = viewer.step_loader_right
                if hasattr(bottom_loader, 'axis_data') and bottom_loader.axis_data:
                    print(f"Saved file axis_data: {bottom_loader.axis_data}")
                    
                    # Compare with original
                    top_loader = viewer.step_loader_left
                    if hasattr(top_loader, 'axis_data') and top_loader.axis_data:
                        print(f"Original file axis_data: {top_loader.axis_data}")
                        
                        if bottom_loader.axis_data == top_loader.axis_data:
                            print("❌ PROBLEM: axis_data is still IDENTICAL!")
                        else:
                            print("✅ SUCCESS: axis_data is DIFFERENT!")
                            print("The coordinate system was updated!")
                    else:
                        print("❌ No axis_data in original loader")
                        
                    # Check the yellow text
                    print("\nChecking yellow text display...")
                    viewer.update_text_overlays()
                    
                    # Get rotation from bottom viewer
                    bottom_rot = viewer.current_rot_right
                    print(f"Bottom viewer rotation: {bottom_rot}")
                    
                    if bottom_rot['x'] != 0 or bottom_rot['y'] != 0 or bottom_rot['z'] != 0:
                        print("🎉 SUCCESS: Bottom viewer shows non-zero rotation!")
                        print("The coordinate system update worked!")
                    else:
                        print("❌ FAILURE: Bottom viewer still shows (0,0,0)")
                else:
                    print("❌ No axis_data in saved file loader")
            else:
                print("❌ Failed to load saved file")
            
            os.remove(test_save_file)
        else:
            print("❌ No file created")
    else:
        print("❌ Failed to load file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== TEST FINISHED ===")
