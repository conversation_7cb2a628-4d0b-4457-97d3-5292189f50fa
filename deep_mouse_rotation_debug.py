#!/usr/bin/env python3
"""
DEEP DEEP DEBUG: Comprehensive mouse rotation verification system
"""

import sys
import os
import time
import re
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

class DeepMouseRotationDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_results = []
        self.step_count = 0
        
    def log_result(self, test_name, result, details=""):
        """Log a test result"""
        self.test_results.append({
            'step': self.step_count,
            'test': test_name,
            'result': result,
            'details': details
        })
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} Step {self.step_count}: {test_name}")
        if details:
            print(f"    Details: {details}")
        self.step_count += 1
    
    def extract_origin_numbers(self, text):
        """Extract origin numbers from display text"""
        match = re.search(r'Origin \(X = ([-\d.]+) Y = ([-\d.]+) Z = ([-\d.]+)\)', text)
        if match:
            return (float(match.group(1)), float(match.group(2)), float(match.group(3)))
        return None
    
    def start_deep_debug(self):
        """Start comprehensive debugging"""
        print("🔬 DEEP DEEP DEBUG: Mouse Rotation Verification System")
        print("=" * 70)
        
        # Step 1: Create viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        self.log_result("Create Viewer", True, "StepViewerTDK instance created")
        
        # Step 2: Load SOIC file
        QTimer.singleShot(1000, self.step2_load_file)
        
        return self.app.exec_()
    
    def step2_load_file(self):
        """Step 2: Load SOIC file and verify initial state"""
        print("\n🔬 STEP 2: Loading SOIC file...")
        
        soic_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(soic_file):
            self.log_result("File Exists", False, f"{soic_file} not found")
            self.finish_debug()
            return
        
        self.log_result("File Exists", True, f"{soic_file} found")
        
        # Load the file
        self.viewer.load_step_file_direct(soic_file)
        self.app.processEvents()
        time.sleep(2)
        
        # Verify file loaded
        if hasattr(self.viewer, 'orig_pos_left'):
            orig_pos = self.viewer.orig_pos_left
            self.log_result("File Loaded", True, f"orig_pos_left = {orig_pos}")
        else:
            self.log_result("File Loaded", False, "No orig_pos_left found")
            self.finish_debug()
            return
        
        QTimer.singleShot(1000, self.step3_capture_initial_state)
    
    def step3_capture_initial_state(self):
        """Step 3: Capture initial yellow text state"""
        print("\n🔬 STEP 3: Capturing initial yellow text state...")
        
        try:
            # Get initial display numbers
            initial_display = self.viewer._calculate_unified_display_numbers("top")
            
            # Extract origin numbers
            model_origin = self.extract_origin_numbers(initial_display['model'])
            local_origin = self.extract_origin_numbers(initial_display['local_origin'])
            
            self.initial_model_origin = model_origin
            self.initial_local_origin = local_origin
            
            self.log_result("Initial Model Origin", model_origin is not None, f"{model_origin}")
            self.log_result("Initial Local Origin", local_origin is not None, f"{local_origin}")
            
            # Verify they match
            if model_origin and local_origin:
                match = (abs(model_origin[0] - local_origin[0]) < 0.001 and
                        abs(model_origin[1] - local_origin[1]) < 0.001 and
                        abs(model_origin[2] - local_origin[2]) < 0.001)
                self.log_result("Origins Match", match, f"Model={model_origin}, Local={local_origin}")
            
            # Check if origins are non-zero (should be for SOIC file)
            if model_origin:
                non_zero = (abs(model_origin[0]) > 0.1 or abs(model_origin[1]) > 0.1 or abs(model_origin[2]) > 0.1)
                self.log_result("Origins Non-Zero", non_zero, f"Model origin: {model_origin}")
            
        except Exception as e:
            self.log_result("Initial State Capture", False, f"Error: {e}")
            self.finish_debug()
            return
        
        QTimer.singleShot(1000, self.step4_test_mouse_interaction)
    
    def step4_test_mouse_interaction(self):
        """Step 4: Test mouse interaction setup"""
        print("\n🔬 STEP 4: Testing mouse interaction setup...")
        
        # Check if interaction style is set up
        try:
            top_interactor = self.viewer.vtk_renderer_left.interactor
            if top_interactor:
                style = top_interactor.GetInteractorStyle()
                self.log_result("Interactor Exists", True, f"Style: {type(style).__name__}")
                
                # Check if it's our custom style
                if hasattr(style, 'viewer_instance'):
                    self.log_result("Custom Style", True, "FixedCameraModelRotateStyle detected")
                else:
                    self.log_result("Custom Style", False, "Not our custom interaction style")
            else:
                self.log_result("Interactor Exists", False, "No interactor found")
                
        except Exception as e:
            self.log_result("Interaction Setup", False, f"Error: {e}")
        
        QTimer.singleShot(1000, self.step5_simulate_mouse_rotation)
    
    def step5_simulate_mouse_rotation(self):
        """Step 5: Simulate mouse rotation and capture results"""
        print("\n🔬 STEP 5: Simulating mouse rotation...")
        
        try:
            # Simulate left button down
            print("🖱️ SIMULATING: Left button down")
            
            # Directly call the rotation method (simulating what mouse should do)
            print("🖱️ SIMULATING: Calling _apply_model_rotation directly")
            self.viewer._apply_model_rotation("top", "x", 15.0)
            
            self.app.processEvents()
            time.sleep(1)
            
            # Capture AFTER state
            after_display = self.viewer._calculate_unified_display_numbers("top")
            after_model_origin = self.extract_origin_numbers(after_display['model'])
            after_local_origin = self.extract_origin_numbers(after_display['local_origin'])
            
            self.log_result("After Model Origin", after_model_origin is not None, f"{after_model_origin}")
            self.log_result("After Local Origin", after_local_origin is not None, f"{after_local_origin}")
            
            # Check if numbers changed
            if self.initial_model_origin and after_model_origin:
                model_changed = (abs(self.initial_model_origin[0] - after_model_origin[0]) > 0.001 or
                               abs(self.initial_model_origin[1] - after_model_origin[1]) > 0.001 or
                               abs(self.initial_model_origin[2] - after_model_origin[2]) > 0.001)
                self.log_result("Model Origin Changed", model_changed, 
                              f"Before: {self.initial_model_origin}, After: {after_model_origin}")
            
            if self.initial_local_origin and after_local_origin:
                local_changed = (abs(self.initial_local_origin[0] - after_local_origin[0]) > 0.001 or
                               abs(self.initial_local_origin[1] - after_local_origin[1]) > 0.001 or
                               abs(self.initial_local_origin[2] - after_local_origin[2]) > 0.001)
                self.log_result("Local Origin Changed", local_changed,
                              f"Before: {self.initial_local_origin}, After: {after_local_origin}")
            
        except Exception as e:
            self.log_result("Mouse Rotation Simulation", False, f"Error: {e}")
        
        QTimer.singleShot(1000, self.step6_test_position_tracking)
    
    def step6_test_position_tracking(self):
        """Step 6: Test position tracking variables"""
        print("\n🔬 STEP 6: Testing position tracking variables...")
        
        try:
            # Check current_pos_left
            if hasattr(self.viewer, 'current_pos_left'):
                current_pos = self.viewer.current_pos_left
                self.log_result("current_pos_left exists", True, f"{current_pos}")
                
                # Check if it matches the green ball position
                if hasattr(self.viewer.vtk_renderer_left, 'part_origin_sphere'):
                    green_ball = self.viewer.vtk_renderer_left.part_origin_sphere
                    if green_ball:
                        ball_pos = green_ball.GetPosition()
                        pos_match = (abs(current_pos['x'] - ball_pos[0]) < 0.001 and
                                   abs(current_pos['y'] - ball_pos[1]) < 0.001 and
                                   abs(current_pos['z'] - ball_pos[2]) < 0.001)
                        self.log_result("Position Tracking Synced", pos_match,
                                      f"current_pos: {current_pos}, ball_pos: {ball_pos}")
                    else:
                        self.log_result("Green Ball Exists", False, "part_origin_sphere is None")
                else:
                    self.log_result("Green Ball Exists", False, "No part_origin_sphere attribute")
            else:
                self.log_result("current_pos_left exists", False, "No current_pos_left attribute")
                
        except Exception as e:
            self.log_result("Position Tracking Test", False, f"Error: {e}")
        
        QTimer.singleShot(1000, self.finish_debug)
    
    def finish_debug(self):
        """Finish debugging and show results"""
        print("\n" + "=" * 70)
        print("🔬 DEEP DEBUG RESULTS SUMMARY:")
        print("=" * 70)
        
        passed = 0
        failed = 0
        
        for result in self.test_results:
            status = "✅ PASS" if result['result'] else "❌ FAIL"
            print(f"{status} Step {result['step']}: {result['test']}")
            if result['details']:
                print(f"    {result['details']}")
            
            if result['result']:
                passed += 1
            else:
                failed += 1
        
        print("\n" + "=" * 70)
        print(f"📊 FINAL SCORE: {passed} PASSED, {failed} FAILED")
        
        if failed == 0:
            print("🎉 ALL TESTS PASSED: Mouse rotation should work correctly!")
        else:
            print("❌ SOME TESTS FAILED: Mouse rotation needs more fixes")
        
        print("=" * 70)
        
        # Keep app running for manual testing
        print("\n🔬 Application is still running for manual testing...")
        print("Try LEFT-click and drag to rotate the model and watch console output.")

if __name__ == "__main__":
    debugger = DeepMouseRotationDebugger()
    debugger.start_deep_debug()
