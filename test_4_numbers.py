#!/usr/bin/env python3

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from step_viewer import StepViewerTDK

class Test4Numbers:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
    def test_4_numbers(self):
        print("🔍 TESTING 4 NUMBERS AFTER ROTATION")
        print("=" * 60)
        
        # Load the actual SOIC file
        step_files = ["SOIC16P127_1270X940X610L89X51.STEP"]
        soic_file = None

        for filename in step_files:
            if os.path.exists(filename):
                soic_file = filename
                break
        
        if soic_file:
            try:
                success = self.viewer.load_step_file_direct(soic_file)
                if success:
                    print(f"✅ STEP file loaded: {soic_file}")
                else:
                    print(f"❌ Failed to load STEP file: {soic_file}")
                    self.exit_app()
                    return
            except Exception as e:
                print(f"❌ Error loading STEP file: {e}")
                self.exit_app()
                return
        else:
            print(f"❌ No STEP files found. Tried: {step_files}")
            self.exit_app()
            return

        # Wait for file to load
        time.sleep(2)
        
        print("\n📊 BEFORE ROTATION:")
        self.show_4_numbers()

        # Apply rotations in all 3 directions at once
        print(f"\n🔄 Applying rotations: 25° X, 30° Y, 20° Z...")

        # Use the actual rotation methods from the viewer
        try:
            # Try the button rotation methods
            if hasattr(self.viewer, 'on_rotate_x_plus_clicked'):
                for _ in range(5):  # 5 clicks = 25°
                    self.viewer.on_rotate_x_plus_clicked()
                    time.sleep(0.1)

            if hasattr(self.viewer, 'on_rotate_y_plus_clicked'):
                for _ in range(6):  # 6 clicks = 30°
                    self.viewer.on_rotate_y_plus_clicked()
                    time.sleep(0.1)

            if hasattr(self.viewer, 'on_rotate_z_plus_clicked'):
                for _ in range(4):  # 4 clicks = 20°
                    self.viewer.on_rotate_z_plus_clicked()
                    time.sleep(0.1)

        except Exception as e:
            print(f"❌ Error applying rotations: {e}")
            # Fallback to direct rotation if available
            if hasattr(self.viewer, 'rotate_shape_x_plus'):
                self.viewer.rotate_shape_x_plus(25)
            if hasattr(self.viewer, 'rotate_shape_y_plus'):
                self.viewer.rotate_shape_y_plus(30)
            if hasattr(self.viewer, 'rotate_shape_z_plus'):
                self.viewer.rotate_shape_z_plus(20)

        # Wait for rotations to complete
        time.sleep(2)

        print("\n📊 AFTER ALL 3 ROTATIONS:")
        self.show_4_numbers()

        print("\n🏁 Test complete - exiting...")
        self.exit_app()

    def show_4_numbers(self):
        """Show exactly 4 numbers we need to see"""
        try:
            # Get TOP renderer
            renderer = getattr(self.viewer, 'vtk_renderer_left', None)
            if not renderer:
                print("❌ No TOP renderer found")
                return
                
            print("   🔍 RAW VTK POSITIONS:")
            
            # 1. VTK green ball position (raw)
            if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
                green_ball_pos = renderer.part_origin_sphere.GetPosition()
                print(f"   1. VTK Green Ball: ({green_ball_pos[0]:.6f}, {green_ball_pos[1]:.6f}, {green_ball_pos[2]:.6f})")
            else:
                print("   1. VTK Green Ball: NOT FOUND")
            
            # 2. VTK model origin position (try to find the actual model origin actor)
            model_origin_pos = None
            if hasattr(renderer, 'step_actors') and renderer.step_actors:
                # Get first model actor bounds
                first_actor = renderer.step_actors[0]
                if first_actor:
                    bounds = first_actor.GetBounds()
                    if bounds and len(bounds) >= 6:
                        # This is center, but let's see what we get
                        center_x = (bounds[0] + bounds[1]) / 2.0
                        center_y = (bounds[2] + bounds[3]) / 2.0
                        center_z = (bounds[4] + bounds[5]) / 2.0
                        model_origin_pos = (center_x, center_y, center_z)
                        print(f"   2. VTK Model Center: ({center_x:.6f}, {center_y:.6f}, {center_z:.6f})")
                    else:
                        print("   2. VTK Model Center: NO BOUNDS")
                else:
                    print("   2. VTK Model Center: NO ACTOR")
            else:
                print("   2. VTK Model Center: NO STEP ACTORS")
            
            print("   🔍 CONVERTED POSITIONS (what shows in yellow text):")
            
            # 3. Get the actual converted Model Origin from the calculation
            current_pos = getattr(self.viewer, 'current_pos_left', {'x': 0, 'y': 0, 'z': 0})
            
            # Use the same logic as the actual code
            if hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
                actual_pos = renderer.part_origin_sphere.GetPosition()
                model_pos = {
                    'x': actual_pos[0],
                    'y': actual_pos[1],
                    'z': actual_pos[2]
                }
            else:
                model_pos = current_pos
                
            print(f"   3. Converted Model Origin: ({model_pos['x']:.6f}, {model_pos['y']:.6f}, {model_pos['z']:.6f})")
            print(f"   4. Converted Local Origin: ({model_pos['x']:.6f}, {model_pos['y']:.6f}, {model_pos['z']:.6f})")
            
        except Exception as e:
            print(f"   ❌ Error getting positions: {e}")

    def exit_app(self):
        """Exit the application"""
        self.app.quit()

if __name__ == "__main__":
    test = Test4Numbers()
    test.test_4_numbers()
