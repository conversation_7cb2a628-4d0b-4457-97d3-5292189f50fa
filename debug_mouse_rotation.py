#!/usr/bin/env python3
"""
Debug script to test mouse rotation and capture debug output
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK

def test_mouse_rotation():
    """Test mouse rotation and capture debug output"""
    print("🔍 DEBUG TEST: Starting mouse rotation test")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    def load_and_test():
        print("🔍 DEBUG TEST: Loading STEP file...")
        
        # Use a known STEP file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print("❌ DEBUG TEST: STEP file not found")
            app.quit()
            return
        print(f"🔍 DEBUG TEST: Loading {step_file}")
        
        # Load the STEP file using the correct method
        try:
            # Use the direct load method
            success = viewer.load_step_file_direct(step_file)
            if success:
                print("✅ DEBUG TEST: STEP file loaded")
            else:
                print("❌ DEBUG TEST: STEP file load failed")
                app.quit()
                return
            
            # Wait a moment for loading to complete
            QTimer.singleShot(2000, test_rotation)
            
        except Exception as e:
            print(f"❌ DEBUG TEST: Error loading STEP file: {e}")
            app.quit()
    
    def test_rotation():
        print("🔍 DEBUG TEST: Testing mouse rotation...")
        
        # Check initial state
        print(f"🔍 INITIAL STATE: current_pos_left = {getattr(viewer, 'current_pos_left', 'NOT SET')}")
        print(f"🔍 INITIAL STATE: current_rot_left = {getattr(viewer, 'current_rot_left', 'NOT SET')}")
        print(f"🔍 INITIAL STATE: orig_pos_left = {getattr(viewer, 'orig_pos_left', 'NOT SET')}")
        
        # Simulate mouse rotation by calling _apply_model_rotation directly
        print("🔍 DEBUG TEST: Simulating mouse rotation X+15°...")
        viewer._apply_model_rotation("top", "x", 15.0)
        
        print("🔍 DEBUG TEST: Simulating mouse rotation Y+10°...")
        viewer._apply_model_rotation("top", "y", 10.0)
        
        # Check final state
        print(f"🔍 FINAL STATE: current_pos_left = {getattr(viewer, 'current_pos_left', 'NOT SET')}")
        print(f"🔍 FINAL STATE: current_rot_left = {getattr(viewer, 'current_rot_left', 'NOT SET')}")
        print(f"🔍 FINAL STATE: orig_pos_left = {getattr(viewer, 'orig_pos_left', 'NOT SET')}")
        
        print("✅ DEBUG TEST: Mouse rotation test complete")
        app.quit()
    
    # Start the test after a short delay
    QTimer.singleShot(1000, load_and_test)
    
    # Run the application
    app.exec_()

if __name__ == "__main__":
    test_mouse_rotation()
