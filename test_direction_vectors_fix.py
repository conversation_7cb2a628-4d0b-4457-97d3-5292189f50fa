#!/usr/bin/env python3

"""
Test the direction vectors fix for Local Origin display.
This test loads the SOIC-16 footprint and verifies that the Local Origin
display shows the actual STEP file direction vectors instead of zeros.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import Step<PERSON>iewerTDK as StepViewer
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import time

def test_direction_vectors_fix():
    """Test that Local Origin display shows actual STEP file direction vectors"""
    
    print("=" * 80)
    print("TESTING DIRECTION VECTORS FIX FOR LOCAL ORIGIN DISPLAY")
    print("=" * 80)
    
    app = QApplication(sys.argv)
    
    try:
        # Create the main viewer
        viewer = StepViewer()
        viewer.show()
        
        # Wait for GUI to initialize
        app.processEvents()
        time.sleep(1)
        
        print("\n1. Loading SOIC-16 footprint...")
        
        # Load the SOIC-16 file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        # Set to TOP viewer and load file
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        print("✅ STEP file loaded successfully")
        
        # Wait for loading to complete
        app.processEvents()
        time.sleep(2)
        
        print("\n2. Checking stored direction vectors...")
        
        # Check if direction vectors were stored
        if hasattr(viewer, 'orig_z_direction_left') and viewer.orig_z_direction_left:
            print(f"✅ Z Direction stored: {viewer.orig_z_direction_left}")
        else:
            print("❌ Z Direction not stored")
            
        if hasattr(viewer, 'orig_x_direction_left') and viewer.orig_x_direction_left:
            print(f"✅ X Direction stored: {viewer.orig_x_direction_left}")
        else:
            print("❌ X Direction not stored")
            
        print("\n3. Checking green sphere VTK actor...")
        
        # Check if green sphere has direction vectors
        if hasattr(viewer.vtk_renderer_left, 'part_origin_sphere') and viewer.vtk_renderer_left.part_origin_sphere:
            sphere = viewer.vtk_renderer_left.part_origin_sphere
            print(f"✅ Green sphere exists at position: {sphere.GetPosition()}")
            
            if hasattr(sphere, 'z_direction') and sphere.z_direction:
                print(f"✅ Green sphere has Z direction: {sphere.z_direction}")
            else:
                print("❌ Green sphere missing Z direction")
                
            if hasattr(sphere, 'x_direction') and sphere.x_direction:
                print(f"✅ Green sphere has X direction: {sphere.x_direction}")
            else:
                print("❌ Green sphere missing X direction")
        else:
            print("❌ Green sphere not found")
            
        print("\n4. Triggering text overlay update...")
        
        # Trigger text overlay update to see the Local Origin display
        viewer.update_text_overlays()
        app.processEvents()
        
        print("\n5. Test completed - check debug output above for Local Origin display values")
        print("   Look for 'DEBUG LOCAL ORIGIN:' messages")
        
        # Keep GUI open for visual verification
        print("\n6. GUI is open for visual verification...")
        print("   Check that Local Origin display shows non-zero Direction and REF. Direction values")
        print("   Press Ctrl+C to exit")
        
        # Run the GUI event loop
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_direction_vectors_fix()
