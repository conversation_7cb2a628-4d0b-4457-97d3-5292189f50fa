#!/usr/bin/env python3
"""
Debug the origin_actors list during button vs mouse rotation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK
import time

class OriginActorsDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def run_debug(self):
        """Run origin_actors list debug"""
        print("🧪 ORIGIN_ACTORS LIST DEBUG")
        print("=" * 50)
        
        # Create viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for viewer to initialize, then start test
        QTimer.singleShot(3000, self.check_initial_state)
        
        # Run the application
        self.app.exec_()
        
    def check_initial_state(self):
        """Check initial origin_actors state"""
        print("\n📊 INITIAL STATE:")
        self.print_origin_actors_state()
        
        # Test button rotation
        QTimer.singleShot(1000, self.test_button_rotation)
        
    def test_button_rotation(self):
        """Test button rotation and check origin_actors"""
        print("\n🔄 TESTING BUTTON ROTATION (X+):")
        
        # Check BEFORE button rotation
        print("BEFORE button rotation:")
        self.print_origin_actors_state()
        
        # Execute button rotation
        self.viewer.rotate_shape('x', 15.0)
        
        # Check AFTER button rotation
        print("AFTER button rotation:")
        self.print_origin_actors_state()
        
        # Now test second button rotation
        QTimer.singleShot(1000, self.test_second_button_rotation)
        
    def test_second_button_rotation(self):
        """Test second button rotation (Y+)"""
        print("\n🔄 TESTING SECOND BUTTON ROTATION (Y+):")
        
        # Check BEFORE second button rotation
        print("BEFORE Y+ button rotation:")
        self.print_origin_actors_state()
        
        # Execute Y+ button rotation
        self.viewer.rotate_shape('y', 15.0)
        
        # Check AFTER second button rotation
        print("AFTER Y+ button rotation:")
        self.print_origin_actors_state()
        
        # Complete test
        QTimer.singleShot(2000, self.app.quit)
        
    def print_origin_actors_state(self):
        """Print current state of origin_actors list"""
        if not hasattr(self.viewer, 'vtk_renderer_left') or not self.viewer.vtk_renderer_left:
            print("   ❌ No left renderer")
            return
            
        renderer = self.viewer.vtk_renderer_left
        
        # Check origin_actors list
        if hasattr(renderer, 'origin_actors'):
            origin_actors = renderer.origin_actors
            print(f"   📋 origin_actors list: {len(origin_actors) if origin_actors else 0} actors")
            
            if origin_actors:
                for i, actor in enumerate(origin_actors):
                    if actor:
                        pos = actor.GetPosition()
                        orient = actor.GetOrientation()
                        prop = actor.GetProperty()
                        color = prop.GetColor() if prop else (0,0,0)
                        print(f"      [{i}] Pos=({pos[0]:.3f},{pos[1]:.3f},{pos[2]:.3f}), Orient=({orient[0]:.1f}°,{orient[1]:.1f}°,{orient[2]:.1f}°), Color=({color[0]:.2f},{color[1]:.2f},{color[2]:.2f})")
                    else:
                        print(f"      [{i}] NULL ACTOR")
            else:
                print("   📋 origin_actors list is EMPTY")
        else:
            print("   ❌ origin_actors attribute does not exist")
            
        # Also check total actors in renderer
        if hasattr(renderer, 'renderer') and renderer.renderer:
            actors = renderer.renderer.GetActors()
            total_actors = actors.GetNumberOfItems()
            print(f"   🎭 Total actors in renderer: {total_actors}")
        else:
            print("   ❌ No VTK renderer")

if __name__ == "__main__":
    debugger = OriginActorsDebugger()
    debugger.run_debug()
