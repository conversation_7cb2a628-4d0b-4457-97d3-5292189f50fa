#!/usr/bin/env python3
"""
COMPREHENSIVE TEST: Both Rotation AND Translation Buttons
Tests that all visual elements move together for both rotations and translations
"""

import sys
import os
import time
import numpy as np
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

# Add current directory to path
sys.path.append('.')

def test_rotation_and_translation_buttons():
    """Test both rotation and translation buttons with strange values"""
    
    print("🎯 COMPREHENSIVE TEST: Rotation AND Translation Buttons")
    print("=" * 70)
    
    # Import and create the application
    from step_viewer import StepViewerTDK
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Wait for GUI to initialize
    QTest.qWait(2000)
    print("✅ GUI initialized")
    
    # Load a test STEP file
    test_files = [
        r"e:\python\viewer\save\test_part.step",
        r"e:\python\viewer\save\sample.step",
        r"e:\python\3d-view\test.step",
        r"e:\python\3d-view\sample.step"
    ]
    
    test_file = None
    for f in test_files:
        if os.path.exists(f):
            test_file = f
            break
    
    if test_file:
        print(f"📁 Loading test file: {test_file}")
        try:
            viewer.load_step_file_direct(test_file)
            QTest.qWait(3000)
            print("✅ STEP file loaded successfully")
        except Exception as e:
            print(f"❌ Error loading STEP file: {e}")
    else:
        print("⚠️  No test STEP file found - testing with default geometry")
    
    # Get TOP renderer and find all actors
    top_renderer = None
    if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
        top_renderer = viewer.vtk_renderer_left.renderer
    
    if not top_renderer:
        print("❌ Could not access TOP renderer")
        return False
    
    # Find all actors
    all_actors = []
    actor_collection = top_renderer.GetActors()
    actor_collection.InitTraversal()
    
    while True:
        actor = actor_collection.GetNextActor()
        if not actor:
            break
        all_actors.append(actor)
    
    print(f"✅ Found {len(all_actors)} actors to test")
    
    if len(all_actors) == 0:
        print("❌ No actors found to test!")
        return False
    
    # Test both ROTATION and TRANSLATION with strange values
    test_cases = [
        # ROTATION TESTS
        ("ROTATION X+37.3°", "rotation", "rotate_shape", ("x", 37.3)),
        ("ROTATION Y-67.1°", "rotation", "rotate_shape", ("y", -67.1)),
        ("ROTATION Z+123.7°", "rotation", "rotate_shape", ("z", 123.7)),
        
        # TRANSLATION TESTS - need to find the correct method names
        ("TRANSLATION X+2.5", "translation", "translate_shape", ("x", 2.5)),
        ("TRANSLATION Y-1.8", "translation", "translate_shape", ("y", -1.8)),
        ("TRANSLATION Z+3.2", "translation", "translate_shape", ("z", 3.2)),
    ]
    
    print(f"\n🧪 TESTING BOTH ROTATION AND TRANSLATION BUTTONS...")
    print("-" * 60)
    
    all_results = []
    
    for test_name, test_type, method_name, params in test_cases:
        print(f"\n🔄 Testing {test_name}...")
        
        # Capture BEFORE state with high precision
        before_states = []
        for i, actor in enumerate(all_actors):
            before_state = {
                'index': i,
                'position': tuple(actor.GetPosition()),
                'orientation': tuple(actor.GetOrientation()),
                'bounds': tuple(actor.GetBounds())
            }
            before_states.append(before_state)
        
        print(f"   📊 Captured BEFORE state for {len(before_states)} actors")
        
        try:
            # Try to call the method
            if hasattr(viewer, method_name):
                method = getattr(viewer, method_name)
                print(f"   🔄 Calling {method_name}{params}...")
                method(*params)
                QTest.qWait(2000)  # Wait for movement and rendering
                
                # Force update
                if hasattr(viewer, 'update_text_overlays'):
                    viewer.update_text_overlays()
                QTest.qWait(500)
                
                print(f"   ✅ {test_name} applied successfully")
                
            else:
                # Try alternative method names for translation
                if test_type == "translation":
                    alt_methods = [
                        "move_shape", "translate_model", "move_model", 
                        "translate_x", "translate_y", "translate_z",
                        "move_x", "move_y", "move_z"
                    ]
                    
                    method_found = False
                    for alt_method in alt_methods:
                        if hasattr(viewer, alt_method):
                            method = getattr(viewer, alt_method)
                            print(f"   🔄 Using alternative method: {alt_method}{params}...")
                            try:
                                method(*params)
                                method_found = True
                                break
                            except Exception as e:
                                print(f"   ⚠️  {alt_method} failed: {e}")
                                continue
                    
                    if not method_found:
                        print(f"   ❌ No translation method found - checking available methods...")
                        # List all methods that might be translation-related
                        methods = [m for m in dir(viewer) if 'move' in m.lower() or 'translate' in m.lower() or 'position' in m.lower()]
                        print(f"   🔍 Available movement methods: {methods}")
                        continue
                else:
                    print(f"   ❌ Method {method_name} not found")
                    continue
                
        except Exception as e:
            print(f"   ❌ {test_name} FAILED with exception: {e}")
            continue
        
        # Capture AFTER state with high precision
        after_states = []
        for i, actor in enumerate(all_actors):
            after_state = {
                'index': i,
                'position': tuple(actor.GetPosition()),
                'orientation': tuple(actor.GetOrientation()),
                'bounds': tuple(actor.GetBounds())
            }
            after_states.append(after_state)
        
        print(f"   📊 Captured AFTER state for {len(after_states)} actors")
        
        # Detailed analysis of each actor
        test_results = {
            'test_name': test_name,
            'test_type': test_type,
            'moved_actors': 0,
            'stationary_actors': 0,
            'actor_details': []
        }
        
        print(f"   📈 MOVEMENT ANALYSIS:")
        
        for before, after in zip(before_states, after_states):
            # Calculate precise differences
            pos_diff = sum(abs(a - b) for a, b in zip(after['position'], before['position']))
            orient_diff = sum(abs(a - b) for a, b in zip(after['orientation'], before['orientation']))
            bounds_diff = sum(abs(a - b) for a, b in zip(after['bounds'], before['bounds']))
            
            # Detailed analysis
            pos_moved = pos_diff > 0.0001
            orient_moved = orient_diff > 0.01  # 0.01 degrees threshold
            bounds_changed = bounds_diff > 0.0001
            
            index = before['index']
            
            if pos_moved or orient_moved or bounds_changed:
                print(f"     ✅ Actor {index+1}: MOVED")
                if pos_moved:
                    print(f"        Position: {before['position']} → {after['position']} (Δ={pos_diff:.6f})")
                if orient_moved:
                    print(f"        Orientation: {before['orientation']} → {after['orientation']} (Δ={orient_diff:.3f}°)")
                if bounds_changed:
                    print(f"        Bounds changed: Δ={bounds_diff:.6f}")
                test_results['moved_actors'] += 1
            else:
                print(f"     ❌ Actor {index+1}: NO MOVEMENT DETECTED")
                print(f"        Position: {before['position']} (unchanged)")
                print(f"        Orientation: {before['orientation']} (unchanged)")
                test_results['stationary_actors'] += 1
        
        # Summary for this test
        success_rate = test_results['moved_actors'] / len(all_actors) * 100 if all_actors else 0
        print(f"   📊 {test_name} Results:")
        print(f"      ✅ Moved: {test_results['moved_actors']}")
        print(f"      ❌ Stationary: {test_results['stationary_actors']}")
        print(f"      📈 Success rate: {success_rate:.1f}%")
        
        if test_results['moved_actors'] == len(all_actors):
            print(f"      ✅ PERFECT: All elements moved together")
        elif test_results['moved_actors'] == 0:
            print(f"      ❌ FAILURE: No elements moved at all")
        else:
            print(f"      ⚠️  PROBLEM: Some elements not moving together!")
        
        all_results.append(test_results)
    
    # Final comprehensive analysis
    print("\n" + "=" * 70)
    print("🏁 COMPREHENSIVE ROTATION & TRANSLATION TEST RESULTS:")
    print("=" * 70)
    
    rotation_tests = [r for r in all_results if r['test_type'] == 'rotation']
    translation_tests = [r for r in all_results if r['test_type'] == 'translation']
    
    rotation_perfect = sum(1 for r in rotation_tests if r['moved_actors'] == len(all_actors))
    translation_perfect = sum(1 for r in translation_tests if r['moved_actors'] == len(all_actors))
    
    print(f"📊 ROTATION TESTS:")
    print(f"   Total: {len(rotation_tests)}")
    print(f"   Perfect: {rotation_perfect}")
    print(f"   Success rate: {rotation_perfect/len(rotation_tests)*100:.1f}%" if rotation_tests else "   No rotation tests")
    
    print(f"📊 TRANSLATION TESTS:")
    print(f"   Total: {len(translation_tests)}")
    print(f"   Perfect: {translation_perfect}")
    print(f"   Success rate: {translation_perfect/len(translation_tests)*100:.1f}%" if translation_tests else "   No translation tests")
    
    total_perfect = rotation_perfect + translation_perfect
    total_tests = len(all_results)
    
    if total_perfect == total_tests and total_tests > 0:
        print("🎉 EXCELLENT: ALL rotation and translation buttons work perfectly!")
        print("   All visual elements move together for both rotations and translations!")
        return True
    elif total_perfect > 0:
        print("⚠️  MIXED RESULTS: Some buttons work, some don't")
        if rotation_perfect == len(rotation_tests) and len(rotation_tests) > 0:
            print("   ✅ Rotation buttons work perfectly")
        if translation_perfect == len(translation_tests) and len(translation_tests) > 0:
            print("   ✅ Translation buttons work perfectly")
        return False
    else:
        print("❌ PROBLEMS DETECTED: Issues with button functionality")
        return False

if __name__ == "__main__":
    success = test_rotation_and_translation_buttons()
    sys.exit(0 if success else 1)
