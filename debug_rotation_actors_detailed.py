#!/usr/bin/env python3
"""
DETAILED ROTATION BUTTON ACTOR DEBUG
Tests each rotation button and shows EXACT before/after positions of ALL actors
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🔍 DETAILED ROTATION BUTTON ACTOR DEBUG")
    print("=" * 60)
    
    try:
        from step_viewer import StepViewerTDK
        
        app = QApplication(sys.argv)
        viewer = StepViewerTDK()
        viewer.show()
        
        # Start test after initialization
        QTimer.singleShot(2000, lambda: start_rotation_tests(viewer, app))
        
        app.exec_()
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

def start_rotation_tests(viewer, app):
    """Start the rotation button tests"""
    try:
        print("\n📂 Loading STEP file...")
        
        # Find STEP file
        step_files = [f for f in os.listdir('.') if f.endswith('.STEP') or f.endswith('.step')]
        if not step_files:
            print("❌ No STEP files found")
            app.quit()
            return
            
        step_file = step_files[0]
        print(f"📁 Loading: {step_file}")
        
        # Set active viewer and load file
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        
        if not success:
            print("❌ Failed to load STEP file")
            app.quit()
            return
            
        print("✅ STEP file loaded successfully")
        
        # Wait for loading to complete, then start tests
        QTimer.singleShot(3000, lambda: run_rotation_tests(viewer, app))
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        app.quit()

def run_rotation_tests(viewer, app):
    """Run all 6 rotation button tests"""
    try:
        print("\n🔄 STARTING ROTATION BUTTON TESTS")
        print("=" * 60)
        
        # Get renderer
        if not hasattr(viewer, 'vtk_renderer_left') or not viewer.vtk_renderer_left:
            print("❌ TOP renderer not found")
            app.quit()
            return
            
        renderer = viewer.vtk_renderer_left.renderer
        if not renderer:
            print("❌ VTK renderer not found")
            app.quit()
            return
            
        # Test each rotation button
        rotation_tests = [
            ('X+', 'x', 15.0),
            ('X-', 'x', -15.0),
            ('Y+', 'y', 15.0),
            ('Y-', 'y', -15.0),
            ('Z+', 'z', 15.0),
            ('Z-', 'z', -15.0)
        ]
        
        for button_name, axis, degrees in rotation_tests:
            print(f"\n🧪 TESTING ROTATION BUTTON: {button_name} ({axis} axis, {degrees}°)")
            print("-" * 50)
            
            # Capture BEFORE state
            before_actors = capture_all_actors(renderer, f"BEFORE {button_name}")
            
            # Execute rotation
            print(f"🔄 Executing rotation: {axis} axis {degrees}°")
            viewer.rotate_shape(axis, degrees)
            
            # Small delay for operation to complete
            QTimer.singleShot(200, lambda: None)
            
            # Capture AFTER state
            after_actors = capture_all_actors(renderer, f"AFTER {button_name}")
            
            # Analyze changes
            analyze_actor_changes(before_actors, after_actors, button_name)
            
        print(f"\n🎯 ALL ROTATION TESTS COMPLETED")
        print("=" * 60)
        
        # Exit after tests
        QTimer.singleShot(2000, app.quit)
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        app.quit()

def capture_all_actors(renderer, stage_name):
    """Capture detailed state of all actors"""
    print(f"\n📊 {stage_name} - ACTOR STATES:")
    
    actors_data = []
    
    try:
        actors = renderer.GetActors()
        actors.InitTraversal()
        
        actor_count = 0
        while True:
            actor = actors.GetNextActor()
            if not actor:
                break
                
            actor_count += 1
            
            # Get actor properties
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            color = actor.GetProperty().GetColor() if actor.GetProperty() else (0.0, 0.0, 0.0)
            
            # Classify actor by color
            actor_type = classify_actor_type(color)
            
            # Store actor data
            actor_data = {
                'id': actor_count,
                'type': actor_type,
                'position': pos,
                'orientation': orient,
                'color': color
            }
            actors_data.append(actor_data)
            
            # Print actor info
            print(f"   Actor_{actor_count:02d}_{actor_type}: "
                  f"Pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), "
                  f"Orient=({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
                  
        print(f"📊 Total actors: {actor_count}")
        
    except Exception as e:
        print(f"❌ Error capturing actors: {e}")
        
    return actors_data

def classify_actor_type(color):
    """Classify actor by color"""
    r, g, b = color
    
    if r > 0.8 and g < 0.3 and b < 0.3:
        return "RED_WORLD_ORIGIN"
    elif r < 0.3 and g > 0.8 and b < 0.3:
        return "GREEN_PART_ORIGIN"
    elif r > 0.8 and g > 0.8 and b < 0.3:
        return "YELLOW_ARROW"
    elif r < 0.3 and g < 0.3 and b > 0.8:
        return "BLUE_UNKNOWN"
    elif r > 0.5 and g > 0.5 and b > 0.5:
        return "WHITE_GRAY_MODEL"
    else:
        return "OTHER_GEOMETRY"

def analyze_actor_changes(before_actors, after_actors, button_name):
    """Analyze what changed between before and after states"""
    print(f"\n📈 ANALYSIS FOR {button_name}:")
    print("-" * 40)
    
    if len(before_actors) != len(after_actors):
        print(f"⚠️  Actor count changed: {len(before_actors)} → {len(after_actors)}")
        return
        
    moved_actors = []
    rotated_actors = []
    unchanged_actors = []
    
    for i, (before, after) in enumerate(zip(before_actors, after_actors)):
        # Calculate position change
        pos_change = [
            after['position'][j] - before['position'][j] 
            for j in range(3)
        ]
        pos_distance = sum(abs(x) for x in pos_change)
        
        # Calculate orientation change
        orient_change = [
            after['orientation'][j] - before['orientation'][j] 
            for j in range(3)
        ]
        orient_distance = sum(abs(x) for x in orient_change)
        
        # Classify change
        if pos_distance > 0.001:
            moved_actors.append({
                'actor': before,
                'pos_change': pos_change,
                'pos_distance': pos_distance,
                'orient_change': orient_change,
                'orient_distance': orient_distance
            })
        elif orient_distance > 0.1:
            rotated_actors.append({
                'actor': before,
                'pos_change': pos_change,
                'pos_distance': pos_distance,
                'orient_change': orient_change,
                'orient_distance': orient_distance
            })
        else:
            unchanged_actors.append(before)
    
    # Report results
    print(f"✅ MOVED ACTORS ({len(moved_actors)}):")
    for item in moved_actors:
        actor = item['actor']
        print(f"   Actor_{actor['id']:02d}_{actor['type']}: "
              f"Moved ({item['pos_change'][0]:.3f}, {item['pos_change'][1]:.3f}, {item['pos_change'][2]:.3f})")
              
    print(f"🔄 ROTATED ONLY ({len(rotated_actors)}):")
    for item in rotated_actors:
        actor = item['actor']
        print(f"   Actor_{actor['id']:02d}_{actor['type']}: "
              f"Rotated ({item['orient_change'][0]:.1f}°, {item['orient_change'][1]:.1f}°, {item['orient_change'][2]:.1f}°)")
              
    print(f"❌ UNCHANGED ACTORS ({len(unchanged_actors)}):")
    for actor in unchanged_actors:
        print(f"   Actor_{actor['id']:02d}_{actor['type']}: NO CHANGE")
        
    # Summary analysis
    print(f"\n📊 SUMMARY FOR {button_name}:")
    print(f"   Total actors: {len(before_actors)}")
    print(f"   Moved: {len(moved_actors)}")
    print(f"   Rotated only: {len(rotated_actors)}")
    print(f"   Unchanged: {len(unchanged_actors)}")
    
    # Expected behavior check
    if len(unchanged_actors) > 0:
        print(f"   ⚠️  PROBLEM: {len(unchanged_actors)} actors did not move or rotate!")
        print(f"   🎯 EXPECTED: During rotation, ALL actors should either move or rotate")
    else:
        print(f"   ✅ SUCCESS: All actors moved or rotated during rotation")

if __name__ == "__main__":
    main()
