FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-30.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=-15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-30.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=-15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-30.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=-15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15.0
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=30
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=30
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=30
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=45
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=60
DEBUG ROTATION TEST: active_viewer='bottom'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=30
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='z', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='y', degrees=-15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
FIREFIREFIRE NEW CODE RUNNING: rotate_shape called with axis='x', degrees=15
DEBUG ROTATION TEST: active_viewer='top'
