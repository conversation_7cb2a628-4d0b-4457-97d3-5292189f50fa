#!/usr/bin/env python3
"""
DEBUG PROGRAM: Test the display number calculations
This program tests the _calculate_unified_display_numbers function
to verify that Model and Local Origin Marker show the same values.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import just the calculation function by copying it here
def calculate_unified_display_numbers_test(viewer_data):
    """Test version of the calculation function"""

    current_rot = viewer_data['current_rot']
    current_pos = viewer_data['current_pos']
    cursor_pos = viewer_data['cursor_pos']
    renderer = viewer_data.get('renderer')
    step_loader = viewer_data.get('step_loader')

    # Calculate cursor display
    cursor_display = f"CURSOR: X={cursor_pos['x']:.2f} Y={cursor_pos['y']:.2f} Z={cursor_pos['z']:.2f}"

    # Calculate model display - MUST BE SAME AS LOCAL ORIGIN MARKER
    model_pos = current_pos
    model_dir_x, model_dir_y, model_dir_z = 0.000, 0.000, 0.000  # Will be calculated same as local origin
    model_ref_x, model_ref_y, model_ref_z = 0.000, 0.000, 0.000  # Will be calculated same as local origin

    # Calculate local origin marker display (green ball) - FIXED: Show actual transformed directions
    if renderer and hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
        pos = renderer.part_origin_sphere.GetPosition()
        orient = renderer.part_origin_sphere.GetOrientation()

        # FIXED: Use the current rotation tracking values that actually change with transformations
        # The stored direction vectors are static, but current_rot changes with each rotation
        dir_x, dir_y, dir_z = current_rot['x'], current_rot['y'], current_rot['z']
        ref_x, ref_y, ref_z = current_rot['x'], current_rot['y'], current_rot['z']
        print(f"🔢 LOCAL ORIGIN: Using current rotation tracking values: {current_rot}")

        local_origin_display = f"Local Origin Direction (X = {dir_x:.3f} Y = {dir_y:.3f} Z = {dir_z:.3f}) REF. Direction (X = {ref_x:.3f} Y = {ref_y:.3f} Z = {ref_z:.3f}) Origin (X = {pos[0]:.3f} Y = {pos[1]:.3f} Z = {pos[2]:.3f})"

        # FIXED: Make Model display use the SAME values as Local Origin Marker
        model_display = f"Model Direction (X = {dir_x:.3f} Y = {dir_y:.3f} Z = {dir_z:.3f}) REF. Direction (X = {ref_x:.3f} Y = {ref_y:.3f} Z = {ref_z:.3f}) Origin (X = {current_pos['x']:.3f} Y = {current_pos['y']:.3f} Z = {current_pos['z']:.3f})"
        print(f"🔢 MODEL & LOCAL ORIGIN: Using SAME direction values - dir=({dir_x:.3f},{dir_y:.3f},{dir_z:.3f}), ref=({ref_x:.3f},{ref_y:.3f},{ref_z:.3f})")
    else:
        # Fallback: use current rotation tracking values
        dir_x, dir_y, dir_z = current_rot['x'], current_rot['y'], current_rot['z']
        ref_x, ref_y, ref_z = current_rot['x'], current_rot['y'], current_rot['z']
        local_origin_display = f"Local Origin Direction (X = {dir_x:.3f} Y = {dir_y:.3f} Z = {dir_z:.3f}) REF. Direction (X = {ref_x:.3f} Y = {ref_y:.3f} Z = {ref_z:.3f}) Origin (X = {current_pos['x']:.3f} Y = {current_pos['y']:.3f} Z = {current_pos['z']:.3f})"
        # Model uses same values
        model_display = f"Model Direction (X = {dir_x:.3f} Y = {dir_y:.3f} Z = {dir_z:.3f}) REF. Direction (X = {ref_x:.3f} Y = {ref_y:.3f} Z = {ref_z:.3f}) Origin (X = {current_pos['x']:.3f} Y = {current_pos['y']:.3f} Z = {current_pos['z']:.3f})"

    # Calculate world origin display (always stays at 0,0,0)
    world_origin_display = f"World Origin Direction (X = 0.000 Y = 0.000 Z = 0.000) REF. Direction (X = 0.000 Y = 0.000 Z = 0.000) Origin (X = 0.000 Y = 0.000 Z = 0.000)"

    return {
        'cursor': cursor_display,
        'model': model_display,
        'local_origin': local_origin_display,
        'world_origin': world_origin_display
    }

class MockRenderer:
    """Mock renderer for testing"""
    def __init__(self):
        self.part_origin_sphere = MockActor()
        self.step_actors = [MockActor()]

class MockActor:
    """Mock VTK actor for testing"""
    def __init__(self):
        self.position = (0.0, 0.0, 0.0)
        self.orientation = (0.0, 0.0, 0.0)
        # Add direction vectors like the real green ball
        self.z_direction = [0.000, 0.910, -0.414]
        self.x_direction = [0.000, 0.414, 0.910]
    
    def GetPosition(self):
        return self.position
    
    def GetOrientation(self):
        return self.orientation
    
    def SetPosition(self, x, y, z):
        self.position = (x, y, z)
    
    def SetOrientation(self, rx, ry, rz):
        self.orientation = (rx, ry, rz)

class MockStepLoader:
    """Mock STEP loader for testing"""
    def get_original_axis2_placement(self):
        return {
            'dir1': [0, 0, 1],
            'dir2': [1, 0, 0],
            'origin': [0, 0, 0]
        }

def test_display_calculations():
    """Test the display number calculations"""
    print("🔧 DEBUG PROGRAM: Testing display number calculations")
    print("=" * 60)

    # Set up test data
    test_data = {
        'current_rot': {'x': 10.5, 'y': 20.3, 'z': -15.7},
        'current_pos': {'x': -4.19, 'y': -3.667, 'z': 0.491},
        'cursor_pos': {'x': -12.5, 'y': 2.3, 'z': 1.8},
        'renderer': MockRenderer(),
        'step_loader': MockStepLoader()
    }

    print("🔍 TEST DATA:")
    print(f"   current_rot = {test_data['current_rot']}")
    print(f"   current_pos = {test_data['current_pos']}")
    print(f"   cursor_pos = {test_data['cursor_pos']}")
    print()

    # Test the calculation function
    print("🧮 CALLING calculate_unified_display_numbers_test()...")
    try:
        results = calculate_unified_display_numbers_test(test_data)

        print("✅ CALCULATION RESULTS:")
        print(f"   Cursor: {results['cursor']}")
        print(f"   Model: {results['model']}")
        print(f"   Local Origin: {results['local_origin']}")
        print(f"   World Origin: {results['world_origin']}")
        print()

        # Check if Model and Local Origin match
        model_display = results['model']
        local_display = results['local_origin']

        print("🔍 CHECKING IF MODEL AND LOCAL ORIGIN MATCH:")

        # Extract direction values
        if "Model Direction" in model_display and "Local Origin Direction" in local_display:
            try:
                # Parse Model direction
                model_parts = model_display.split("Direction")[1].split("REF. Direction")
                model_dir = model_parts[0].strip() if len(model_parts) >= 1 else "PARSE_ERROR"
                model_ref = model_parts[1].split("Origin")[0].strip() if len(model_parts) >= 2 else "PARSE_ERROR"

                # Parse Local Origin direction
                local_parts = local_display.split("Direction")[1].split("REF. Direction")
                local_dir = local_parts[0].strip() if len(local_parts) >= 1 else "PARSE_ERROR"
                local_ref = local_parts[1].split("Origin")[0].strip() if len(local_parts) >= 2 else "PARSE_ERROR"

                print(f"   Model Direction: {model_dir}")
                print(f"   Local Direction: {local_dir}")
                print(f"   Model REF Direction: {model_ref}")
                print(f"   Local REF Direction: {local_ref}")

                if model_dir == local_dir and model_ref == local_ref:
                    print("✅ SUCCESS: Model and Local Origin directions MATCH!")
                else:
                    print("❌ PROBLEM: Model and Local Origin directions are DIFFERENT!")
                    print("   This needs to be fixed.")

            except Exception as e:
                print(f"❌ ERROR parsing directions: {e}")
        else:
            print("❌ ERROR: Could not find direction strings in displays")

    except Exception as e:
        print(f"❌ ERROR calling calculation function: {e}")
        import traceback
        traceback.print_exc()

def test_with_different_rotations():
    """Test with different rotation values"""
    print("\n" + "=" * 60)
    print("🔧 TESTING WITH DIFFERENT ROTATION VALUES")
    print("=" * 60)

    test_rotations = [
        {'x': 0, 'y': 0, 'z': 0},
        {'x': 45, 'y': 0, 'z': 0},
        {'x': 0, 'y': 90, 'z': 0},
        {'x': 30, 'y': 45, 'z': 60}
    ]

    for i, rot in enumerate(test_rotations):
        print(f"\n🔄 TEST {i+1}: Rotation = {rot}")

        # Create test data for this rotation
        test_data = {
            'current_rot': rot,
            'current_pos': {'x': 0, 'y': 0, 'z': 0},
            'cursor_pos': {'x': 0, 'y': 0, 'z': 0},
            'renderer': MockRenderer(),
            'step_loader': MockStepLoader()
        }

        # Update mock green ball to simulate rotation
        test_data['renderer'].part_origin_sphere.SetOrientation(rot['x'], rot['y'], rot['z'])

        try:
            results = calculate_unified_display_numbers_test(test_data)
            model = results['model']
            local = results['local_origin']

            print(f"   Model: {model}")
            print(f"   Local: {local}")

            # Quick check if they match
            if "Direction" in model and "Direction" in local:
                model_dir_part = model.split("Direction")[1].split("REF")[0] if "Direction" in model else ""
                local_dir_part = local.split("Direction")[1].split("REF")[0] if "Direction" in local else ""
                match = model_dir_part == local_dir_part
                print(f"   Match: {'✅ YES' if match else '❌ NO'}")

        except Exception as e:
            print(f"   ❌ ERROR: {e}")

if __name__ == "__main__":
    test_display_calculations()
    test_with_different_rotations()
    print("\n🏁 DEBUG PROGRAM COMPLETE")
