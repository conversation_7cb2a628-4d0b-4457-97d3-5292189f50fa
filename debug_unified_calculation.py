#!/usr/bin/env python3
"""
Debug script to trace the unified calculation system step by step
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== DEBUGGING UNIFIED CALCULATION SYSTEM ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Testing with: {test_file}")
    
    # Load STEP file
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ STEP file loaded successfully")
        
        print("\n=== INITIAL STATE ===")
        print(f"current_rot_left: {viewer.current_rot_left}")
        print(f"orig_rot_left: {viewer.orig_rot_left}")
        print(f"current_pos_left: {viewer.current_pos_left}")
        print(f"orig_pos_left: {viewer.orig_pos_left}")
        
        # Check direction vectors
        if hasattr(viewer, 'orig_direction_vectors_left'):
            print(f"orig_direction_vectors_left: {viewer.orig_direction_vectors_left}")
        else:
            print("❌ orig_direction_vectors_left NOT SET")
            
        # Test unified calculation BEFORE rotation
        print("\n=== UNIFIED CALCULATION BEFORE ROTATION ===")
        result = viewer._calculate_unified_display_numbers("top")
        
        # Apply a rotation
        print("\n=== APPLYING ROTATION ===")
        viewer._apply_model_rotation("top", "x", 15.0)
        
        print(f"AFTER rotation - current_rot_left: {viewer.current_rot_left}")
        print(f"AFTER rotation - orig_rot_left: {viewer.orig_rot_left}")
        
        # Calculate user_only_rot manually
        user_only_rot = {
            'x': viewer.current_rot_left['x'] - viewer.orig_rot_left['x'],
            'y': viewer.current_rot_left['y'] - viewer.orig_rot_left['y'],
            'z': viewer.current_rot_left['z'] - viewer.orig_rot_left['z']
        }
        print(f"user_only_rot: {user_only_rot}")
        
        # Check if rotation should be detected
        has_rotation = any(abs(user_only_rot[axis]) > 0.001 for axis in ['x', 'y', 'z'])
        print(f"Should detect rotation (>0.001): {has_rotation}")
        
        # Test unified calculation AFTER rotation
        print("\n=== UNIFIED CALCULATION AFTER ROTATION ===")
        result = viewer._calculate_unified_display_numbers("top")
        
        # Check direction vector transformation
        if hasattr(viewer, 'orig_direction_vectors_left'):
            direction_vectors = viewer.orig_direction_vectors_left
            original_x_dir = direction_vectors.get('x_axis', [0, 0, 0])
            original_z_dir = direction_vectors.get('z_axis', [0, 0, 0])
            
            print(f"\nDIRECTION VECTORS:")
            print(f"original_x_dir: {original_x_dir}")
            print(f"original_z_dir: {original_z_dir}")
            
            if has_rotation:
                print("Testing direction vector transformation...")
                try:
                    transformed_x_dir = viewer._transform_direction_vector(original_x_dir, user_only_rot)
                    transformed_z_dir = viewer._transform_direction_vector(original_z_dir, user_only_rot)
                    print(f"transformed_x_dir: {transformed_x_dir}")
                    print(f"transformed_z_dir: {transformed_z_dir}")
                except Exception as e:
                    print(f"❌ Direction vector transformation failed: {e}")
            else:
                print("No rotation detected - using original vectors")
                
    else:
        print("❌ STEP file loading failed")
else:
    print("❌ No STEP files found for testing")

app.quit()
print("=== DEBUG COMPLETED ===")
