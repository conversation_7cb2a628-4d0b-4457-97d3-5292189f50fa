#!/usr/bin/env python3
"""
Test script to verify that mouse interaction events are being triggered.
"""

import sys
import os
import vtk
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK as <PERSON><PERSON>iewer

def test_mouse_events():
    """Test mouse interaction events"""
    print("🔍 TESTING MOUSE INTERACTION EVENTS")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    viewer = StepViewer()
    viewer.show()
    
    # Wait for GUI to initialize
    QTimer.singleShot(1000, lambda: load_and_test(viewer, app))
    
    app.exec_()

def load_and_test(viewer, app):
    """Load STEP file and test mouse events"""
    try:
        print("\n1. Loading STEP file...")
        
        # Load the STEP file
        step_file = "e:/Python/3d-view/SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            app.quit()
            return
            
        viewer.load_step_file_direct(step_file)
        print("✅ STEP file loaded")
        
        # Wait for loading to complete
        QTimer.singleShot(2000, lambda: setup_event_monitoring(viewer, app))
        
    except Exception as e:
        print(f"❌ Error loading STEP file: {e}")
        app.quit()

def setup_event_monitoring(viewer, app):
    """Set up enhanced event monitoring"""
    try:
        print("\n2. Setting up enhanced event monitoring...")
        
        # Get the VTK interactor
        if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
            interactor = viewer.vtk_renderer_left.interactor
            if interactor:
                print("✅ Found VTK interactor")
                
                # Create a custom event handler that logs all events
                def log_all_events(obj, event):
                    print(f"🔥 VTK EVENT FIRED: {event}")
                    
                    # Check if this is a rotation-related event
                    if event in ['InteractionEvent', 'LeftButtonReleaseEvent', 'EndInteractionEvent', 'ModifiedEvent']:
                        print(f"  🎯 ROTATION-RELATED EVENT: {event}")
                        
                        # Try to call the original mouse interaction handler
                        try:
                            viewer.on_mouse_interaction_left(obj, event)
                            print(f"  ✅ Called mouse interaction handler successfully")
                        except Exception as e:
                            print(f"  ❌ Error calling mouse interaction handler: {e}")
                
                # Add observers for ALL possible events
                events_to_monitor = [
                    'InteractionEvent',
                    'StartInteractionEvent', 
                    'EndInteractionEvent',
                    'LeftButtonPressEvent',
                    'LeftButtonReleaseEvent',
                    'MiddleButtonPressEvent',
                    'MiddleButtonReleaseEvent',
                    'RightButtonPressEvent',
                    'RightButtonReleaseEvent',
                    'MouseMoveEvent',
                    'ModifiedEvent',
                    'RenderEvent'
                ]
                
                for event in events_to_monitor:
                    try:
                        interactor.AddObserver(event, log_all_events)
                        print(f"  ✅ Added observer for {event}")
                    except Exception as e:
                        print(f"  ❌ Failed to add observer for {event}: {e}")
                
                print(f"\n✅ Enhanced event monitoring set up")
                print(f"Now try rotating the model with the mouse in the TOP viewer.")
                print(f"You should see event messages in the console.")
                
            else:
                print("❌ No VTK interactor found")
                app.quit()
        else:
            print("❌ No VTK renderer found")
            app.quit()
            
    except Exception as e:
        print(f"❌ Error setting up event monitoring: {e}")
        app.quit()

if __name__ == "__main__":
    test_mouse_events()
