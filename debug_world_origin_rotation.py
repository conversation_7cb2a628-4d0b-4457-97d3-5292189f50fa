#!/usr/bin/env python3
"""
Debug World Origin Rotation Issue
Test to understand why world origin direction text is not updating during mouse rotation
"""

import sys
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import <PERSON><PERSON>iewerTDK

def debug_world_origin_rotation():
    """Debug the world origin rotation issue"""
    print("🔍 DEBUGGING WORLD ORIGIN ROTATION ISSUE")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Process events to ensure GUI is ready
    app.processEvents()
    time.sleep(1)
    
    # Load a test file
    test_files = [
        "SOIC16P127_1270X940X610L89X51.STEP",
        "test.step"
    ]
    
    test_file = None
    for filename in test_files:
        import os
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ No test file found")
        return False
    
    print(f"📁 Loading test file: {test_file}")
    
    # Set active viewer to top
    viewer.active_viewer = "top"
    
    # Load the file
    success = viewer.load_step_file_direct(test_file)
    if not success:
        print("❌ Failed to load test file")
        return False
    
    print("✅ Test file loaded successfully")
    
    # Process events and wait
    app.processEvents()
    time.sleep(2)
    
    # Check initial world origin state
    print("\n🔍 INITIAL STATE:")
    check_world_origin_state(viewer, "top")
    
    # Apply a button rotation to see if world origin updates
    print("\n🔄 APPLYING BUTTON ROTATION (X+15°):")
    viewer.rotate_shape('x', 15.0)
    
    # Process events and wait
    app.processEvents()
    time.sleep(1)
    
    # Check world origin state after button rotation
    print("\n🔍 AFTER BUTTON ROTATION:")
    check_world_origin_state(viewer, "top")
    
    # Now test mouse rotation simulation
    print("\n🖱️ SIMULATING MOUSE ROTATION (Y+10°):")
    viewer._apply_model_rotation("top", 'y', 10.0)
    
    # Process events and wait
    app.processEvents()
    time.sleep(1)
    
    # Check world origin state after mouse rotation
    print("\n🔍 AFTER MOUSE ROTATION:")
    check_world_origin_state(viewer, "top")
    
    print("\n✅ Debug test completed")
    return True

def check_world_origin_state(viewer, viewer_name):
    """Check the current state of world origin markers and text"""
    try:
        if viewer_name == "top":
            renderer = viewer.vtk_renderer_left
            text_actor = getattr(viewer, 'world_origin_text_actor_left', None)
        else:
            renderer = viewer.vtk_renderer_right
            text_actor = getattr(viewer, 'world_origin_text_actor_right', None)
        
        # Check world origin actors
        if hasattr(renderer, 'origin_actors') and renderer.origin_actors:
            print(f"   📍 Found {len(renderer.origin_actors)} world origin actors")
            
            for i, actor in enumerate(renderer.origin_actors):
                if actor:
                    pos = actor.GetPosition()
                    orient = actor.GetOrientation()
                    print(f"   📍 Actor {i}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), orient=({orient[0]:.1f}°, {orient[1]:.1f}°, {orient[2]:.1f}°)")
        else:
            print("   ❌ No world origin actors found")
        
        # Check world origin text
        if text_actor:
            text = text_actor.GetInput()
            print(f"   📝 World origin text: {text}")
        else:
            print("   ❌ No world origin text actor found")
        
        # Check current rotation tracking
        if viewer_name == "top":
            current_rot = getattr(viewer, 'current_rot_left', {})
        else:
            current_rot = getattr(viewer, 'current_rot_right', {})
        
        print(f"   🔄 Tracked rotation: {current_rot}")
        
    except Exception as e:
        print(f"   ❌ Error checking world origin state: {e}")

if __name__ == "__main__":
    debug_world_origin_rotation()
