#!/usr/bin/env python3
"""
Debug VTK display to see why STEP geometry is not showing
"""

import sys
import os

def debug_vtk_display():
    """Debug the VTK display process"""
    print("DEBUG VTK DISPLAY PROCESS")
    print("=" * 50)
    
    try:
        # Load the STEP file
        from step_loader import STEPLoader
        loader = STEPLoader()
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        
        print("1. Loading STEP file...")
        success, message = loader.load_step_file(step_file)
        if not success:
            print(f"   FAIL: Could not load STEP file: {message}")
            return False
            
        polydata = loader.current_polydata
        if not polydata:
            print(f"   FAIL: No polydata created")
            return False
            
        points = polydata.GetNumberOfPoints()
        cells = polydata.GetNumberOfCells()
        print(f"   SUCCESS: Loaded geometry with {points} points, {cells} cells")
        
        # Check the actual geometry bounds
        bounds = polydata.GetBounds()
        print(f"   Geometry bounds: X=[{bounds[0]:.2f}, {bounds[1]:.2f}], Y=[{bounds[2]:.2f}, {bounds[3]:.2f}], Z=[{bounds[4]:.2f}, {bounds[5]:.2f}]")
        
        # Check if geometry is at origin or far away
        center_x = (bounds[0] + bounds[1]) / 2
        center_y = (bounds[2] + bounds[3]) / 2  
        center_z = (bounds[4] + bounds[5]) / 2
        print(f"   Geometry center: ({center_x:.2f}, {center_y:.2f}, {center_z:.2f})")
        
        # Test VTK renderer
        print("\n2. Testing VTK renderer...")
        from vtk_renderer import VTKRenderer
        
        # Create renderer without GUI
        renderer = VTKRenderer(None)  # No parent widget
        print(f"   VTK renderer created")
        
        # Test display_polydata
        print(f"   Testing display_polydata...")
        result = renderer.display_polydata(polydata)
        print(f"   display_polydata result: {result}")
        
        # Check if actors were created
        if hasattr(renderer, 'renderer') and renderer.renderer:
            num_actors = renderer.renderer.GetActors().GetNumberOfItems()
            print(f"   Number of actors in renderer: {num_actors}")
            
            if num_actors > 0:
                print(f"   SUCCESS: Actors were created")
                
                # Get first actor and check its bounds
                actors = renderer.renderer.GetActors()
                actors.InitTraversal()
                first_actor = actors.GetNextActor()
                if first_actor:
                    actor_bounds = first_actor.GetBounds()
                    print(f"   First actor bounds: X=[{actor_bounds[0]:.2f}, {actor_bounds[1]:.2f}], Y=[{actor_bounds[2]:.2f}, {actor_bounds[3]:.2f}], Z=[{actor_bounds[4]:.2f}, {actor_bounds[5]:.2f}]")
            else:
                print(f"   FAIL: No actors created")
                return False
        else:
            print(f"   FAIL: No VTK renderer created")
            return False
            
        # Test camera positioning
        print("\n3. Testing camera positioning...")
        if hasattr(renderer, 'renderer') and renderer.renderer:
            camera = renderer.renderer.GetActiveCamera()
            if camera:
                pos = camera.GetPosition()
                focal = camera.GetFocalPoint()
                print(f"   Camera position: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f})")
                print(f"   Camera focal point: ({focal[0]:.2f}, {focal[1]:.2f}, {focal[2]:.2f})")
                
                # Check if camera is looking at the geometry
                distance = ((pos[0]-center_x)**2 + (pos[1]-center_y)**2 + (pos[2]-center_z)**2)**0.5
                print(f"   Distance from camera to geometry center: {distance:.2f}")
                
                if distance > 1000:
                    print(f"   WARNING: Camera is very far from geometry - might not be visible")
                elif distance < 0.1:
                    print(f"   WARNING: Camera is very close to geometry - might be inside it")
                else:
                    print(f"   Camera distance looks reasonable")
            else:
                print(f"   FAIL: No camera found")
                
        # Test fit_view
        print("\n4. Testing fit_view...")
        try:
            renderer.fit_view()
            print(f"   fit_view called successfully")
            
            # Check camera position after fit_view
            if hasattr(renderer, 'renderer') and renderer.renderer:
                camera = renderer.renderer.GetActiveCamera()
                if camera:
                    pos = camera.GetPosition()
                    focal = camera.GetFocalPoint()
                    print(f"   Camera position after fit_view: ({pos[0]:.2f}, {pos[1]:.2f}, {pos[2]:.2f})")
                    print(f"   Camera focal point after fit_view: ({focal[0]:.2f}, {focal[1]:.2f}, {focal[2]:.2f})")
        except Exception as e:
            print(f"   FAIL: fit_view error: {e}")
            
        print("\n5. SUMMARY:")
        print(f"   - STEP geometry loads correctly ({points} points, {cells} cells)")
        print(f"   - Geometry bounds: {bounds}")
        print(f"   - VTK renderer creates actors")
        print(f"   - Camera positioning may need adjustment")
        
        return True
        
    except Exception as e:
        print(f"FAIL: VTK display debug error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_vtk_display()
