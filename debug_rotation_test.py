#!/usr/bin/env python3
"""
Debug test script to test mouse rotation and see which actors are missing
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Import the main application
from step_viewer import Step<PERSON>iewerTDK

def test_rotation_debug():
    """Test rotation and capture debug output"""
    print("🔍 STARTING ROTATION DEBUG TEST")
    
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    def run_test():
        print("🔍 TEST: Loading STEP file...")
        
        # Look for a STEP file to test with
        test_files = [
            "test.step", "test.stp", 
            "../save/test.step", "../save/test.stp",
            "sample.step", "sample.stp"
        ]
        
        test_file = None
        for f in test_files:
            if os.path.exists(f):
                test_file = f
                break
        
        if not test_file:
            print("❌ No test STEP file found. Creating dummy test...")
            # Test without file - just check the rotation system
            viewer.active_viewer = "top"
            print("🔍 TEST: Simulating mouse rotation without file...")
            
            # Simulate calling the unified rotation system
            try:
                viewer._apply_unified_rotation('x', 10.0)
                print("✅ Unified rotation system called successfully")
            except Exception as e:
                print(f"❌ Error in unified rotation: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"🔍 TEST: Found test file: {test_file}")
            # Load the file
            try:
                # Set active viewer first
                viewer.active_viewer = "top"
                if viewer.load_step_file_direct(test_file):
                    print("✅ File loaded successfully")

                    # Wait a moment for loading to complete
                    QTimer.singleShot(2000, test_rotation)
                else:
                    print("❌ Failed to load file")
            except Exception as e:
                print(f"❌ Error loading file: {e}")
                import traceback
                traceback.print_exc()
    
    def test_rotation():
        print("🔍 TEST: Testing rotation system...")
        viewer.active_viewer = "top"

        try:
            # Test the unified rotation system
            print("🔍 TEST: Calling _apply_unified_rotation('x', 10.0)")
            viewer._apply_unified_rotation('x', 10.0)
            print("✅ Rotation test completed")

            # Test mouse drag state
            print("🔍 TEST: Testing mouse drag state...")
            print(f"   is_dragging_left exists: {hasattr(viewer, 'is_dragging_left')}")
            if hasattr(viewer, 'is_dragging_left'):
                print(f"   is_dragging_left value: {viewer.is_dragging_left}")

            # Exit after test
            QTimer.singleShot(1000, app.quit)

        except Exception as e:
            print(f"❌ Error in rotation test: {e}")
            import traceback
            traceback.print_exc()
            app.quit()
    
    # Start the test after a short delay
    QTimer.singleShot(1000, run_test)
    
    # Run the app
    app.exec_()

if __name__ == "__main__":
    test_rotation_debug()
