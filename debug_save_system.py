#!/usr/bin/env python3
"""
Debug script to trace the save system step by step
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== DEBUGGING SAVE SYSTEM ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Testing with: {test_file}")
    
    # Load STEP file
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ STEP file loaded successfully")
        
        print("\n=== INITIAL STATE ===")
        print(f"current_rot_left: {viewer.current_rot_left}")
        print(f"orig_rot_left: {viewer.orig_rot_left}")
        print(f"current_pos_left: {viewer.current_pos_left}")
        print(f"orig_pos_left: {viewer.orig_pos_left}")
        
        # Apply a rotation
        print("\n=== APPLYING ROTATION ===")
        viewer._apply_model_rotation("top", "x", 15.0)
        
        print(f"AFTER rotation - current_rot_left: {viewer.current_rot_left}")
        print(f"AFTER rotation - orig_rot_left: {viewer.orig_rot_left}")
        
        # Test the save extraction process
        print("\n=== TESTING SAVE EXTRACTION ===")
        
        # Test _extract_current_position_from_display
        try:
            display_text = viewer._calculate_unified_display_numbers("top")
            print(f"Unified display calculated")
            
            # Get the actual display text that would be shown
            if hasattr(viewer, 'combined_text_actor_left'):
                actual_display = viewer.combined_text_actor_left.GetInput()
                print(f"Actual display text: {actual_display}")
                
                # Test position extraction (using correct method name)
                extracted_pos = viewer._extract_position_from_display("top")
                print(f"Extracted position: {extracted_pos}")

                # Test rotation extraction (using correct method name)
                extracted_rot = viewer._extract_rotation_from_vtk_actor("top")
                print(f"Extracted rotation: {extracted_rot}")
                
        except Exception as e:
            print(f"❌ Error in extraction: {e}")
            
        # Test the actual save process
        print("\n=== TESTING SAVE PROCESS ===")
        try:
            # Test save_step_file_option1 (green button)
            test_save_file = "test_save_debug.step"
            
            # Get current values for save (using correct method names)
            current_pos = viewer._extract_position_from_display("top")
            current_rot = viewer._extract_rotation_from_vtk_actor("top")
            orig_pos = viewer.orig_pos_left
            orig_rot = viewer.orig_rot_left
            
            print(f"Save inputs:")
            print(f"  current_pos: {current_pos}")
            print(f"  current_rot: {current_rot}")
            print(f"  orig_pos: {orig_pos}")
            print(f"  orig_rot: {orig_rot}")
            
            # Check if rotation changed
            rotation_changed = any(abs(current_rot.get(axis, 0) - orig_rot.get(axis, 0)) > 0.1 for axis in ['x', 'y', 'z'])
            position_changed = any(abs(current_pos.get(axis, 0) - orig_pos.get(axis, 0)) > 0.1 for axis in ['x', 'y', 'z'])
            
            print(f"  rotation_changed: {rotation_changed}")
            print(f"  position_changed: {position_changed}")
            
            if rotation_changed or position_changed:
                print("✅ Changes detected - save should apply transformations")
                
                # Test the transformation save method
                loader = viewer.step_loader_left
                success = viewer._save_step_with_transformations(test_save_file, loader, current_pos, current_rot, orig_pos, orig_rot)
                print(f"Save result: {success}")
                
                if success and os.path.exists(test_save_file):
                    print(f"✅ Save file created: {test_save_file}")
                    
                    # Test loading the saved file
                    print("\n=== TESTING SAVED FILE LOAD ===")
                    viewer.active_viewer = "bottom"  # Switch to bottom viewer
                    load_success = viewer.load_step_file_direct(test_save_file)
                    
                    if load_success:
                        print("✅ Saved file loaded successfully")
                        print(f"Bottom viewer position: {viewer.current_pos_right}")
                        print(f"Bottom viewer rotation: {viewer.current_rot_right}")
                        
                        # Compare with expected values
                        print("\n=== COMPARING RESULTS ===")
                        print(f"Expected position: {current_pos}")
                        print(f"Actual position: {viewer.current_pos_right}")
                        print(f"Expected rotation: {current_rot}")
                        print(f"Actual rotation: {viewer.current_rot_right}")
                        
                    else:
                        print("❌ Failed to load saved file")
                else:
                    print("❌ Save failed or file not created")
            else:
                print("❌ No changes detected - save would not apply transformations")
                
        except Exception as e:
            print(f"❌ Error in save process: {e}")
            import traceback
            traceback.print_exc()
            
    else:
        print("❌ STEP file loading failed")
else:
    print("❌ No STEP files found for testing")

app.quit()
print("=== DEBUG COMPLETED ===")
