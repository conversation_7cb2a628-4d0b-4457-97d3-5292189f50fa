#!/usr/bin/env python3

"""
Test rotation fixes for both mouse rotation and button rotation.
This test will systematically check both rotation methods and verify
that both local and world origin displays update correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK as <PERSON><PERSON>iewer
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import time

def test_rotation_fixes():
    """Test both mouse and button rotation fixes"""
    
    print("=" * 80)
    print("TESTING ROTATION FIXES FOR ORIGIN DISPLAYS")
    print("=" * 80)
    
    app = QApplication(sys.argv)
    
    try:
        # Create the main viewer
        viewer = StepViewer()
        viewer.show()
        
        # Wait for GUI to initialize
        app.processEvents()
        time.sleep(1)
        
        print("\n1. Loading SOIC-16 footprint...")
        
        # Load the SOIC-16 file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        # Set to TOP viewer and load file
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        print("✅ STEP file loaded successfully")
        
        # Wait for loading to complete
        app.processEvents()
        time.sleep(2)
        
        print("\n2. Testing initial origin displays...")
        
        # Trigger text overlay update to see initial values
        viewer.update_text_overlays()
        app.processEvents()
        
        print("\n3. Testing button rotation (15 degrees X)...")
        
        # Simulate clicking the X rotation button (15 degrees)
        if hasattr(viewer, 'rotate_x_pos_left'):
            print("   Clicking X+ rotation button...")
            viewer.rotate_x_pos_left()
            app.processEvents()
            time.sleep(1)
            
            # Trigger text overlay update
            viewer.update_text_overlays()
            app.processEvents()
            
            print("   ✅ X+ rotation applied - check debug output for origin updates")
        else:
            print("   ❌ X+ rotation button method not found")
            
        print("\n4. Testing another button rotation (15 degrees Y)...")
        
        # Simulate clicking the Y rotation button
        if hasattr(viewer, 'rotate_y_pos_left'):
            print("   Clicking Y+ rotation button...")
            viewer.rotate_y_pos_left()
            app.processEvents()
            time.sleep(1)
            
            # Trigger text overlay update
            viewer.update_text_overlays()
            app.processEvents()
            
            print("   ✅ Y+ rotation applied - check debug output for origin updates")
        else:
            print("   ❌ Y+ rotation button method not found")
            
        print("\n5. Test completed - check debug output above")
        print("   Look for 'DEBUG LOCAL ORIGIN:' and 'DEBUG WORLD ORIGIN:' messages")
        print("   Both local and world origins should update after button rotations")
        
        # Keep GUI open for visual verification
        print("\n6. GUI is open for visual verification...")
        print("   Try mouse rotation and button rotation to verify both work correctly")
        print("   Press Ctrl+C to exit")
        
        # Run the GUI event loop
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_rotation_fixes()
