#!/usr/bin/env python3
"""
Comprehensive debug program to test ALL buttons and verify correct behavior:

MOVE BUTTONS (X+, Y+, Z+):
- Model moves ✅
- Local origin moves with model ✅  
- World origin moves with model ✅

ROTATE BUTTONS (X+, Y+, Z+):
- Model rotates ✅
- Local origin moves with model ✅
- World origin stays at (0,0,0) but arrows rotate ✅
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK as StepViewer

class AllButtonsDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = StepViewer()
        self.test_sequence = [
            # Format: (operation_type, axis, amount, description)
            # ONLY ROTATION BUTTONS
            ('rotate', 'x', 15.0, 'ROT X+ (15°)'),
            ('rotate', 'x', -15.0, 'ROT X- (-15°)'),
            ('rotate', 'y', 15.0, 'ROT Y+ (15°)'),
            ('rotate', 'y', -15.0, 'ROT Y- (-15°)'),
            ('rotate', 'z', 15.0, 'ROT Z+ (15°)'),
            ('rotate', 'z', -15.0, 'ROT Z- (-15°)'),
        ]
        self.current_test = 0
        self.test_results = []
        
    def analyze_actors(self, stage_name):
        """Analyze all actors and return structured data"""
        if not hasattr(self.viewer, 'vtk_renderer_left') or not self.viewer.vtk_renderer_left:
            return None
            
        renderer = self.viewer.vtk_renderer_left.renderer
        if not renderer:
            return None
            
        print(f"\n🔍 {stage_name}")
        print(f"-" * 60)
        
        # Collect actors by type
        red_actors = []      # World origin
        green_actors = []    # Local origin  
        yellow_actors = []   # Local arrows
        model_actors = []    # Model geometry
        
        actors = renderer.GetActors()
        actors.InitTraversal()
        
        while True:
            actor = actors.GetNextActor()
            if not actor:
                break
                
            color = actor.GetProperty().GetColor()
            pos = actor.GetPosition()
            orient = actor.GetOrientation()
            bounds = actor.GetBounds()
            size = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4]) if bounds else 0
            
            actor_info = {'pos': pos, 'orient': orient, 'size': size}
            
            # Classify by color and size
            if color[0] > 0.8 and color[1] < 0.3 and color[2] < 0.3:  # Red
                red_actors.append(actor_info)
            elif color[0] < 0.3 and color[1] > 0.8 and color[2] < 0.3:  # Green
                green_actors.append(actor_info)
            elif color[0] > 0.8 and color[1] > 0.8 and color[2] < 0.3:  # Yellow
                yellow_actors.append(actor_info)
            elif size > 5.0:  # Large actors (likely model geometry)
                model_actors.append(actor_info)
                
        # Print summary
        print(f"🔴 World Origin (Red): {len(red_actors)} actors")
        for i, actor in enumerate(red_actors):
            print(f"   Red {i+1}: pos=({actor['pos'][0]:.3f}, {actor['pos'][1]:.3f}, {actor['pos'][2]:.3f}), orient=({actor['orient'][0]:.1f}°, {actor['orient'][1]:.1f}°, {actor['orient'][2]:.1f}°)")
            
        print(f"🟢 Local Origin (Green): {len(green_actors)} actors")
        for i, actor in enumerate(green_actors):
            print(f"   Green {i+1}: pos=({actor['pos'][0]:.3f}, {actor['pos'][1]:.3f}, {actor['pos'][2]:.3f}), orient=({actor['orient'][0]:.1f}°, {actor['orient'][1]:.1f}°, {actor['orient'][2]:.1f}°)")
            
        print(f"🟡 Local Arrows (Yellow): {len(yellow_actors)} actors")
        for i, actor in enumerate(yellow_actors):
            print(f"   Yellow {i+1}: pos=({actor['pos'][0]:.3f}, {actor['pos'][1]:.3f}, {actor['pos'][2]:.3f}), orient=({actor['orient'][0]:.1f}°, {actor['orient'][1]:.1f}°, {actor['orient'][2]:.1f}°)")
            
        print(f"📦 Model (Geometry): {len(model_actors)} actors")
        for i, actor in enumerate(model_actors):
            print(f"   Model {i+1}: pos=({actor['pos'][0]:.3f}, {actor['pos'][1]:.3f}, {actor['pos'][2]:.3f}), orient=({actor['orient'][0]:.1f}°, {actor['orient'][1]:.1f}°, {actor['orient'][2]:.1f}°)")
            
        return {
            'red_actors': red_actors,
            'green_actors': green_actors, 
            'yellow_actors': yellow_actors,
            'model_actors': model_actors
        }
        
    def run_all_buttons_test(self):
        """Run comprehensive test of all buttons"""
        print("🔍 STARTING COMPREHENSIVE ALL BUTTONS DEBUG TEST")
        print("="*80)
        
        # Show the viewer
        self.viewer.show()
        
        # Wait for GUI to initialize
        QTimer.singleShot(1000, self.load_step_file)
        
        # Start the event loop
        self.app.exec_()
        
    def load_step_file(self):
        """Load a STEP file for testing"""
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            self.app.quit()
            return
            
        print(f"📂 Loading STEP file: {step_file}")
        
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        
        # Load the file
        success = self.viewer.load_step_file_direct(step_file)
        if not success:
            print(f"❌ Failed to load STEP file")
            self.app.quit()
            return
            
        print(f"✅ STEP file loaded successfully")
        
        # Analyze initial state
        QTimer.singleShot(2000, self.analyze_initial_state)
        
    def analyze_initial_state(self):
        """Analyze initial state and start test sequence"""
        self.initial_data = self.analyze_actors("INITIAL STATE")
        
        # Start test sequence
        QTimer.singleShot(1000, self.run_next_test)
        
    def run_next_test(self):
        """Run the next test in the sequence"""
        if self.current_test >= len(self.test_sequence):
            # All tests complete, analyze results
            QTimer.singleShot(1000, self.analyze_all_results)
            return
            
        operation_type, axis, amount, description = self.test_sequence[self.current_test]
        
        print(f"\n🔄 TESTING: {description} ({operation_type} {axis} {amount})")
        print(f"=" * 60)
        
        # Capture before state
        before_data = self.analyze_actors(f"BEFORE {description}")
        
        # Ensure top viewer is active
        self.viewer.active_viewer = "top"
        
        # Perform the operation
        if operation_type == 'rotate':
            self.viewer.rotate_shape(axis, amount)
        elif operation_type == 'move':
            self.viewer.move_shape(axis, amount)
            
        # Capture after state and analyze
        QTimer.singleShot(1000, lambda: self.analyze_test_result(before_data, description, operation_type, axis, amount))
        
    def analyze_test_result(self, before_data, description, operation_type, axis, amount):
        """Analyze the result of a single test"""
        after_data = self.analyze_actors(f"AFTER {description}")
        
        # Analyze the changes
        result = self.check_operation_correctness(before_data, after_data, operation_type, axis, amount)
        self.test_results.append({
            'description': description,
            'operation_type': operation_type,
            'axis': axis,
            'amount': amount,
            'result': result
        })
        
        print(f"\n📊 ANALYSIS: {description}")
        print(f"-" * 40)
        for check, status in result.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {check}")
            
        # Move to next test
        self.current_test += 1
        QTimer.singleShot(1000, self.run_next_test)
        
    def check_operation_correctness(self, before_data, after_data, operation_type, axis, amount):
        """Check if an operation behaved correctly"""
        result = {}
        
        if operation_type == 'move':
            # MOVE BUTTONS: All actors should move
            result['Model moved'] = self.check_actors_moved(before_data['model_actors'], after_data['model_actors'], axis, amount)
            result['Local origin moved'] = self.check_actors_moved(before_data['green_actors'], after_data['green_actors'], axis, amount)
            result['World origin moved'] = self.check_actors_moved(before_data['red_actors'], after_data['red_actors'], axis, amount)
            
        elif operation_type == 'rotate':
            # ROTATE BUTTONS: Model and local origin move, world origin stays at (0,0,0) but rotates
            result['Model rotated'] = self.check_actors_rotated(before_data['model_actors'], after_data['model_actors'])
            result['Local origin moved with model'] = self.check_actors_moved_or_rotated(before_data['green_actors'], after_data['green_actors'])
            result['World origin stayed at (0,0,0)'] = self.check_world_origin_stayed_at_origin(after_data['red_actors'])
            result['World origin arrows rotated'] = self.check_actors_rotated(before_data['red_actors'], after_data['red_actors'])
            
        return result
        
    def check_actors_moved(self, before_actors, after_actors, axis, expected_amount):
        """Check if actors moved the expected amount along the specified axis"""
        if not before_actors or not after_actors or len(before_actors) != len(after_actors):
            return False
            
        axis_index = {'x': 0, 'y': 1, 'z': 2}[axis]
        
        for before, after in zip(before_actors, after_actors):
            movement = after['pos'][axis_index] - before['pos'][axis_index]
            if abs(movement - expected_amount) > 0.1:  # Allow small tolerance
                return False
        return True
        
    def check_actors_rotated(self, before_actors, after_actors):
        """Check if actors rotated (orientation changed)"""
        if not before_actors or not after_actors or len(before_actors) != len(after_actors):
            return False
            
        for before, after in zip(before_actors, after_actors):
            orient_change = sum(abs(after['orient'][i] - before['orient'][i]) for i in range(3))
            if orient_change < 1.0:  # Must have some rotation
                return False
        return True
        
    def check_actors_moved_or_rotated(self, before_actors, after_actors):
        """Check if actors either moved or rotated (for local origin during rotation)"""
        if not before_actors or not after_actors or len(before_actors) != len(after_actors):
            return False
            
        for before, after in zip(before_actors, after_actors):
            pos_change = sum(abs(after['pos'][i] - before['pos'][i]) for i in range(3))
            orient_change = sum(abs(after['orient'][i] - before['orient'][i]) for i in range(3))
            if pos_change < 0.1 and orient_change < 1.0:  # Must have some change
                return False
        return True
        
    def check_world_origin_stayed_at_origin(self, red_actors):
        """Check if world origin actors are still at (0,0,0)"""
        if not red_actors:
            return False
            
        for actor in red_actors:
            distance_from_origin = sum(abs(actor['pos'][i]) for i in range(3))
            if distance_from_origin > 0.1:  # Allow small tolerance
                return False
        return True
        
    def analyze_all_results(self):
        """Analyze all test results and provide final summary"""
        print(f"\n🎯 FINAL COMPREHENSIVE ANALYSIS")
        print(f"=" * 80)
        
        all_passed = True
        
        for test in self.test_results:
            print(f"\n📋 {test['description']}:")
            test_passed = all(test['result'].values())
            all_passed = all_passed and test_passed
            
            for check, status in test['result'].items():
                status_icon = "✅" if status else "❌"
                print(f"   {status_icon} {check}")
                
        print(f"\n🏁 OVERALL RESULT:")
        if all_passed:
            print(f"   ✅ ALL TESTS PASSED - All buttons work correctly!")
        else:
            print(f"   ❌ SOME TESTS FAILED - Fixes needed")
            
        # Close the application
        QTimer.singleShot(3000, self.app.quit)

if __name__ == "__main__":
    debugger = AllButtonsDebugger()
    debugger.run_all_buttons_test()
