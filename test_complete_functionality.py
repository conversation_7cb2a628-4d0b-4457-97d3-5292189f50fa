#!/usr/bin/env python3

"""
COMPLETE TEST: Check both rotation functionality and Local Origin display
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK as <PERSON><PERSON><PERSON><PERSON>
from PyQt5.QtWidgets import QApplication
import time

def test_complete_functionality():
    """Test both rotation and Local Origin display functionality"""
    
    print("=" * 80)
    print("COMPLETE FUNCTIONALITY TEST")
    print("=" * 80)
    
    app = QApplication(sys.argv)
    
    try:
        # Create the main viewer
        viewer = StepViewer()
        viewer.show()
        
        # Wait for GUI to initialize
        app.processEvents()
        time.sleep(1)
        
        print("\n1. Loading SOIC-16 footprint...")
        
        # Load the SOIC-16 file
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        if not os.path.exists(step_file):
            print(f"❌ STEP file not found: {step_file}")
            return False
            
        # Set to TOP viewer and load file
        viewer.active_viewer = "top"
        success = viewer.load_step_file_direct(step_file)
        
        if not success:
            print("❌ Failed to load STEP file")
            return False
            
        print("✅ STEP file loaded successfully")
        
        # Wait for loading to complete
        app.processEvents()
        time.sleep(1)
        
        print("\n2. CHECKING INITIAL STATE...")
        
        # Check initial position
        print(f"✅ Initial current_pos_left: {viewer.current_pos_left}")
        
        # Check green sphere
        if hasattr(viewer.vtk_renderer_left, 'part_origin_sphere') and viewer.vtk_renderer_left.part_origin_sphere:
            user_transform = viewer.vtk_renderer_left.part_origin_sphere.GetUserTransform()
            if user_transform:
                matrix = user_transform.GetMatrix()
                pos = [matrix.GetElement(0, 3), matrix.GetElement(1, 3), matrix.GetElement(2, 3)]
                print(f"✅ Green sphere UserTransform position: {pos}")
            else:
                print("❌ Green sphere UserTransform not found")
                
            actor_pos = viewer.vtk_renderer_left.part_origin_sphere.GetPosition()
            print(f"   Green sphere GetPosition(): {actor_pos}")
            
            # Check direction vectors
            if hasattr(viewer.vtk_renderer_left.part_origin_sphere, 'z_direction'):
                z_dir = viewer.vtk_renderer_left.part_origin_sphere.z_direction
                print(f"✅ Z direction stored: {z_dir}")
            else:
                print("❌ Z direction not stored")
                
            if hasattr(viewer.vtk_renderer_left.part_origin_sphere, 'x_direction'):
                x_dir = viewer.vtk_renderer_left.part_origin_sphere.x_direction
                print(f"✅ X direction stored: {x_dir}")
            else:
                print("❌ X direction not stored")
        
        print("\n3. TESTING ROTATION FUNCTIONALITY...")
        
        # Test rotation method
        if hasattr(viewer, 'rotate_shape'):
            print("   Testing rotate_shape method...")
            
            # Get initial position
            initial_pos = dict(viewer.current_pos_left)
            print(f"   Initial position: {initial_pos}")
            
            # Perform rotation
            viewer.rotate_shape('x', 15)
            app.processEvents()
            time.sleep(0.5)
            
            # Check position after rotation
            after_pos = dict(viewer.current_pos_left)
            print(f"   Position after X rotation: {after_pos}")
            
            # Check if position changed
            if initial_pos != after_pos:
                print("✅ Position updated after rotation")
            else:
                print("❌ Position did NOT change after rotation")
        else:
            print("❌ rotate_shape method not found")
        
        print("\n4. TESTING MOUSE INTERACTION...")
        
        # Test mouse interaction handler
        print("   Testing mouse interaction handler...")
        initial_pos = dict(viewer.current_pos_left)
        
        viewer.on_mouse_interaction_left(None, "TestEvent")
        app.processEvents()
        
        after_pos = dict(viewer.current_pos_left)
        print(f"   Position after mouse interaction: {after_pos}")
        
        print("\n5. TESTING LOCAL ORIGIN DISPLAY...")
        
        # Force update of text overlays
        viewer.update_text_overlays()
        
        # Check if Local Origin display shows correct values
        if hasattr(viewer, 'local_origin_text_actor_left'):
            print("✅ Local Origin text actor exists")
        else:
            print("❌ Local Origin text actor not found")
        
        print("\n" + "=" * 80)
        print("COMPLETE FUNCTIONALITY TEST COMPLETE")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_complete_functionality()
