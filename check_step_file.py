#!/usr/bin/env python3
"""
Check the actual STEP file content to see what's wrong
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== CHECK STEP FILE CONTENT ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"Using: {test_file}")
    
    # Load file
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ File loaded")
        
        # Apply rotation
        print("\nApplying 90° X rotation...")
        viewer._apply_model_rotation("top", "x", 90.0)
        
        # Save the file
        loader = viewer.step_loader_left
        current_rot = viewer.current_rot_left
        
        test_save_file = "check_saved.step"
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
        
        # Save using the method
        success = viewer._save_step_with_current_rotation(test_save_file, loader, current_rot)
        print(f"Save result: {success}")
        
        if os.path.exists(test_save_file):
            print(f"\n=== COMPARING FILES ===")
            
            # Read original
            with open(test_file, 'r') as f:
                orig_lines = f.readlines()
            
            # Read saved
            with open(test_save_file, 'r') as f:
                saved_lines = f.readlines()
            
            print(f"Original: {len(orig_lines)} lines")
            print(f"Saved: {len(saved_lines)} lines")
            
            # Find differences
            differences = 0
            for i, (orig, saved) in enumerate(zip(orig_lines, saved_lines)):
                if orig != saved:
                    differences += 1
                    if differences <= 5:  # Show first 5 differences
                        print(f"\nDifference at line {i+1}:")
                        print(f"  Original: {orig.strip()}")
                        print(f"  Saved:    {saved.strip()}")
            
            print(f"\nTotal differences: {differences}")
            
            if differences == 0:
                print("❌ PROBLEM: Files are IDENTICAL - no changes made!")
            else:
                print("✅ Files are different")
                
                # Load the saved file to test
                print("\n=== TESTING SAVED FILE ===")
                viewer.active_viewer = "bottom"
                load_success = viewer.load_step_file_direct(test_save_file)
                
                if load_success:
                    print("✅ Saved file loads")
                    
                    # Check if geometry is rotated
                    top_renderer = viewer.vtk_renderer_left
                    bottom_renderer = viewer.vtk_renderer_right
                    
                    if (hasattr(top_renderer, 'step_actors') and top_renderer.step_actors and
                        hasattr(bottom_renderer, 'step_actors') and bottom_renderer.step_actors):
                        
                        top_bounds = top_renderer.step_actors[0].GetBounds()
                        bottom_bounds = bottom_renderer.step_actors[0].GetBounds()
                        
                        print(f"Top bounds:    {top_bounds}")
                        print(f"Bottom bounds: {bottom_bounds}")
                        
                        bounds_different = any(abs(a - b) > 0.1 for a, b in zip(top_bounds, bottom_bounds))
                        print(f"Geometry different: {bounds_different}")
                        
                        if not bounds_different:
                            print("❌ PROBLEM: Geometry is NOT rotated in saved file!")
                        else:
                            print("✅ Geometry is rotated")
                else:
                    print("❌ Saved file won't load")
            
            os.remove(test_save_file)
        else:
            print("❌ No file created")
    else:
        print("❌ Failed to load file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== CHECK FINISHED ===")
