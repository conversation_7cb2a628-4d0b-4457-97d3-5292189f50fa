#!/usr/bin/env python3
"""
Test to see what the transformation matrix contains
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication
import numpy as np

print("=== TRANSFORMATION MATRIX DEBUG TEST ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"🔧 Testing with: {test_file}")
    
    print("\n=== STEP 1: LOAD STEP FILE ===")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ STEP file loaded")
        print(f"   Initial rotation: {viewer.current_rot_left}")
        
        print("\n=== STEP 2: APPLY ROTATION ===")
        print("🔄 Applying X+15° rotation")
        viewer._apply_model_rotation("top", "x", 15.0)
        print(f"   After rotation: {viewer.current_rot_left}")
        
        print("\n=== STEP 3: GET VTK TRANSFORMATION MATRIX ===")
        
        # Get the VTK renderer and actors
        vtk_renderer = viewer.vtk_renderer_left
        
        if hasattr(vtk_renderer, 'step_actors') and vtk_renderer.step_actors:
            first_actor = vtk_renderer.step_actors[0]
            print(f"✅ Found multi-actor model with {len(vtk_renderer.step_actors)} actors")
            
            # Get actor transformation info
            position = first_actor.GetPosition()
            orientation = first_actor.GetOrientation()
            user_transform = first_actor.GetUserTransform()
            
            print(f"   Actor position: {position}")
            print(f"   Actor orientation: {orientation}")
            print(f"   Actor has UserTransform: {user_transform is not None}")
            
            # Create the combined transform like the save method does
            import vtk
            combined_transform = vtk.vtkTransform()
            combined_transform.Identity()
            
            if user_transform:
                combined_transform.DeepCopy(user_transform)
                print("   Using UserTransform")
            
            # Add position and orientation
            if position != (0, 0, 0):
                combined_transform.Translate(*position)
                print(f"   Added translation: {position}")
            if orientation != (0, 0, 0):
                combined_transform.RotateX(orientation[0])
                combined_transform.RotateY(orientation[1])
                combined_transform.RotateZ(orientation[2])
                print(f"   Added rotation: {orientation}")
            
            # Get the 4x4 matrix
            matrix_4x4 = vtk.vtkMatrix4x4()
            combined_transform.GetMatrix(matrix_4x4)
            
            # Convert to numpy
            transform_matrix = np.eye(4)
            for i in range(4):
                for j in range(4):
                    transform_matrix[i, j] = matrix_4x4.GetElement(i, j)
            
            print(f"\n=== VTK TRANSFORMATION MATRIX ===")
            print("Matrix (4x4):")
            for i in range(4):
                row = [f"{transform_matrix[i,j]:8.4f}" for j in range(4)]
                print(f"   [{' '.join(row)}]")
            
            print(f"\nMatrix components:")
            print(f"   Translation: ({transform_matrix[0,3]:.4f}, {transform_matrix[1,3]:.4f}, {transform_matrix[2,3]:.4f})")
            print(f"   Rotation matrix (3x3):")
            for i in range(3):
                row = [f"{transform_matrix[i,j]:8.4f}" for j in range(3)]
                print(f"     [{' '.join(row)}]")
            
            # Check if this is an identity matrix
            identity_check = np.allclose(transform_matrix, np.eye(4), atol=1e-6)
            print(f"   Is identity matrix: {identity_check}")
            
            if identity_check:
                print("❌ PROBLEM: Transformation matrix is identity - no transformation!")
                print("   This explains why the rotation is not preserved.")
                print("   The VTK actors are not storing the transformation in the matrix.")
            else:
                print("✅ Matrix contains transformation data")
                
                # Try to extract rotation angles from the rotation matrix
                # This is a simplified extraction - real rotation extraction is more complex
                import math
                
                # Extract Euler angles (simplified)
                r11, r12, r13 = transform_matrix[0,0], transform_matrix[0,1], transform_matrix[0,2]
                r21, r22, r23 = transform_matrix[1,0], transform_matrix[1,1], transform_matrix[1,2]
                r31, r32, r33 = transform_matrix[2,0], transform_matrix[2,1], transform_matrix[2,2]
                
                # Simple extraction (may not be accurate for all cases)
                if abs(r31) < 0.99999:
                    y_rot = math.asin(-r31)
                    x_rot = math.atan2(r32, r33)
                    z_rot = math.atan2(r21, r11)
                else:
                    # Gimbal lock case
                    y_rot = -math.pi/2 if r31 < 0 else math.pi/2
                    x_rot = math.atan2(-r12, r22)
                    z_rot = 0
                
                x_deg = math.degrees(x_rot)
                y_deg = math.degrees(y_rot)
                z_deg = math.degrees(z_rot)
                
                print(f"   Extracted rotation angles:")
                print(f"     X: {x_deg:.1f}°")
                print(f"     Y: {y_deg:.1f}°")
                print(f"     Z: {z_deg:.1f}°")
                
                expected_x = viewer.current_rot_left['x']
                print(f"   Expected X rotation: {expected_x:.1f}°")
                print(f"   Matrix X rotation:   {x_deg:.1f}°")
                print(f"   Difference:          {abs(expected_x - x_deg):.1f}°")
        
        else:
            print("❌ No step_actors found")
            
    else:
        print("❌ Failed to load STEP file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== TRANSFORMATION MATRIX DEBUG TEST FINISHED ===")
