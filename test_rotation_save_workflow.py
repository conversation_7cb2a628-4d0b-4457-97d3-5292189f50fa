#!/usr/bin/env python3
"""
Complete test of rotation and save workflow
This will load a STEP file, apply rotations, and test save functionality
"""

import sys
import os
import time
sys.path.append('.')

def test_complete_workflow():
    """Test the complete rotation and save workflow"""
    print("=" * 60)
    print("TESTING COMPLETE ROTATION AND SAVE WORKFLOW")
    print("=" * 60)
    
    try:
        from step_viewer import Step<PERSON>iewerTDK
        from PyQt5.QtWidgets import QApplication
        
        # Create app
        app = QApplication(sys.argv)
        viewer = StepViewerTDK()
        
        # Find a STEP file to test with
        step_files = [f for f in os.listdir('.') if f.endswith('.step')]
        if not step_files:
            print("❌ No STEP files found for testing")
            return False
            
        test_file = step_files[0]
        print(f"📁 Using test file: {test_file}")
        
        # Test 1: Load STEP file properly
        print("\n1. LOADING STEP FILE:")
        try:
            # Simulate proper file loading (like clicking "Open STEP File" button)
            if hasattr(viewer, 'step_loader_left') and viewer.step_loader_left:
                print(f"   Loading {test_file} into TOP viewer...")
                success = viewer.step_loader_left.load_step_file(test_file)
                if success:
                    print(f"   ✅ STEP file loaded successfully")
                    print(f"   📊 Polydata: {viewer.step_loader_left.current_polydata}")
                    
                    # Display the loaded geometry
                    if hasattr(viewer, 'vtk_renderer_left') and viewer.vtk_renderer_left:
                        viewer.vtk_renderer_left.display_polydata(viewer.step_loader_left.current_polydata)
                        print(f"   ✅ Geometry displayed in TOP viewer")
                else:
                    print(f"   ❌ Failed to load STEP file")
                    return False
            else:
                print(f"   ❌ No STEP loader available")
                return False
                
        except Exception as e:
            print(f"   ❌ Error loading STEP file: {e}")
            return False
        
        # Test 2: Check VTK actors after loading
        print("\n2. CHECKING VTK ACTORS:")
        if hasattr(viewer.vtk_renderer_left, 'renderer') and viewer.vtk_renderer_left.renderer:
            actors = viewer.vtk_renderer_left.renderer.GetActors()
            actor_count = actors.GetNumberOfItems()
            print(f"   📊 Total actors in TOP renderer: {actor_count}")
            
            if actor_count > 0:
                print(f"   ✅ VTK actors exist - rotation should work")
            else:
                print(f"   ❌ No VTK actors - rotation won't work")
                return False
        else:
            print(f"   ❌ No renderer available")
            return False
            
        # Test 3: Apply button rotation
        print("\n3. TESTING BUTTON ROTATION:")
        initial_rot = viewer.current_rot_left.copy()
        print(f"   Initial rotation: {initial_rot}")
        
        viewer.active_viewer = "top"
        viewer._apply_unified_rotation('x', 15.0)
        
        after_rot = viewer.current_rot_left.copy()
        print(f"   After X+15°: {after_rot}")
        
        if after_rot['x'] != initial_rot['x']:
            print(f"   ✅ Button rotation tracking works")
        else:
            print(f"   ❌ Button rotation tracking failed")
            
        # Test 4: Test rotation extraction
        print("\n4. TESTING ROTATION EXTRACTION:")
        extracted = viewer._extract_rotation_from_vtk_actor("top")
        print(f"   Extracted rotation: {extracted}")
        
        if extracted['x'] == after_rot['x']:
            print(f"   ✅ Rotation extraction works")
        else:
            print(f"   ❌ Rotation extraction mismatch")
            
        # Test 5: Test save threshold
        print("\n5. TESTING SAVE THRESHOLD:")
        orig_rot = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        delta_rot = {
            'x': after_rot['x'] - orig_rot['x'],
            'y': after_rot['y'] - orig_rot['y'],
            'z': after_rot['z'] - orig_rot['z']
        }
        
        rot_changed = (abs(delta_rot['x']) > 0.1 or abs(delta_rot['y']) > 0.1 or abs(delta_rot['z']) > 0.1)
        print(f"   Delta rotation: {delta_rot}")
        print(f"   Should save (>0.1°): {rot_changed}")
        
        if rot_changed:
            print(f"   ✅ Save threshold logic works")
        else:
            print(f"   ❌ Save threshold too high")
            
        # Test 6: Test save functionality (simulation)
        print("\n6. TESTING SAVE FUNCTIONALITY:")
        try:
            # Test the save method components
            current_pos = viewer._extract_position_from_display("top")
            current_rot = viewer._extract_rotation_from_vtk_actor("top")
            orig_pos = getattr(viewer, 'orig_pos_left', {'x': 0, 'y': 0, 'z': 0})
            orig_rot = getattr(viewer, 'orig_rot_left', {'x': 0, 'y': 0, 'z': 0})
            
            print(f"   Current position: {current_pos}")
            print(f"   Current rotation: {current_rot}")
            print(f"   Original position: {orig_pos}")
            print(f"   Original rotation: {orig_rot}")
            
            # Calculate deltas
            delta_pos = {
                'x': current_pos['x'] - orig_pos['x'],
                'y': current_pos['y'] - orig_pos['y'],
                'z': current_pos['z'] - orig_pos['z']
            }
            delta_rot = {
                'x': current_rot['x'] - orig_rot['x'],
                'y': current_rot['y'] - orig_rot['y'],
                'z': current_rot['z'] - orig_rot['z']
            }
            
            pos_changed = (abs(delta_pos['x']) > 0.1 or abs(delta_pos['y']) > 0.1 or abs(delta_pos['z']) > 0.1)
            rot_changed = (abs(delta_rot['x']) > 0.1 or abs(delta_rot['y']) > 0.1 or abs(delta_rot['z']) > 0.1)
            
            print(f"   Position changed: {pos_changed}")
            print(f"   Rotation changed: {rot_changed}")
            
            if rot_changed:
                print(f"   ✅ Save would apply transformations")
            else:
                print(f"   ❌ Save would skip transformations")
                
        except Exception as e:
            print(f"   ❌ Save functionality test failed: {e}")
            
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("STEP VIEWER COMPLETE WORKFLOW TEST")
    print("=" * 60)
    
    success = test_complete_workflow()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ WORKFLOW TEST COMPLETED - Check results above")
    else:
        print("❌ WORKFLOW TEST FAILED - Check errors above")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    main()
