#!/usr/bin/env python3
"""
DEBUG SPECIFIC ISSUES FOUND BY USER:
1. X+ moves bounding box MORE steps than model/origin markers
2. Green arrows (X+, X-, Y, Z) do not move with model
3. World origin arrows do not rotate during rotations
4. Multiple operations (X+ then Y-) mess everything up
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK
import time

def debug_specific_issues():
    """Debug the specific issues found by user"""
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Load a STEP file
    step_file = "test.step"
    if os.path.exists(step_file):
        print(f"🔄 Loading STEP file: {step_file}")
        viewer.load_step_file_direct(step_file)
        time.sleep(2)
    else:
        print(f"❌ STEP file not found: {step_file}")
        return
    
    print("\n" + "="*80)
    print("🔍 DEBUGGING SPECIFIC ISSUES")
    print("="*80)
    
    # Issue 1: Check bounding box vs model movement amounts
    print("\n🔍 ISSUE 1: Checking bounding box vs model movement amounts")
    print("-" * 60)
    
    # Get initial positions
    renderer = viewer.vtk_renderer_left
    bbox_initial = None
    model_initial = []
    
    if hasattr(renderer, 'bounding_box_actor') and renderer.bounding_box_actor:
        bbox_initial = renderer.bounding_box_actor.GetPosition()
        print(f"📊 Bounding box initial: {bbox_initial}")
    
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        for i, actor in enumerate(renderer.step_actors[:2]):  # Check first 2 model actors
            pos = actor.GetPosition()
            model_initial.append(pos)
            print(f"📊 Model actor {i+1} initial: {pos}")
    
    # Perform X+ movement
    print(f"\n🔄 Performing X+ movement...")
    viewer.move_shape('x', 2.0)
    time.sleep(1)
    
    # Check positions after movement
    if bbox_initial and hasattr(renderer, 'bounding_box_actor') and renderer.bounding_box_actor:
        bbox_after = renderer.bounding_box_actor.GetPosition()
        bbox_movement = bbox_after[0] - bbox_initial[0]
        print(f"📊 Bounding box after: {bbox_after}")
        print(f"🔍 Bounding box X movement: {bbox_movement:.3f}mm")
    
    if model_initial and hasattr(renderer, 'step_actors') and renderer.step_actors:
        for i, actor in enumerate(renderer.step_actors[:2]):
            pos_after = actor.GetPosition()
            movement = pos_after[0] - model_initial[i][0]
            print(f"📊 Model actor {i+1} after: {pos_after}")
            print(f"🔍 Model actor {i+1} X movement: {movement:.3f}mm")
    
    # Issue 2: Check green arrows movement
    print("\n🔍 ISSUE 2: Checking green arrows movement")
    print("-" * 60)
    
    green_arrows_initial = {}
    green_arrows = ['part_origin_x_arrow', 'part_origin_y_arrow', 'part_origin_z_arrow']
    
    for arrow_name in green_arrows:
        if hasattr(renderer, arrow_name):
            arrow = getattr(renderer, arrow_name)
            if arrow:
                pos = arrow.GetPosition()
                green_arrows_initial[arrow_name] = pos
                print(f"📊 {arrow_name} initial: {pos}")
    
    # Perform another X+ movement
    print(f"\n🔄 Performing another X+ movement...")
    viewer.move_shape('x', 2.0)
    time.sleep(1)
    
    # Check green arrows after movement
    for arrow_name in green_arrows:
        if hasattr(renderer, arrow_name):
            arrow = getattr(renderer, arrow_name)
            if arrow and arrow_name in green_arrows_initial:
                pos_after = arrow.GetPosition()
                initial_pos = green_arrows_initial[arrow_name]
                movement = pos_after[0] - initial_pos[0]
                print(f"📊 {arrow_name} after: {pos_after}")
                print(f"🔍 {arrow_name} X movement: {movement:.3f}mm")
                
                if abs(movement) < 0.1:
                    print(f"❌ {arrow_name} DID NOT MOVE!")
                else:
                    print(f"✅ {arrow_name} moved correctly")
    
    # Issue 3: Check world origin arrows rotation
    print("\n🔍 ISSUE 3: Checking world origin arrows rotation")
    print("-" * 60)
    
    world_arrows = ['origin_x_arrow', 'origin_y_arrow', 'origin_z_arrow']
    world_arrows_initial = {}
    
    for arrow_name in world_arrows:
        if hasattr(renderer, arrow_name):
            arrow = getattr(renderer, arrow_name)
            if arrow:
                orientation = arrow.GetOrientation()
                world_arrows_initial[arrow_name] = orientation
                print(f"📊 {arrow_name} initial orientation: {orientation}")
    
    # Perform X+ rotation
    print(f"\n🔄 Performing X+ rotation...")
    viewer.rotate_shape('x', 15.0)
    time.sleep(1)
    
    # Check world origin arrows after rotation
    for arrow_name in world_arrows:
        if hasattr(renderer, arrow_name):
            arrow = getattr(renderer, arrow_name)
            if arrow and arrow_name in world_arrows_initial:
                orientation_after = arrow.GetOrientation()
                initial_orientation = world_arrows_initial[arrow_name]
                rotation_change = [orientation_after[i] - initial_orientation[i] for i in range(3)]
                print(f"📊 {arrow_name} after rotation: {orientation_after}")
                print(f"🔍 {arrow_name} rotation change: {rotation_change}")
                
                if all(abs(change) < 0.1 for change in rotation_change):
                    print(f"❌ {arrow_name} DID NOT ROTATE!")
                else:
                    print(f"✅ {arrow_name} rotated correctly")
    
    # Issue 4: Test cumulative operations
    print("\n🔍 ISSUE 4: Testing cumulative operations (X+ then Y-)")
    print("-" * 60)
    
    # Get positions before cumulative operations
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        model_before_cumulative = renderer.step_actors[0].GetPosition()
        print(f"📊 Model position before cumulative: {model_before_cumulative}")
    
    # Perform X+ then Y- operations
    print(f"🔄 Performing X+ movement...")
    viewer.move_shape('x', 2.0)
    time.sleep(0.5)
    
    print(f"🔄 Performing Y- movement...")
    viewer.move_shape('y', -2.0)
    time.sleep(0.5)
    
    print(f"🔄 Performing another X+ movement...")
    viewer.move_shape('x', 2.0)
    time.sleep(0.5)
    
    print(f"🔄 Performing another Y- movement...")
    viewer.move_shape('y', -2.0)
    time.sleep(1)
    
    # Check final positions
    if hasattr(renderer, 'step_actors') and renderer.step_actors:
        model_after_cumulative = renderer.step_actors[0].GetPosition()
        print(f"📊 Model position after cumulative: {model_after_cumulative}")
        
        expected_x = model_before_cumulative[0] + 4.0  # Two X+ movements
        expected_y = model_before_cumulative[1] - 4.0  # Two Y- movements
        expected_z = model_before_cumulative[2]        # No Z movement
        
        print(f"🔍 Expected position: ({expected_x:.1f}, {expected_y:.1f}, {expected_z:.1f})")
        print(f"🔍 Actual position: ({model_after_cumulative[0]:.1f}, {model_after_cumulative[1]:.1f}, {model_after_cumulative[2]:.1f})")
        
        x_error = abs(model_after_cumulative[0] - expected_x)
        y_error = abs(model_after_cumulative[1] - expected_y)
        z_error = abs(model_after_cumulative[2] - expected_z)
        
        if x_error > 0.5 or y_error > 0.5 or z_error > 0.5:
            print(f"❌ CUMULATIVE OPERATIONS FAILED! Errors: X={x_error:.1f}, Y={y_error:.1f}, Z={z_error:.1f}")
        else:
            print(f"✅ Cumulative operations working correctly")
    
    print("\n" + "="*80)
    print("🎯 SPECIFIC ISSUES DEBUG COMPLETE")
    print("="*80)
    
    # Exit cleanly
    app.quit()

if __name__ == "__main__":
    debug_specific_issues()
