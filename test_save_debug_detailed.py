#!/usr/bin/env python3
"""
Detailed debug test to see what happens during the save process
"""

import sys
import os
sys.path.append('.')

from step_viewer import StepViewerTDK
from PyQt5.QtWidgets import QApplication

print("=== DETAILED SAVE DEBUG TEST ===")

app = QApplication(sys.argv)
viewer = StepViewerTDK()

# Find STEP file
step_files = [f for f in os.listdir('.') if f.endswith('.step')]
if step_files:
    test_file = step_files[0]
    print(f"🔧 Testing with: {test_file}")
    
    print("\n=== STEP 1: LOAD STEP FILE ===")
    viewer.active_viewer = "top"
    success = viewer.load_step_file_direct(test_file)
    
    if success:
        print("✅ STEP file loaded")
        print(f"   Initial rotation: {viewer.current_rot_left}")
        
        print("\n=== STEP 2: APPLY ROTATION ===")
        print("🔄 Applying X+15° rotation")
        viewer._apply_model_rotation("top", "x", 15.0)
        print(f"   After rotation: {viewer.current_rot_left}")
        
        print("\n=== STEP 3: DETAILED SAVE DEBUG ===")
        test_save_file = "test_detailed_save_debug.step"
        
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
            
        print(f"🔧 Saving to: {test_save_file}")
        
        # Get save parameters
        current_pos = viewer._extract_position_from_display("top")
        current_rot = viewer._extract_rotation_from_vtk_actor("top")
        orig_pos = viewer.orig_pos_left
        orig_rot = viewer.orig_rot_left
        
        print(f"   Save parameters:")
        print(f"     current_pos: {current_pos}")
        print(f"     current_rot: {current_rot}")
        print(f"     orig_pos: {orig_pos}")
        print(f"     orig_rot: {orig_rot}")
        
        # Calculate deltas
        delta_rot = {
            'x': current_rot['x'] - orig_rot['x'],
            'y': current_rot['y'] - orig_rot['y'],
            'z': current_rot['z'] - orig_rot['z']
        }
        print(f"     delta_rot: {delta_rot}")
        
        # Check if rotation threshold is met
        rot_changed = (abs(delta_rot['x']) > 0.1 or abs(delta_rot['y']) > 0.1 or abs(delta_rot['z']) > 0.1)
        print(f"     rotation_changed: {rot_changed}")
        
        # Call save method with detailed output capture
        print("\n=== CALLING SAVE METHOD ===")
        loader = viewer.step_loader_left

        # Call save method directly without capturing output so we can see debug messages
        try:
            save_success = viewer._save_step_with_transformations(
                test_save_file, loader, current_pos, current_rot, orig_pos, orig_rot
            )

            debug_output = "Save method completed"  # Placeholder since we're not capturing
            
            if save_success and os.path.exists(test_save_file):
                print(f"✅ Save completed successfully")
                file_size = os.path.getsize(test_save_file)
                print(f"   File size: {file_size:,} bytes")
                
                print("\n=== STEP 4: LOAD AND VERIFY ===")
                viewer.active_viewer = "bottom"
                load_success = viewer.load_step_file_direct(test_save_file)
                
                if load_success:
                    print("✅ Saved file loaded successfully")
                    print(f"   Loaded rotation: {viewer.current_rot_right}")
                    
                    # Compare rotations
                    expected_x = current_rot['x']
                    actual_x = viewer.current_rot_right['x']
                    x_diff = abs(expected_x - actual_x)
                    
                    print(f"\n=== ROTATION COMPARISON ===")
                    print(f"   Expected X rotation: {expected_x:.1f}°")
                    print(f"   Actual X rotation:   {actual_x:.1f}°")
                    print(f"   Difference:          {x_diff:.1f}°")
                    
                    if x_diff < 1.0:
                        print("🎉 SUCCESS: Rotation preserved correctly!")
                    else:
                        print("❌ FAILURE: Rotation not preserved")
                        
                        # Check if the transformation was actually applied in OpenCASCADE
                        print("\n=== CHECKING OPENCASCADE TRANSFORMATION ===")
                        if "Successfully applied transformation to OpenCASCADE shape" in debug_output:
                            print("✅ OpenCASCADE transformation was applied")
                        elif "Failed to apply transformation to shape" in debug_output:
                            print("❌ OpenCASCADE transformation failed")
                        elif "Error applying transformation to shape" in debug_output:
                            print("❌ OpenCASCADE transformation had errors")
                        else:
                            print("❓ OpenCASCADE transformation status unclear")
                else:
                    print("❌ Failed to load saved file")
            else:
                print("❌ Save failed")
                
        except Exception as e:
            print(f"❌ Exception during save: {e}")
            import traceback
            traceback.print_exc()
        
        # Clean up
        if os.path.exists(test_save_file):
            os.remove(test_save_file)
            
    else:
        print("❌ Failed to load STEP file")
else:
    print("❌ No STEP files found")

app.quit()
print("=== DETAILED SAVE DEBUG TEST FINISHED ===")
