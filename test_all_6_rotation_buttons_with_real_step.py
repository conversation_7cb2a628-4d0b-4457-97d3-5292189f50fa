#!/usr/bin/env python3
"""
COMPREHENSIVE TEST: ALL 6 ROTATION BUTTONS WITH REAL STEP FILE
Tests X+, X-, Y+, Y-, Z+, Z- rotation buttons with actual STEP file loaded
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from step_viewer import StepViewerTDK
import time

def check_world_origin_positions(viewer, test_name):
    """Check if all red actors (world origin) are at (0,0,0)"""
    print(f"\n🔴 {test_name} - World Origin Check:")
    
    renderer = viewer.vtk_renderer_left
    if not hasattr(renderer, 'renderer'):
        print("   ❌ No renderer found")
        return False
        
    red_actors_found = 0
    all_at_origin = True
    
    actor_collection = renderer.renderer.GetActors()
    if actor_collection:
        actor_collection.InitTraversal()
        actor = actor_collection.GetNextActor()
        while actor:
            color = actor.GetProperty().GetColor()
            # Check if this is a red actor (world origin marker)
            if color[0] > 0.8 and color[1] < 0.3 and color[2] < 0.3:
                pos = actor.GetPosition()
                red_actors_found += 1
                if abs(pos[0]) < 0.001 and abs(pos[1]) < 0.001 and abs(pos[2]) < 0.001:
                    print(f"   ✅ Red {red_actors_found}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) - AT ORIGIN")
                else:
                    print(f"   ❌ Red {red_actors_found}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) - NOT AT ORIGIN")
                    all_at_origin = False
            actor = actor_collection.GetNextActor()
    
    if red_actors_found == 0:
        print("   ❌ No red actors found!")
        return False
    elif all_at_origin:
        print(f"   ✅ {test_name}: World origin correctly stayed at (0,0,0)")
        return True
    else:
        print(f"   ❌ {test_name}: World origin moved away from (0,0,0) - FIX FAILED!")
        return False

def check_local_origin_moved(viewer, test_name):
    """Check if green actors (local origin) moved with model"""
    print(f"\n🟢 {test_name} - Local Origin Check:")
    
    renderer = viewer.vtk_renderer_left
    if not hasattr(renderer, 'renderer'):
        print("   ❌ No renderer found")
        return False
        
    green_actors_found = 0
    any_moved = False
    
    actor_collection = renderer.renderer.GetActors()
    if actor_collection:
        actor_collection.InitTraversal()
        actor = actor_collection.GetNextActor()
        while actor:
            color = actor.GetProperty().GetColor()
            # Check if this is a green actor (local origin marker)
            if color[0] < 0.3 and color[1] > 0.8 and color[2] < 0.3:
                pos = actor.GetPosition()
                green_actors_found += 1
                if abs(pos[0]) > 0.001 or abs(pos[1]) > 0.001 or abs(pos[2]) > 0.001:
                    print(f"   ✅ Green {green_actors_found}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) - MOVED WITH MODEL")
                    any_moved = True
                else:
                    print(f"   ❌ Green {green_actors_found}: pos=({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) - STAYED AT ORIGIN")
            actor = actor_collection.GetNextActor()
    
    if green_actors_found == 0:
        print("   ❌ No green actors found!")
        return False
    elif any_moved:
        print(f"   ✅ {test_name}: Local origin correctly moved with model")
        return True
    else:
        print(f"   ❌ {test_name}: Local origin did not move with model - FIX FAILED!")
        return False

def test_all_6_rotation_buttons_with_real_step():
    """Test all 6 rotation buttons with real STEP file loaded"""
    app = QApplication(sys.argv)
    viewer = StepViewerTDK()
    viewer.show()
    
    # Find and load a real STEP file
    step_files = [
        "SOIC16P127_1270X940X610L89X51.STEP",
        "test.step",
        "test1.step",
        "test2.step"
    ]
    
    step_file_loaded = None
    for step_file in step_files:
        if os.path.exists(step_file):
            print(f"📁 Loading STEP file: {step_file}")
            viewer.load_step_file_direct(step_file)
            step_file_loaded = step_file
            time.sleep(3)  # Allow loading to complete
            print("✅ STEP file loaded successfully")
            break
    
    if not step_file_loaded:
        print("❌ No STEP file found - cannot test rotation buttons")
        app.quit()
        return
    
    print("\n" + "="*80)
    print("🎯 TESTING ALL 6 ROTATION BUTTONS WITH REAL STEP FILE")
    print(f"📁 Using: {step_file_loaded}")
    print("="*80)
    
    # Test all 6 rotation buttons
    rotation_tests = [
        ("X+ Rotation", lambda: viewer.rotate_shape('x', 15.0)),
        ("X- Rotation", lambda: viewer.rotate_shape('x', -15.0)),
        ("Y+ Rotation", lambda: viewer.rotate_shape('y', 15.0)),
        ("Y- Rotation", lambda: viewer.rotate_shape('y', -15.0)),
        ("Z+ Rotation", lambda: viewer.rotate_shape('z', 15.0)),
        ("Z- Rotation", lambda: viewer.rotate_shape('z', -15.0)),
    ]
    
    results = []
    
    for test_name, rotation_func in rotation_tests:
        print(f"\n🔄 PERFORMING: {test_name}")
        
        # Perform rotation
        print(f"🔄 EXECUTING: {test_name}")
        rotation_func()
        time.sleep(1.5)  # Allow rotation to complete
        
        # Check world origin (should stay at 0,0,0 but arrows should rotate)
        world_result = check_world_origin_positions(viewer, f"AFTER {test_name}")
        
        # Check local origin (should move with model during rotation)
        local_result = check_local_origin_moved(viewer, f"AFTER {test_name}")
        
        # Overall result for this rotation
        overall_result = world_result and local_result
        results.append((test_name, overall_result, world_result, local_result))
        
        if overall_result:
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
            if not world_result:
                print(f"   ❌ World origin issue")
            if not local_result:
                print(f"   ❌ Local origin issue")
    
    # Final summary
    print("\n" + "="*80)
    print("🎯 FINAL RESULTS - ALL 6 ROTATION BUTTONS")
    print("="*80)
    
    passed = 0
    failed = 0
    
    for test_name, overall_result, world_result, local_result in results:
        if overall_result:
            print(f"   ✅ PASSED: {test_name}")
            passed += 1
        else:
            print(f"   ❌ FAILED: {test_name}")
            if not world_result:
                print(f"      ❌ World origin not at (0,0,0)")
            if not local_result:
                print(f"      ❌ Local origin not moving with model")
            failed += 1
    
    print(f"\n📊 SUMMARY: {passed} PASSED, {failed} FAILED out of 6 rotation buttons")
    
    if failed == 0:
        print("\n🎉 ALL 6 ROTATION BUTTONS WORKING CORRECTLY!")
        print("   ✅ World origin stays at (0,0,0) during ALL rotations")
        print("   ✅ Local origin moves with model during ALL rotations")
    else:
        print(f"\n❌ {failed} ROTATION BUTTONS FAILED!")
        print("   🔧 FIXES NEEDED!")
    
    print("="*80)
    
    # Keep app running briefly to see results
    QTimer.singleShot(5000, app.quit)
    app.exec_()

if __name__ == "__main__":
    test_all_6_rotation_buttons_with_real_step()
