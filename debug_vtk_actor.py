#!/usr/bin/env python3

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from step_viewer import StepViewerTDK

class VTKActorDebugger:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        
    def start_debug(self):
        print("🔧 Starting VTK Actor Debug Test...")
        
        # Create viewer
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Wait for viewer to initialize, then load file and test
        QTimer.singleShot(1000, self.load_and_test)
        
        # Start the application
        self.app.exec_()
        
    def load_and_test(self):
        print("🔧 Loading SOIC file...")
        
        # Try different STEP file names
        step_files = ["SOIC.step", "soic.step", "SOIC.STEP", "test.step", "Test.step"]
        soic_file = None

        for filename in step_files:
            if os.path.exists(filename):
                soic_file = filename
                break

        if soic_file:
            try:
                success = self.viewer.load_step_file_direct(soic_file)
                if success:
                    print(f"✅ STEP file loaded: {soic_file}")
                else:
                    print(f"❌ Failed to load STEP file: {soic_file}")
                    self.exit_app()
                    return
                
                # Wait for file to load, then test rotation
                QTimer.singleShot(2000, self.test_rotation)
                
            except Exception as e:
                print(f"❌ Error loading SOIC file: {e}")
                self.exit_app()
        else:
            print(f"❌ No STEP files found. Tried: {step_files}")
            self.exit_app()
    
    def test_rotation(self):
        print("🔧 Testing VTK actor position during rotation...")
        
        try:
            # Get initial VTK actor position
            print("\n📊 BEFORE ROTATION:")
            self.debug_vtk_actor_position()
            
            # Apply a small rotation
            print("\n🔄 Applying 15° X rotation...")
            self.viewer._apply_model_rotation("top", "x", 15.0)
            
            # Get VTK actor position after rotation
            print("\n📊 AFTER ROTATION:")
            self.debug_vtk_actor_position()
            
            # Apply another rotation
            print("\n🔄 Applying 25° Y rotation...")
            self.viewer._apply_model_rotation("top", "y", 25.0)
            
            # Get VTK actor position after second rotation
            print("\n📊 AFTER SECOND ROTATION:")
            self.debug_vtk_actor_position()
            
        except Exception as e:
            print(f"❌ Error during rotation test: {e}")
        
        # Exit after test
        QTimer.singleShot(1000, self.exit_app)
    
    def debug_vtk_actor_position(self):
        """Debug the VTK actor position"""
        try:
            renderer = self.viewer.vtk_renderer_left
            
            if renderer and hasattr(renderer, 'part_origin_sphere') and renderer.part_origin_sphere:
                pos = renderer.part_origin_sphere.GetPosition()
                orient = renderer.part_origin_sphere.GetOrientation()
                print(f"   🎯 Green ball position: {pos}")
                print(f"   🎯 Green ball orientation: {orient}")
                
                # Also check current_pos for comparison
                current_pos = getattr(self.viewer, 'current_pos_left', None)
                print(f"   📍 current_pos_left: {current_pos}")
                
                # Check if they're different
                if current_pos:
                    pos_tuple = (current_pos['x'], current_pos['y'], current_pos['z'])
                    different = abs(pos[0] - pos_tuple[0]) > 0.001 or abs(pos[1] - pos_tuple[1]) > 0.001 or abs(pos[2] - pos_tuple[2]) > 0.001
                    print(f"   🔍 VTK actor vs current_pos different? {different}")
                
            else:
                print("   ❌ No green ball (part_origin_sphere) found")
                
        except Exception as e:
            print(f"   ❌ Error getting VTK actor position: {e}")
    
    def exit_app(self):
        print("\n🏁 Debug test complete - exiting...")
        try:
            if self.viewer:
                self.viewer.close()
            self.app.quit()
            sys.exit(0)
        except:
            sys.exit(0)

if __name__ == "__main__":
    debugger = VTKActorDebugger()
    debugger.start_debug()
