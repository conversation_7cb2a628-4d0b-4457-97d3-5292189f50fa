#!/usr/bin/env python3
"""
Simple mouse test with VTK
"""

import sys
import vtk
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget

try:
    from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
except ImportError:
    from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor

class SimpleMouseTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Simple Mouse Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create VTK widget
        self.vtk_widget = QVTKRenderWindowInteractor()
        layout.addWidget(self.vtk_widget)
        
        # Create renderer
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)
        
        # Add renderer to render window
        render_window = self.vtk_widget.GetRenderWindow()
        render_window.AddRenderer(self.renderer)
        
        # Create a simple cube
        cube_source = vtk.vtkCubeSource()
        cube_mapper = vtk.vtkPolyDataMapper()
        cube_mapper.SetInputConnection(cube_source.GetOutputPort())
        
        self.cube_actor = vtk.vtkActor()
        self.cube_actor.SetMapper(cube_mapper)
        self.cube_actor.GetProperty().SetColor(1.0, 0.0, 0.0)  # Red
        
        self.renderer.AddActor(self.cube_actor)
        
        # Get interactor
        self.interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
        
        # Create custom interaction style
        class TestMouseStyle(vtk.vtkInteractorStyle):
            def __init__(self, parent):
                super().__init__()
                self.parent = parent
                self.dragging = False
                self.last_pos = None
                print("🔧 TestMouseStyle created")
                
            def OnLeftButtonDown(self):
                print("🖱️ LEFT DOWN")
                self.dragging = True
                self.last_pos = self.GetInteractor().GetEventPosition()
                
            def OnLeftButtonUp(self):
                print("🖱️ LEFT UP")
                self.dragging = False
                
            def OnMouseMove(self):
                if self.dragging and self.last_pos:
                    current_pos = self.GetInteractor().GetEventPosition()
                    dx = current_pos[0] - self.last_pos[0]
                    dy = current_pos[1] - self.last_pos[1]
                    print(f"🖱️ MOUSE MOVE: dx={dx}, dy={dy}")
                    
                    # Rotate the cube
                    if abs(dx) > 2:
                        self.parent.cube_actor.RotateWXYZ(dx * 0.5, 0, 1, 0)
                    if abs(dy) > 2:
                        self.parent.cube_actor.RotateWXYZ(dy * 0.5, 1, 0, 0)
                    
                    self.parent.vtk_widget.GetRenderWindow().Render()
                    self.last_pos = current_pos
        
        # Set the custom style
        style = TestMouseStyle(self)
        self.interactor.SetInteractorStyle(style)
        print("✅ Custom interaction style set")

        # Initialize interactor
        self.interactor.Initialize()

        # CRITICAL: Enable the interactor
        self.interactor.Enable()
        print("✅ Interactor enabled")

        # Make sure the render window is active
        render_window.Render()
        print("✅ Initial render complete")
        
def main():
    print("🔍 SIMPLE MOUSE TEST")
    print("=" * 30)
    
    app = QApplication(sys.argv)
    
    window = SimpleMouseTest()
    window.show()
    
    print("✅ Window shown - try dragging the red cube with LEFT mouse button")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
