#!/usr/bin/env python3

import sys
import vtk
import numpy as np

def test_vtk_system():
    """Test VTK system and rendering capabilities"""
    
    print("🔍 Testing VTK System...")
    print(f"VTK Version: {vtk.vtkVersion.GetVTKVersion()}")
    print(f"VTK Build Information: {vtk.vtkVersion.GetVTKSourceVersion()}")
    
    # Test basic VTK objects
    try:
        print("\n🔧 Testing VTK Objects...")
        
        # Test cube creation
        cube = vtk.vtkCubeSource()
        cube.SetXLength(10.0)
        cube.SetYLength(10.0) 
        cube.SetZLength(5.0)
        cube.Update()
        print("✅ VTK Cube creation: OK")
        
        # Test polydata
        polydata = cube.GetOutput()
        print(f"✅ Polydata points: {polydata.GetNumberOfPoints()}")
        print(f"✅ Polydata cells: {polydata.GetNumberOfCells()}")
        
        # Test mapper
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputData(polydata)
        print("✅ VTK Mapper creation: OK")
        
        # Test actor
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        actor.GetProperty().SetColor(0.8, 0.2, 0.2)  # Red
        print("✅ VTK Actor creation: OK")
        
        # Test renderer
        renderer = vtk.vtkRenderer()
        renderer.AddActor(actor)
        renderer.SetBackground(0.1, 0.1, 0.1)  # Dark background
        print("✅ VTK Renderer creation: OK")
        
        # Test render window
        render_window = vtk.vtkRenderWindow()
        render_window.AddRenderer(renderer)
        render_window.SetSize(800, 600)
        print("✅ VTK RenderWindow creation: OK")
        
        # Test interactor
        interactor = vtk.vtkRenderWindowInteractor()
        interactor.SetRenderWindow(render_window)
        print("✅ VTK Interactor creation: OK")
        
        print("\n🎯 All VTK components created successfully!")
        
        # Test rendering (without showing window)
        render_window.Render()
        print("✅ VTK Rendering test: OK")
        
        return True
        
    except Exception as e:
        print(f"❌ VTK System Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_opengl():
    """Test OpenGL capabilities"""
    try:
        print("\n🔍 Testing OpenGL...")
        
        # Get OpenGL info
        render_window = vtk.vtkRenderWindow()
        render_window.SetOffScreenRendering(1)  # Don't show window
        render_window.Render()
        
        # Get OpenGL renderer info
        opengl_info = render_window.ReportCapabilities()
        print("✅ OpenGL capabilities retrieved")
        print(f"OpenGL Info: {opengl_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenGL Error: {e}")
        return False

def test_qt_vtk_integration():
    """Test Qt-VTK integration"""
    try:
        print("\n🔍 Testing Qt-VTK Integration...")
        
        from PyQt5 import QtWidgets, QtCore
        from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
        
        print("✅ PyQt5 import: OK")
        print("✅ QVTKRenderWindowInteractor import: OK")
        
        # Test creating Qt application (don't show)
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        # Test QVTKRenderWindowInteractor
        vtk_widget = QVTKRenderWindowInteractor()
        print("✅ QVTKRenderWindowInteractor creation: OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Qt-VTK Integration Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 VTK System Diagnostic Test")
    print("=" * 50)
    
    vtk_ok = test_vtk_system()
    opengl_ok = test_opengl()
    qt_vtk_ok = test_qt_vtk_integration()
    
    print("\n" + "=" * 50)
    print("📊 DIAGNOSTIC RESULTS:")
    print(f"VTK System: {'✅ OK' if vtk_ok else '❌ FAILED'}")
    print(f"OpenGL: {'✅ OK' if opengl_ok else '❌ FAILED'}")
    print(f"Qt-VTK Integration: {'✅ OK' if qt_vtk_ok else '❌ FAILED'}")
    
    if not (vtk_ok and opengl_ok and qt_vtk_ok):
        print("\n🔧 ISSUES DETECTED - This explains the pink box problem!")
    else:
        print("\n✅ All systems OK - Issue may be elsewhere")
