#!/usr/bin/env python3
"""
Test Current System - What Actually Works
Let's test what the current step_viewer.py actually does
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_current_system():
    """Test what the current system actually does"""
    print("🧪 TESTING CURRENT SYSTEM - WHAT ACTUALLY WORKS")
    print("=" * 60)
    
    try:
        # Import the main viewer
        from step_viewer import StepViewerTDK
        print("✅ step_viewer.py imports successfully")
        
        app = QApplication(sys.argv)
        
        # Create viewer
        print("Step 1: Creating viewer...")
        viewer = StepViewerTDK()
        print("✅ StepViewerTDK created successfully")
        
        # Check what methods exist
        print("\n🔍 CHECKING WHAT METHODS EXIST:")
        print("-" * 40)
        
        methods_to_check = [
            'rotate_shape',
            'move_shape', 
            'update_transform_display',
            'update_combined_rotation',
            'on_mouse_interaction_left',
            '_apply_unified_rotation',
            '_update_unified_display'
        ]
        
        for method in methods_to_check:
            if hasattr(viewer, method):
                print(f"✅ {method} - EXISTS")
            else:
                print(f"❌ {method} - MISSING")
        
        # Test what we know exists
        print("\n🔄 TESTING ROTATE_SHAPE METHOD:")
        print("-" * 40)
        
        # Set active viewer
        viewer.active_viewer = "top"
        print(f"Active viewer: {viewer.active_viewer}")
        
        # Check initial values
        print(f"Initial model_rot_left: {viewer.model_rot_left}")
        print(f"Initial current_rot_left: {viewer.current_rot_left}")
        
        # Test rotation
        print("Calling rotate_shape('x', 15)...")
        try:
            viewer.rotate_shape('x', 15)
            print("✅ rotate_shape() called successfully")
            print(f"After model_rot_left: {viewer.model_rot_left}")
            print(f"After current_rot_left: {viewer.current_rot_left}")
        except Exception as e:
            print(f"❌ rotate_shape() failed: {e}")
        
        # Test display update
        print("\n📊 TESTING DISPLAY UPDATE:")
        print("-" * 40)
        try:
            viewer.update_transform_display()
            print("✅ update_transform_display() called successfully")
        except Exception as e:
            print(f"❌ update_transform_display() failed: {e}")
        
        print("\n📋 SUMMARY OF CURRENT STATE:")
        print("=" * 60)
        print("✅ Basic viewer creation works")
        print("✅ rotate_shape() method exists and works")
        print("❌ move_shape() method does not exist")
        print("❌ Mouse rotation handlers do not exist")
        print("❌ Unified system methods do not exist")
        print("\n🎯 NEXT STEPS:")
        print("1. Add missing move_shape() method")
        print("2. Add mouse rotation handlers")
        print("3. Create unified display update system")
        
        # Close after a short delay
        QTimer.singleShot(2000, app.quit)
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ FAILED: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    test_current_system()
