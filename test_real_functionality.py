#!/usr/bin/env python3
"""
Test real functionality by actually loading STEP file and testing transformations
"""

import sys
import os
import time

def test_step_loading_and_unified_system():
    """Test actual STEP loading and unified system calls"""
    print("TESTING REAL STEP LOADING AND UN<PERSON>IED SYSTEM")
    print("=" * 50)
    
    try:
        # Import the actual modules
        from step_viewer import Step<PERSON>iewerTDK
        from step_loader import STEPLoader
        from unittest.mock import Mock
        
        # Create a test viewer with minimal GUI mocking
        class TestViewer(StepViewerTDK):
            def __init__(self):
                # Skip full GUI initialization
                self.active_viewer = "top"
                self.step_loader_left = STEPLoader()
                self.step_loader_right = STEPLoader()
                
                # Mock VTK renderers
                self.vtk_renderer_left = Mock()
                self.vtk_renderer_right = Mock()
                self.vtk_renderer_left.clear_view = Mock()
                self.vtk_renderer_left.display_polydata = Mock(return_value=True)
                self.vtk_renderer_left.fit_view = Mock()
                self.vtk_renderer_left.toggle_bounding_box = Mock()
                
                # Mock GUI elements
                self.top_file_label = Mock()
                self.top_file_label.setText = Mock()
                
                # Track unified system calls
                self.unified_calls = []
                self.old_rotation_calls = []
                self.old_movement_calls = []
                
            def unified_transform(self, operation, **kwargs):
                """Override to track calls"""
                self.unified_calls.append((operation, kwargs))
                print(f"UNIFIED_TRANSFORM CALLED: {operation} with {kwargs}")
                
                # Call the real implementation
                return super().unified_transform(operation, **kwargs)
                
            def _apply_unified_rotation(self, axis, degrees):
                """Override to track old system calls"""
                self.old_rotation_calls.append((axis, degrees))
                print(f"OLD_ROTATION CALLED: {axis}, {degrees}")
                return True
                
            def _apply_unified_movement(self, axis, amount):
                """Override to track old system calls"""
                self.old_movement_calls.append((axis, amount))
                print(f"OLD_MOVEMENT CALLED: {axis}, {amount}")
                return True
                
            # Mock methods that would cause GUI issues
            def extract_step_transformation_data(self, viewer):
                pass
            def store_original_actor_transforms(self, viewer):
                pass
            def setup_text_overlay_for_viewer(self, viewer):
                pass
        
        # Create test viewer
        viewer = TestViewer()
        
        # Test 1: Load actual STEP file
        print("\n1. TESTING STEP FILE LOADING...")
        step_file = "SOIC16P127_1270X940X610L89X51.STEP"
        
        if os.path.exists(step_file):
            print(f"Loading STEP file: {step_file}")
            result = viewer.load_step_file_direct(step_file)
            print(f"Load result: {result}")
            
            # Check if unified system was called for loading
            load_calls = [call for call in viewer.unified_calls if call[0] == 'load']
            if load_calls:
                print(f"SUCCESS: Unified system called for loading: {len(load_calls)} times")
                print(f"  Load call details: {load_calls[0]}")
            else:
                print("FAIL: Unified system NOT called for loading")
                
            # Check if STEP data was actually loaded
            if hasattr(viewer.step_loader_left, 'current_polydata') and viewer.step_loader_left.current_polydata:
                points = viewer.step_loader_left.current_polydata.GetNumberOfPoints()
                cells = viewer.step_loader_left.current_polydata.GetNumberOfCells()
                print(f"SUCCESS: STEP geometry loaded - {points} points, {cells} cells")
                
                if points > 8:
                    print("SUCCESS: Real geometry (not just simple cube)")
                else:
                    print("INFO: Fallback geometry (OpenCASCADE not available)")
            else:
                print("FAIL: No STEP geometry loaded")
        else:
            print(f"FAIL: STEP file not found: {step_file}")
            return False
            
        # Test 2: Button rotations
        print("\n2. TESTING BUTTON ROTATIONS...")
        viewer.unified_calls.clear()  # Clear previous calls
        
        # Test rotate_shape
        viewer.rotate_shape('x', 15)
        viewer.rotate_shape('y', -30)
        viewer.rotate_shape('z', 45)
        
        rotate_calls = [call for call in viewer.unified_calls if call[0] == 'rotate']
        if len(rotate_calls) == 3:
            print("SUCCESS: All button rotations called unified system")
            for i, call in enumerate(rotate_calls):
                axis = call[1].get('axis')
                degrees = call[1].get('degrees')
                expected = [('x', 15), ('y', -30), ('z', 45)][i]
                if (axis, degrees) == expected:
                    print(f"  SUCCESS: Rotation {i+1}: {axis}={degrees} degrees - CORRECT")
                else:
                    print(f"  FAIL: Rotation {i+1}: expected {expected}, got ({axis}, {degrees})")
        else:
            print(f"FAIL: Expected 3 rotation calls, got {len(rotate_calls)}")
            
        # Test 3: Button movements
        print("\n3. TESTING BUTTON MOVEMENTS...")
        viewer.unified_calls.clear()  # Clear previous calls
        
        # Test move_shape
        viewer.move_shape('x', 5.5)
        viewer.move_shape('y', -2.3)
        viewer.move_shape('z', 10.0)
        
        move_calls = [call for call in viewer.unified_calls if call[0] == 'move']
        if len(move_calls) == 3:
            print("SUCCESS: All button movements called unified system")
            for i, call in enumerate(move_calls):
                axis = call[1].get('axis')
                amount = call[1].get('amount')
                expected = [('x', 5.5), ('y', -2.3), ('z', 10.0)][i]
                if (axis, amount) == expected:
                    print(f"  SUCCESS: Movement {i+1}: {axis}={amount} units - CORRECT")
                else:
                    print(f"  FAIL: Movement {i+1}: expected {expected}, got ({axis}, {amount})")
        else:
            print(f"FAIL: Expected 3 movement calls, got {len(move_calls)}")
            
        # Test 4: Check that old system is still called by new system
        print("\n4. TESTING OLD SYSTEM INTEGRATION...")
        if viewer.old_rotation_calls:
            print(f"SUCCESS: Old rotation system called {len(viewer.old_rotation_calls)} times")
            print(f"  Old rotation calls: {viewer.old_rotation_calls}")
        else:
            print("FAIL: Old rotation system not called")
            
        if viewer.old_movement_calls:
            print(f"SUCCESS: Old movement system called {len(viewer.old_movement_calls)} times")
            print(f"  Old movement calls: {viewer.old_movement_calls}")
        else:
            print("FAIL: Old movement system not called")
            
        print("\nSUCCESS: Real functionality test completed")
        return True
        
    except Exception as e:
        print(f"FAIL: Real functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("REAL FUNCTIONALITY TEST")
    print("=" * 60)
    print("Testing actual STEP file loading and unified system...")
    print()
    
    success = test_step_loading_and_unified_system()
    
    print("\n" + "=" * 60)
    if success:
        print("ALL REAL FUNCTIONALITY TESTS PASSED!")
        print("\nThe system correctly:")
        print("- Loads actual STEP files")
        print("- Calls unified system for file loading")
        print("- Calls unified system for button rotations")
        print("- Calls unified system for button movements")
        print("- Integrates with old system for compatibility")
        print("\nThe unified transformation system is WORKING!")
    else:
        print("REAL FUNCTIONALITY TESTS FAILED!")
        print("The system needs fixes for actual operation")
        
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
