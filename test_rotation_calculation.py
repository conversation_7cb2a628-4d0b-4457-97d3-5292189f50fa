#!/usr/bin/env python3
"""
Test the rotation calculation directly
"""

import numpy as np

def test_rotation_calculation():
    print("=== ROTATION CALCULATION TEST ===")
    
    # Test with a 90-degree X rotation
    rotation_x = 90.0
    rotation_y = 0.0
    rotation_z = 0.0
    
    print(f"Input rotation: X={rotation_x}° Y={rotation_y}° Z={rotation_z}°")
    
    # Create rotation matrices (same as in step_loader.py)
    rx = np.radians(rotation_x)
    ry = np.radians(rotation_y)
    rz = np.radians(rotation_z)
    
    print(f"Radians: X={rx:.6f} Y={ry:.6f} Z={rz:.6f}")
    
    # Rotation matrices (same order as VTK: X, then Y, then Z)
    Rx = np.array([[1, 0, 0],
                  [0, np.cos(rx), -np.sin(rx)],
                  [0, np.sin(rx), np.cos(rx)]])
    
    Ry = np.array([[np.cos(ry), 0, np.sin(ry)],
                  [0, 1, 0],
                  [-np.sin(ry), 0, np.cos(ry)]])
    
    Rz = np.array([[np.cos(rz), -np.sin(rz), 0],
                  [np.sin(rz), np.cos(rz), 0],
                  [0, 0, 1]])
    
    print(f"\nRotation matrix Rx (90° X rotation):")
    print(Rx)
    
    # Combined rotation matrix
    R = Rz @ Ry @ Rx
    
    print(f"\nCombined rotation matrix R:")
    print(R)
    
    # Apply to standard coordinate system
    x_axis = R @ np.array([1, 0, 0])  # New X direction
    y_axis = R @ np.array([0, 1, 0])  # New Y direction
    z_axis = R @ np.array([0, 0, 1])  # New Z direction
    
    print(f"\nNew coordinate system after 90° X rotation:")
    print(f"   X-axis: [{x_axis[0]:.6f}, {x_axis[1]:.6f}, {x_axis[2]:.6f}]")
    print(f"   Y-axis: [{y_axis[0]:.6f}, {y_axis[1]:.6f}, {y_axis[2]:.6f}]")
    print(f"   Z-axis: [{z_axis[0]:.6f}, {z_axis[1]:.6f}, {z_axis[2]:.6f}]")
    
    # Test what happens to some common direction vectors
    print(f"\nTesting common direction vectors:")
    
    # Test (1,0,0) - should remain (1,0,0)
    test_vec = np.array([1, 0, 0])
    result = R @ test_vec
    print(f"   (1,0,0) -> ({result[0]:.3f}, {result[1]:.3f}, {result[2]:.3f})")
    
    # Test (0,1,0) - should become (0,0,1) after 90° X rotation
    test_vec = np.array([0, 1, 0])
    result = R @ test_vec
    print(f"   (0,1,0) -> ({result[0]:.3f}, {result[1]:.3f}, {result[2]:.3f})")
    
    # Test (0,0,1) - should become (0,-1,0) after 90° X rotation
    test_vec = np.array([0, 0, 1])
    result = R @ test_vec
    print(f"   (0,0,1) -> ({result[0]:.3f}, {result[1]:.3f}, {result[2]:.3f})")
    
    # Test (0,-1,0) - should become (0,0,-1) after 90° X rotation
    test_vec = np.array([0, -1, 0])
    result = R @ test_vec
    print(f"   (0,-1,0) -> ({result[0]:.3f}, {result[1]:.3f}, {result[2]:.3f})")
    
    print(f"\n✅ Rotation calculation test completed")
    
    # Check if the rotation is actually changing things
    identity_test = np.allclose(R, np.eye(3))
    print(f"Is rotation matrix identity? {identity_test}")
    
    if identity_test:
        print("❌ Problem: Rotation matrix is identity - no rotation is being applied!")
    else:
        print("✅ Rotation matrix is not identity - rotation should be applied")

if __name__ == "__main__":
    test_rotation_calculation()
